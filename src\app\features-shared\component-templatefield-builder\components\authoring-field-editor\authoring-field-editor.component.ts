import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-authoring-field-editor',
    templateUrl: './authoring-field-editor.component.html',
    styleUrls: ['./authoring-field-editor.component.scss'],
    standalone: false
})
export class AuthoringFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  authoringForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.authoringForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }

      this.setFormValues();
    }
  }

  createForm() {
    this.authoringForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      description: [this.component?.templateField?.helpDescription],
      placeHolderText: [this.component?.templateField?.placeHolderText],
      defaultText: [this.component?.templateField?.defaultText],
      caption: [this.component?.templateField?.helpTitle],
      rowNumber: [this.component?.builderRowNumber ?? 0],
      hoverSortOrder: [this.component?.hoverSortOrder ?? 0],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      systemProperty: [this.component?.templateField?.systemProperty?.id],
      isInherit: [this.component?.templateField?.isInherit ?? false],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.authoringForm) {
      return;
    }

    this.authoringForm.controls.label.setValue(this.component.templateField.label);
    this.authoringForm.controls.description.setValue(this.component.templateField.helpDescription);
    this.authoringForm.controls.placeHolderText.setValue(this.component.templateField.placeHolderText);
    this.authoringForm.controls.defaultText.setValue(this.component.templateField.defaultText);
    this.authoringForm.controls.caption.setValue(this.component.templateField.helpTitle);
    this.authoringForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.authoringForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.authoringForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.authoringForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.authoringForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.authoringForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.authoringForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.authoringForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.authoringForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.authoringForm.controls.systemProperty.setValue(this.component.templateField.systemProperty);
    this.authoringForm.controls.isInherit.setValue(this.component.templateField.isInherit);
    this.authoringForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.authoringForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.authoringForm.controls.isInherit.setValue(this.component?.templateField?.isInherit);
    this.authoringForm.controls.isVisibleRepository.setValue(this.component?.templateField?.isVisibleRepository);
    this.authoringForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.authoringForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.authoringForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.authoringForm.valid) {
      this.component.templateField.label = this.authoringForm.controls.label.value;
      this.component.templateField.helpDescription = this.authoringForm.controls.description.value;
      this.component.templateField.placeHolderText = this.authoringForm.controls.placeHolderText.value;
      this.component.templateField.defaultText = this.authoringForm.controls.defaultText.value;
      this.component.templateField.helpTitle = this.authoringForm.controls.caption.value;
      this.component.builderRowNumber = this.authoringForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.authoringForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.authoringForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.authoringForm.controls.isRequiredField.value;
      this.component.templateField.isVisibleRepository = this.authoringForm.controls.isVisibleRepository.value;
      this.component.templateField.isBuilderEnabled = this.authoringForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.authoringForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.authoringForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.authoringForm.controls.isViewField.value;
      this.component.templateField.isInherit = this.authoringForm.controls.isInherit.value;
      this.component.templateField.colspan = this.authoringForm.controls.colspan.value;
      this.component.templateField.colNumber = this.authoringForm.controls.colNumber.value;
      this.component.templateField.isInherit = this.authoringForm.controls.isInherit.value;
      this.component.templateField.isVisibleRepository = this.authoringForm.controls.isVisibleRepository.value;
      this.component.templateField.useMaxWidth = this.authoringForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
