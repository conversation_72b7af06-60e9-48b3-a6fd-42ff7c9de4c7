import { EventEmitter, Directive, HostBinding, HostListener, Output } from '@angular/core';

@Directive({
  selector: '[appFileUploadControl]',
  standalone: true,
})
export class FileUploadControlDirective {
  @Output() fileDropped = new EventEmitter<any>();

  @HostBinding('style.background-color') private background = '#181818';
  constructor() {}

  @HostListener('dragover', ['$event']) public onDragOver(evt: any) {
    evt.preventDefault();
    evt.stopPropagation();
    this.background = '#979797';
  }

  @HostListener('dragleave', ['$event']) public onDragLeave(evt: any) {
    evt.preventDefault();
    evt.stopPropagation();
    this.background = '#181818';
  }

  @HostListener('drop', ['$event']) public onFileDrop(evt: any) {
    evt.preventDefault();
    evt.stopPropagation();
    const files = evt.dataTransfer.files;
    if (files.length > 0) {
      this.fileDropped.emit(files);
    }
    this.background = '#181818';
  }
}
