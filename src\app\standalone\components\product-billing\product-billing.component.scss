.table-container {
  display: flex;
  flex-direction: column;
  align-content: stretch;
  align-items: stretch;

  .table-grid {
    flex: 1;
    width: 100%;
    background-color: #444444;
  }

  .mat-mdc-header-cell {
    color: white;
    background-color: rgba(34, 34, 34) !important;
    border-right: 1px solid rgba(155, 152, 152, 0.5);
    padding-left: 15px;
  }
  .mat-column-invoiceId {
    background-color: rgba(42, 42, 42);
    width: 900px;
  }
  .mat-column-dueDate {
    background-color: rgba(42, 42, 42);
    width: 150px;
  }
  .mat-column-amount {
    background-color: rgba(42, 42, 42);
    width: 200px;
  }
  .mat-column-status {
    background-color: rgba(42, 42, 42);
    width: 150px;
  }
  .mat-column-download {
    background-color: rgba(42, 42, 42);
    width: 150px;
  }
  .mat-mdc-cell {
    color: white;
    max-width: 900px;
    padding-left: 15px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .download-link {
    text-decoration: underline;
    color: rgba(250, 167, 0) !important;
    cursor: pointer;
  }
}

.mat-mdc-paginator {
  background-color: #aaa;
}
