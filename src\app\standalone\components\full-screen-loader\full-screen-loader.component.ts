import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { LoaderService } from '@app/core/services/loader.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-full-screen-loader',
  templateUrl: './full-screen-loader.component.html',
  styleUrls: ['./full-screen-loader.component.scss'],
  imports: [CommonModule],
  standalone: true,
})
export class FullScreenLoaderComponent implements OnInit {
  visible$: Observable<boolean>;
  message$: Observable<string>;

  constructor(private loaderService: LoaderService) {}

  ngOnInit() {
    this.visible$ = this.loaderService.loaderVisible$;
    this.message$ = this.loaderService.loaderMessage$;
  }
}
