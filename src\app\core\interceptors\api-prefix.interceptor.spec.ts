import { HttpClient, HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { inject, TestBed } from '@angular/core/testing';
import { environment } from '@env/environment';
import { ApiPrefixInterceptor } from './api-prefix.interceptor';

describe('ApiPrefixInterceptor', () => {
  let HTTP: HttpClient;
  let HTTPMOCK: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: HTTP_INTERCEPTORS,
          useClass: ApiPrefixInterceptor,
          multi: true,
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });
  });

  beforeEach(inject([HttpClient, HttpTestingController], (http: HttpClient, httpMock: HttpTestingController) => {
    HTTP = http;
    HTTPMOCK = httpMock;
  }));

  afterEach(() => {
    HTTPMOCK.verify();
  });

  it('should prepend environment.serverUrl to the request url', () => {
    // Act
    HTTP.get('/toto').subscribe();

    // Assert
    HTTPMOCK.expectOne({ url: environment.API_URL + '/toto' });
  });

  it('should not prepend environment.serverUrl to request url', () => {
    // Act
    HTTP.get('hTtPs://domain.com/toto').subscribe();

    // Assert
    HTTPMOCK.expectOne({ url: 'hTtPs://domain.com/toto' });
  });
});
