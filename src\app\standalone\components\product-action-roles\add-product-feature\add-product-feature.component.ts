import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IFeatureSearch, IProductFeature } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { PopoverController, IonicModule } from '@ionic/angular';
import { debounceTime, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-add-product-feature',
    templateUrl: './add-product-feature.component.html',
    styleUrls: ['./add-product-feature.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule]
})
export class AddProductFeatureComponent implements OnInit, OnDestroy {
  componentDestroyed$: Subject<boolean> = new Subject();
  searchForm: UntypedFormGroup;
  featureSearch: UntypedFormControl;
  features: IFeatureSearch[];
  productId: string;
  constructor(
    private popoverController: PopoverController,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.createFormControls();
    this.createForm();
    this.searchFeatures();
  }

  createFormControls() {
    this.featureSearch = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      featureSearch: this.featureSearch,
    });
  }

  searchFeatures() {
    if (this.featureSearch.value !== '') {
      this.features = [];
      this.dataService
        .searchFeaturesByProductId(this.productId, this.featureSearch.value)
        .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
        .subscribe((res: IFeatureSearch[]) => {
          this.features = res;
        });
    }
  }

  addProductFeature(featId: string) {
    this.dataService
      .addProductFeatureByProductId(this.productId, featId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((addedProdFeature: IProductFeature) => {
        this.close(addedProdFeature);
      });
  }

  close(res: IProductFeature) {
    this.popoverController.dismiss(res);
  }

  cancel(res: boolean) {
    this.popoverController.dismiss(res);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
