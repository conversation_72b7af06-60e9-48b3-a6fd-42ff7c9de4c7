import { NgClass } from '@angular/common';
import { Component, Input } from '@angular/core';

@Component({
    selector: 'app-builder-side-panel-description',
    templateUrl: './builder-side-panel-description.component.html',
    styleUrls: ['./builder-side-panel-description.component.scss'],
    imports: [NgClass]
})
export class BuilderSidePanelDescriptionComponent {
  @Input() text: string | null;
  @Input() noPadding = false;
  constructor() {}
}
