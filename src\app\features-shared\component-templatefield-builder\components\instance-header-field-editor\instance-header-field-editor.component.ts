import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-instance-header-field-editor',
    templateUrl: './instance-header-field-editor.component.html',
    styleUrls: ['./instance-header-field-editor.component.scss'],
    standalone: false
})
export class InstanceHeaderFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  headerFieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(private formBuilder: UntypedFormBuilder) {}

  get isInheritControl(): AbstractControl {
    return this.headerFieldForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.headerFieldForm = this.formBuilder.group({
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.headerFieldForm) {
      return;
    }

    this.headerFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.headerFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.headerFieldForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.headerFieldForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.headerFieldForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.headerFieldForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.headerFieldForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.headerFieldForm.controls.useMaxWidth.setValue(this.component.templateField.useMaxWidth);
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.headerFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.headerFieldForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.headerFieldForm.valid) {
      this.component.hoverSortOrder = this.headerFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.headerFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.headerFieldForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.headerFieldForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.headerFieldForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.headerFieldForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.headerFieldForm.controls.isViewField.value;
      this.component.templateField.useMaxWidth = this.headerFieldForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
