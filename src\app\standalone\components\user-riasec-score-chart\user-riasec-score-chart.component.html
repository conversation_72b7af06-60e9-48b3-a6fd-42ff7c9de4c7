@if (score) {
  <h1 class="title">Who are you?</h1>
  <h2 class="riasec-description">
    Your results are in! You're a
    <font color="white">{{ riasecResultsSorted[0]?.name }}</font>
    ,
    <font color="white">{{ riasecResultsSorted[1]?.name }}</font>
    and a
    <font color="white">{{ riasecResultsSorted[2]?.name }}</font>
    .
  </h2>
  <div class="riasec-grid">
    @for (riasec of riasecResultsSorted.slice(0, 3); track riasec.name; let index = $index) {
      <div class="riasec-card" [ngClass]="riasec?.name">
        <div class="content">
          <img class="riasec-count" [src]="numberedIcons[index]" />
          <img [src]="riasec?.img" alt="" />
          <span class="riasec-name">A {{ riasec?.name }}</span>
          <span class="riasec-description">{{ riasec?.description }}</span>
        </div>
      </div>
    }
    <div class="riasec-card Stats">
      <div class="riasec-stats-content">
        <div class="riasec-stats-row">
          <div class="riasec-stats-row">
            <img class="riasec-stats-icon" [src]="sanitizeUrl(statsIconUrl)" alt="" />
            <span class="riasec-stats-title">Your Score</span>
          </div>
          <!-- <ion-button (click)="reset()">
            <span>Reset</span>
          </ion-button> -->
        </div>

        <div id="riasec-stats-list">
          @for (riasec of riasecResults; track riasec.name) {
            <div class="riasec-stats-list-item-row">
              <span class="name">{{ riasec?.name }}</span>
              <span class="value">{{ riasec?.value }}%</span>
            </div>
            <ion-progress-bar [ngClass]="riasec?.name" [value]="riasec.value / 100"></ion-progress-bar>
          }
          <span class="riasec-footer">* Based on the Holland Codes model</span>
        </div>
      </div>
    </div>
  </div>
}
