import { AccordionListValueComponent } from '@app/standalone/components/accordion-list-value/accordion-list-value.component';
import { AnalyticsComponent } from '@app/standalone/components/analytics/analytics.component';
import { AuthoringHeaderComponent } from '@app/standalone/components/authoring-header/authoring-header.component';
import { BannerComponent } from '@app/standalone/components/banner/banner.component';
import { BulletListValueComponent } from '@app/standalone/components/bullet-list-value/bullet-list-value.component';
import { CommunicationManagerComponent } from '@app/standalone/components/communication-manager/communication-manager.component';
import { ContactInfoValueComponent } from '@app/standalone/components/contact-info-value/contact-info-value.component';
import { CriteriaManagerComponent } from '@app/standalone/components/criteria-manager/criteria-manager.component';
import { DynamicTextValueComponent } from '@app/standalone/components/dynamic-text-value/dynamic-text-value.component';
import { EmailChipsValueComponent } from '@app/standalone/components/email-chips-value/email-chips-value.component';
import { ExternalHtmlBlockValueComponent } from '@app/standalone/components/external-html-block-value/external-html-block-value.component';
import { FileDownloadControlComponent } from '@app/standalone/components/file-download-control/file-download-control.component';
import { FlashCardListValueComponent } from '@app/standalone/components/flash-card-list-value/flash-card-list-value.component';
import { FullNameValueComponent } from '@app/standalone/components/full-name-value/full-name-value.component';
import { GoogleMapsAutocompleteValueComponent } from '@app/standalone/components/google-maps-autocomplete-value/google-maps-autocomplete-value.component';
import { Html5GameComponent } from '@app/standalone/components/html5-game/html5-game.component';
import { IconAndDropdownValueComponent } from '@app/standalone/components/icon-and-dropdown-value/icon-and-dropdown-value.component';
import { IconAndTextValueComponent } from '@app/standalone/components/icon-and-text-value/icon-and-text-value.component';
import { ImageCenteredValueComponent } from '@app/standalone/components/image-centered-value/image-centered-value.component';
import { ImageViewerValueComponent } from '@app/standalone/components/image-viewer-value/image-viewer-value.component';
import { InstanceDetailsValueComponent } from '@app/standalone/components/instance-details-value/instance-details-value.component';
import { InstanceInterestValueComponent } from '@app/standalone/components/instance-interest-value/instance-interest-value.component';
import { LabelComponent } from '@app/standalone/components/label/label.component';
import { MediaAndTextValueComponent } from '@app/standalone/components/media-and-text-value/media-and-text-value.component';
import { NetworkOrganizationExpansionAccordionComponent } from '@app/standalone/components/network-organization-expansion-accordion/network-organization-expansion-accordion.component';
import { NetworkQlikViewDownloadComponent } from '@app/standalone/components/network-qlik-view-download/network-qlik-view-download.component';
import { OrganizationExpansionControlComponent } from '@app/standalone/components/organization-expansion-control/organization-expansion-control.component';
import { OrganizationSettingsComponent } from '@app/standalone/components/organization-settings/organization-settings.component';
import { PageBannerComponent } from '@app/standalone/components/page-banner/page-banner.component';
import { PasswordValueComponent } from '@app/standalone/components/password-value/password-value.component';
import { PeopleTableComponent } from '@app/standalone/components/people-table/people-table.component';
import { PersonaTagPopoverComponent } from '@app/standalone/components/persona-tag-popover/persona-tag-popover.component';
import { PhoneNumberValueComponent } from '@app/standalone/components/phone-number-value/phone-number-value.component';
import { ProductActionRolesComponent } from '@app/standalone/components/product-action-roles/product-action-roles.component';
import { ProductBillingComponent } from '@app/standalone/components/product-billing/product-billing.component';
import { ProductExpansionAccordionComponent } from '@app/standalone/components/product-expansion-accordion/product-expansion-accordion.component';
import { ProductHistoryComponent } from '@app/standalone/components/product-history/product-history.component';
import { QrCodeComponent } from '@app/standalone/components/qr-code/qr-code.component';
import { SelectOptionValueComponent } from '@app/standalone/components/select-option-value/select-option-value.component';
import { TagValuesComponent } from '@app/standalone/components/tag-values/tag-values.component';
import { TextAndButtonValueComponent } from '@app/standalone/components/text-and-button-value/text-and-button-value.component';
import { TextValueComponent } from '@app/standalone/components/text-value/text-value.component';
import { UserNotificationPreferencesComponent } from '@app/standalone/components/user-notification-preferences/user-notification-preferences.component';
import { UserRiasecScoreChartComponent } from '@app/standalone/components/user-riasec-score-chart/user-riasec-score-chart.component';
import { UsersTableComponent } from '@app/standalone/components/users-table/users-table.component';
import { VideoPlayerComponent } from '@app/standalone/components/video-player/video-player.component';
import { WebsiteLinkValueComponent } from '@app/standalone/components/website-link-value/website-link-value.component';
import { ComponentSelectorComponent } from './components/component-selector/component-selector.component';
import { HeadingValueComponent } from '@app/standalone/components/heading-value/heading-value.component';
import { CheckboxValueComponent } from '@app/standalone/components/checkbox-value/checkbox-value.component';
import { QlikAnalyticsComponent } from '@app/standalone/components/qlik-analytics/qlik-analytics.component';
import { QlikAnalyticsTypeSelectorComponent } from '@app/standalone/components/qlik-analytics-type-selector/qlik-analytics-type-selector.component';
import { ButtonValueComponent } from '@app/standalone/components/button-value/button-value.component';
import { SpacingValueComponent } from '@app/standalone/components/spacing-value/spacing-value.component';
import { GradingTableComponent } from '@app/standalone/components/grading-table/grading-table.component';

export const featureComponents: any[] = [ComponentSelectorComponent];

export const standaloneComponents: any[] = [
  TextValueComponent,
  DynamicTextValueComponent,
  NetworkQlikViewDownloadComponent,
  LabelComponent,
  SelectOptionValueComponent,
  TagValuesComponent,
  PersonaTagPopoverComponent,
  ImageViewerValueComponent,
  OrganizationExpansionControlComponent,
  VideoPlayerComponent,
  FileDownloadControlComponent,
  EmailChipsValueComponent,
  ProductExpansionAccordionComponent,
  ProductHistoryComponent,
  ProductBillingComponent,
  ProductActionRolesComponent,
  UsersTableComponent,
  AnalyticsComponent,
  PeopleTableComponent,
  ExternalHtmlBlockValueComponent,
  CommunicationManagerComponent,
  OrganizationSettingsComponent,
  NetworkOrganizationExpansionAccordionComponent,
  UserNotificationPreferencesComponent,
  CriteriaManagerComponent,
  BannerComponent,
  GoogleMapsAutocompleteValueComponent,
  HeadingValueComponent,
  MediaAndTextValueComponent,
  IconAndTextValueComponent,
  IconAndDropdownValueComponent,
  ImageCenteredValueComponent,
  TextAndButtonValueComponent,
  InstanceInterestValueComponent,
  BulletListValueComponent,
  AccordionListValueComponent,
  FlashCardListValueComponent,
  InstanceDetailsValueComponent,
  PhoneNumberValueComponent,
  FullNameValueComponent,
  PasswordValueComponent,
  ContactInfoValueComponent,
  WebsiteLinkValueComponent,
  AuthoringHeaderComponent,
  PageBannerComponent,
  UserRiasecScoreChartComponent,
  Html5GameComponent,
  QrCodeComponent,
  CheckboxValueComponent,
  QlikAnalyticsComponent,
  QlikAnalyticsTypeSelectorComponent,
  SpacingValueComponent,
  ButtonValueComponent,
  GradingTableComponent,
];
