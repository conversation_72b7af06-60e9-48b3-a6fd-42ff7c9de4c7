@if (bannerBlockForm) {
  <form [formGroup]="bannerBlockForm" class="parent-container">
    <ion-grid>
      <ion-card class="banner-card-container">
        <ion-row>
          <ion-col>
            <div class="header-container">
              <h4>Banner</h4>
            </div>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control formControlName="title" [backgroundColor]="backgroundColor" [label]="'Title'" [placeHolder]="'Enter Title'"></app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <div class="message-container">
              <ion-label position="stacked"> Message </ion-label>
              <div class="quill-container">
                <app-content-quill-editor
                  [hideQuillPersonalize]="hideQuillPersonalize"
                  (dataChanged)="setMessageValue($event)"
                  [value]="bannerBlockForm.controls['message'].value"
                  [placeHolder]="'Enter Message'">
                </app-content-quill-editor>
              </div>
            </div>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-select-option-control [options]="notificationTypes" formControlName="notificationType" [backgroundColor]="backgroundColor" [label]="'Notification Type'" [placeHolder]="'Information'">
            </app-select-option-control>
          </ion-col>
          <ion-col>
            <app-select-option-control
              [options]="behaviours"
              formControlName="behaviour"
              [backgroundColor]="backgroundColor"
              [label]="'On-click Behaviour'"
              [placeHolder]="'Open the instance'"></app-select-option-control>
          </ion-col>
        </ion-row>
      </ion-card>
    </ion-grid>
  </form>
}
