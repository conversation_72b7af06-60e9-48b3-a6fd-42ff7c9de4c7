<div class="parent-container">
  @if (imageCenteredList.length > 0) {
  <div class="swiper-main-container">
    <swiper-container #swiper [config]="swiperConfig" class="swiper">
      @for (item of imageCenteredList; track item) {
      <swiper-slide class="swiper-container">
        <div [ngClass]="{ 'swiper-slide': !item.noClickPreview}">
          <div class="swiper-wrapper">
            <div class="image-container">
              <img [style]="{'height':item.height, 'aspect-ratio': item.aspectRatio, 'object-fit':item.objectFit}"
                src="{{ item.assetUrl }}"
                (click)="openImageModal(item.assetUrl, item.caption, item.noClickPreview)" />
            </div>
            @if(item.caption){
            <div class="bottom-container">
              <div class="text-container">
                <p class="caption">{{ item.caption }}</p>
              </div>
              <div class="border-container">
                <div class="border-bottom"></div>
              </div>
            </div>
            }
          </div>
        </div>
      </swiper-slide>
      }
    </swiper-container>
    <div class="swiper-pagination paging-buttons"></div>
    @if (imageCenteredList.length > 1) {
    <div>
      <div (click)="slidePrev()" class="swiper-button-prev nav-button"></div>
      <div (click)="slideNext()" class="swiper-button-next nav-button"></div>
    </div>
    }
  </div>
  }
</div>
