PODS:
  - Capacitor (6.0.0):
    - Capacitor<PERSON>ordova
  - CapacitorApp (6.0.0):
    - Capacitor
  - CapacitorCordova (6.0.0)
  - CapacitorHaptics (6.0.0):
    - Capacitor
  - CapacitorKeyboard (6.0.0):
    - Capacitor
  - CapacitorStatusBar (6.0.0):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"

SPEC CHECKSUMS:
  Capacitor: 559d073c4ca6c27f8e7002c807eea94c3ba435a9
  CapacitorApp: 9d53aec7101f7b030a950c5bdc4df8612576b279
  CapacitorCordova: 8c4bfdf69368512e85b1d8b724dd7546abeb30af
  CapacitorHaptics: 9ebc9363f0e9b8eb4295088a0b474530acf1859b
  CapacitorKeyboard: deacbd09d8d1029c3681197fb05d206b721d5f73
  CapacitorStatusBar: 2e4369f99166125435641b1908d05f561eaba6f6

PODFILE CHECKSUM: 481b90c01cf7ba51cc0b3b166926c5af446138fd

COCOAPODS: 1.15.2
