import { KeyValue } from '../dtos/KeyValue';

export const thumbNailSizeList: KeyValue[] = [
  {
    id: 'landscape',
    value: 'Landscape',
  },
  {
    id: 'portrait',
    value: 'Portrait',
  },
];

export const titleStyleList: KeyValue[] = [
  {
    id: 'h1',
    value: 'Header 1',
  },
  {
    id: 'h2',
    value: 'Header 2',
  },
  {
    id: 'h3',
    value: 'Header 3',
  },
  {
    id: 'h4',
    value: 'Header 4',
  },
];
export const descStyleList: KeyValue[] = [
  {
    id: 'p',
    value: 'Normal',
  },
  {
    id: 'i',
    value: 'Italics',
  },
  {
    id: 'strong',
    value: 'Bold',
  },
];
export const allignmentList: KeyValue[] = [
  {
    id: 'left',
    value: 'Left',
  },
  {
    id: 'center',
    value: 'Center',
  },
];

export const displayTypeList: KeyValue[] = [
  {
    id: true,
    value: 'Carousel',
  },
  {
    id: false,
    value: 'Block',
  },
];

export const selectedByList: KeyValue[] = [
  {
    id: '4',
    value: 'Alphabetical',
  },
  {
    id: '1',
    value: 'Random',
  },
  {
    id: '2',
    value: 'Recently Modified',
  },
  {
    id: '3',
    value: 'Oldest',
  },
  {
    id: '5',
    value: 'GeoLocation',
  },
  {
    id: '6',
    value: 'RIASEC',
  },
  {
    id: '7',
    value: 'RIASEC Split',
  },
  {
    id: '8',
    value: 'Newest',
  },
  {
    id: '9',
    value: 'Feature',
  },
  {
    id: '10',
    value: 'Tags',
  },
];

export const limitToList: KeyValue[] = [
  {
    id: '1',
    value: 'Items',
  },
  {
    id: '2',
    value: 'Minutes',
  },
  {
    id: '3',
    value: 'Hours',
  },
];

export const instanceDisplayList: KeyValue[] = [
  {
    id: '1',
    value: 'Instance Viewer',
  },
  {
    id: '2',
    value: 'Instance Builder',
  },
  {
    id: '3',
    value: 'Player View',
  },
  {
    id: '4',
    value: 'Instance Player View',
  },
];

export const showByList: KeyValue[] = [
  {
    id: '1',
    value: 'Features',
  },
  {
    id: '32',
    value: 'Instances',
  },
  {
    id: '4',
    value: 'Tags',
  },
  {
    id: '16',
    value: 'Organizations',
  },
  {
    id: '64',
    value: 'FeatureSite',
  },
  {
    id: '128',
    value: 'DefaultInstance',
  },
  {
    id: '256',
    value: 'Achievements',
  },
  {
    id: '512',
    value: 'Networks',
  },
  //{
  //  id: '512',
  //  value: 'Favorites'
  //}
];

export const rowFilterFieldList = [
  {
    id: '1',
    value: 'Feature Name',
    conditionBw: 255,
    valueInputType: 'text',
    optionBw: 1,
  },
  //{
  //  id: '2',
  //  value: 'Feature Access'
  //},
  {
    id: '3',
    value: 'Product Name',
    conditionBw: 255,
    valueInputType: 'text',
    optionBw: 1,
  },
  {
    id: '4',
    value: 'User Role',
    conditionBw: 15,
    valueInputType: 'role',
    optionBw: 3,
  },
  {
    id: '5',
    value: 'User Enrollment',
    conditionBw: 3,
    valueInputType: 'enrollment',
    optionBw: 3,
  },
  {
    id: '6',
    value: 'Feature Type',
    conditionBw: 15,
    valueInputType: 'featureType',
    optionBw: 1,
  },
  {
    id: '7',
    value: 'Organization Type',
    conditionBw: 773,
    valueInputType: 'tag',
    optionBw: 1,
  },
  {
    id: '8',
    value: 'Organization Name',
    conditionBw: 1023,
    valueInputType: 'text',
    optionBw: 3,
  },
  {
    id: '9',
    value: 'Network Name',
    conditionBw: 255,
    valueInputType: 'text',
    optionBw: 1,
  },
  {
    id: '10',
    value: 'Instance Name',
    conditionBw: 255,
    valueInputType: 'text',
    optionBw: 1,
  },
  {
    id: '11',
    value: 'Instance Tag',
    conditionBw: 309,
    valueInputType: 'instanceTag',
    optionBw: 2051,
  },
  {
    id: '12',
    value: 'Instance Progress',
    conditionBw: 3075,
    valueInputType: 'number',
    optionBw: 2,
  },
  {
    id: '13',
    value: 'Instance Status',
    conditionBw: 3,
    valueInputType: 'progress',
    optionBw: 1,
  },
  {
    id: '14',
    value: 'Feature Journey Stage',
    conditionBw: 15,
    valueInputType: 'journeyStage',
    optionBw: 3,
  },
  {
    id: '15',
    value: 'User Access',
    conditionBw: 51,
    valueInputType: 'access',
    optionBw: 1,
  },
  {
    id: '16',
    value: 'Instance Country',
    conditionBw: 1015,
    valueInputType: 'usermatching',
    optionBw: 3,
  },
  {
    id: '17',
    value: 'Instance Province',
    conditionBw: 1015,
    valueInputType: 'usermatching',
    optionBw: 3,
  },
  {
    id: '18',
    value: 'Instance Postal Code',
    conditionBw: 1015,
    valueInputType: 'usermatching',
    optionBw: 3,
  },
  {
    id: '19',
    value: 'Organization Id',
    conditionBw: 768,
    optionBw: 3,
  },
  {
    id: '20',
    value: 'Created/Modified By',
    conditionBw: 768,
    optionBw: 2,
  },
  {
    id: '21',
    value: 'Instance Organization Id',
    conditionBw: 5,
    optionBw: 3,
  },
  {
    id: '22',
    value: 'Instance User Id',
    conditionBw: 5,
    optionBw: 3,
  },
  {
    id: '23',
    value: 'Instance Role Tags',
    conditionBw: 771,
    valueInputType: 'roleTags',
    optionBw: 3,
  },
  {
    id: '24',
    value: 'Feature Products',
    conditionBw: 771,
    valueInputType: 'productTags',
    optionBw: 3,
  },
  {
    id: '26',
    value: 'User Location Distance',
    conditionBw: 1024,
    valueInputType: 'number',
    optionBw: 2,
  },
  {
    id: '27',
    value: 'Instance City',
    conditionBw: 1015,
    valueInputType: 'usermatching',
    optionBw: 3,
  },
  {
    id: '28',
    value: 'Completion Badges Status',
    conditionBw: 1,
    valueInputType: 'progress',
    optionBw: 1,
  },
  {
    id: '29',
    value: 'User Products',
    conditionBw: 3,
    valueInputType: 'productTags',
    optionBw: 3,
  },
  {
    id: '30',
    value: 'Instance RIASEC',
    conditionBw: 256,
    optionBw: 64,
  },
  {
    id: '31',
    value: 'Network Type',
    conditionBw: 773,
    valueInputType: 'networkTag',
    optionBw: 1,
  },
  {
    id: '32',
    value: 'Instance Products',
    conditionBw: 771,
    optionBw: 3,
  },
  {
    id: '33',
    value: 'RIASEC1',
    conditionBw: 256,
    optionBw: 64,
  },
  {
    id: '34',
    value: 'RIASEC2',
    conditionBw: 256,
    optionBw: 64,
  },
  {
    id: '35',
    value: 'RIASEC3',
    conditionBw: 256,
    optionBw: 64,
  },
  {
    id: '36',
    value: 'User Classes',
    conditionBw: 256,
    optionBw: 1,
  },
  {
    id: '37',
    value: 'Instance Organizations',
    conditionBw: 256,
    optionBw: 768,
  },
  {
    id: '38',
    value: 'Organization Instances',
    conditionBw: 256,
    optionBw: 768,
  },
  {
    id: '39',
    value: 'Instance Interest',
    conditionBw: 768,
    valueInputType: 'instanceInterest',
    optionBw: 1024,
  },
  {
    id: '40',
    value: 'Organization Tag',
    conditionBw: 309,
    valueInputType: 'organizationTag',
    optionBw: 3,
  },
  {
    id: '41',
    value: 'Sponsored By',
    conditionBw: 261,
    valueInputType: 'sponsoredBy',
    optionBw: 4096,
  },
  {
    id: '42',
    value: 'Instance Assignment Feature Type',
    conditionBw: 15,
    valueInputType: 'instanceAssignmentFeatureType',
    optionBw: 1,
  },
  {
    id: '43',
    value: 'Instance State',
    conditionBw: 3,
    valueInputType: 'status',
    optionBw: 1,
  },
  {
    id: '44',
    value: 'Organization Interest',
    conditionBw: 768,
    valueInputType: 'organizationInterest',
    optionBw: 1024,
  },
];

export const rowFilterConditionList = [
  {
    id: 'Equal',
    value: 'is',
    bitWise: 1,
  },
  {
    id: 'NotEqual',
    value: 'is not',
    bitWise: 2,
  },
  {
    id: 'IsIn',
    value: 'is in',
    bitWise: 4,
  },
  {
    id: 'IsNotIn',
    value: 'is not in',
    bitWise: 8,
  },
  {
    id: 'Contains',
    value: 'contains',
    bitWise: 16,
  },
  {
    id: 'DoesNotContain',
    value: 'does not contain',
    bitWise: 32,
  },
  {
    id: 'BeginsWith',
    value: 'begins with',
    bitWise: 64,
  },
  {
    id: 'EndsWith',
    value: 'ends with',
    bitWise: 128,
  },
  {
    id: 'Matches',
    value: 'matches',
    bitWise: 256,
  },
  {
    id: 'NotMatches',
    value: 'does not match',
    bitWise: 512,
  },
  {
    id: 'LessOrEqual',
    value: 'less or equal to',
    bitWise: 1024,
  },
  {
    id: 'GreaterOrEqual',
    value: 'greater or equal to',
    bitWise: 2048,
  },
];

export const rowFilterOptionList = [
  {
    id: 'This',
    value: 'This',
    bitWise: 1,
  },
  {
    id: 'UserIdentity',
    value: 'User Identity',
    bitWise: 2,
  },
  {
    id: 'UserInterest',
    value: 'User Interest',
    bitWise: 4,
  },
  {
    id: 'UserKnowledge',
    value: 'User Knowledge',
    bitWise: 8,
  },
  {
    id: 'UserAwareness',
    value: 'User Awareness',
    bitWise: 16,
  },
  {
    id: 'UserEngagement',
    value: 'User Engagement',
    bitWise: 32,
  },
  {
    id: 'UserRIASEC',
    value: 'User RIASEC Score',
    bitWise: 64,
  },
  {
    id: 'NetworkType',
    value: 'Network Type',
    bitWise: 128,
  },
  {
    id: 'Attributed',
    value: 'Attributed To This',
    bitWise: 256,
  },
  {
    id: 'Associated',
    value: 'Associated To This',
    bitWise: 512,
  },
  {
    id: 'NotForMe',
    value: 'Not for me',
    bitWise: 1024,
  },
  {
    id: 'ILikeThis',
    value: 'I like this',
    bitWise: 1024,
  },
  {
    id: 'ILoveThis',
    value: 'I love this',
    bitWise: 1024,
  },
  {
    id: 'OrganizationNetworkTag',
    value: 'User`s Organization/Network Tag',
    bitWise: 2048,
  },
  {
    id: 'SponsoredByThis',
    value: 'Sponsored By This',
    bitWise: 4096,
  },
];

export const statusOptions: KeyValue[] = [
  { id: 'organization', value: 'Organization' },
  { id: 'network', value: 'Network' },
  { id: 'public', value: 'Public' },
  { id: 'deleted', value: 'deleted' },
];

export const qlikComponentTypes: KeyValue[] = [
  { id: 'sheets', value: 'Sheets' },
  { id: 'components', value: 'Components' },
];

export const qlikReportingTypes: KeyValue[] = [
  { id: 'network', value: 'Network' },
  { id: 'organization', value: 'Organization' },
];
