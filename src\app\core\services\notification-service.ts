import { Injectable } from '@angular/core';
import { environment } from '@env/environment';
import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
import { BehaviorSubject, map, share, Subject, takeUntil } from 'rxjs';
import { GlobalToastService } from './global-toast.service';
import { INotification } from '../contracts/contract';
import { DataService } from './data-service';

@Injectable({ providedIn: 'root' })
export class NotificationService {
  componentDestroyed$: Subject<boolean> = new Subject();
  hubConnection: HubConnection | undefined;
  notifications: INotification[] = [];
  newNotification = new BehaviorSubject(0);
  showBanner = false;
  constructor(
    private dataService: DataService,
    private toast: GlobalToastService
  ) {}

  startConnection(access_token: string) {
    if (!this.hubConnection) {
      const connection = environment.signalRUrl + 'notification';
      this.hubConnection = new HubConnectionBuilder()
        .withUrl(connection, { accessTokenFactory: () => access_token })
        .withAutomaticReconnect()
        .configureLogging(LogLevel.Information)
        .build();
      this.hubConnection.start().catch(err => {
        console.error(err);
      });

      this.hubConnection.on('Send', () => {
        this.loadNotifications(true).subscribe();
      });
    }
  }

  getBellNotifications() {
    return this.notifications ? this.notifications.filter(x => x.notificationTypeBW === 1) : null;
  }

  checkNotRead() {
    return this.notifications ? this.notifications?.some(x => x.hasRead === false) : false;
  }

  loadNotifications(newNotifications: boolean = false) {
    if (newNotifications) {
      return this.dataService.getNotifications().pipe(
        share(),
        map(data => {
          if (data?.length > 0 && this.notifications?.length <= data?.length) {
            this.newNotification.next(1);
          }

          // With new notiications only show the first result
          this.notifications = data.slice(0, 1) ?? [];
        })
      );
    } else {
      return this.dataService.getNotifications().pipe(
        share(),
        map(data => {
          if (data?.length > 0 && this.notifications?.length <= data?.length) {
            this.newNotification.next(1);
          }
          this.notifications = data ?? [];
        })
      );
    }
  }

  completeNotification(notificationId: string) {
    return this.dataService
      .completeNotification(notificationId)
      .pipe()
      .subscribe(() => {
        // Fetch new results after completing one of the notifications
        this.dataService
          .getNotifications()
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(notificationData => {
            this.notifications = notificationData;
          });
      });
  }

  openSnackBar(message: string) {
    this.toast.presentNotificationToast(message, true, true);
  }
}
