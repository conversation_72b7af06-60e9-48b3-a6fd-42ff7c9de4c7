import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ComponentRowSelectorModule } from '@app/features-shared/component-row-selector/component-row-selector.module';
import { InstanceDetailsExpanderModule } from '@app/features-shared/instance-details-expander/instance-details-expander.module';
import { InstanceSectionsAndComponentsModule } from '@app/features-shared/instance-section-components/instance-sections-and-components.module';
import { RowInstanceModule } from '@app/features-shared/row-instance/row-instance.module';
import { BuilderService } from '../feature-repository-builder/services/builder-service';
import { featureComponents, standaloneComponents } from './scorm-feature-instance.declarations';
import { ROUTES } from './scorm-feature-instance.routes';
import { TabFilterPipe } from '@app/shared/pipes/tab-filter';
import { SharedModule } from '@app/shared/shared.module';

@NgModule({
  declarations: [...featureComponents],
  imports: [...standaloneComponents, SharedModule, RowInstanceModule, ComponentRowSelectorModule, InstanceSectionsAndComponentsModule, InstanceDetailsExpanderModule, RouterModule.forChild(ROUTES)],
  exports: [...featureComponents, ComponentRowSelectorModule, InstanceSectionsAndComponentsModule],
  providers: [BuilderService, TabFilterPipe],
})
export class ScormFeatureInstanceModule {}
