<div class="header-parent-container">
  <ion-grid>
    <ion-row>
      @if (iconUrl) {
        <ion-col size="auto" class="image-container">
          <img class="row-icon" [src]="iconUrl" (error)="displayNoImage()" />
        </ion-col>
      }
      <ion-col class="title-container">
        <ion-row>
          @if (status) {
            @if (status === 'InProgress') {
              <div class="status-heading-container" style="color: 'orange'">
                <div class="inner-container">
                  <ion-icon name="time-outline"></ion-icon>
                  IN PROGRESS
                </div>
              </div>
            }
            @if (status === 'Complete') {
              <div class="status-heading-container" style="color: 'green'">
                <div class="inner-container">
                  <ion-icon name="checkmark-circle-outline"></ion-icon>
                  COMPLETED
                </div>
              </div>
            }
          }
        </ion-row>
        <ion-row>
          <span class="title" [ngClass]="{ 'title-centered ': !featureTitle && !instanceDescriptors }"> {{ instanceTitle }}</span>
        </ion-row>
        @if (featureTitle) {
          <ion-row>
            <span class="title-property">{{ featureTitle }}</span>
          </ion-row>
        }
        @if (entityType === enityTypes.DefaultInstance) {
          <ion-row>
            @if (featureDescriptors) {
              <span class="inner-html" [innerHTML]="featureDescriptors ?? '' | parsePipe: instanceId : null : false | async"></span>
            }
          </ion-row>
        }
        @if (entityType === enityTypes.Instances) {
          <ion-row>
            @if (instanceDescriptors) {
              <span class="inner-html" [innerHTML]="instanceDescriptors ?? '' | parsePipe: instanceId : null : false | async"></span>
            }
          </ion-row>
        }
      </ion-col>
    </ion-row>
  </ion-grid>
</div>
