import { DragDropModule } from '@angular/cdk/drag-drop';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ComponentTemplatefieldBuilderModule } from '@app/features-shared/component-templatefield-builder/component-templatefield-builder.module';
import { FormControlSelectorModule } from '@app/features-shared/form-control-selector/form-control-selector.module';
import { RowInstanceModule } from '@app/features-shared/row-instance/row-instance.module';
import { SharedModule } from '@app/shared/shared.module';
import { featureComponents, featureServices, standaloneComponents } from './feature-repository-builder.declarations';
import { ROUTES } from './feature-repository-builder.routes';

@NgModule({
  declarations: [...featureComponents],
  imports: [...standaloneComponents, SharedModule, RowInstanceModule, FormControlSelectorModule, RouterModule.forChild(ROUTES), DragDropModule, ComponentTemplatefieldBuilderModule],
  providers: [...featureServices],
  exports: [...featureComponents],
})
export class FeatureRepositoryBuilderModule {}
