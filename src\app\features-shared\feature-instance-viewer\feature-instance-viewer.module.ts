import { NgModule } from '@angular/core';
import { RowInstanceModule } from '@app/features-shared/row-instance/row-instance.module';
import { SharedModule } from '@app/shared/shared.module';
import { ComponentRowSelectorModule } from '../component-row-selector/component-row-selector.module';
import { InstanceDetailsExpanderModule } from '../instance-details-expander/instance-details-expander.module';
import { InstanceSectionsAndComponentsModule } from '../instance-section-components/instance-sections-and-components.module';
import { featureComponents, standaloneComponents } from './feature-instance-viewer.declarations';

@NgModule({
  declarations: [...featureComponents],
  imports: [...standaloneComponents, SharedModule, RowInstanceModule, ComponentRowSelectorModule, InstanceDetailsExpanderModule, InstanceSectionsAndComponentsModule],
  exports: [...featureComponents, RowInstanceModule, ComponentRowSelectorModule],
})
export class FeatureInstanceViewerModule {}
