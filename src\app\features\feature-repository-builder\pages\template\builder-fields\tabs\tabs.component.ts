import { Component, EventEmitter, Input, OnChanges, OnDestroy, Output, SimpleChanges, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { IComponentIn, IFeatureTab, IFeatureTabActionIn, IFeatureTabButtonActionIn, IFeatureTabButtonIn, IFeatureTabIn, IInstance, ISection, ITemplateFieldIn } from '@app/core/contracts/contract';
import { AlertService } from '@app/core/services/alert-service';
import { DataService } from '@app/core/services/data-service';
import { IonContent, ModalController } from '@ionic/angular';
import { OverlayEventDetail } from '@ionic/core';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { TabSettingsComponent } from '../tab-settings/tab-settings.component';

@Component({
    selector: 'app-feature-repository-builder-tabs',
    templateUrl: './tabs.component.html',
    styleUrls: ['./tabs.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class TabsComponent implements OnChanges, OnDestroy {
  @Input() featureId: string;
  @Input() featureTabs: IFeatureTab[];
  @Input() instance: IInstance;
  @Output() showDefaultComponents: EventEmitter<boolean> = new EventEmitter<boolean>();
  @ViewChild(IonContent) content: IonContent;
  scrollTop = 100;
  componentDestroyed$: Subject<boolean> = new Subject();
  isPopoverOpen = false;
  tabIndex = 0;
  selectedSection: ISection;
  constructor(
    private dataService: DataService,
    private alertService: AlertService,
    private modalController: ModalController
  ) {}

  async ngOnChanges(changes: SimpleChanges): Promise<void> {
    // This will rememeber the scroll position when editing row numbers and refreshing page.
    if (changes['featureTabs']) {
      this.scrollToPrevPosition();
    }
  }

  scrollToPrevPosition() {
    if (this.content) {
      this.content.getScrollElement().then(() => {
        this.content.scrollByPoint(0, this.scrollTop, 500);
      });
    }
  }

  addTab(selectAfterAdding: boolean, isDefaultInstanceTab: boolean) {
    const featureTabIn: IFeatureTabIn = {
      id: this.featureId,
      featureId: this.featureId,
      isDefaultInstanceTab: isDefaultInstanceTab,
      showTab: true,
      showForEfAdmin: false,
      showForGuest: false,
      sortOrder: 0,
      primaryFilter: null,
      secondaryFilter: null,
      typeId: null,
      buttonText: null,
      featureTabActions: null,
      featureTabEditActions: null,
      featureTabButtons: null,
      featureTabRowTypeIds: null,
      featureTabPersonas: null,
    };

    this.dataService
      .createFeatureTab(featureTabIn)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(featureTab => {
        if (!this.featureTabs) {
          this.featureTabs = [];
        }
        this.featureTabs.push(featureTab);
        this.isPopoverOpen = false;
      });
  }

  removeTabConfirmation(tabIndex: number) {
    this.alertService.presentAlert('Confirm Delete', 'Are you sure you want to delete this Tab').then(() => {
      this.removeTab(tabIndex);
    });
  }

  removeTab(tabIndex: any) {
    const featureTabId = this.featureTabs[tabIndex].id;

    this.dataService
      .deleteFeatureTab(featureTabId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(isDeleted => {
        if (isDeleted) {
          this.featureTabs.splice(tabIndex, 1);
        }
      });
  }

  updateTab(event: any, index: number) {
    const tab = this.featureTabs[index].tab;
    if (event) {
      tab.name = event.target.value;
    }
    this.dataService
      .updateTab(tab)
      .pipe(debounceTime(5000))
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {});
  }

  updateFeatureTab(featureTab: IFeatureTab) {
    const featureTabIn: IFeatureTabIn = {
      id: featureTab.id,
      showTab: featureTab.showTab,
      showForEfAdmin: featureTab.showForEfAdmin,
      showForGuest: featureTab.showForGuest,
      sortOrder: featureTab.sortOrder,
      isDefaultInstanceTab: featureTab.isDefaultInstanceTab,
      featureId: featureTab.featureId,
      primaryFilter: featureTab.primaryFilter
        ? ({
            ...featureTab.primaryFilter,
            componentTypeBw: null,
            componentTypeId: featureTab?.primaryFilter?.componentType?.id ?? null,
            sectionId: null,
            templateField: {
              ...featureTab.primaryFilter.templateField,
              dropDownValues: [],
              systemPropertyId: featureTab.primaryFilter.templateField.systemProperty?.id,
              parentIdSystemPropertyLink: featureTab.primaryFilter.templateField.parentIdSystemPropertyLink?.id,
            } as ITemplateFieldIn,
          } as IComponentIn)
        : null,
      secondaryFilter: featureTab.secondaryFilter
        ? ({
            ...featureTab.secondaryFilter,
            componentTypeBw: null,
            componentTypeId: featureTab?.secondaryFilter?.componentType?.id ?? null,
            sectionId: null,
            templateField: {
              ...featureTab.secondaryFilter.templateField,
              dropDownValues: [],
              systemPropertyId: featureTab.secondaryFilter.templateField.systemProperty?.id,
              parentIdSystemPropertyLink: featureTab.secondaryFilter.templateField.parentIdSystemPropertyLink?.id,
            } as ITemplateFieldIn,
          } as IComponentIn)
        : null,
      typeId: featureTab.typeId ?? featureTab.type?.id ?? null,
      buttonText: featureTab.buttonText,
      featureTabActions: featureTab.featureTabActions?.map(x => ({ id: null, actionId: x.id, featureTabId: featureTab.id }) as IFeatureTabActionIn),
      featureTabEditActions: featureTab?.featureTabEditActions?.map(x => ({ id: null, actionId: x.id, featureTabId: featureTab.id }) as IFeatureTabActionIn),
      featureTabButtons: featureTab.featureTabButtons?.map(
        x => ({ ...x, featureTabButtonActions: x.featureTabButtonActions.map(a => ({ ...a, id: null }) as IFeatureTabButtonActionIn), buttonLinkId: x.buttonLinkType?.id }) as IFeatureTabButtonIn
      ),
      featureTabRowTypeIds: featureTab.featureTabRowTypes.map(x => x.rowType.id),
      featureTabPersonas: featureTab.featureTabPersonas.map(x => x.id),
    };

    this.dataService
      .updateFeatureTab(featureTabIn)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {});
  }

  tabChanged(event: MatTabChangeEvent) {
    this.tabIndex = event.index;
    const isDefaultInstanceTab = this.featureTabs[event.index].isDefaultInstanceTab;
    this.showDefaultComponents.emit(isDefaultInstanceTab);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }

  logScrolling(event: any) {
    this.scrollTop = event.detail.scrollTop;
  }

  selectSection(section: ISection) {
    this.selectedSection = section;
  }

  async openSettings(featureTab: IFeatureTab) {
    this.dataService.getFeatureTab(featureTab.id).subscribe(async data => {
      const modal = await this.modalController.create({
        component: TabSettingsComponent,
        cssClass: 'tab-settings',
        componentProps: {
          featureTab: data,
        },
        backdropDismiss: false,
      });

      modal.onDidDismiss().then((detail: OverlayEventDetail) => {
        if (detail?.data) {
          this.updateFeatureTab(detail.data);
        }
      });

      return await modal.present();
    });
  }
}
