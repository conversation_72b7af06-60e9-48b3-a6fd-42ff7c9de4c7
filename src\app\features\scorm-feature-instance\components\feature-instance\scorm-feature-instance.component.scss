:host {
  height: 100%;
  background-color: #181818;
  color: white;
  position: relative;

  .header-container {
    z-index: 1000;
    position: relative;
    padding: 8px;
    max-height: 250px;
    background-color: #111;
    background-image: var(--background-image);
    background-size: 100% 100%;
    h1 {
      position: absolute;
      bottom: 30px;
      left: 20px;
    }
    p {
      position: absolute;
      bottom: 0px;
    }
  }

  ion-content {
    height: calc(100% - 250px);
  }

  .sticky {
    height: 100px;
    width: 100%;
  }

  .sticky-content {
    height: calc(100% - 100px);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Exo 2';
    text-shadow: 2px 2px #000;
    margin: 0;
    font-weight: 800;
    align-self: flex-end;
  }

  h1 {
    font-size: 40px;
  }
  h2 {
    font-size: 2.5em;
  }
  h3 {
    font-size: 2em;
  }
  h4 {
    font-size: 1.8em;
  }
  h5 {
    font-size: 1.5em;
  }
  h6 {
    font-size: 1.2em;
  }
  p {
    font-family: 'Roboto';
    text-shadow: 2px 2px #000;
  }

.scormloader {
    background-color: #111;
    height: 100%;

    ion-row {
      height: 100%;

      ion-col {
        text-align: center;
        color: #f99e00;
      }
    }

}

}
