import { BuilderSidePanelDescriptionComponent } from '@app/standalone/components/builder-side-panel-description/builder-side-panel-description.component';
import { BuilderSidePanelHeadingComponent } from '@app/standalone/components/builder-side-panel-heading/builder-side-panel-heading.component';
import { SectionEditComponent } from '@app/standalone/components/section-edit/section-edit.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { TextValueComponent } from '@app/standalone/components/text-value/text-value.component';
import { BuilderInstancePreviewComponent } from './components/builder-instance-preview/builder-instance-preview.component';
import { BuilderSidePanelComponent } from './components/builder-side-panel/builder-side-panel.component';
import { BuilderViewOptionsRowComponent } from './components/builder-view-options-row/builder-view-options-row.component';
import { DynamicSectionComponentsComponent } from './components/dynamic-section-components/dynamic-section-components.component';
import { DynamicSectionSelectorComponent } from './components/dynamic-section-selector/dynamic-section-selector.component';
import { FeatureInstanceSidePanelBuilderComponent } from './components/feature-instance-side-panel-builder/feature-instance-side-panel-builder.component';
import { InstanceSectionEditComponent } from './components/instance-section-edit/instance-section-edit.component';
import { HeadingValueComponent } from '@app/standalone/components/heading-value/heading-value.component';

export const featureComponents: any[] = [
  FeatureInstanceSidePanelBuilderComponent,
  BuilderInstancePreviewComponent,
  BuilderSidePanelComponent,
  DynamicSectionComponentsComponent,
  BuilderViewOptionsRowComponent,
  InstanceSectionEditComponent,
  DynamicSectionSelectorComponent,
];

export const standaloneComponets: any[] = [
  HeadingValueComponent,
  TextValueComponent,
  BuilderSidePanelHeadingComponent,
  BuilderSidePanelDescriptionComponent,
  SectionEditComponent,
  TextInputControlComponent,
];
