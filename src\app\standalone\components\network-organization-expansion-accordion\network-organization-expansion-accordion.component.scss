.org-networks-parent-container {
  mat-expansion-panel {
    background-color: rgba(41, 41, 41);
    margin-bottom: 15px !important;

    .expansion-panel-header {
      height: 100%;
      border-radius: 8px;
      padding: 15px;

      .inner-panel {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        .heading {
          font-weight: bold;
          font-size: 20px;
          color: white;
        }
      }
    }

    .expansion-panel-header:hover {
      background-color: rgba(41, 41, 41) !important;
    }

    ::ng-deep .mat-expansion-indicator::after,
    .mat-expansion-panel-header-description {
      border-color: white;
    }

    ::ng-deep .mat-mdc-tab {
      min-width: 25px !important;
      height: 25px;
      padding: 0px;
      background-color: transparent;
      margin-right: 15px;
      font-size: 16px;
      color: white;
    }

    ::ng-deep .mat-mdc-tab-header {
      margin-bottom: 15px !important;
      color: white;
    }

    ::ng-deep .mat-ink-bar {
      background-color: rgba(250, 167, 0) !important;
    }

    ::ng-deep .mat-expansion-panel-body {
      padding-left: 15px !important;
    }
  }

  .load-more {
    font-size: 16px;
    color: white;
    margin-top: 10px;
    text-align: center;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }
}
