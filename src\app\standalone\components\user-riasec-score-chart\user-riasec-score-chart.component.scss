.title {
  text-align: center;
  color: white;
  font-family: 'Exo 2', sans-serif;
  font-size: 33px;
  font-weight: 900;
  letter-spacing: 4%;
}

.riasec-description {
  text-align: center;
  color: #aaaaaa;
  font-style: italic;
  font-family: 'Inter', sans-serif;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 5%;
  line-height: 1.5;
  margin: 0 0 20px 0;
}

.riasec-grid {
  display: grid;
  gap: 15px;
  height: auto;
  width: 100%;
  margin: 0 0 20px 0;

  // Desktop: 4 columns x 1 row
  grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: 460px;

  // Tablet breakpoint
  @media (max-width: 1100px) {
    grid-template-columns: repeat(2, 1fr); // 2 columns x 2 rows
  }

  // Mobile breakpoint
  @media (max-width: 769px) {
    grid-template-columns: 1fr; // 1 column x 4 rows
  }
}

.riasec-card {
  height: 100%;
  width: 100%;
  border-radius: 15px;

  .content {
    padding: 15px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;

    .riasec-name {
      margin: 0 0 5px 0;
      font-style: normal;
      font-size: 25px;
      font-weight: 800;
      color: #ffffff;
      font-family: 'Inter';
      letter-spacing: 3%;
    }

    .riasec-description {
      font-style: normal;
      font-size: 16px;
      font-weight: 500;
      color: #ccc;
      letter-spacing: 4%;
      text-align: center;
      line-height: 22px;
      font-family: 'Inter';
    }

    // Tablet breakpoint
    @media (max-width: 1100px) {
      .riasec-description {
        width: 325px;
      }
    }

    // Mobile breakpoint
    @media (max-width: 769px) {
      .riasec-description {
        width: 310px;
      }
    }

    .riasec-count {
      width: 55px;
      height: 55px;
      position: absolute;
      left: 15px;
      top: 15px;
    }
  }
}

.Thinker {
  background: linear-gradient(to bottom, #228b22, #155115);
}

.Creator {
  background: linear-gradient(to bottom, #1e90ff, #04417c);
}

.Do-er {
  background: linear-gradient(to bottom, #6a0dad, #3f0966);
}

.Persuader {
  background: linear-gradient(to bottom, #cc5500, #841c05);
}

.Helper {
  background: linear-gradient(to bottom, #b8860b, #603f03);
}

.Organizer {
  background: linear-gradient(to bottom, #8b0000, #550000);
}

.Stats {
  background: #1e1e1e;
  border: 2px solid #333;
}

.riasec-stats-content {
  padding: 15px;
  .riasec-stats-row {
    display: flex;
    align-items: center;
    gap: 5px;
    flex-direction: row;
    justify-content: space-between;

    .riasec-stats-icon {
      height: 33px;
      width: 33px;
    }

    .riasec-stats-title {
      font-family: 'Inter';
      font-weight: 700;
      font-size: 22px;
      line-height: 27px;
      color: #ffffff;
    }
  }

  ion-button {
    --background: white;
    height: 32px;
    width: 69px;
    display: flex;
    justify-content: center;
    align-items: center;

    ion-icon {
      width: 17px;
      height: 17px;
    }
  }

  #riasec-stats-list {
    .riasec-stats-list-item-row {
      margin: 20px 0 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: white;

      .name {
        margin-bottom: 5px;
        font-size: 16px;
        font-weight: 600;
        font-family: 'Inter';
        letter-spacing: 3%;
      }

      .value {
        font-family: 'Inter';
        font-weight: 600;
        font-size: 12px;
        color: #cccccc;
      }
    }

    ion-progress-bar {
      margin-bottom: 20px;
      border-radius: 500px;
      height: 7px;
      --background: #444444;

      &.Do-er {
        --progress-background: #6a0dad;
      }

      &.Creator {
        --progress-background: #1e90ff;
      }

      &.Thinker {
        --progress-background: #228b22;
      }

      &.Helper {
        --progress-background: #b8860b;
      }

      &.Persuader {
        --progress-background: #cc5500;
      }

      &.Organizer {
        --progress-background: #8b0000;
      }
    }

    .riasec-footer {
      font-family: 'Inter';
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: #aaa;
    }
  }
}
