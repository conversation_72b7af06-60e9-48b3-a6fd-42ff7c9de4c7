import { Component, Input, On<PERSON><PERSON>roy, OnInit, forwardRef } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR, UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IInstance } from '@app/core/contracts/contract';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { NgClass } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { EmailChipComponent } from '../email-chips/email-chip/email-chip.component';
import { PhoneNumberComponent } from '../phone-number/phone-number.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';

@Component({
    selector: 'app-contact-info',
    templateUrl: './contact-info.component.html',
    styleUrls: ['./contact-info.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => ContactInfoComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => ContactInfoComponent),
        },
    ],
    imports: [NgClass, IonicModule, FormsModule, ReactiveFormsModule, TextInputControlComponent, EmailChipComponent, PhoneNumberComponent]
})
export class ContactInfoComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() instance: IInstance;
  @Input() id: string;
  @Input() sidePanelPadding = false;
  contactInfoForm: UntypedFormGroup;
  formValueChanges$: Subscription;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(private systemPropertiesService: SystemPropertiesService) {
    super();
  }

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    if (!this.systemPropertiesService.userProperties) {
      this.contactInfoForm = new UntypedFormGroup({
        primaryEmail: new UntypedFormControl(''),
        phoneNumber: new UntypedFormControl(''),
      });
      return;
    }

    const i1 = this.systemPropertiesService.userProperties.find(x => x.key.includes('email'));
    const i2 = this.systemPropertiesService.userProperties.find(x => x.key.includes('phone_number'));

    this.contactInfoForm = new UntypedFormGroup({
      primaryEmail: new UntypedFormControl(i1?.value),
      phoneNumber: new UntypedFormControl(i2?.value),
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    if (!this.contactInfoForm) {
      return;
    }

    this.formValueChanges$ = this.contactInfoForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      const i1 = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('email'));
      const i2 = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('phone_number'));

      this.systemPropertiesService.userProperties[i1].value = this.contactInfoForm.controls.primaryEmail.value;
      this.systemPropertiesService.userProperties[i2].value = this.contactInfoForm.controls.phoneNumber.value;
    });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
