import { NgModule } from '@angular/core';
import { RowInstanceModule } from '@app/features-shared/row-instance/row-instance.module';
import { ComponentSelectorModule } from '../component-selector/component-selector.module';
import { RowManagerModule } from '../row-manager/row-manager.module';
import { featureComponents } from './component-row-selector.declarations';
import { SharedModule } from '@app/shared/shared.module';

@NgModule({
  declarations: [...featureComponents],
  imports: [SharedModule, RowInstanceModule, RowManagerModule, ComponentSelectorModule],
  exports: [...featureComponents, RowInstanceModule],
})
export class ComponentRowSelectorModule {}
