mat-expansion-panel {
  background-color: rgb(68, 68, 68);
  margin-bottom: 15px !important;
  .expansion-panel-header {
    height: 100%;
    border-radius: 5px;
    padding: 15px;
    .inner-panel {
      .heading {
        font-weight: bold;
        font-size: 20px;
        color: white;
      }
      .sub-heading {
        width: 800px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 5px;
        font-style: italic;
        color: rgba(170, 170, 170);
      }
    }

    .toggle-disable-button {
      color: rgba(170, 170, 170);
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: auto;
      margin-right: 20px;

      mat-icon {
        color: rgba(250, 167, 0) !important;
      }

      .select-container {
        display: flex;
        align-items: center;
        .toggle {
          margin-left: 10px;
          margin-right: 10px;
        }
      }
    }
  }

  // @media screen and (max-device-width: 960px) {
  // }
  ::ng-deep .mat-expansion-panel-body {
    padding: 0;
  }
  .expansion-panel-header:hover {
    background-color: rgb(92, 92, 92) !important;
  }
}
