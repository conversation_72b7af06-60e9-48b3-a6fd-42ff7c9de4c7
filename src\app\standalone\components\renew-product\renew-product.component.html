@if (productRenewForm) {
  <form [formGroup]="productRenewForm">
    <ion-row style="margin-top: 30px">
      <ion-col style="text-align: center">
        <ion-label class="heading">{{ title }}</ion-label>
      </ion-col>
    </ion-row>
    <ion-row style="margin-bottom: 20px">
      <ion-col>
        <ion-label position="stacked">
          <span class="sub-heading">Subscription Term</span>
          <span class="reqAsterisk">
            <span> * </span>
            <ion-icon name="information-circle-outline"></ion-icon>
          </span>
        </ion-label>
        @if (!customValues.value) {
          <div>
            <select formControlName="period" class="dropdown">
              @for (items of subscriptionTerms; track items) {
                <option>{{ items }}</option>
              }
            </select>
          </div>
        }
        @if (customValues.value) {
          <div>
            <ion-input placeholder="Enter subscription term e.g.2 months" formControlName="period" class="dropdown"></ion-input>
            @if (period.invalid) {
              <ion-note slot="error">Valid input is e.g.2 months</ion-note>
            }
          </div>
        }
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-label position="stacked">
          <span class="sub-heading">Subscription Start</span>
          <span class="reqAsterisk">
            <span> * </span>
            <ion-icon name="information-circle-outline"></ion-icon>
          </span>
        </ion-label>
        <ion-input placeholder="MM/DD/YYYY" value="{{ startDate.value | date: 'MM/dd/YYYY' }}" [id]="identifier" [readonly]="true">
          <ion-icon class="calendar-icon" name="calendar-outline" item-right></ion-icon>
        </ion-input>
        <ion-popover [trigger]="identifier" size="cover">
          <ng-template>
            <ion-datetime [presentation]="identifier" [min]="minDate" formControlName="startDate" displayFormat="mm/dd/yyyy"></ion-datetime>
          </ng-template>
        </ion-popover>
      </ion-col>
    </ion-row>
    <ng-container>
      <ion-row>
        <ion-col>
          <ion-checkbox (ionChange)="customTermClicked($event)" formControlName="customValues" color="primary"></ion-checkbox>
          <ion-label> <span style="margin-left: 5px" class="sub-heading">Custom subscription term</span></ion-label>
        </ion-col>
      </ion-row>
    </ng-container>
    @if (featureType === 'Network Manager') {
      <ion-row>
        <ion-col>
          <ion-label position="stacked">
            <span class="sub-heading">Available Licenses</span>
            <span class="reqAsterisk">
              <span> * </span>
              <ion-icon name="information-circle-outline"></ion-icon>
            </span>
          </ion-label>
          <ion-input [disabled]="unlimitedLicenses.value" type="number" formControlName="availableLicenses" [min]="newProduct ? 1 : networkOrgCount"></ion-input>
          @if (newProduct === false) {
            <ion-note>
              <span>The minimum is {{ networkOrgCount }}, that is the number of assigned network organizations to this product.</span>
            </ion-note>
          }
          @if (newProduct === true) {
            <ion-note>
              <span>The maximum number of licenses that can be assigned to organizations part of your network.</span>
            </ion-note>
          }
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-checkbox (click)="customUnlimitedLicenses()" formControlName="unlimitedLicenses" color="primary"></ion-checkbox>
          <ion-label> <span style="margin-left: 5px" class="sub-heading">Unlimited licenses</span></ion-label>
        </ion-col>
      </ion-row>
    }
  </form>
}
