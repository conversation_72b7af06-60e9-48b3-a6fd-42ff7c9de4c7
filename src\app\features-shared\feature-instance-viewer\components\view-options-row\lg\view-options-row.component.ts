import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { DataService } from '@app/core/services/data-service';
import { LayoutService } from '@app/core/services/layout-service';
import { ScormService } from '@app/core/services/scorm.service';
import { StorageService } from '@app/core/services/storage-service';
import { ViewOptionsRowBaseComponent } from '../base/view-options-row-base.component';
import { PendingRequestsInterceptor } from '@app/core/interceptors/pending-requests-interceptor';

@Component({
    selector: 'app-view-options-row-lg',
    templateUrl: './view-options-row.component.html',
    styleUrls: ['./view-options-row.component.scss'],
    standalone: false
})
export class ViewOptionsRowLgComponent extends ViewOptionsRowBaseComponent {
  constructor(
    dataService: DataService,
    storageService: StorageService,
    dialog: MatDialog,
    activatedRoute: ActivatedRoute,
    layoutService: LayoutService,
    scormService: ScormService,
    breadCrumbservice: BreadcrumbService,
    pendingRequestsInterceptor: PendingRequestsInterceptor

  ) {
    super(dataService, storageService, dialog, activatedRoute, layoutService, scormService, breadCrumbservice, pendingRequestsInterceptor);
  }
}
