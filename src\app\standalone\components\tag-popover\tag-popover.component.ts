import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { Component, Input, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { MatChipInputEvent, MatChipGrid, MatChipOption, MatChipRemove, MatChipInput } from '@angular/material/chips';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { IComponent, IInstance, IOrganizationSearch, ITag } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { Observable, Subject, debounceTime, map, takeUntil } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { AsyncPipe, KeyValue } from '@angular/common';
import { TagSelectorDialogComponent } from '@app/standalone/modals/tag-selector-dialog/tag-selector-dialog.component';

@Component({
    selector: 'app-tag-popover',
    templateUrl: './tag-popover.component.html',
    styleUrls: ['./tag-popover.component.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [MatFormField, MatChipGrid, MatChipOption, MatChipRemove, MatIcon, MatChipInput, MatLabel, AsyncPipe]
})
export class TagPopoverComponent implements OnInit, OnDestroy {
  @Input() instanceId: string | undefined;
  @Input() component: IComponent | undefined;
  @Input() selectedUserId: string | undefined;
  @Input() dropDownLinkType: string | '';
  @Input() campaignId: string | null;
  @Input() viewType = 3;
  @Input() featureTypeName: string | undefined;
  tagDialog: MatDialogRef<TagSelectorDialogComponent>;
  componentDestroyed$: Subject<boolean> = new Subject();
  existingTags$: Observable<ITag[]>;
  addOnBlur = true;
  organizationId: string | undefined;
  campaignTagExists = false;
  isTag = true;
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  constructor(
    private dialog: MatDialog,
    private dataService: DataService,
    private activatedRoute: ActivatedRoute,
    private eventsService: Events
  ) {}

  ngOnInit() {
    if (this.dropDownLinkType === 'Organization Tags') {
      this.activatedRoute.params.pipe(takeUntil(this.componentDestroyed$)).subscribe(params => {
        this.organizationId = params['instanceslug'];
        this.loadExistingTags();
      });
    } else if (this.viewType !== 3) {
      this.loadExistingTags();
    }
    this.isTag = this.dropDownLinkType.includes('Tags') ?? false;
  }

  openTagModal() {
    this.tagDialog = this.dialog.open(TagSelectorDialogComponent, {
      disableClose: true,
      data: {
        tagId: this.component?.templateField?.tagTreeId ?? '',
        instanceId: this.instanceId,
        level: this.component?.templateField?.limitTo ?? '',
        view: this.dropDownLinkType === 'Organizations' ? 'isOrganizations' : this.dropDownLinkType === 'Instances' ? 'isInstances' : 'isTag',
        componentId: this.component?.id ?? '',
        campaignId: this.campaignId,
        dropDownLinkType: this.dropDownLinkType,
        systemPropertyType: this.component?.templateField?.systemProperty?.type?.title,
        featureTypeName: this.featureTypeName,
        selectedUserId: this.selectedUserId,
        organizationId: this.organizationId,
      },
    });

    this.tagDialog.afterClosed().subscribe(response => {
      if (response) {
        this.loadExistingTags();
      }
    });
  }

  loadExistingTags() {
    if (this.dropDownLinkType === 'User Tags') {
      this.existingTags$ = this.dataService.getUserTags(this.selectedUserId).pipe(
        map(tags => {
          return tags
            .filter(x => x.type !== 'Persona')
            .map(userTag => {
              return { id: userTag?.tagId, name: userTag?.tag?.name } as ITag;
            });
        })
      );
    } else if (this.campaignId && this.component?.templateField?.dropDownLinkType?.title === 'Campaign User Tags') {
      this.existingTags$ = this.dataService.getCampaignTags(this.campaignId);
    } else if (this.campaignId && this.component?.templateField?.systemProperty?.type?.title === 'Campaign') {
      this.existingTags$ = this.dataService.getCampaignTag(this.campaignId);
    } else if (this.dropDownLinkType === 'Criteria Manager') {
      this.existingTags$ = this.dataService.getCriteriaTags(this.instanceId);
    } else if (this.dropDownLinkType === 'Organization Tags' && this.organizationId) {
      this.existingTags$ = this.dataService.getOrganizationTags(this.organizationId);
    } else if (this.dropDownLinkType === 'Organizations' && this.instanceId) {
      this.existingTags$ = this.dataService
        .getOrganizationsByInstanceId(this.instanceId)
        .pipe(map(organizations => organizations.map(organization => ({ id: organization.id, name: organization.name }) as ITag)));
    } else if (this.dropDownLinkType === 'Instances' && this.organizationId) {
      this.existingTags$ = this.dataService.getInstancesByOrganizationId(this.organizationId).pipe(map(instances => instances.map(instance => ({ id: instance.id, name: instance.title }) as ITag)));
    } else if (this.component) {
      this.existingTags$ = this.dataService.getInstanceTags(this.instanceId ?? '', this.component.id);
    }
    this.eventsService.publish('loadTags');
  }

  removeDirect(tag: ITag) {
    if (this.dropDownLinkType === 'User Tags') {
      this.existingTags$.subscribe((tags: ITag[]) => {
        const userTagIds = tags.map(y => y.id);
        const index = userTagIds.findIndex(x => x === tag.id);
        if (index !== -1) {
          userTagIds.splice(index, 1);
          this.dataService
            .updateRemoveUserTags(userTagIds, this.selectedUserId)
            .pipe(takeUntil(this.componentDestroyed$))
            .subscribe((userTagsUpdated: boolean) => {
              if (userTagsUpdated) {
                this.loadExistingTags();
              }
            });
        }
      });
    } else if (this.dropDownLinkType === 'Organizations' && this.instanceId) {
      this.dataService
        .removeOrganizationFromInstance(this.instanceId, tag.id)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.loadExistingTags();
        });
    } else if (this.dropDownLinkType === 'Instances' && this.organizationId) {
      this.dataService
        .removeInstanceFromOrganization(this.organizationId, tag.id)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.loadExistingTags();
        });
    } else if (this.campaignId && this.component?.templateField?.dropDownLinkType?.title === 'Campaign User Tags') {
      this.dataService
        .deleteCampaignUserTag(tag.id, this.campaignId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.loadExistingTags();
        });
    } else if (this.campaignId && this.component?.templateField?.systemProperty?.type?.title === 'Campaign') {
      this.dataService
        .deleteCampaignTag(this.campaignId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.loadExistingTags();
        });
    } else if (this.dropDownLinkType === 'Organization Tags' && this.instanceId) {
      if (this.organizationId != null) {
        this.dataService
          .updateRemoveOrganizationTag(this.organizationId, tag.id)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(() => {
            this.loadExistingTags();
          });
      }
    } else if (this.component && this.instanceId) {
      this.dataService
        .deleteInstanceTag(tag.id, this.instanceId, this.component.id)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((instanceTagsUpdated: boolean) => {
          if (instanceTagsUpdated) {
            this.loadExistingTags();
          }
        });
    } else if (this.dropDownLinkType === 'Criteria Manager') {
      this.existingTags$.subscribe((tags: ITag[]) => {
        const tagIds = tags.map(y => y.id);
        const index = tagIds.findIndex(x => x === tag.id);
        if (index !== -1) {
          tagIds.splice(index, 1);
          this.dataService
            .updateRemoveCriteriaTags(this.instanceId, tagIds)
            .pipe(takeUntil(this.componentDestroyed$))
            .subscribe((userTagsUpdated: boolean) => {
              if (userTagsUpdated) {
                this.loadExistingTags();
              }
            });
        }
      });
    }
  }

  add(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();
    event.chipInput!.clear();
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
