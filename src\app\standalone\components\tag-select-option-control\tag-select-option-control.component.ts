import { Ng<PERSON><PERSON>, NgStyle } from '@angular/common';
import { AfterViewInit, Component, EventEmitter, input, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { TagService } from '@app/core/services/tag.service';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { IonicModule } from '@ionic/angular';
import { Subject, map, takeUntil } from 'rxjs';
import { ChipListComponent } from '../chip-list/chip-list.component';
import { TagTreeNavigationSelectorComponent } from '../tag-tree-navigation-selector/tag-tree-navigation-selector.component';

@Component({
    selector: 'app-tag-select-option-control',
    templateUrl: './tag-select-option-control.component.html',
    styleUrls: ['./tag-select-option-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: TagSelectOptionControlComponent,
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: TagSelectOptionControlComponent,
        },
    ],
    imports: [NgClass, IonicModule, ChipListComponent, NgStyle, TagTreeNavigationSelectorComponent]
})
export class TagSelectOptionControlComponent extends BaseControlComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() toolTip!: string;
  @Input() override label!: string;
  @Input() override textValue: string | undefined;
  @Input() placeHolder!: string;
  @Input() dropDownLinkType!: string;
  @Input() options!: KeyValue[];
  @Input() selectedOptions!: KeyValue[];
  @Input() backgroundColor = '#181818';
  @Input() identifierText: string;
  @Input() multiple = false;
  @Input() isFilterRowContainer = false;
  @Input() limitTo = 5;
  @Input() viewType = 0;
  @Input() noPadding = false;
  @Input() noBorder = false;
  @Input() sidePanelPadding = false;
  @Input() showSelected = false;
  @Input() disableChipDelete = false;
  @Input() allowClear = false;
  @ViewChild('tagSelectionPopover') tagSelectionPopover: any;
  @ViewChild('selection') selectEl: HTMLIonInputElement;
  @Output() userTagsUpdated: EventEmitter<string | null> = new EventEmitter();
  showPopover = false;
  displayValue = '';
  selectedTagList: KeyValue[] = [];
  selectedTagIdList: string[] = [];
  filterChipList: KeyValue[] = [];
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private tagService: TagService
  ) {
    super();
  }

  ngOnInit(): void {
    if (this.textValue && !this.multiple) {
      this.getTagDetails(this.textValue);
    } else {
      this.setTagSelections();
    }
  }

  ngAfterViewInit(): void {
    this.displayValue = this.options?.find(x => x.id === this.textValue)?.value ?? '';
  }

  clear(event: Event) {
    event.stopPropagation();
    this.selectEl.value = undefined;
    this.writeValue(null);
    this.displayValue = '';
  }

  presentPopover(event: Event) {
    event.stopPropagation();
    this.tagSelectionPopover.event = event;
    this.showPopover = true;
  }

  singleSelectTag(option?: KeyValue) {
    if (option && option.value) {
      this.displayValue = option.value;
      if (this.dropDownLinkType === 'User Tags' && option.id) {
        //JsonParseAsStringArray.
        this.selectedTagIdList.push(option.id as string);
        this.userTagsUpdated.emit(JSON.stringify(this.selectedTagIdList));
      } else {
        this.writeValue(option.id);
      }

      this.tagSelectionPopover.dismiss();
    }
  }

  selectMultipleTags(selectedTagIdList?: string[]) {
    if (selectedTagIdList) {
      this.selectedTagIdList = selectedTagIdList;
      this.writeValue(this.selectedTagIdList);
    }
  }

  getTagDetails(tagId?: any) {
    //SetCompareSelected-AgainstOptions.
    if (tagId) {
      if (this.tagService.isValidGUID(tagId)) {
        this.dataService
          .getTag(tagId)
          .pipe(
            takeUntil(this.componentDestroyed$),
            map(tag => {
              if (tag) {
                return { id: tag.id, value: tag.name, parentId: tag.parentId, tagAncestors: tag.tagAncestors, level: tag.treeLevel } as KeyValue;
              }

              return null;
            })
          )
          .subscribe(tag => {
            if (tag && tag.value) {
              this.displayValue = tag.value;
            }
          });
      }
    }
  }

  setTagSelections() {
    if ((!this.selectedOptions || this.selectedOptions.length === 0) && this.textValue !== undefined && this.textValue !== '') {
      this.selectedOptions = (this.textValue as unknown as string[])?.map(x => ({ id: x }) as KeyValue);
    } else {
      return;
    }

    if (this.selectedOptions?.length > 0 && this.multiple) {
      this.dataService
        .getTagsByIds(this.selectedOptions.map(x => x.id) as string[])
        .pipe(
          takeUntil(this.componentDestroyed$),
          map(tags => {
            if (tags) {
              return tags.map(tag => {
                return { id: tag.id, value: tag.name, parentId: tag.parentId, tagAncestors: tag.tagAncestors, level: tag.treeLevel } as KeyValue;
              });
            }

            return null;
          })
        )
        .subscribe(tags => {
          if (tags) {
            const mappedArray = tags.map(x => x.value + '');
            this.displayValue = mappedArray.join(', ');

            if (this.multiple) {
              this.filterChipList = tags;
              this.selectedTagList = this.filterChipList;
            }
          }
        });
    }
  }

  saveTagList() {
    let selectedIds = this.selectedTagList.map(x => x.id as string) ?? '';

    if (this.multiple) {
      selectedIds = this.selectedTagIdList;
    }

    if (this.dropDownLinkType === 'User Tags') {
      this.userTagsUpdated.emit(JSON.stringify(selectedIds));
    } else {
      this.writeValue(selectedIds);
    }

    this.showPopover = false;
    this.tagSelectionPopover.dismiss();
  }

  removeChipById(keyValue: KeyValue) {
    const index = this.selectedTagList.findIndex(x => x.id === keyValue.id);
    if (index > -1) {
      this.selectedTagList.splice(index, 1);
      this.saveTagList();
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
