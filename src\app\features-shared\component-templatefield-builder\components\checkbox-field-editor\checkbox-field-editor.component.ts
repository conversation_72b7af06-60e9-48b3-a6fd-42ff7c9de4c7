import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-checkbox-field-editor',
    templateUrl: './checkbox-field-editor.component.html',
    styleUrls: ['./checkbox-field-editor.component.scss'],
    standalone: false
})
export class CheckboxFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  checkboxFieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(private formBuilder: UntypedFormBuilder) {}

  get isInheritControl(): AbstractControl {
    return this.checkboxFieldForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.checkboxFieldForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      placeholder: [this.component?.templateField?.placeHolderText, Validators.required],
      helpTitle: [this.component?.templateField?.helpTitle],
      helpDescription: [this.component?.templateField?.helpDescription],
      rowNumber: [this.component?.builderRowNumber],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      isInherit: [this.component?.templateField?.isInherit ?? false],
      isVariable: [this.component.templateField?.isVariable ?? false],
      systemProperty: [this.component?.templateField?.systemProperty?.id],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.checkboxFieldForm) {
      return;
    }

    this.checkboxFieldForm.controls.label.setValue(this.component.templateField.label);
    this.checkboxFieldForm.controls.placeholder.setValue(this.component.templateField.placeHolderText);
    this.checkboxFieldForm.controls.helpTitle.setValue(this.component.templateField.helpTitle);
    this.checkboxFieldForm.controls.helpDescription.setValue(this.component.templateField.helpDescription);
    this.checkboxFieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.checkboxFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.checkboxFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.checkboxFieldForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.checkboxFieldForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.checkboxFieldForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.checkboxFieldForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.checkboxFieldForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.checkboxFieldForm.controls.isInherit.setValue(this.component.templateField.isInherit);
    this.checkboxFieldForm.controls.systemProperty.setValue(this.component.templateField.systemProperty);
    this.checkboxFieldForm.controls.isVariable.setValue(this.component.templateField.isVariable);
    this.checkboxFieldForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.checkboxFieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.checkboxFieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.checkboxFieldForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.checkboxFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.checkboxFieldForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.checkboxFieldForm.valid) {
      this.component.templateField.label = this.checkboxFieldForm.controls.label.value;
      this.component.templateField.placeHolderText = this.checkboxFieldForm.controls.placeholder.value;
      this.component.templateField.helpTitle = this.checkboxFieldForm.controls.helpTitle.value;
      this.component.templateField.helpDescription = this.checkboxFieldForm.controls.helpDescription.value;
      this.component.builderRowNumber = this.checkboxFieldForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.checkboxFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.checkboxFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.checkboxFieldForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.checkboxFieldForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.checkboxFieldForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.checkboxFieldForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.checkboxFieldForm.controls.isViewField.value;
      this.component.templateField.isInherit = this.checkboxFieldForm.controls.isInherit.value;
      this.component.templateField.isVariable = this.checkboxFieldForm.controls.isVariable.value;
      this.component.templateField.isVisibleRepository = this.checkboxFieldForm.controls.isVisibleRepository.value;
      this.component.templateField.colspan = this.checkboxFieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.checkboxFieldForm.controls.colNumber.value;
      this.component.templateField.useMaxWidth = this.checkboxFieldForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
