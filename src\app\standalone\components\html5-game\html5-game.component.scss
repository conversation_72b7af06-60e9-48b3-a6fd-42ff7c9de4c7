:host {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .progress {
      margin: 0 auto 50px auto;
  }

  .html5-object {
    overflow: hidden;
    z-index: 10;
    height: 42vw;
    max-height: 42vw;
  }

  @media screen and (max-width: 1299px) {
    .html5-object {
      height: 41vw;
      max-height: 41vw;
    }
    
  }
  
  @media screen and (max-width: 992px) {
    .html5-object {
      height: 58.5vw;
      max-height: 58.5vw;
    }
  }
  
  @media screen and (max-width: 650px) {
    .html5-object {
      height: 56.5vw;
      max-height: 56.5vw;
    }
  }
  
  @media screen and (max-width: 600px) {
    .html5-object {
      height: 55.5vw;
      max-height: 55.5vw;
    }
  }

  .border-completed {
    border: 2px solid green !important;
    position: relative;
    margin-top: 8px;
    border-radius: 11px;
  }

  .border-in-progress {
    border: 2px solid orange !important;
    position: relative;
    margin-top: 8px;
    border-radius: 11px;
  }

  .completed-header-container {
    ion-row {
      z-index: 999;
      position: absolute;
      top: -10px;
      left: 50px;
      .inner-completed {
        background-color: green;
        padding: 2px;
        font-size: 12px;
        border-radius: 5px;
        color: white;
        margin-right: 16px;
      }
      .inner-in-progress {
        background-color: orange;
        padding: 2px;
        font-size: 12px;
        border-radius: 5px;
        color: white;
        margin-right: 16px;
      }
      .inner-required {
        background-color: grey;
        padding: 2px;
        font-size: 12px;
        border-radius: 5px;
        color: white;
      }
    }
  }

  iframe {
    border-radius: 11px;
    margin-bottom: -4px;
  }
}
