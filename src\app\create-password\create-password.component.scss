.container {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #171717;
  .card {
    width: 25%;
    height: auto;
    color: white;
    background-color: #1e1e1e;
    border-radius: 6px;
    font-family: 'Exo 2';
    font-weight: 300;
    font-size: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    span {
      text-align: center;
    }
    img {
      width: 100%;
      padding: 20px 20px 5px 20px;
    }
    ion-item {
      width: 100%;
      margin-top: 15px;
      ion-input {
        max-height: 50px;
        background: #181818;
        color: #929292;
        border-color: #333333;
        border-width: 2px;
        border-style: solid;
        border-radius: 3px 3px 3px 3px;
        font-family: 'Roboto';
        font-weight: 400;
        font-size: 18px;
        line-height: 1.3;
        text-align: left;
      }
    }

    ion-button {
      margin: 15px 0 20px 0;
      width: 90%;
    }
  }
}
.scormloader {
  background-color: #111;
  height: 100%;

  ion-row {
    height: 100%;

    ion-col {
      text-align: center;
      color: #f99e00;
    }
  }
}
