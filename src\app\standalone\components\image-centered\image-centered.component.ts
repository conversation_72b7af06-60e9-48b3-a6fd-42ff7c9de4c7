import { Component, forwardRef, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, NG_VALIDATORS, NG_VALUE_ACCESSOR, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { IComponent, IImageCentered } from '@app/core/contracts/contract';
import { ItemReorderEventDetail, IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { FileUploadControlComponent } from '@app/standalone/components/file-upload-control/file-upload-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';

@Component({
    selector: 'app-image-centered',
    templateUrl: './image-centered.component.html',
    styleUrls: ['./image-centered.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => ImageCenteredComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => ImageCenteredComponent),
        },
    ],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, FileUploadControlComponent, TextInputControlComponent, MatSlideToggle]
})
export class ImageCenteredComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override label: string;
  @Input() description: string;
  @Input() component!: IComponent;
  imageList: IImageCentered[] = [];
  imageForm: UntypedFormGroup;
  imageFormArray = new FormArray<any>([]);
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private formBuilder: FormBuilder,
    private signalService: ComponentUpdateSignalService
  ) {
    super();
  }

  ngOnInit() {
    this.fieldValueChanged.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.textValue) {
        this.imageList = JSON.parse(this.textValue) as IImageCentered[];
      }

      if (!this.imageList.length) {
        this.imageList.push({ sortOrder: 0, caption: '' } as IImageCentered);
      }

      this.createForm();
      this.formChanges();
    });
  }

  createForm() {
    this.imageForm = this.formBuilder.group({
      items: this.imageFormArray,
    });
    for (const item of this.imageList) {
      this.addControlToForm(item);
    }
  }

  reorder() {
    for (let i = 0; i < this.imageFormArray.length; i++) {
      const item = this.imageFormArray.at(i);
      item.patchValue({ sortOrder: i }, { emitEvent: false });
    }
  }

  addControlToForm(item: IImageCentered) {
    this.imageFormArray.push(
      this.formBuilder.group({
        sortOrder: [item.sortOrder],
        caption: [item.caption],
        height: [item.height],
        aspectRatio: [item.aspectRatio],
        objectFit: [item.objectFit],
        noClickPreview: [item.noClickPreview],
        asset: [item.asset],
      })
    );
  }

  add() {
    const nextIndex = this.imageFormArray.length;
    const newItem = { sortOrder: nextIndex, caption: '' } as IImageCentered;
    this.addControlToForm(newItem);
  }

  remove(index: number) {
    this.imageFormArray.removeAt(index);
    this.reorder();
  }

  handleReorder(event: CustomEvent<ItemReorderEventDetail>) {
    const currentItem = this.imageFormArray.at(event.detail.from);
    this.imageFormArray.removeAt(event.detail.from);
    this.imageFormArray.insert(event.detail.to, currentItem);
    this.reorder();
    event.detail.complete();
  }

  formChanges() {
    this.imageForm.valueChanges.subscribe(() => {
      const reorderedList = this.imageFormArray.getRawValue();
      this.updateImageList(reorderedList as IImageCentered[]);
    });
  }

  updateImageList(listIn: IImageCentered[]) {
    listIn = listIn.map((item, index) => ({ ...item, sortOrder: index + 1 }));
    const valueAsString = JSON.stringify(listIn);
    this.setValue(valueAsString);
    if(this.component.id)
    {
      this.signalService.triggerSignal({ componentId: this.component.id, updateValue: valueAsString });
    }
  }

  override setValue(value: string) {
    this.writeValue(value);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
