import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { IInstanceSectionComponent, INetworkAnalytics } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { NetworkItemComponent } from '../network-item/network-item.component';

@Component({
    selector: 'app-network-qlik-view-download',
    templateUrl: './network-qlik-view-download.component.html',
    styleUrls: ['./network-qlik-view-download.component.scss'],
    imports: [IonicModule, NetworkItemComponent]
})
export class NetworkQlikViewDownloadComponent implements OnInit, OnDestroy {
  @Input() id: string;
  @Input() instanceComponent: IInstanceSectionComponent | undefined;
  @Input() defaultText: string | undefined;
  componentDestroyed$: Subject<boolean> = new Subject();
  networkAnalytics: INetworkAnalytics[] = [];

  constructor(private dataService: DataService) {}

  ngOnInit(): void {
    if (this.id) {
      this.getNetworkAnalytics();
    }
  }

  getNetworkAnalytics() {
    this.dataService
      .getNetworkAnalytics(this.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((data: INetworkAnalytics[]) => {
        if (data) {
          this.networkAnalytics = data;
        }
      });
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
