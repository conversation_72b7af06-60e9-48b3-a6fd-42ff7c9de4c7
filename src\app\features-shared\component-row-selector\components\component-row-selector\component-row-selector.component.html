<!-- NB! THIS RETURNS A COMPONENT FOR VIEWING -->
<!-- ROW COMPONENT -->
<div
  class="full-parent-height"
  [ngClass]="{
    'side-panel-input-padding': sidePanelPadding && instanceSectionComponent.component.componentType?.name !== 'Page Banner',
    'sales-page-padding': instance.feature.isFullWidth === true && instanceSectionComponent.component.componentType?.name !== 'Page Banner',
  }">
  @if (instanceSectionComponent.component.rowId) {
    @switch (instanceSectionComponent?.component?.componentType?.name) {
      @case ('Default Row') {
        <app-row-instance
          [isPlayerSidePanel]="isPlayerSidePanel"
          [readingMode]="true"
          [rowId]="instanceSectionComponent.component.rowId"
          [instance]="instance"
          [routeParams]="routeParams"
          [selectedUserId]="selectedUserId"
          [searchFilter]="searchFilter"
          (rowContentSelected)="selectedRowContent($event.event, $event.actionBw)">
        </app-row-instance>
      }
      @case ('Smart Row') {
        <app-row-instance
          [isPlayerSidePanel]="isPlayerSidePanel"
          [readingMode]="true"
          [rowId]="instanceSectionComponent.component.rowId"
          [instance]="instance"
          [searchFilter]="searchFilter"
          [selectedUserId]="selectedUserId"
          [playerSidePanel]="playerSidePanel"
          [routeParams]="routeParams"
          (rowContentSelected)="selectedRowContent($event.event, $event.actionBw)"></app-row-instance>
      }
    }
  } @else if (instanceSectionComponent?.component?.componentType?.name === 'Row Manager') {
    <app-row-manager
      [isPlayerSidePanel]="isPlayerSidePanel"
      [instanceSectionComponent]="instanceSectionComponent"
      [readingMode]="true"
      [searchFilter]="searchFilter"
      [instance]="instance"
      [componentId]="instanceSectionComponent.component.id"
      [routeParams]="routeParams"
      [selectedUserId]="selectedUserId"
      [playerSidePanel]="playerSidePanel"
      (updateInstanceComponentValue)="setNewValue($event)"
      (rowContentSelected)="selectedRowContent($event.event, $event.actionBw)">
    </app-row-manager>
  } @else if ((instanceSectionComponent?.component?.templateField || instanceSectionComponent?.component?.componentType?.name === 'Assessment Block') && onlyRows === false) {
    <div
      [ngClass]="{
        'page-margin': routeParams?.viewType === viewTypes.Grid && instance.feature.isFullWidth !== true,
      }">
      <app-component-selector
        [instance]="instance"
        [instanceSectionComponent]="instanceSectionComponent"
        [instanceSection]="instanceSection"
        [routeParams]="routeParams"
        [searchFilter]="searchFilter"
        [builderPreviewView]="sidePanelPadding"
        [selectedUserId]="selectedUserId"
        [isEducator]="isEducator"
        [continuousFeedback]="continuousFeedback"
        [actionBw]="actionBw"
        (triggerCompletionCheck)="triggerSectionCompletionCheck()"
        (updateInstanceComponentValue)="setNewValue($event)">
      </app-component-selector>
    </div>
  }
</div>
