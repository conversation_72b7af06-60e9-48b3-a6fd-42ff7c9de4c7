.caption {
  color: #ffffff;
  font-family: '<PERSON><PERSON>';
  font-weight: bold;
  font-size: 22px;
  font-style: italic;
  line-height: 1.3;
  letter-spacing: 0.3px;
  text-align: left;
  margin: 0px;
}

.description {
  color: #cccccc;
  font-family: '<PERSON><PERSON>';
  font-weight: 400;
  font-size: 16px;
  line-height: 1.4;
  text-align: left;
  margin: 0px;
}

.parent-container {
  margin: 8px;

  .card-container {
    background-color: #2d2e32;
    padding: 10px;
  }
}

.side-panel-input-padding {
  margin: 0px !important;

  ion-card {
    margin: 0px !important;
  }

  .styling-container {
    width: 100%;
    border-top: 2px solid #1d1d1d;
    color: #ffffff;
    font-family: 'Roboto';
    font-weight: bold;

    .styling-heading {
      font-size: 16px !important;
    }

    .styles-container {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      gap: 5px;

      .styling-img-container {
        width: 49%;

        .image-container {
          background-size: contain;
          width: 100%;
          object-fit: contain;
          background-position: left;
          background-repeat: no-repeat;
          border-radius: 5px;
          justify-content: center;
          display: flex;
        }

        .selected {
          border: 2px solid #f99e00;
          border-radius: 8px;
        }

        img {
          border-radius: 8px;
          max-width: 200px;
          width: 100%;
        }

        .image-text {
          display: flex;
          justify-content: center;
        }
      }
    }
  }
}