.parent-container {
  padding-bottom: 25px;
  padding-right: 2vw;
  padding-left: 2vw;
  padding-top: 0px;
  display: flex;
  justify-content: center;
  width: 100%;
  
  .swiper-main-container {
    position: relative;
    height: 100%;
    display: grid;

    .swiper-container {
      height: 100%;
      width: 100% !important;

      .swiper-wrapper {
        flex-direction: column;

        .image-container {
          display: flex;
          width: 100%;
          height: 100%;
          border-radius: 7px;
        }

        img {
          display: flex;
          justify-content: center;
          border-radius: 7px;
          max-width: 100%;
          max-height: 100%;
          width: auto;
          margin: auto;
        }

        .bottom-container {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: baseline;
          justify-content: flex-end;

          .text-container {
            width: 100%;
            text-align: center;
            padding: 10px 10px 0px 10px;
        
            .caption {
              color: #aaaaaa;
              font-family: "Roboto";
              font-weight: 400;
              font-size: 14px;
              letter-spacing: 0.2px;
            }
          }

          .border-container {
            width: 100%;
            display: flex;
            justify-content: center;
            padding: 5px;

            .border-bottom {
              width: 80%;
              height: 2px;
              background: #454545;
            }
          }
        }
      }
    }
  }

  .nav-button {
    color: black;
    --swiper-navigation-size: 1em;
    padding: 1em;
    background-color: #abaeb5;
    border-radius: 1em;
  }

  @media (min-width: 540px) {
    .nav-button {
      top: 45%;
    }
  }

  @media (max-width: 540px) {
    .nav-button {
      top: 35%;
    } 
  }

  .swiper-slide {
    cursor: pointer;
  }

  .swiper-pagination {
    bottom: 0px !important;
  }

  .swiper-button-prev,
  .swiper-rtl .swiper-button-next {
    left: -10px;
    z-index: 1000;
  }

  .swiper-button-next,
  .swiper-rtl .swiper-button-prev {
    right: -10px;
    z-index: 1000;
  }
}