import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { IBreadcrumb } from '../contracts/contract';
import { ViewType } from '../enums/view-type';

@Injectable()
export class BreadcrumbService {
  hardRefresh = false;
  firstLevelViewSlugs: string[] = ['library', 'workspace', 'admin', 'my-journey', 'directory', 'splash'];
  private breadcrumbs: IBreadcrumb[] = [];
  constructor(private router: Router) {
    this.initBreadcrumbsFromSessionStorage();
  }

  addBreadCrumb(id: string, href: string, title: string, orgId: string | null, featureTypeName: string) {
    // First check if we need to restore from session storage
    const sessionBreadcrumbs = this.getFromSessionStorage();
    if (sessionBreadcrumbs && sessionBreadcrumbs.length > 0) {
      // If the last breadcrumb in session storage matches the one we're trying to add,
      // just restore from session storage and return
      const lastIndex = sessionBreadcrumbs.length - 1;
      if (sessionBreadcrumbs[lastIndex].id === id && sessionBreadcrumbs[lastIndex].orgId === orgId) {
        this.breadcrumbs = sessionBreadcrumbs;
        return;
      }
    }

    // Check if this breadcrumb already exists in our current list
    if (!this.breadcrumbs.find(x => x.id === id && x.orgId === orgId)) {
      // If not, add it to the end
      this.breadcrumbs.push({ id: id, url: href, name: title, oldName: title, orgId: orgId, featureTypeName: featureTypeName } as IBreadcrumb);
    } else {
      // If it exists, truncate the list to that point (remove everything after it)
      const index = this.breadcrumbs.findIndex(x => x.id === id && x.orgId === orgId);
      this.breadcrumbs.length = index + 1;
    }

    // Save the updated breadcrumbs to session storage
    this.saveToSessionStorage();
  }

  removeBreadCrumb(id: string) {
    if (this.breadcrumbs && this.breadcrumbs.length > 0) {
      const index = this.breadcrumbs.findIndex(x => x.id === id) + 1;
      const length = this.breadcrumbs?.length;

      if (index === length) {
        this.breadcrumbs.pop();
        this.saveToSessionStorage();
        return;
      }

      if (index >= 0) {
        this.breadcrumbs.splice(index, length - index);
        this.saveToSessionStorage();
      }
    }
  }

  /**
   * Navigate to the previous breadcrumb with an optional tab name replacement
   * @param viewType The view type to get breadcrumbs for
   * @param tabName Optional tab name to replace in the URL
   */
  goToPrev(viewType: ViewType, tabName?: string) {
    const crumbs = this.getBreadcrumbs(viewType);
    const crumb = viewType !== ViewType.Player ? crumbs[crumbs?.length - 1] : crumbs[crumbs?.length - 2];

    if (crumb && !tabName) {
      this.router.navigateByUrl(crumb.url);
    } else if (crumb && tabName) {
      // Get the URL parts
      const urlParts = crumb.url.split('/');

      // Replace the second-to-last part (if there are enough parts)
      if (urlParts.length >= 4) {
        urlParts[urlParts.length - 2] = encodeURIComponent(tabName);
      }

      // Reconstruct the URL
      const newUrl = urlParts.join('/');
      this.router.navigateByUrl(newUrl);
    }
  }

  resetBreadCrumbs() {
    this.breadcrumbs = [];
    this.saveToSessionStorage();
  }

  saveToSessionStorage() {
    sessionStorage.setItem(
      'breadcrumbs',
      JSON.stringify(
        this.breadcrumbs.map(breadcrumb => ({
          ...breadcrumb,
          name: breadcrumb.oldName,
        }))
      )
    );
  }

  getFromSessionStorage(): IBreadcrumb[] | null {
    const sessionBreadcrumbs = sessionStorage.getItem('breadcrumbs');
    if (sessionBreadcrumbs) {
      return JSON.parse(sessionBreadcrumbs);
    }

    return null;
  }

  getBreadcrumbs(viewType: ViewType): IBreadcrumb[] {
    if (viewType !== ViewType.Player) {
      return this.breadcrumbs.slice(0, -1);
    }

    return this.breadcrumbs;
  }

  featureTypeExists(featureTypeName: string) {
    return this.breadcrumbs?.some(x => x.featureTypeName === featureTypeName);
  }

  getByfeatureType(featureTypeName: string) {
    // Find the last item in the breadcrumb list with the provided feature type
    if (!this.breadcrumbs || this.breadcrumbs.length === 0) return undefined;

    // Iterate from the end to find the last matching item
    for (let i = this.breadcrumbs.length - 1; i >= 0; i--) {
      if (this.breadcrumbs[i].featureTypeName === featureTypeName) {
        return this.breadcrumbs[i];
      }
    }

    return undefined;
  }

  /**
   * Updates the URL of the current (last) breadcrumb
   * @param url The new URL to set for the current breadcrumb
   * @returns True if the URL was updated, false if there are no breadcrumbs
   */
  updateCurrentBreadcrumbUrl(url: string): boolean {
    if (url.endsWith('/player')) {
      return false;
    }

    if (this.breadcrumbs && this.breadcrumbs.length > 0) {
      const lastIndex = this.breadcrumbs.length - 1;
      this.breadcrumbs[lastIndex].url = url;
      this.saveToSessionStorage();
      return true;
    }
    return false;
  }

  /**
   * Initialize breadcrumbs from session storage when service is created
   */
  private initBreadcrumbsFromSessionStorage(): void {
    const sessionBreadcrumbs = this.getFromSessionStorage();
    if (sessionBreadcrumbs && sessionBreadcrumbs.length > 0) {
      this.breadcrumbs = sessionBreadcrumbs;
    }
  }
}
