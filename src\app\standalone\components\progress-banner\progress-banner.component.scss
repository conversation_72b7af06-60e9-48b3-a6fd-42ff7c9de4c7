// Fixed banner styles - extremely simple and highly visible
.fixed-banner {
  position: fixed;
  bottom: 15px;
  left: 0;
  right: 0;
  z-index: 998 !important; // High z-index but below some critical UI elements
  display: flex;
  justify-content: center;
  pointer-events: none; // Allow clicks to pass through
  animation: banner-fade-in 0.5s ease-in-out; // Add animation for visibility

  .banner-content {
    pointer-events: auto; // Re-enable pointer events for content
    width: auto;
    max-width: 800px;
    padding: 4px 4px 4px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    color: white;
    font-weight: normal;
    text-align: center;
  }

  // Banner message layout
  .banner-message {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 16px;

    .celebration-icon {
      margin-right: 5px;
      object-fit: contain;
      transform: scale(-1, 1);
    }

    span {
      flex: 1;
    }
  }

  // Continue button styling
  .continue-btn {
    color: #000;
    font-weight: 600;
  }

  // Responsive adjustments
  @media screen and (max-width: 767px) {
    .banner-content {
      width: 90%;
    }

    .banner-message {
      flex-direction: column;
      align-items: center;

      span {
        margin-bottom: 10px;
      }
    }
  }
}

.fixed-banner-mobile {
  bottom: 95px !important;
}

// Banner color variations with enhanced visibility
.green-banner {
  background-color: rgba(31, 40, 29, 0.9); // Semi-transparent dark green
  border: 2px solid #008000;
  box-shadow: 0 0 15px rgba(40, 100, 40, 0.8);
}

.orange-banner {
  background-color: rgba(23, 23, 23, 0.9); // Semi-transparent dark gray
  border: 2px solid #797979;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

// Player route specific styling
.player-route {
  bottom: 0; // Position at the very bottom when in player view

  .banner-content {
    border-radius: 0; // Remove border radius for a full-width look in player view
    max-width: 100%; // Full width in player view
    width: 100%;
  }
}

// Animation keyframes for banner visibility
@keyframes banner-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

ion-content::part(scroll) {
  padding-top: 0px !important;
}
