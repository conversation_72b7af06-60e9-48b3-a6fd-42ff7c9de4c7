import { moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { IComponent, IComponentIn, IComponentSortOrderIn, IInstance, ISection, ITemplate } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { ComponentType } from '@app/core/enums/component-type.enum';
import { SectionTypes } from '@app/core/enums/section-types.enum';
import { AlertService } from '@app/core/services/alert-service';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-feature-repository-builder-section',
    templateUrl: './section.component.html',
    styleUrls: ['./section.component.scss'],
    standalone: false
})
export class SectionComponent implements OnInit, OnDestroy {
  @Input() template: ITemplate;
  @Input() section: ISection;
  @Input() index: number;
  @Input() instance: IInstance;
  @Input() sectionTypes: KeyValue[];
  @Input() templateForm: UntypedFormGroup = new UntypedFormGroup({});
  @Input() featureId: string;
  @Output() sectionRemoved = new EventEmitter<string>();
  @Output() sectionChanged = new EventEmitter<any>();

  activeComponentId: string | null;
  componentTypeEnum = ComponentType;
  componentDestroyed$: Subject<boolean> = new Subject();
  draggingOver = false;
  sectionTypeEnums = SectionTypes;

  constructor(
    private dataService: DataService,
    private builderService: BuilderService,
    private alertService: AlertService
  ) {}

  ngOnInit() {
    this.builderService.selectedComponent$.pipe(takeUntil(this.componentDestroyed$)).subscribe(comp => {
      this.activeComponentId = comp?.id ?? '';
    });
  }

  componentSelected(comp: IComponent) {
    this.builderService.selectedComponent$.next(comp);
  }

  componentHover(componentId: string | null) {
    this.activeComponentId = componentId;
  }

  removeSection(sectionId: string) {
    this.sectionRemoved.emit(sectionId);
  }

  isRowComponent(component: IComponent) {
    return (component.componentType.id = '4983442B-F094-4B9D-8AD1-645A6485EE11');
  }

  reorderDropped(sectionId: string, event: any) {
    moveItemInArray(event.container.data.components, event.previousIndex, event.currentIndex);
    const componentArray: IComponentSortOrderIn[] = [];
    event.container.data.components.forEach((data: IComponent, index: number) => {
      componentArray.push({ id: data.id, sectionId: sectionId, builderSortOrder: index });
    });

    this.dataService
      .orderComponents(componentArray)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.section.components?.forEach(comp => {
          comp.builderSortOrder = componentArray.find(x => x.id === comp.id)?.builderSortOrder as number;
        });
      });
  }

  addFieldDropped(event: any) {
    const index = this.template.sections.indexOf(event.container.data);
    const newComponent: IComponentIn = {
      sectionId: event.container.data.id,
      componentTypeId: event.item.data.id,
      componentTypeBw: event.item.data.typeBw,
      templateField: null,
      builderRowNumber: null,
      isLocked: null,
      parentComponentId: null,
    };

    this.dataService
      .createComponent(newComponent)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(returnComponent => {
        (this.templateForm.get(this.section.id) as UntypedFormGroup).addControl(returnComponent.id, new UntypedFormControl());
        this.template.sections[index].components?.push(returnComponent);
        this.sectionChanged.emit(null);
        this.componentSelected(returnComponent);
        this.section.components = this.section.components?.slice(); // *Note: this is for ngx groupBy change detection
      });
  }

  deleteComponent(section: ISection, component: IComponent) {
    this.dataService
      .deleteComponent(component.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(isDeleted => {
        if (isDeleted) {
          const sectionIndex = this.template.sections.indexOf(section);
          const componentIndex = this.template.sections[sectionIndex].components?.indexOf(component) as number;
          this.template.sections[sectionIndex].components?.splice(componentIndex, 1);
          this.section.components = this.section.components?.slice();
        }
      });

    this.builderService.selectedComponent$.next(null);
  }

  deleteInstanceTags(tagTreeId: string) {
    if (this.instance.id && this.activeComponentId) {
      this.dataService.deleteInstanceTag(tagTreeId, this.instance.id, this.activeComponentId).pipe(takeUntil(this.componentDestroyed$)).subscribe();
    }
  }

  deleteComponentConfirmation(section: ISection, component: IComponent) {
    this.alertService.presentAlert('Confirm Delete', 'Are you sure you want to delete this Component?').then(() => {
      if (component.rowId == null && component.templateField?.isTag) {
        this.deleteInstanceTags(component.templateField.tagTreeId);
      }
      this.deleteComponent(section, component);
    });
  }

  reorderSection(moveUp: boolean, section: ISection) {
    const sectionIndex = this.template.sections.indexOf(section);
    const newIndex = moveUp ? sectionIndex - 1 : sectionIndex + 1;
    const arrayLength = this.template.sections.length;

    //Check index out of bounds
    if (newIndex >= 0 && newIndex <= arrayLength - 1) {
      this.template.sections.splice(sectionIndex, 1);
      this.template.sections.splice(newIndex, 0, section);
      for (let index = 0; index < arrayLength; index++) {
        const currentSection = this.template.sections[index];
        currentSection.sortOrder = index;

        const sectionForm = this.templateForm.get(currentSection.id) as UntypedFormGroup;
        sectionForm.controls['sortOrder'].patchValue(index);

        this.updateSectionOrder(currentSection.id, index);
      }
    }
  }

  updateSectionOrder(sectionId: string, sortOrder: number) {
    this.dataService
      .updateSectionOrder(sectionId, sortOrder)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.builderService.sectionUpdated$.next({ ...this.section, sortOrder: sortOrder } as ISection);
      });
  }

  toggleComponentLocked(sectionId: string, component: IComponent, isLocked: boolean) {
    this.dataService
      .toggleComponentLocked(component.id, isLocked)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        const sectionIndex = this.template.sections.findIndex(x => x.id === sectionId);
        const componentIndex = this.template.sections[sectionIndex].components?.findIndex(x => x.id === component.id);
        this.template.sections[sectionIndex].components[componentIndex].isLocked = isLocked;
      });
  }

  dragEntered(event: MouseEvent) {
    this.draggingOver = event.buttons > 0;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }

  selectSection(section: ISection) {
    this.builderService.selectedSection$.next(section);
    this.activeComponentId = null;
  }
}
