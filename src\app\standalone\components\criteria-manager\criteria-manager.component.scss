.parent-container {
  padding-left: 10px;
  .parent-card-container {
    border: 2px solid rgb(54, 54, 54);
    box-shadow: none !important;
    .inner-col {
      display: flex;
      border-radius: 8px;

      .content-container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        font-size: 16px;
        padding: 10px;
        margin-right: 10px;
        color: rgba(255, 255, 255, 0.8);

        .content {
          align-items: center;
          width: 100%;
        }
      }

      .remove-button-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        cursor: pointer;

        ion-icon {
          font-size: 25px;
          color: white !important;
        }
      }

      .drag-button-container {
        display: flex;
        margin-right: 20px;
        cursor: pointer;

        ion-reorder {
          z-index: 1000;
          display: flex;
          justify-content: center;
          align-items: center;
          ion-icon {
            font-size: 25px;
            color: white !important;
          }
        }
      }
    }
  }

  .add-button-col {
    margin: 10px 0px 10px 10px;
    justify-content: flex-start;
    display: flex;
    .add-button-container {
      display: flex;
      justify-content: center;
      align-items: center;

      mat-icon {
        cursor: pointer;
        margin-right: 10px;
        color: rgba(250, 167, 0) !important;
        min-width: 25px;
      }

      .button-text {
        color: white !important;
      }
    }
  }
}
