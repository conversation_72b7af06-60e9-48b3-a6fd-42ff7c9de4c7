import { Component } from '@angular/core';
import { MediaUploadType } from '@app/core/enums/media-upload-type';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { PopoverController } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-add-media',
    templateUrl: './add-media.component.html',
    styleUrls: ['./add-media.component.scss'],
    standalone: false
})
export class AddMediaComponent {
  mediaUploadTypeEnum = MediaUploadType;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private popover: PopoverController,
    private dataService: DataService,
    private instanceService: InstanceService
  ) {}

  async addMediaItem(uploadType: MediaUploadType) {
    this.dataService
      .addAsset(MediaUploadType[uploadType])
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(mediaId => {
        this.instanceService.openInstance('media-manager', mediaId, null, null, { uploadType: uploadType });
      });

    this.popover.dismiss();
  }
}
