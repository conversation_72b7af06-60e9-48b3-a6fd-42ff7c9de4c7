import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { IOrganizationSsoAuthIn, IProductFeature, IProductJoinCodeSetting, IProductOrgDomain, IOrgPrivacyTypeIn, IUserRole } from '@app/core/contracts/contract';
import { ProductTabType } from '@app/core/enums/product-tab-type';
import { DataService } from '@app/core/services/data-service';
import { Subject } from 'rxjs';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { JoinCodeComponent } from '../join-code/join-code.component';
import { IonicModule } from '@ionic/angular';
import { ProductSettingsComponent } from '../product-settings/product-settings.component';

@Component({
    selector: 'app-product-details',
    templateUrl: './product-details.component.html',
    styleUrls: ['./product-details.component.scss'],
    imports: [MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, JoinCodeComponent, IonicModule, ProductSettingsComponent]
})
export class ProductDetailsComponent implements OnInit, OnDestroy {
  @Input() productId: string;
  @Input() productOrgId: string;
  @Input() organizationId: string;
  @Input() privacyTypeId: string | undefined;
  @Input() productTabType: ProductTabType;
  @Input() joinCode: string | undefined;
  @Input() isJoinCodeProduct: boolean | undefined = false;
  @Input() productJoinCodeSettings?: IProductJoinCodeSetting[];
  @Output() productOrgDomainsChange = new EventEmitter<IProductOrgDomain[]>();
  @Output() orgSsoAuthChange = new EventEmitter<IOrganizationSsoAuthIn>();
  @Output() productOrgPrivacyTypeChange = new EventEmitter<IOrgPrivacyTypeIn>();
  @Output() productJoinCodesChange = new EventEmitter<any>();
  componentDestroyed$: Subject<boolean> = new Subject();
  userRoles: IUserRole[] = [];
  productFeatures: IProductFeature[] = [];
  productTabTypeValue = ProductTabType;
  settingsDisabled = false;
  currentAmount = 0;
  getAmount = 25;
  moreResults = false;
  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.getUserRoles();
    this.getProductFeaturesById(this.productId, false);
  }

  getUserRoles() {
    this.dataService.getUserRoles().subscribe((userRoles: IUserRole[]) => {
      if (userRoles.length > 0) {
        this.userRoles = userRoles;
      }
    });
  }

  getProductFeaturesById(productId: string, loadMore: boolean) {
    if (productId) {
      this.dataService.getProductFeaturesById(productId, this.currentAmount, this.getAmount).subscribe((productFeatures: IProductFeature[]) => {
        if (productFeatures.length > 0) {
          //OnLoadMoreData
          if (!loadMore) {
            this.productFeatures = productFeatures;
            this.currentAmount += productFeatures.length;
          } else {
            productFeatures.forEach(feat => {
              this.productFeatures = [...this.productFeatures, feat];
            });
            this.currentAmount += productFeatures.length;
          }

          if (productFeatures.length < this.getAmount) {
            this.moreResults = false;
          } else {
            this.moreResults = true;
          }
        }
      });
    }
  }

  updateProductFeature(productFeature: IProductFeature) {
    this.dataService.updateProductFeature(productFeature).subscribe((updatedProductFeature: IProductFeature) => {
      if (updatedProductFeature) {
        let existingProductFeature = this.productFeatures.find(x => x.id === updatedProductFeature.id);
        if (existingProductFeature) {
          existingProductFeature = updatedProductFeature;
        }
      }
    });
  }

  saveOrgSsoAuth(orgSsoAuthIn: IOrganizationSsoAuthIn) {
    this.orgSsoAuthChange.emit(orgSsoAuthIn);
  }

  saveProductOrgDomains(domains: IProductOrgDomain[]) {
    this.productOrgDomainsChange.emit(domains);
  }

  saveProductJoinCodes() {
    this.productJoinCodesChange.emit(null);
  }

  saveProductOrgPrivacyType(privacyTypeIn: IOrgPrivacyTypeIn) {
    this.productOrgPrivacyTypeChange.emit(privacyTypeIn);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
