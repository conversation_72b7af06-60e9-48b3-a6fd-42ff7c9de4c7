<mat-accordion multi>
  <mat-expansion-panel (opened)="openGroup(true)" (closed)="openGroup(false)">
    <mat-expansion-panel-header class="expansion-panel-header">
      <div class="inner-container">
        @for (header of expansionPanelHeader; track header; let i = $index) {
          <div>
            <div class="label">
              @if (header.amount || header.amount === 0) {
                <div class="amount-container">
                  <div class="amount">{{ formatNumber(header.amount) }}</div>
                </div>
              }
              <div [ngStyle]="{ 'min-width': i === 0 ? '80px' : 'auto' }">
                <span class="title">{{ header.title }}</span>
              </div>
            </div>
          </div>
        }
      </div>
    </mat-expansion-panel-header>
    <div class="users-table">
      <div class="table-container">
        <mat-table class="table-grid" #table [dataSource]="dataSource">
          @for (column of columns; track column; let colIndex = $index; let colCount = $count) {
            <ng-container [cdkColumnDef]="column.columnDef">
              <mat-header-cell *cdkHeaderCellDef [ngClass]="{ 'align-header-right': colIndex !== 0 && column.header !== 'Persona', 'name-header': colIndex === 0 }">
                {{ column.header }}
              </mat-header-cell>
              <mat-cell [ngClass]="{ 'name-cell': colIndex === 0, 'cell-width-small': colIndex !== 0 && isNumberOrDate(column.cell(row)) }" *cdkCellDef="let row">
                {{ isNumber(column.cell(row)) ? formatNumber(+column.cell(row)) : column.cell(row) }}
              </mat-cell>
            </ng-container>
          }
          <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
        </mat-table>
      </div>
    </div>
  </mat-expansion-panel>
</mat-accordion>
