import { AfterViewChecked, Component, CUSTOM_ELEMENTS_SCHEMA, ElementRef, Input, NO_ERRORS_SCHEMA, OnInit, signal, ViewChild } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { timer } from 'rxjs';

@Component({
  selector: 'app-qlik-selection',
  standalone: true,
  imports: [IonicModule, MatProgressSpinner],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
  templateUrl: './qlik-selection.component.html',
  styleUrls: ['./qlik-selection.component.scss'],
})
export class QlikSelectionComponent implements OnInit {
  @Input() id: string | null | undefined;
  @Input() filterType: string | undefined;
  @Input() date: string | undefined;
  @Input() componentType: string | undefined;
  @Input() componentId: string | undefined;
  @Input() showQlik: boolean = false;
  @Input() bypassWait: boolean = true;

  showComponent = false;

  constructor() {}

  ngOnInit(): void {
    if (!this.bypassWait) {
      timer(6000).subscribe(() => {
        this.showComponent = true;
      });
    } else {
      this.showComponent = true;
    }
  }
}
