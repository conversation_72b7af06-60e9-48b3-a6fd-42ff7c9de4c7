import { Component, Input } from '@angular/core';
import { PlayerViewBaseComponent } from '../base/player-view.component';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-player-view-scorm',
    templateUrl: './player-view.component.html',
    styleUrls: ['./player-view.component.scss'],
    standalone: false
})
export class PlayerViewScormComponent extends PlayerViewBaseComponent {
  @Input() searchFilter: string;
  constructor(public layoutService: LayoutService) {
    super();
  }

  rowFiltered(filter: string) {
    this.searchFilter = filter;
  }
}
