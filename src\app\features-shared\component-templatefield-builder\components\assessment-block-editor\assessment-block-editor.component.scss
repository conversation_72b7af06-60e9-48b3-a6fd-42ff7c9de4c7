.assessment-form-container {
  .required-questions-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: center;
    margin: 16px;
    margin-bottom: 10px;
    color: white;

    ion-input {
      background-color: #292929;
      height: 25px;
      width: 50px;
      border-radius: 4px;
      margin-right: 8px;
    }
  }

  .slide-fields-container-card {
    .percentage-container-row {
      margin-top: 10px;
      margin-bottom: 10px;
      .counter-col {
        ion-input {
          margin-right: 10px;
          height: 25px;
          width: 60px;
          --background: #292929;
          --padding-start: 8px !important;
          border: 1px solid #4e4e4e;
          border-radius: 5px;
          color: white;
          font-size: 16px;
        }
      }
    }
  }

  .file-size-parent-container {
    .reqAsterisk {
      color: #7f550c;
      font-size: 28px;
    }

    ion-item {
      font-size: 20px;
      --border-color: transparent;
      --color: white;
      --background: rgb(51, 51, 51);
    }
  }

  .mat-mdc-slide-toggle .mdc-label {
    color: #8f8f8f !important;
  }
}
