import { Injectable, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { IInstance } from '@app/core/contracts/contract';
import { InstanceService } from '@app/core/services/instance-service';
import { ToastController, ToastOptions, IonicSafeString } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { LayoutService } from './layout-service';
import { LoaderService } from './loader.service';

@Injectable({
  providedIn: 'root',
})
export class GlobalToastService implements OnDestroy {
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  rowProgressCompleteToast: HTMLIonToastElement | undefined;
  toast: HTMLIonToastElement | undefined = undefined;
  toastVisible$ = new Subject<{ visible: boolean }>();
  private destroy$ = new Subject<void>();
  private isToastHiddenByLoader = false;

  constructor(
    public toastController: ToastController,
    private router: Router,
    private instanceService: InstanceService,
    private layoutService: LayoutService,
    private loaderService: LoaderService
  ) {
    // Subscribe to loader visibility changes
    this.loaderService.loaderVisible$.pipe(takeUntil(this.destroy$)).subscribe(isLoaderVisible => {
      if (isLoaderVisible) {
        this.hideToastForLoader();
      } else {
        this.showToastAfterLoader();
      }
    });
  }

  /**
   * Hides the toast when the loader is visible without dismissing it
   */
  private hideToastForLoader() {
    if (this.toast && !this.isToastHiddenByLoader) {
      this.isToastHiddenByLoader = true;
      // Get the toast element and hide it using CSS class
      const toastElement = document.querySelector('ion-toast');
      if (toastElement) {
        toastElement.classList.add('toast-hidden');
      }
    }
  }

  /**
   * Shows the toast again when the loader is hidden
   */
  private async showToastAfterLoader() {
    if (this.toast && this.isToastHiddenByLoader) {
      this.isToastHiddenByLoader = false;
      // Get the toast element and show it by removing the CSS class
      const toastElement = document.querySelector('ion-toast');
      if (toastElement) {
        toastElement.classList.remove('toast-hidden');
      }
      // If the toast is not already presented, present it
      try {
        await this.toast.present();
      } catch (error) {
        // Toast might already be presented, ignore the error
      }
    }
  }

  private async presentToastInternal(toastOptions: ToastOptions) {
    if (this.toast) {
      await this.toast.dismiss();
    }

    const toast = await this.toastController.create({
      ...toastOptions,
    });

    this.toast = toast;
    this.toastVisible$.next({ visible: true });

    // Only present the toast if the loader is not visible
    if (!this.isToastHiddenByLoader) {
      await toast.present();
    }

    toast.onDidDismiss().then(() => {
      this.toastVisible$.next({ visible: false });
    });
  }

  async presentToast(message: string) {
    const toastOptions: ToastOptions = {
      message: new IonicSafeString(message),
      color: 'primary',
      duration: 3000,
    };
    await this.presentToastInternal(toastOptions);
  }

  async addToNotificationToast(instanceId: string, featureType: string) {
    const toastOptions: ToastOptions = {
      message: new IonicSafeString(`Added to ${featureType}`),
      cssClass: 'success-toast',
      icon: 'checkmark-circle-outline',
    } as ToastOptions;

    toastOptions.buttons = [
      {
        text: 'OPEN',
        handler: () => {
          if (featureType === 'Classroom') {
            this.router.navigateByUrl(`${instanceId}/${instanceId}/assignments/grid`);
          } else if (featureType === 'Favorites') {
            this.router.navigateByUrl(`my-favorites`);
          } else if (featureType === 'Portfolio') {
            this.router.navigateByUrl(`my-portfolio/${instanceId}/default/grid`);
          }
        },
      },
    ];
    toastOptions.duration = 5000;
    await this.presentToastInternal(toastOptions);
  }

  async addClassAssignments(instanceId: string) {
    const toastOptions: ToastOptions = {
      message: new IonicSafeString(`Add Classroom Assignments`),
      cssClass: 'class-assignment-toast',
      icon: 'checkmark-circle-outline',
      position: 'middle',
    } as ToastOptions;

    toastOptions.buttons = [
      {
        text: 'OPEN',
        handler: () => {
          this.router.navigateByUrl(`${instanceId}/${instanceId}/assignments/grid`);
        },
      },
    ];
    toastOptions.duration = 15000;
    await this.presentToastInternal(toastOptions);
  }

  async presentToastWithOptions() {
    return new Promise((resolve, reject) => {
      const toastOptions: ToastOptions = {
        message: new IonicSafeString(`Add Classroom Assignments`),
        cssClass: 'class-assignment-toast',
        buttons: [
          {
            text: 'Add',
            role: 'Cancel',
            handler: () => {
              resolve('Open');
            },
          },
          {
            text: 'Cancel',
            role: 'Cancel',
            handler: () => {
              resolve('Close');
            },
          },
        ],
        duration: 10000,
      };
      this.presentToastInternal(toastOptions).then(() => resolve('Open'));
    });
  }

  async presentCustomToastWithOptions(message: string, isErrorToast = false, buttonText = 'OK') {
    return new Promise((resolve, reject) => {
      const toastOptions: ToastOptions = {
        message: new IonicSafeString(message),
        cssClass: isErrorToast === false ? 'success-toast' : 'error-toast',
        icon: isErrorToast === false ? 'checkmark-circle-outline' : 'alert-circle-outline',
        buttons: [
          {
            text: buttonText,
            role: 'cancel',
            handler: () => {
              resolve('Open');
            },
          },
        ],
        duration: 10000,
      };
      this.presentToastInternal(toastOptions).then(() => resolve('Open'));
    });
  }

  async presentNotificationToast(message: string, isErrorToast = false, showButton = false) {
    const toastOptions: ToastOptions = {
      message: new IonicSafeString(message),
      cssClass: isErrorToast === false ? 'success-toast' : 'error-toast',
      icon: isErrorToast === false ? 'checkmark-circle-outline' : 'alert-circle-outline',
    } as ToastOptions;

    if (showButton === true) {
      toastOptions.buttons = [
        {
          text: 'OK',
          role: 'cancel',
        },
      ];
    } else {
      toastOptions.duration = 3000;
    }

    await this.presentToastInternal(toastOptions);
  }

  async presentLinkCopiedToast() {
    const toastOptions: ToastOptions = {
      message: new IonicSafeString(
        '<div style="display: flex; align-items: center;"><ion-icon name="copy-outline" style="margin-right: 10px; font-size: 16px; color: #f99e00;"></ion-icon> Link copied to clipboard</div>'
      ),
      cssClass: 'link-copied-toast',
      duration: 2000,
      position: 'bottom',
    };

    await this.presentToastInternal(toastOptions);
  }
}
