@defer (when date && this.qlikService.qlikSelection()) {
  @if (date && this.qlikService.qlikSelection()) {
    <div class="parent-header">
      <ion-row>
        <app-heading-value [inheritedPropertyValue]="templateField?.label ?? ''" [fontSize]="22"></app-heading-value>
      </ion-row>
    </div>
    @for (item of this.qlikService.qlikSelection()?.filterOptions; track $index) {
      @if (item === this.qlikService.qlikSelection()?.filterValue) {
        <app-qlik-selection
          [id]="item"
          [filterType]="this.qlikService.qlikSelection()?.filterType"
          [date]="date"
          [componentType]="componentType"
          [componentId]="componentId"
          [bypassWait]="this.qlikService.qlikSelection()?.bypassWait ?? false">
        </app-qlik-selection>
      }
    }
  }
}
