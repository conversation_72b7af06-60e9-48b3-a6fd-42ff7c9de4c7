import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { ModalController } from '@ionic/angular';
import { Subject } from 'rxjs';

@Component({
    selector: 'app-repository-dashboard-editor-dialog',
    templateUrl: './repository-dashboard-editor-dialog.component.html',
    styleUrls: ['./repository-dashboard-editor-dialog.component.scss'],
    standalone: false
})
export class RepositoryDashboardEditorDialogComponent implements OnInit {
  @Input() component: IComponent;
  @Input() textValue: string;
  @Input() id: string;

  formGroup: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(private modalController: ModalController) {}

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    const parentGroup: any = {};
    const group: any = {};
    group[this.component.id] = new UntypedFormControl({ value: this.textValue, disabled: !this.component?.templateField?.isBuilderEnabled });
    parentGroup['parentGroup'] = new UntypedFormGroup(group);
    this.formGroup = new UntypedFormGroup(parentGroup);
  }

  save() {
    let value = this.formGroup.get(['parentGroup', this.component.id])?.value;
    if (this.component.componentType.name === 'Image Upload Field' && value === null) {
      value = 'imageRemoved';
    }

    if (this.component.componentType.name === 'Dropdown' && this.component.templateField.dropDownLinkType?.title === 'User Tags') {
      value = 'userTagsUpdated';
    }

    this.modalController.dismiss(value);
  }

  cancel() {
    this.modalController.dismiss();
  }
}
