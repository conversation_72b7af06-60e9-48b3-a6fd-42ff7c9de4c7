import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { IFeatureTab, IInstance, IInstanceTemplate, IRouteParams } from '@app/core/contracts/contract';
import { ComponentType } from '@app/core/enums/component-type.enum';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { Observable, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-scorm-template',
    templateUrl: './template.component.html',
    styleUrls: ['./template.component.scss'],
    standalone: false
})
export class TemplateComponent implements OnInit, OnDestroy {
  @Input() templateId: string;
  @Input() instance: IInstance;
  @Input() featureTab: IFeatureTab;
  @Input() disabled = true;
  @Input() routeParams: IRouteParams;
  template$: Observable<IInstanceTemplate>;
  componentType = ComponentType;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private instanceService: InstanceService
  ) {}

  ngOnInit(): void {
    this.dataService.reload$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setTemplate();
    });

    this.setTemplate();
  }

  getId() {
    return this.instanceService.isValidGUID(this.routeParams.instanceSlug ?? '') ? this.routeParams.instanceSlug : this.instance.id;
  }

  setTemplate() {
    this.template$ = this.dataService.getInstanceTemplate(this.instance.id, this.templateId, undefined, undefined, true);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
