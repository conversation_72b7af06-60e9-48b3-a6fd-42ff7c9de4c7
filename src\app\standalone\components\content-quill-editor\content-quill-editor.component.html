<div class="quill-edit-parent-container">
  <quill-editor
    conveythis-no-translate
    customToolbarPosition="bottom"
    trackChanges="all"
    [(ngModel)]="value"
    format="html"
    (keyup)="onKeyUp()"
    (onContentChanged)="onContentChanged($event)"
    (onEditorCreated)="getEditorInstance($event)"
    [placeholder]="placeHolder"
    [debounceTime]="1000"
    [maxLength]="100000000"
    [readOnly]="disabled">
    <div class="quill-toolbar" quill-editor-toolbar>
      <span class="ql-formats">
        <button class="ql-bold" [title]="'bold'"></button>
        <button class="ql-italic" [title]="'italic'"></button>
        <button class="ql-underline" [title]="'underline'"></button>
        <button class="ql-strike" [title]="'strikethrough'"></button>
        <button class="ql-list" value="ordered" [title]="'ordered list'"></button>
        <button class="ql-list" value="bullet" [title]="'bullet list'"></button>
        <select class="ql-color" [title]="'color picker'"></select>
        <button class="ql-link" [title]="'link'"></button>
        <select class="ql-align"></select>
        <select class="ql-header">
          <option value="1">Heading</option>
          <option value="2">Subheading</option>
          <option value>Normal Text</option>
        </select>
      </span>
      @if (!hideQuillPersonalize) {
        <span class="ql-formats">
          <button class="custom-button" (click)="personalizeContent()">Personalize</button>
        </span>
      }
    </div>
  </quill-editor>
  @if (charCount > 0) {
    <span class="text-counter">{{ charCount }}</span>
  }
</div>
