import { Component, Input, OnInit } from '@angular/core';
import { IAction, IFeature, IFeatureTab, IFeatureTabButton, IFeatureTabButtonLinkType } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Observable } from 'rxjs';

@Component({
    selector: 'app-tab-button-actions',
    templateUrl: './tab-button-actions.component.html',
    styleUrls: ['./tab-button-actions.component.scss'],
    standalone: false
})
export class TabButtonActionsComponent implements OnInit {
  @Input() featureTab: IFeatureTab;
  buttonLinkTypes$: Observable<IFeatureTabButtonLinkType[]>;
  actions$: Observable<IAction[]>;
  features$: Observable<IFeature[]>;

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.initData();
  }

  initData() {
    this.buttonLinkTypes$ = this.dataService.getFeatureTabButtonLinkTypes();
    this.actions$ = this.dataService.getActions();
    this.features$ = this.dataService.getFeatures();
  }

  addButton() {
    const button = {} as IFeatureTabButton;

    if (this.featureTab.featureTabButtons) {
      this.featureTab.featureTabButtons.push(button);
    } else {
      this.featureTab.featureTabButtons = [button];
    }
  }

  removeButton(button: IFeatureTabButton) {
    const index = this.featureTab.featureTabButtons.indexOf(button);

    if (index !== -1) {
      this.featureTab.featureTabButtons.splice(index, 1);
    }
  }
}
