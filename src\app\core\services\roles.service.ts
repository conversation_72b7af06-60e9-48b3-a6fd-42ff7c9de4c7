import { Injectable, signal } from '@angular/core';
import { IProductFeatureRole } from '../contracts/contract';
import { DataService } from './data-service';
import { map, Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class RolesService {
  private productFeatureRoles = signal<IProductFeatureRole[]>([]);
  private playerProductFeatureRoles = signal<IProductFeatureRole[]>([]);
  private featureId = signal<string | undefined>(undefined);
  constructor(private dataService: DataService) {}

  getProductFeatureRoles(featureId?: string): Observable<void> {
    if (!featureId || this.featureId() === featureId) {
      return of(undefined);
    }

    this.featureId.set(featureId ?? '');

    return this.dataService.getProductFeatureRoles(featureId ?? '').pipe(
      map(data => {
        this.productFeatureRoles.set(data);
      })
    );
  }

  getPlayerUserRoles(featureId: string) {
    return this.dataService.getProductFeatureRoles(featureId).pipe(
      map(data => {
        this.playerProductFeatureRoles.set(data);
      })
    );
  }

  hasFeatureRoleAccess(roleBws: number[]): boolean {
    if (this.productFeatureRoles()?.length <= 0 && this.featureId()) {
      return false;
    }

    // Show everything on instances for which a product and roles was not set up.
    if (!this.featureId) {
      return true;
    }
    return this.productFeatureRoles()?.some(x => roleBws?.some(r => r & x.actionBw));
  }

  // NB: This is specifically for the player view instance.
  hasPlayerProductRoleAccess(roleBws: number[]): boolean {
    // Show everything on instances for which a product and roles was not set up.
    if (!this.playerProductFeatureRoles() || this.playerProductFeatureRoles()?.length <= 0) {
      return false;
    }

    return this.playerProductFeatureRoles()?.some(x => roleBws?.some(r => r & x.actionBw));
  }
}
