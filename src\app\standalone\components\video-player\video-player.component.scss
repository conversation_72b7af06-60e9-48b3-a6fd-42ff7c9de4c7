.placeholder-container {
  .card-content-container {
    margin: 0px !important;
    background-color: transparent;
  }
}

.parent-container {
  background: #232323;
  border-radius: 11px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  position: relative;
  margin-top: 10px;
  aspect-ratio: 16/9;
  margin-bottom: 25px;
  video {
    width: 100%;
  }
  vg-player {
    border-radius: 11px;
  }
}
vg-track-selector{
line-height:20px
}

.video-border-completed {
  border-color: green !important;
}
.video-border-default {
  border-color: #454545;
}

.completed-header-container {
  ion-row {
    z-index: 999;
    position: absolute;
    top: -10px;
    left: 50px;
    .inner-completed {
      background-color: green;
      padding: 2px;
      font-size: 12px;
      border-radius: 5px;
      color: white;
      margin-right: 16px;
    }
    .inner-required {
      background-color: grey;
      padding: 2px;
      font-size: 12px;
      border-radius: 5px;
      color: white;
    }
  }
}

.required-header-container {
  position: absolute;
  top: -7px;
  left: 50px;
  z-index: 999;

  .inner {
    background-color: grey;
    padding: 2px;
    font-size: 12px;
    border-radius: 5px;
    color: white;
    z-index: 999;
    position: absolute;
  }
}

.vg-controls{
  padding-right: 16px;
}

.vg-scrub-bar{
  // pointer-events: none;
  margin:0;
}

#vid1::cue {
  background-color: rgba($color: #000000, $alpha: 0.6);
  font-size: 28px;
  font-family: 'Exo 2';
  letter-spacing: 0, 01em;
  line-height: 1.1;
  color: #fff;
}
