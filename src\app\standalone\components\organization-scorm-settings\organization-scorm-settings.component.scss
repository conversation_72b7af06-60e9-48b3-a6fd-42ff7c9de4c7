.card-container {
  background-color: rgba(41, 41, 41);

  .middle-line {
    margin: 15px;

    hr {
      border-top: 1px solid rgb(164, 164, 164);
    }
  }

  .top-form-container {
    h4 {
      color: white;
      font-size: 12px;
      margin-left: 15px;
      margin-right: 15px;
    }
  }

  .slider-form-container {
    h4 {
      color: white;
      font-size: 12px;
      margin-left: 15px;
      margin-right: 15px;
    }
    .card-container {
      padding: 10px;

      .heading-col {
        display: flex;
        justify-items: center;
        justify-content: flex-start;

        .heading {
          font-weight: bold;
          font-size: 20px;
          color: white;
        }
        .sub-heading {
          margin-bottom: 5px;
          font-style: italic;
          color: rgba(170, 170, 170);
        }
      }

      .slider-col {
        display: flex;
        justify-items: center;
        align-items: center;
        justify-content: flex-end;
        border-left: 2px solid gray;
      }
    }
  }

  .role-form-container {
    h4 {
      color: white;
      font-size: 12px;
      margin-left: 15px;
      margin-right: 15px;
    }
    .card-container {
      background-color: rgba(41, 41, 41);
      .inner-container {
        display: flex;
        justify-content: center;
        justify-content: flex-start;
        align-items: center;
        font-size: 16px;
        padding: 10px;
        color: rgba(255, 255, 255, 0.8);

        .static-input-container {
          margin-left: 15px;
          margin-right: 15px;
          ion-input {
            --padding-start: 12px;
            --padding-end: 12px;
            height: 30px;
            width: 175px;
            border: 2px solid rgb(83, 67, 41);
            border-radius: 5px;
            text-overflow: ellipsis;
            background-color: rgba(52, 43, 27);
          }
        }

        .drop-down-container {
          margin-left: 15px;
          margin-right: 15px;
          ion-select {
            --padding-start: 12px;
            --padding-end: 12px;
            height: 30px;
            width: 175px;
            border-radius: 5px;
            border: 2px solid rgb(83, 67, 41);
            text-overflow: ellipsis;
            background-color: rgba(52, 43, 27);
          }
        }
      }
    }
  }

  .externalUrl{
    margin-left: 1em;
  }

  .center-col{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
}
