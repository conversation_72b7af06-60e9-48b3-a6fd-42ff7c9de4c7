<div class="feature-repository-dashboard-container">
  <ion-row class="top-container">
    <ion-col size="3" class="start-col-search">
      <form [formGroup]="searchForm">
        <ion-searchbar color="dark" formControlName="featureRepoSearchValue" (ionInput)="searchRepoValue($event)" type="search" placeholder="Search" showCancelButton="focus" debounce="800">
        </ion-searchbar>
      </form>
    </ion-col>
    <ion-col size="2" class="start-col-search select-option">
        <app-select-option-control
          [toolTip]="'Filter by Status'"
          [placeHolder]="'Filter by Status'"
          [label]="''"
          [backgroundColor]="controlBackground"
          [textValue]="statusTypeId"
          [options]="statusTypes"
          (valueChanged)="filterValues($event)"></app-select-option-control>   
    </ion-col>
    <ion-col size="7" class="end-col-buttons">
      <div class="buttons">
        @if (name === 'Achievement Completion') {
          <ion-button (click)="openSyncBadgeModal()"> Sync Badges </ion-button>
        }
        @if (showAdd) {
          <ion-button (click)="add($event)"> <mat-icon svgIcon="add"></mat-icon> Add </ion-button>
        }
      </div>
    </ion-col>
  </ion-row>
  @if (dataSource) {
    <app-repository-dashboard-table
      (routeOnClick)="openInstanceBuilder($event)"
      (loadMoreData)="getRepositoryDashboard($event)"
      (refreshData)="refreshData()"
      (sortChange)="sortChanged($event)"
      [feature]="feature"
      [columnsToDisplayIn]="displayedColumns"
      [dataSourceIn]="dataSource"
      [loading]="loading">
    </app-repository-dashboard-table>
  }

  <div class="scroll-cap"></div>
</div>
