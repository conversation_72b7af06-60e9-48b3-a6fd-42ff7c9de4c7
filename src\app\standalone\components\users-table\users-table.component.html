<div class="parent-container">
  <ion-grid>
    <ion-row class="view-options-row">
      <div class="top-header">
        <form [formGroup]="searchForm">
          <ion-searchbar
            color="dark"
            formControlName="repoSearchValue"
            float="left"
            (ionChange)="searchRepoValue()"
            type="search"
            placeholder="Search for a user"
            showCancelButton="focus"
            debounce="600">
          </ion-searchbar>
        </form>
        <div class="end-container">
          @if (userCount) {
            <div id="user-count">{{ userCount }} users</div>
          }
          <ion-button (click)="presentParentAddPopover($event)">&plus; User</ion-button>
        </div>
        <!-- <ion-button (click)="openBulkUpload()" color="warning">Bulk Upload</ion-button> -->
        <ion-popover #parentAddPopover [isOpen]="isParentPopoverOpen" (didDismiss)="isParentPopoverOpen = false" side="bottom" alignment="start">
          <ng-template>
            <ion-content>
              <ion-item-group>
                @if (canManage) {
                  <ion-item button="true" value="single" (click)="addUserModal()">Add a user</ion-item>
                }
                @if (type === 'Organization Manager') {
                  <ion-item button="true" value="single" (click)="createUserModal()">Create a user</ion-item>
                }
                <!-- <ion-item button="true" value="bulk">Bulk add a user</ion-item> -->
              </ion-item-group>
            </ion-content>
          </ng-template>
        </ion-popover>
      </div>
    </ion-row>
  </ion-grid>
  <div class="users-table">
    <section class="table-container">
      @if (dataSource) {
        <table class="table-grid" mat-table [dataSource]="dataSource" matSort (matSortChange)="sortChanged($event)" #sort="matSort">
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox (change)="$event ? masterToggle() : null" [checked]="selection.hasValue() && isAllSelected()" [indeterminate]="selection.hasValue() && !isAllSelected()"> </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(row) : null" [checked]="selection.isSelected(row)"> </mat-checkbox>
            </td>
          </ng-container>
          <ng-container matColumnDef="id">
            <th hidden mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
            <td hidden mat-cell *matCellDef="let element">{{ element.id }}</td>
          </ng-container>
          <ng-container matColumnDef="name" sticky>
            <th mat-header-cell *matHeaderCellDef mat-sort-header>NAME</th>
            <td style="color: rgba(250, 167, 0) !important" mat-cell *matCellDef="let element">{{ element?.name ?? '-' }}</td>
          </ng-container>
          <ng-container matColumnDef="details">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>ORG, PRODUCT & ROLE</th>
            <td
              mat-cell
              *matCellDef="let element"
              (click)="type === 'Organization Manager' ? updateProductOrgUserRole(element) : updateProductOrgUserRolePopover($event, element.id, element.roleId)"
              style="cursor: pointer">
              <div class="details-container">
                <span class="org-name">{{ element.organizationName }}</span>
                <div class="inner-container"><ion-icon name="ellipse-outline"></ion-icon>{{ element.productName }} [{{ element.roleName }}]</div>
              </div>
            </td>
          </ng-container>
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>EMAIL</th>
            <td mat-cell *matCellDef="let element">{{ element?.email ?? '-' }}</td>
          </ng-container>
          <ng-container matColumnDef="designation">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>DESIGNATION</th>
            <td mat-cell *matCellDef="let element"></td>
          </ng-container>
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>STATUS</th>
            <td mat-cell *matCellDef="let element">
              <span [ngClass]="{ red: element.status === 'Pending', yellow: element.status === 'Suspended' }">{{ element?.status ?? '-' }}</span>
            </td>
          </ng-container>
          <ng-container matColumnDef="address">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>LOCATION</th>
            <td mat-cell *matCellDef="let element">{{ element?.address ?? '-' }}</td>
          </ng-container>
          <ng-container matColumnDef="personas">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>PERSONAS</th>
            <td mat-cell *matCellDef="let element">
              <!--            <app-tag-popover [viewType]="2" [selectedUserId]="element.userId" [dropDownLinkType]="'User Tags'" [component]="instanceSectionComponent?.component"> </app-tag-popover>-->
              <app-persona-chip-list [existingTags]="element?.personas" [component]="instanceSectionComponent?.component" [selectedUserId]="element.userId"></app-persona-chip-list>
            </td>
          </ng-container>
          @if (canManage) {
            <ng-container matColumnDef="userTags">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>USER TAGS</th>
              <td mat-cell *matCellDef="let element">
                <app-tag-popover [viewType]="2" [selectedUserId]="element.userId" [dropDownLinkType]="'User Tags'" [component]="instanceSectionComponent?.component"> </app-tag-popover>
              </td>
            </ng-container>
          }
          <ng-container matColumnDef="lastActivity">
            <th style="border-right: none" mat-header-cell *matHeaderCellDef mat-sort-header>LAST ACTIVITY</th>
            <td mat-cell *matCellDef="let element">{{ element?.userActivity?.lastActivity | date: 'dd/MM/yyyy' ?? '-' }}</td>
          </ng-container>
          @if (canManage && type === 'Organization Manager') {
            <ng-container matColumnDef="qlikUser">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>ANALYTICS USER</th>
              <td mat-cell *matCellDef="let element">
                <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? updateQlikUser(element) : null" [checked]="element?.qlikUser === true"> </mat-checkbox>
              </td>
            </ng-container>
          }
          @if (hasAdminAccess()) {
            <ng-container matColumnDef="header-row-first-group">
              <th class="first-group" mat-header-cell *matHeaderCellDef [attr.colspan]="11">
                <div class="parent-topheader-container">
                  <div class="selected-container">{{ selection.selected.length }} Selected</div>
                  <div class="buttons-container">
                    <!-- <ion-label> <ion-icon name="remove-circle-outline"></ion-icon>Remove</ion-label>
                    <ion-label> <ion-icon name="add"></ion-icon>Add To</ion-label> -->
                    <ion-label [ngClass]="{ 'disabled-btn': selection.selected.length === 0 }" (click)="selection.selected.length !== 0 ? updateTableUsers('Active') : null">
                      <ion-icon name="people"></ion-icon>Approve
                    </ion-label>
                    <ion-label [ngClass]="{ 'disabled-btn': selection.selected.length === 0 }" (click)="selection.selected.length !== 0 ? updateTableUsers('Deny') : null">
                      <ion-icon name="ban-outline"></ion-icon>Deny
                    </ion-label>
                    <ion-label [ngClass]="{ 'disabled-btn': selection.selected.length === 0 }" (click)="selection.selected.length !== 0 ? updateStatus('Deleted') : null">
                      <ion-icon name="trash-outline"></ion-icon>Remove
                    </ion-label>
                  </div>
                </div>
              </th>
            </ng-container>
            <tr mat-header-row *matHeaderRowDef="['header-row-first-group']"></tr>
          }
          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      }
    </section>
    @if (moreResults && id) {
      <div (click)="getTableUsersData(id, true)" class="load-more">
        <ion-row>
          <ion-col size="12">
            <div>Load More</div>
            <div><ion-icon name="chevron-down-outline"></ion-icon></div>
          </ion-col>
        </ion-row>
      </div>
    }
  </div>
</div>
