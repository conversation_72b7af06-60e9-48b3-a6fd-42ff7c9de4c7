import { Component, Input } from '@angular/core';
import { IRouteParams } from '@app/core/contracts/contract';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-scorm-player-view-information',
    templateUrl: './player-view-information.component.html',
    styleUrls: ['./player-view-information.component.scss'],
    standalone: false
})
export class PlayerViewInformationComponent {
  @Input() routeParams: IRouteParams;

  constructor(public layoutService: LayoutService) {}
}
