import { Component, forwardRef, Input, OnInit } from '@angular/core';
import { environment } from '@env/environment';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IInstance } from '@app/core/contracts/contract';
import { IonicModule } from '@ionic/angular';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
@Component({
    selector: 'app-achievement-control',
    templateUrl: './achievement-control.component.html',
    styleUrls: ['./achievement-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => AchievementControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => AchievementControlComponent),
        },
    ],
    imports: [IonicModule]
})
export class AchievementControlComponent extends BaseControlComponent implements OnInit {
  @Input() backgroundColor = '#181818';
  @Input() itemBackgroundColor = '';
  @Input() achievementInstance: IInstance | null;

  achievementName: string;
  issuedBy: string;
  description: string;

  imageUrl: string;
  isLocked = false;

  constructor() {
    super();
  }

  ngOnInit() {
    this.achievementName = this.achievementInstance?.title ? this.achievementInstance.title : 'Achievement Name..';
    this.issuedBy = this.issuedBy ? this.issuedBy : 'Edge Factor';
    this.description = this.achievementInstance?.description ? this.achievementInstance?.description : 'Achievement Description..';

    if (this.achievementInstance?.iconAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.achievementInstance.iconAssetId}/content`;
    } else {
      this.imageUrl = 'assets/images/no-image.png';
    }
  }

  updateLockStatus() {
    if (this.isLocked) {
      this.isLocked = false;
    } else {
      this.isLocked = true;
    }
  }
}
