import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '@env/environment';
import { User, UserManager, UserManagerSettings } from 'oidc-client-ts';
import { BehaviorSubject, Observable, from, of } from 'rxjs';
import { concatMap, map, mergeMap, switchMap } from 'rxjs/operators';
import { IGuestContext, IUserContext, IPersonaChildren } from '../contracts/contract';
import { DataService } from './data-service';
import { MonitoringService } from './monitoring-service';

@Injectable({ providedIn: 'root' })
export class AuthService {
  openGuestModel$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  openPersonaSelector$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  user: User | null;
  userContext: IUserContext | null;
  guestUserContext: IGuestContext | null;
  idLocation: string | null;
  impersonate = false;
  persona = { id: 'e2d52b0e-3bb5-4a98-9896-949cb0aa213e', name: 'As a student', display: 'Student', icon: 'student-icon', children: [] as IPersonaChildren[] };
  manager: UserManager;

  constructor(
    private dataService: DataService,
    private router: Router,
    private monitoringService: MonitoringService
  ) {}

  init() {
    this.idLocation = localStorage.getItem('id_location');
    if (this.userContext && this.idLocation) {
      this.manager = new UserManager(getClientSettings(this.idLocation));
      this.manager.events.addSilentRenewError(() => {});
      this.manager.events.addAccessTokenExpired(() => {});
    }
  }

  canActivateRoute(): Observable<boolean> {
    if (this.manager && !this.user) {
      return from(
        this.manager.getUser().then(user => {
          this.user = user;
          return user;
        })
      ).pipe(concatMap(user => this.authLogic(user)));
    } else if (this.manager && this.user) return this.authLogic(this.user);
    else if (!this.guestUserContext) return of(false);
    return of(false);
  }

  authLogic(user: User | null): Observable<boolean> {
    if ((user && !user.expired) || this.isGuest()) {
      return of(true);
    } else if (user && user.expired) {
      return this.silentRefresh();
    }
    return of(false);
  }

  isGuest(): boolean {
    return this.guestUserContext != null && !this.guestUserContext?.impersonate;
  }

  setGuestContext(context: any) {
    this.guestUserContext = context;
    localStorage.setItem('guest_context', JSON.stringify(context));
  }

  startAuthentication() {
    const returnUrl = sessionStorage.getItem('returnUrl');
    sessionStorage.setItem('returnUrl', returnUrl ?? this.router.url);
    return from(this.manager.signinRedirect());
  }

  startAuthenticationExternal() {
    return from(this.manager.signinRedirect());
  }

  startAuthenticationJoinCode(joinCode: string) {
    return from(
      this.manager.signinRedirect({
        extraQueryParams: {
          joinCode: joinCode,
        },
      })
    );
  }

  silentRefresh() {
    sessionStorage.setItem('returnUrl', this.router.url);
    return from(
      this.manager.signinSilent().then(user => {
        this.user = user;
        return !!user;
      })
    );
  }

  clearCache() {
    localStorage.removeItem('trackingId');
    localStorage.removeItem('user_context');
    localStorage.removeItem('guest_context');
    localStorage.removeItem('logoutWindow');
    sessionStorage.setItem('returnUrl', '');
  }
  endAuthentication() {
    localStorage.removeItem('trackingId');
    localStorage.removeItem('user_context');
    localStorage.removeItem('guest_context');
    sessionStorage.setItem('logoutWindow', 'true');
    sessionStorage.setItem('returnUrl', '');
    this.setGuestContext({
      id: this.persona.id,
      browsingAs: this.persona.display,
      instanceId: '',
      impersonate: false,
    } as IGuestContext);
    this.user = null;
    this.userContext = null;
    this.monitoringService.clearUserId();
    const logout = new BroadcastChannel('logout');
    logout.postMessage('refresh');
    return from(this.manager.clearStaleState())
      .pipe(mergeMap(() => from(this.manager.signoutRedirect())))
      .pipe(concatMap(() => this.setUserContext(false)));
  }

  completeAuthentication() {
    return from(this.manager.signinRedirectCallback()).pipe(
      map(user => {
        localStorage.removeItem('guest_context');
        this.guestUserContext = null;
        this.user = user;
      }),
      switchMap(() => this.setUserContext(true))
    );
  }

  setUserContext(force: boolean) {
    this.userContext = JSON.parse(localStorage.getItem('user_context') as string);
    this.guestUserContext = JSON.parse(localStorage.getItem('guest_context') as string);
    const trackingId = localStorage.getItem('trackingId');
    if (this.userContext && !force) {
      if (this.userContext.userId) {
        this.monitoringService.setUserId(this.userContext.userId);
      }
      return of(this.userContext); //if we have it in session storage don't request again
    }
    return this.dataService.getUserContext(trackingId).pipe(
      map(res => {
        this.userContext = res;
        localStorage.setItem('user_context', JSON.stringify(this.userContext));
        localStorage.setItem('trackingId', this.userContext.userTrackingId);
        this.idLocation = localStorage.getItem('id_location');
        localStorage.setItem('id_location', this.idLocation ?? this.userContext.country ?? 'Canada');
        if (this.userContext.userId) {
          this.monitoringService.setUserId(this.userContext.userId);
        }
        return res;
      })
    );
  }

  getToken(): Observable<string | undefined> {
    if (this.guestUserContext && this.guestUserContext.impersonate) {
      return of('guest');
    }
    if (this.user) {
      return of(this.user.id_token);
    } else {
      return of('guest');
    }
  }

  activePushMessages() {
    // this.oneSignal.startInit('************************************', '176621624030');
    // this.oneSignal.enableSound(true);
    // this.oneSignal.enableVibrate(true);
    // this.oneSignal.setExternalUserId(userId);
    // this.oneSignal.iOSSettings({ kOSSettingsKeyAutoPrompt: false, kOSSettingsKeyInAppLaunchURL: false });
    // this.oneSignal.inFocusDisplaying(this.oneSignal.OSInFocusDisplayOption.Notification);
    // this.oneSignal.endInit();
  }
}

export const getClientSettings = (location?: string): UserManagerSettings => {
  let authorityUrl;
  if (location === 'Canada') {
    authorityUrl = environment.authority_ca;
  } else {
    authorityUrl = environment.authority_usa;
  }
  return {
    authority: authorityUrl,
    client_id: environment.client_id,
    redirect_uri: environment.redirect_uri,
    popup_redirect_uri: environment.redirect_uri,
    post_logout_redirect_uri: environment.post_logout_redirect_uri,
    response_type: 'code',
    scope: 'openid profile edgefactor_api',
    filterProtocolClaims: true,
    loadUserInfo: false,
    automaticSilentRenew: false,
    silent_redirect_uri: environment.silent_redirect_uri,
  };
};
