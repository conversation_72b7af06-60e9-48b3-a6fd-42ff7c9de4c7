@if (instance$ | async; as instance) {
  <mat-expansion-panel #panel hideToggle class="hover-item">
    <mat-expansion-panel-header [style]="'--background-image:url(' + imageUrl + ');'">
      <ion-grid>
        <ion-row>
          <ion-col
            ><div class="panel-left">
              <ion-icon name="lock-closed-outline" matTooltip="You don't have access to this."></ion-icon></div
          ></ion-col>
          <ion-col> </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <div class="panel-left">
              <img class="row-icon" [src]="iconUrl" />
              <div class="description">
                @if (instance?.feature?.title) {
                  <h1>{{ instance?.feature?.title }}</h1>
                }
                @if (instance?.feature?.description) {
                  <h6>{{ instance?.feature?.description }}</h6>
                }
              </div>
            </div></ion-col
          >
          <ion-col></ion-col>
        </ion-row>
        <ion-row></ion-row>
      </ion-grid>
    </mat-expansion-panel-header>
    <div class="content-container">
      <app-instance-section-hover-components [routeParams]="routeParams" [onlyHover]="true"></app-instance-section-hover-components>
    </div>
  </mat-expansion-panel>
}
