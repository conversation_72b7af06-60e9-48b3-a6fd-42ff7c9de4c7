@if (template) {
  <!-- Existing Section -->
  @for (section of template.sections; track section; let i = $index) {
    <app-feature-repository-builder-section
      [template]="template"
      [section]="section"
      [index]="i"
      [instance]="instance"
      [templateForm]="templateForm"
      [featureId]="featureTab.featureId"
      [sectionTypes]="sectionTypes"
      (sectionRemoved)="deleteSectionConfirmation(section.id)"
      (sectionChanged)="sectionUpdated()"></app-feature-repository-builder-section>
  }
  <!-- New -->
  <div class="new-section" (click)="openSelectSectionType()">
    <p>Add section</p>
    <ion-icon color="primary" name="add-circle-outline"></ion-icon>
  </div>
}
