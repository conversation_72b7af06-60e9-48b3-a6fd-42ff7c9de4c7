.parent-container {
  margin-bottom: 10px;

  .search-item {
    --border-color: transparent; // default underline color
    --color: white;
    font-size: 20px;
    font-family: 'Exo 2';
    --background: #181818;

    .reqAsterisk {
      color: #7f550c;
      font-size: 28px;
    }

    ion-text {
      color: red;
      font-style: italic;
      font-size: 14px;
    }

    ion-input {
      border: 1px solid #4e4e4e;
      border-radius: 5px;
      margin-top: 0.2em;
      color: white;
      font-size: 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      --padding-start: 8px !important;
      caret-color: #7f550c;
      background-color: var(--background-color);
    }

    ion-label {
      cursor: help !important;
    }

    ion-icon {
      cursor: help !important;
    }

    .identifier {
      background-color: #7f550c;
      border-radius: 0.2em;
      color: black;
      font-weight: bold;
      padding: 0.3em;
      margin-left: 0.4em;
    }

    .label-header {
      margin-bottom: 0.2em;
      padding-bottom: 0.04em;
      font-weight: 500;
      letter-spacing: 0.03em;
    }

    ion-input.ion-invalid.ion-touched {
      border: 1px solid red;
    }
  }

  .side-panel-input-padding {
    --inner-padding-end: 10px;
    --padding-start: 10px;

    ion-input {
      height: 100%;
      width: 100%;
      border: none !important;
    }
  }
}