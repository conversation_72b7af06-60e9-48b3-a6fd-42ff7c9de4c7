import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { <PERSON><PERSON>uttonToggle, MatButtonToggleChange, MatButtonToggleGroup } from '@angular/material/button-toggle';
import { INetwork, IOrganizationLite, ITemplateField } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { QlikService } from '@app/core/services/qlik-service';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-qlik-analytics-type-selector',
  standalone: true,
  imports: [MatButtonToggleGroup, MatButtonToggle],
  templateUrl: './qlik-analytics-type-selector.component.html',
  styleUrl: './qlik-analytics-type-selector.component.scss',
})
export class QlikAnalyticsTypeSelectorComponent implements OnInit, OnDestroy {
  @Input() id: string | null | undefined;
  @Input() templateField!: ITemplateField | undefined;
  filter: string = '';
  organizations: IOrganizationLite[] = [];
  networks: INetwork[] = [];
  preSelectedOrganizationId: string = '';
  preSelectedNetworkId: string = '';
  filterType: string | undefined;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private qlikService: QlikService
  ) {}

  ngOnInit(): void {
    if (this.templateField?.label4 === 'organization') {
      this.filterType = 'Organization Id';
      this.dataService
        .getMyQlikOrganizations()
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((data: IOrganizationLite[]) => {
          if (data && data.length > 0) {
            // Create array of organization IDs for filter options
            const orgIds = data.map(org => `${org.id}`);

            if (data.length === 1) {
              // Update the QlikService signal with the single organization
              this.qlikService.updateQlikSelection(this.filterType, `${data[0].id}`, orgIds, true);
            } else {
              this.organizations = data;
              this.preSelectedOrganizationId = data[0].id;
              // Update the QlikService signal with the first organization and all options
              this.qlikService.updateQlikSelection(this.filterType, `${data[0].id}`, orgIds, true);
            }
          }
        });
    } else if (this.templateField?.label4 === 'network') {
      this.filterType = 'Network Id';
      this.dataService
        .getMyNetworks()
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((data: INetwork[]) => {
          if (data && data.length > 0) {
            // Create array of network IDs for filter options
            const networkIds = data.map(network => `${network.id}`);

            if (data.length === 1) {
              // Update the QlikService signal with the single network
              this.qlikService.updateQlikSelection(this.filterType, `${data[0].id}`, networkIds, true);
            } else {
              this.networks = data;
              this.preSelectedNetworkId = data[0].id;
              // Update the QlikService signal with the first network and all options
              this.qlikService.updateQlikSelection(this.filterType, `${data[0].id}`, networkIds, true);
            }
          }
        });
    } else {
      this.filterType = 'Instance Id';
      this.filter = `=[Instance Id]=${this.id}`;
      if (this.id) {
        // Update the QlikService signal with the instance ID
        this.qlikService.updateQlikSelection(this.filterType, `${this.id}`, [], true);
      }
    }
  }

  updateOrgSelection(data: MatButtonToggleChange) {
    setTimeout(() => {
      // Get all organization IDs for filter options
      const orgIds = this.organizations.map(org => `${org.id}`);
      // Update the QlikService signal
      this.qlikService.updateQlikSelection(this.filterType, `${data.value}`, orgIds);
    }, 50);
  }

  updateNetworkSelection(data: MatButtonToggleChange) {
    setTimeout(() => {
      // Get all network IDs for filter options
      const networkIds = this.networks.map(network => `${network.id}`);
      // Update the QlikService signal
      this.qlikService.updateQlikSelection(this.filterType, `${data.value}`, networkIds);
    }, 50);
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
