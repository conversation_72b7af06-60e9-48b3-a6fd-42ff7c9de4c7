import { Directive, Input, OnDestroy } from '@angular/core';
import { IAsset, IInstance, IInstanceSectionComponent } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { ParseService } from '@app/core/services/parse-service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { environment } from '@env/environment';
import { Subject, takeUntil } from 'rxjs';

@Directive()
export class HeaderImageBaseComponent implements OnDestroy {
  @Input() instance: IInstance;
  @Input() instanceSectionComponent: IInstanceSectionComponent;
  @Input() defaultIconAssetId: string | undefined;
  @Input() inheritedPropertyValue: string | null;
  @Input() defaultImageUrl: string | null;

  // icon
  iconUrl: string;
  iconAssetId: string;
  iconAsset: IAsset;
  // cover photo
  coverPhotoUrl: string;
  coverPhotoId: string;
  coverPhotoAsset: IAsset;
  height: number;

  setIconAndCoverUrl$ = new Subject<void>();

  protected derivedComponentType: string = 'base';

  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    public parseService: ParseService,
    private builderService: BuilderService,
    public dataService: DataService,
    public systemPropertiesService: SystemPropertiesService
  ) {}

  get isOrgManager() {
    return this.instance?.feature?.featureType?.name === 'Organization Manager';
  }

  get isOrgProps() {
    return !this.instanceSectionComponent || this.instanceSectionComponent?.component?.templateField?.systemProperty?.property === 'Organization.LogoAssetId' || this.isOrgCoverPhoto;
  }

  get isOrgCoverPhoto() {
    return this.instanceSectionComponent && this.instanceSectionComponent?.component?.templateField?.systemProperty?.property === 'Organization.CoverPhotoAssetId';
  }

  get isListingDetails() {
    return this.instanceSectionComponent && this.instanceSectionComponent?.component?.componentType?.name === 'Listing Details';
  }

  get isIconVideo() {
    return this.builderService.videoContentTypes?.some(x => x === this.iconAsset?.contentType);
  }

  get isCoverVideo() {
    return this.builderService.videoContentTypes?.some(x => x === this.coverPhotoAsset?.contentType);
  }

  setIconAndCoverUrl() {
    this.parseService.orgAssetsUpdated$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if ((this.isOrgProps || this.isListingDetails) && this.isOrgManager) {
        this.setOrgValues();
      }
    });

    // THIS IS THE ORG PAGE
    if ((this.isOrgProps || this.isListingDetails) && this.isOrgManager && this.instanceSectionComponent?.component?.templateField?.isInherit !== false) {
      this.setOrgValues();
    } else if (this.derivedComponentType === 'page-banner' && this.instance.coverMediaAssetId && this.instanceSectionComponent.component.templateField.isInherit !== false) {
      this.iconUrl = `${environment.contentUrl}asset/${this.instance.coverMediaAssetId}/content${this.height ? '?height=' + this.height : ''}`;
      this.iconAssetId = this.instance.coverMediaAssetId;
    } else if (this.iconAssetId && this.iconAssetId !== '') {
      this.iconUrl = `${environment.contentUrl}asset/${this.iconAssetId}/content${this.height ? '?height=' + this.height : ''}`;
      //this.iconAssetId = this.iconAssetId;
    } else if (this.defaultIconAssetId && this.defaultIconAssetId !== '') {
      this.iconUrl = `${environment.contentUrl}asset/${this.defaultIconAssetId}/content${this.height ? '?height=' + this.height : ''}`;
      this.iconAssetId = this.defaultIconAssetId;
    } else if (this.inheritedPropertyValue && this.inheritedPropertyValue !== '') {
      this.iconUrl = `${environment.contentUrl}asset/${this.inheritedPropertyValue}/content${this.height ? '?height=' + this.height : ''}`;
      this.iconAssetId = this.inheritedPropertyValue;
    } else if (this.instanceSectionComponent?.value && this.instanceSectionComponent.value !== '') {
      this.iconUrl = `${environment.contentUrl}asset/${this.instanceSectionComponent?.value}/content${this.height ? '?height=' + this.height : ''}`;
      this.iconAssetId = this.instanceSectionComponent?.value;
    } else if (this.defaultImageUrl && !this.defaultImageUrl.includes('http')) {
      this.iconUrl = `${environment.contentUrl}asset/${this.defaultImageUrl}/content${this.height ? '?height=' + this.height : ''}`;
      this.iconAssetId = this.defaultImageUrl;
    } else if (this.defaultImageUrl && this.defaultImageUrl.includes('http')) {
      this.iconUrl = this.defaultImageUrl;
    } else if (!this.iconAssetId && this.instance?.iconAssetId && this.derivedComponentType == 'base') {
      this.iconUrl = `${environment.contentUrl}asset/${this.instance.iconAssetId}/content${this.height ? '?height=' + this.height : ''}`;
      this.iconAssetId = this.instance.iconAssetId;
    } else if (!this.iconAssetId && this.instance?.feature?.iconAssetId && this.instance?.feature?.iconAssetId != '') {
      this.iconUrl = `${environment.contentUrl}asset/${this.instance?.feature?.iconAssetId}/content${this.height ? '?height=' + this.height : ''}`;
      this.iconAssetId = this.instance.feature.iconAssetId;
    } else {
      this.iconUrl = 'assets/images/no-image.png';
    }

    if (!this.coverPhotoUrl && this.instance?.coverMediaAssetId) {
      this.coverPhotoUrl = `${environment.contentUrl}asset/${this.instance.coverMediaAssetId}/content`;
      this.coverPhotoId = this.instance.coverMediaAssetId;
    } else if (!this.coverPhotoUrl && this.instance?.feature?.coverMediaAssetId) {
      this.coverPhotoUrl = `${environment.contentUrl}asset/${this.instance.feature.coverMediaAssetId}/content`;
      this.coverPhotoId = this.instance.feature.coverMediaAssetId;
    }

    if (this.iconAssetId) {
      this.getIconAssetDetails(this.iconAssetId);
    }

    if (this.coverPhotoId) {
      this.getIconAssetDetails(this.coverPhotoId, false);
    }

    this.updateBackgroundImage();
    this.setIconAndCoverUrl$.next();
  }

  setOrgValues() {
    this.iconUrl = this.parseService.orgIconUrl;
    const iconProperty = this.systemPropertiesService.organizationProperties.find(x => x.key === 'Organization.LogoAssetId');
    this.iconAssetId = iconProperty?.value ?? '';

    this.coverPhotoUrl = this.parseService.orgCoverPhotoUrl;
    const coverPhotoProperty = this.systemPropertiesService.organizationProperties.find(x => x.key === 'Organization.CoverPhotoAssetId');
    this.coverPhotoId = coverPhotoProperty?.value ?? '';

    if (this.isOrgCoverPhoto) {
      this.iconUrl = this.coverPhotoUrl;
      this.iconAssetId = this.coverPhotoId;
    }

    if (this.iconAssetId) {
      this.getIconAssetDetails(this.iconAssetId);
    }

    if (this.coverPhotoId) {
      this.getIconAssetDetails(this.coverPhotoId, false);
    }
  }

  getIconAssetDetails(assetId: string, isIcon = true) {
    if (!assetId || assetId.length === 0 || assetId.indexOf('{"') !== -1) {
      return;
    }

    this.dataService
      .getAssetDetailsById(assetId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(async (data: IAsset) => {
        if (isIcon === true) {
          this.iconAsset = data;
        } else {
          this.coverPhotoAsset = data;
        }

        if (this.isIconVideo || this.isCoverVideo) {
          await new Promise(resolve => setTimeout(resolve, 2000)).then(() => {
            const video = document.getElementById(isIcon === true ? 'autoplay-vid-icon' : 'autoplay-vid-cover') as HTMLVideoElement | null;

            if (video != null) {
              video.muted = true;
              video.play();
            }
          });
        }
      });
  }

  updateBackgroundImage(): void {
    this.preloadImage(
      this.iconUrl,
      () => {
        // Success: Keep original URL
      },
      () => {
        // Error: Fallback to default URL
        this.onBackgroundError();
      }
    );
  }

  preloadImage(url: string, onSuccess: () => void, onError: () => void): void {
    const img = new Image();
    img.src = url;
    img.onload = onSuccess;
    img.onerror = onError;
  }

  onBackgroundError(): void {
    // Fallback to default icon
    if (this.defaultImageUrl && !this.defaultImageUrl.includes('http')) {
      this.iconUrl = `${environment.contentUrl}asset/${this.defaultImageUrl}/content${this.height ? '?height=' + this.height : ''}`;
      this.iconAssetId = this.defaultImageUrl;
    } else if (this.defaultImageUrl && this.defaultImageUrl.includes('http')) {
      this.iconUrl = this.defaultImageUrl;
    } else {
      this.iconUrl = 'assets/images/no-image.png';
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
