import { Component, Input, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { IFeature } from '@app/core/contracts/contract';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { DataService } from '@app/core/services/data-service';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-feature-repository-builder',
    templateUrl: './feature-repository-builder.component.html',
    styleUrls: ['./feature-repository-builder.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class FeatureRepositoryBuilderComponent implements OnInit, OnDestroy {
  @Input() featureId: string;
  componentDestroyed$: Subject<boolean> = new Subject();
  feature$: Observable<IFeature>;
  minimized = true;
  userName: string;
  refresh = new Subject<any>();
  tabDescription = 'viewing';
  isEfManager: boolean | undefined;
  constructor(
    private dataService: DataService,
    private activatedRoute: ActivatedRoute,
    private breadcrumbService: BreadcrumbService,
    private router: Router
  ) {}

  onWindowScroll(e: any) {
    if (!this.minimized && e.detail.scrollTop > 10) this.minimized = true;
  }

  ngOnInit() {
    const userContext = JSON.parse(localStorage.getItem('user_context') as string);
    this.isEfManager = userContext?.isEfManager;
    this.activatedRoute.params.pipe(takeUntil(this.componentDestroyed$)).subscribe(params => {
      this.featureId = params['id'];
      this.feature$ = this.dataService.getFeatureById(this.featureId).pipe(
        map(x => {
          this.breadcrumbService.addBreadCrumb(x.id, this.router.url, x.title, null, x.featureType.name);
          return x;
        })
      );
    });
  }

  updateFeature(isUpdatedFeature: IFeature) {
    if (isUpdatedFeature) {
      this.feature$ = new Observable<IFeature>(x => x.next(isUpdatedFeature));
    }
  }

  tabSelected($event: MatTabChangeEvent) {
    switch ($event.index) {
      case 0:
      case 3: {
        this.tabDescription = 'viewing';
        break;
      }
      case 1:
      case 2: {
        this.tabDescription = 'updating';
        break;
      }
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
