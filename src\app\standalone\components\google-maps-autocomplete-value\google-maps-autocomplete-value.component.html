<div class="parent-container">
  <h1>{{ templateField?.label ?? 'Location' }}</h1>
  <div id="mapContainer" [ngClass]="{ 'hide-map': mapSet !== true || templateField?.showMap !== true }"></div>
  <div class="inner-container">
    <div class="icon-col">
      <mat-icon svgIcon="location_icon"></mat-icon>
    </div>
    <div class="address-content-col">
      @if (featureType === 'User Manager') {
        <app-text-value
          defaultValue="{{ addressLine1 ?? 'Address' }}, {{ state ?? 'Province / State' }}, {{ country ?? 'Country' }},
          {{ zip ?? 'Postal/ Zip Code' }}">
        </app-text-value>
      }
      @if (featureType !== 'User Manager') {
        <app-text-value
          defaultValue="{{ addressLine1 ?? 'Address' }}, {{ city ?? 'City' }}, {{ state ?? 'Province / State' }},
          {{ country ?? 'Country' }}, {{ zip ?? 'Postal / ZipCode' }}">
        </app-text-value>
      }
    </div>
  </div>
</div>
