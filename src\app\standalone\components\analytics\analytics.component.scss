.parent-container {
  margin-bottom: 40px;

  .image-container {
    height: fit-content !important;
    background-size: cover !important;
    padding: 50px 16px 9px 25px;
    background: linear-gradient(to bottom, rgba(30, 30, 30, 0) 0%, rgba(30, 30, 30, 0.7) 45%, rgba(30, 30, 30, 0.95) 80%, rgb(30, 30, 30) 99%), var(--background-image) no-repeat;
    border-radius: 10px 10px 0px 0px !important;
  }

  .row {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 15px;
  }

  .label {
    height: 16px;
    color: #cccccc;
    font-family: 'Roboto';
    font-weight: 300;
    line-height: 1.1;
    text-align: left;

    .icons-and-text-container {
      display: flex;

      .icon {
        padding-right: 5px;
      }

      .name {
        margin: 5px 0px;
      }
    }
  }

  @media (min-width: 959px) {
    .image-container {
      min-height: 120px !important;
    }

    .label {
      font-size: 14px;
    }
    .icon-col {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0px 12px 12px 0px;
      width: 100%;
      max-width: 100px;
      position: relative;

      .icon {
        object-fit: contain;
        border-radius: 2px;
      }
    }

    .header-container {
      font-family: 'Roboto';
      margin-top: auto;
      padding-bottom: 10px;
      letter-spacing: 0.3px;
      text-align: left;

      .heading {
        color: #f7f6f6;
        font-weight: 900;
        font-size: 22px;
        line-height: 1.1;
      }

      .sub-heading {
        color: #aaaaaa;
        font-weight: 400;
        font-size: 18px;
        line-height: 1.3;
      }
    }

    .card {
      font-size: 33px;
    }

    .amount {
      font-size: 37px;
    }

    .card-content {
      .inner-panel {
        .heading {
          font-size: 20px;
        }

        .description {
          font-size: 14px;
        }
      }
    }
  }
  @media (max-width: 960px) {
    .image-container {
      min-height: 100px !important;
    }

    .label {
      font-size: 12px;
    }

    .icon-col {
      margin: auto;
      width: 100%;
      padding-right: 5px;
      max-width: 80px;
      padding-bottom: 5px;

      .icon {
        border-radius: 5px;
        background-size: contain;
      }
    }

    .header-container {
      margin-top: auto;
      padding-bottom: 10px;
      font-family: 'Roboto';
      letter-spacing: 0.3px;
      text-align: left;

      .heading {
        color: #f7f6f6;
        font-weight: 900;
        font-size: 20px;
        line-height: 1.1;
        text-align: left;
      }

      .sub-heading {
        color: #aaaaaa;
        font-weight: 400;
        font-size: 12px;
        line-height: 1.3;
      }
    }
    .card {
      font-size: 23px;
    }

    .amount {
      font-size: 27px;
    }

    .card-content {
      .inner-panel {
        .heading {
          font-size: 15px;
        }

        .description {
          font-size: 12px;
        }
      }
    }
  }

  .card {
    max-width: 1000px;
    padding: 15px 16px 9px 25px;
    background: #333333;
    color: #ffffff;
    border-color: #656565;
    border-width: 1.5px;
    border-style: solid;
    border-radius: 7px 7px 7px 7px;
    font-family: 'Roboto';
    font-weight: 900;
    line-height: 1.3;
    text-align: left;
  }

  .amount-container {
    display: flex;
    margin-bottom: 10px;

    .amounts {
      width: 100%;
      max-width: 200px;
    }
  }

  .black-underline {
    margin-bottom: 15px;
    width: 100%;
    height: 2px;
    background: #232323;
  }

  .underline {
    width: 70px;
    height: 1px;
    background: #656565;
    border-radius: 2px 2px 2px 2px;
  }

  .amount {
    color: #ffffff;
    font-family: 'Roboto';
    font-weight: 900;
    line-height: 1;
    letter-spacing: 0.3px;
    text-align: left;
  }

  .card-content {
    height: 100%;
    border-radius: 8px;
    padding: 0;

    .inner-panel {
      .heading {
        font-weight: bold;
        color: white;
        margin-top: 10px;
      }

      .description {
        color: #aaaaaa;
        font-family: 'Roboto';
        font-weight: 300;
        font-style: italic;
        line-height: 1.3;
        letter-spacing: 0.5px;
        text-align: left;
        padding: 10px 0px;
      }
    }
  }
}
