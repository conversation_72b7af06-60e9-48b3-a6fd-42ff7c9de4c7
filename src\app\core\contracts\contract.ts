import { SafeResourceUrl } from '@angular/platform-browser';
import { InstanceInterestTypes } from '../enums/instance-interest-types.enum';

export interface IRowFeatureIn {
  instanceId: string;
  rowId: string;
  feature: IFeatureIn;
}

export interface ILoginOIDCIn {
  token?: string;
}
export interface ILoginIn {
  username?: string;
  password?: string;
  platformInformation?: string;
}
export interface ILogin {
  token?: string;
  refreshToken?: string; // uuid
  requiresPasswordChange?: boolean;
}

export interface IRowType {
  id: string;
  name: string;
  typeBw: number;
}

export interface IRowLite {
  id: string;
  name: string;
}

export interface IInstanceRowLite {
  id: string;
  sortOrder: number;
  hidden?: boolean;
}

export interface IRowModifiable {
  row: IRow;
  editBlocked: boolean;
}

export interface IRow {
  id: string;
  title?: string;
  titleHtmltag?: string;
  description?: string;
  descriptionHtmltag?: string;
  rowTypeId: string;
  alignment?: string;
  courouselFlag: boolean;
  thumbnailTypeId: string;
  rowOrder: number;
  permissionBw: number;
  rowType: IRowType;
  thumbnailType: IThumbnailType;
  displayType: IDisplayType;
  filter: IRowFilter;
  show?: string;
  instanceDisplay?: string;
  contentSentence?: string;
  note?: string;
  componentId: string;
  limitTo: number;
  selectedBy: string;
  liveUpdating: boolean;
  limit?: boolean;
  limitRowCount?: number;
  limitInterval?: string;
  dueDate?: Date;
  status?: string;
  managerIndex?: number;
  allowGuestAccess?: boolean;
  hidden?: boolean;
}

export interface IRowIn {
  id?: string;
  title: string | null;
  titleHtmltag: string | null;
  description: string | null;
  descriptionHtmltag: string | null;
  rowTypeId: string | null;
  alignment: string | null;
  courouselFlag: boolean | null;
  thumbnailTypeId: string | null;
  rowOrder: number | null;
  permissionBw: number | null;
  pageId: string | null;
  componentId: string | null;
  dueDate: number | null;
  status: string | null;
  managerIndex?: number;
  allowGuestAccess?: number;
}

export interface IRowType {
  id: string;
  name: string;
}

export interface IThumbnailType {
  id: string;
  name: string;
}

export interface IRowContent {
  rowComplete?: boolean;
  totalContent?: number;
  totalComplete?: number;
  minValue?: number;
  content: IContent[];
  isAssignmentRow?: boolean;
}

export interface IContent {
  id: string;
  featureName?: string;
  featureDescriptors?: string;
  featureType?: string;
  instanceDescriptors?: string;
  instanceName?: string;
  featureDescription?: string;
  instanceDescription?: string;
  displayObjectCount: boolean;
  property?: string;
  property2?: string;
  coverAssetId?: string;
  action?: string;
  actionUrl?: string;
  actionBW: number;
  tagName?: string;
  title?: string;
  contentOrder?: number;
  entityType: number;
  iconAssetId?: string;
  externalLogoUrl?: string;
  externalCoverUrl?: string;
  totalBlockItems: number;
  joinCode: string | null;
  canExport: boolean | null;
  organizationName?: string;
  status?: string;
  lastModifiedDate?: number;
  progress?: number | null;
  isEnrolled?: boolean | null;
  slug: string | null;
  defaultInstanceSlug: string | null;
  achievementCompletion?: IAchievementCompletion;
  isGraded: boolean | null;
  containsGrading: boolean | null;
  sameUrlNavigation: boolean | null;
  instanceInterest?: InstanceInterestTypes;
  organizationInterest?: number;
  isInstanceOwner?: boolean;
}

export interface FavouriteClicked {
  instanceId: string;
  instanceInterestType: InstanceInterestTypes;
}

export interface IAchievementCompletion {
  id: string;
  issuedBy: string;
  issuedDate?: number;
  featureName: string;
  badgeName: string;
  badgeDescription: string;
  learningOutcomeDescription: string;
  badgeOverlayImage: string;
  badgeIconImage: string;
  type: string;
}

export interface IContentOrder {
  id: string;
  order: number;
}

export interface IRowSettingsIn {
  title: string | null;
  description: string | null;
  dueDate: number | null;
  status: string | null;
  managerIndex?: number;
  allowGuestAccess?: boolean;
  instanceDisplay?: string;
}

export interface IRowContentIn {
  contentItemsOrder: IContentOrder[];
}

export interface IRowStylingIn {
  titleStyle?: string;
  descriptionStyle?: string;
  alignment?: string;
  thumbnailTypeId?: string;
  displayType?: boolean;
}

export interface IRowTaggingIn {
  show?: string;
  contentSentence?: string;
  note?: string;
  instanceDisplay?: string;
  limitTo?: number;
  liveUpdating: boolean;
  selectedBy?: string;
  limit?: boolean;
  limitInterval?: string;
  limitRowCount?: number;
}

export interface IRepositoryBuilderDashboard {
  id: string;
  objectName: string;
  status: string;
  internalName: string;
  coverMedia: string;
  block: string;
  shortDescription: string;
  isDefault?: boolean;
}

export interface IPagingInfo {
  pageNo: number;
  pageSize: number;
  pageCount: number;
  totalRecordCount: number;
}
export interface IResultSetMetaData {
  minDate?: number;
  maxDate?: number;
  filterCollection?: IKeyValue<FilterType, ILookupList>[];
}
export interface IKeyValue<K, V> {
  key: K;
  value: V;
}

export interface IPagedResult<T> {
  paging: IPagingInfo;
  metaData?: IResultSetMetaData;
  data: T;
}
export enum FilterType {}

export enum EntityType {}

export interface ILookupList {
  entityType: EntityType;
  lookupItems: ICommonLookup[];
}
export interface ICommonLookup {
  key: string;
  value: string;
  typeId?: number;
  codeId?: number;
  field1?: string;
}

export interface ITab {
  id: string;
  name: string;
  templateId: string;
  tagTreeId: string;
  tags: string[];
}

export interface IFeatureTab {
  id: string;
  featureId: string;
  tabId: string;
  sortOrder: number;
  showTab: boolean;
  showForEfAdmin: boolean;
  showForGuest: boolean;
  isDefaultInstanceTab: boolean;
  primaryFilter: IComponent | null;
  secondaryFilter: IComponent | null;
  typeId: string | null;
  buttonText: string | null;
  tab: ITab;
  type: IFeatureTabType | null;
  featureTabButtons: IFeatureTabButton[];
  featureTabActions: IAction[];
  featureTabEditActions: IAction[];
  featureTabRowTypes: IFeatureTabRowType[];
  featureTabPersonas: ITag[];
}

export interface IFeatureTabType {
  id: string;
  name: string | null;
}

export interface IFeatureTabButtonLinkType {
  id: string;
  name: string | null;
}

export interface IAction {
  id: string;
  name: string;
  actionBw: number;
}

export interface IFeatureTabButton {
  id: string;
  buttonText: string | null;
  referenceId: string | null;
  featureTabId: string | null;
  buttonLinkType: IFeatureTabButtonLinkType | null;
  featureTabButtonActions: IFeatureTabButtonAction[];
}

export interface IFeatureTabButtonAction {
  id: string;
  featureTabButtonId: string | null;
  actionId: string | null;
}

export interface IFeatureTabActionIn {
  id: string | null;
  featureTabId: string | null;
  actionId: string | null;
}

export interface IFeatureTabButtonActionIn {
  id: string | null;
  featureTabButtonId: string | null;
  actionId: string | null;
}

export interface IFeatureTabButtonIn {
  id: string | null;
  buttonText: string | null;
  buttonLinkId: string | null;
  referenceId: string | null;
  featureTabId: string | null;
  featureTabButtonActions: IFeatureTabButtonActionIn[] | null;
}

export interface IFeatureTabIn {
  id: string;
  showTab: boolean;
  showForEfAdmin: boolean;
  showForGuest: boolean;
  sortOrder: number;
  isDefaultInstanceTab: boolean;
  featureId: string;
  primaryFilter: IComponentIn | null;
  secondaryFilter: IComponentIn | null;
  typeId: string | null;
  buttonText: string | null;
  featureTabRowTypeIds: string[] | null;
  featureTabActions: IFeatureTabActionIn[] | null;
  featureTabEditActions: IFeatureTabActionIn[] | null;
  featureTabButtons: IFeatureTabButtonIn[] | null;
  featureTabPersonas: string[] | null;
}

export interface IFeatureTabRowType {
  id: string;
  rowType: IRowType;
}

export interface IHover {
  title: string;
  description?: string;
}

export interface IFeature {
  id: string;
  code?: string;
  title: string;
  description?: string;
  descriptors?: string;
  instanceDescriptors?: string;
  displayObjectCount: boolean;
  featureSlug: string;
  iconAssetId?: string;
  coverMediaAssetId?: string;
  textureAssetId?: string;
  journeyStageId?: string;
  roleObjectiveId?: string;
  targetAudienceId?: string;
  continuumTimelineId?: string;
  featureCategoryId?: string;
  featureType: IFeatureType;
  isActive: boolean;
  featureTabs: IFeatureTab[];
  featureCommunication: IFeatureCommunication;
  isFullWidth?: boolean;
}
export interface IFeatureType {
  id: string;
  name: string;
  systemPropertyType: ISystemPropertyType;
  status?: string;
}

export interface InstanceBase {
  title: string;
  description?: string;
  coverAssetId?: string;
  iconAssetId?: string;
  actionUrl?: string;
  organizationId?: string;
  status?: string;
  isDefault?: boolean;
  coverMediaAssetId?: string;
  isOwner?: boolean;
  hasScormAccess?: boolean;
  slug?: string | null;
  dueDate?: number;
}

export interface IMyInstanceResult {
  instances: IInstance[];
  lastModified?: IInstance[];
}

export interface IInstance extends InstanceBase {
  id: string;
  feature: IFeature;
  coverMediaAsset?: IAsset;
  joinCode?: string;
  isJoinCodeInstance?: boolean;
  earningCriteria?: IEarningCriteria;
  isEnrolled?: boolean;
  isRetake?: boolean;
  isSubmitted?: boolean;
  isGraded?: boolean;
  feedback?: string;
  grade?: number;
  totalMarks?: number;
  isComplete?: boolean;
  isExternallySynced?: boolean | null;
  educatorId?: string;
  instanceInterest?: InstanceInterestTypes;
}

export interface IInstanceAchievement {
  id: string;
  achievementInstance: IInstance;
  isEnabled: boolean | null;
  isRequired: boolean | null;
}

export interface IInstanceAchievementIn {
  id: string | null;
  achievementInstanceId: string | null;
  instanceId: string | null;
  isEnabled: boolean | null;
  isRequired: boolean | null;
}

export interface IInstanceIn extends InstanceBase {
  id?: string;
  featureId: string;
  coverMediaAssetId?: string;
  educatorId?: string;
  gradeId?: string;
}

export interface IComponentType {
  id: string;
  name: string;
  isDefault: boolean;
  isInstance: boolean;
  isVisibleInRepositoryBuilder: boolean;
  assetId?: string;
  parentTypeName?: string;
}

export interface ITemplateField {
  id: string;
  componentId: string;
  label: string;
  placeHolderText: string;
  helpTitle: string;
  helpDescription: string;
  toolTip: string;
  buttonText: string;
  defaultImageUrl: string;
  defaultText: string;
  tagTreeId: string;
  containerName: string;
  systemProperty?: ISystemProperty;
  isRequiredField?: boolean;
  isPreviewField?: boolean;
  isBuilderEnabled?: boolean;
  isInherit?: boolean;
  isHoverField?: boolean;
  isViewField?: boolean;
  isVariable?: boolean;
  isVisibleRepository?: boolean;
  dropDownValues: ITag[];
  isFilter?: boolean;
  limitTo?: number;
  isSrcDevice?: boolean;
  isSrcRepository?: boolean;
  isSrcEmbedCode?: boolean;
  isTag?: boolean;
  fileTypeBw?: number;
  minFileSize?: number;
  maxFileSize?: number;
  upgradeMessage?: string;
  parentIdSystemPropertyLink?: ISystemProperty;
  isParentSystemPropertyLinkField?: boolean;
  dropDownLinkType: IDropDownLinkType | undefined;
  openExternal?: boolean;
  isBlockRequired?: boolean;
  isAuthorRequired?: boolean;
  percentageToComplete?: number;
  communicationBlock?: ICommunicationBlock;
  colspan?: number;
  colNumber?: number;
  iconAssetId?: string;
  backgroundAssetId?: string;
  label1?: string;
  placeHolder1?: string;
  default1?: string;
  caption1?: string;
  description1?: string;
  label2?: string;
  placeHolder2?: string;
  default2?: string;
  caption2?: string;
  description2?: string;
  label3?: string;
  placeHolder3?: string;
  default3?: string;
  caption3?: string;
  description3?: string;
  label4?: string;
  placeHolder4?: string;
  default4?: string;
  caption4?: string;
  description4?: string;
  heightPx?: number;
  showGradient?: boolean;
  customGradient?: string;
  moveToBack?: boolean;
  aspectRatio: string | null;
  isCoverImage?: boolean;
  isRetake?: boolean;
  isButtonBelowText?: boolean;
  showMap?: boolean;
  stylingDirection?: string;
  darkText?: boolean;
  headingStyle?: string;
  useMaxWidth?: boolean;
  backgroundPosition?: string;
  backgroundSize?: string;
}

export interface ISystemPropertyType {
  id: string;
  title: string;
  typeBw: number;
}

export interface ISystemProperty {
  id: string;
  typeId: string;
  property: string;
  name: string;
  value?: string;
  type: ISystemPropertyType;
}

export interface IComponent {
  id: string;
  builderSortOrder: number;
  componentType: IComponentType;
  rowId: string;
  templateField: ITemplateField;
  builderRowNumber?: number;
  hoverSortOrder?: number;
  instanceSortOrder?: number;
  completed?: boolean;
  grade?: string;
  runtime?: string;
  instanceTags: IInstanceTag[];
  question: IQuestion;
  isLocked: boolean | null;
  parentComponentId: string | null;
}

export interface IComponentAchievement {
  id: string;
  achievementInstance: IInstance;
  isEnabled: boolean | null;
  isRequired: boolean | null;
}

export interface IComponentAchievementIn {
  id: string | null;
  achievementInstanceId: string | null;
  componentId: string | null;
  isEnabled: boolean | null;
  isRequired: boolean | null;
}

export interface IComponentEditIn {
  id: string;
  builderRowNumber?: number;
  hoverSortOrder?: number;
  instanceSortOrder?: number;
}

export interface IComponentIn {
  componentTypeId: string | null;
  componentTypeBw: number | null;
  sectionId: string | null;
  templateFieldId?: string;
  rowId?: string;
  componentId?: string;
  templateField: ITemplateFieldIn | null;
  builderRowNumber: number | null;
  isLocked: boolean | null;
  parentComponentId: string | null;
}

export interface IDropDownLinkType {
  id: string;
  title: string;
}

export interface IComponentSortOrderIn {
  id: string;
  sectionId: string;
  builderSortOrder: number;
}

export interface ISection {
  id: string;
  templateId: string;
  title: string;
  hideBackground: boolean;
  showOnInstanceViewer: boolean;
  showTitleOnPlayer: boolean;
  showOnPlayerSidepanel: boolean;
  showDescOnPlayer: boolean;
  description: string;
  layoutType: number;
  sortOrder: number;
  defaultlRowTypeId: string;
  defaultAligment: string;
  defaultCoureselFlag?: boolean;
  defaultThumbnailTypeId: string;
  defaultDisplayTypeId: string;
  components: IComponent[];
  typeId?: string;
  typeBw?: number;
  caption?: string;
  isEditable: boolean;
  isCompletable?: boolean;
  actions: IAction[];
  backgroundColor: string | null;
  isContinuousFeedback?: boolean;
  webMaxWidth?: string;
  mobileMaxWidth?: string;
}

export interface ISectionIn {
  id: string;
  title: string;
  caption: string;
  description: string;
  hideBackground: boolean;
  showTitleOnPlayer: boolean;
  showDescOnPlayer: boolean;
  sortOrder: number;
  typeId: string;
  isEditable: boolean;
  isCompletable?: boolean;
  showOnInstanceViewer?: boolean;
  showOnPlayerSidepanel?: boolean;
  sectionActions?: ISectionActionIn[];
  backgroundColor: string | null;
  maxSectionWidthWeb?: number;
  maxSectionWidthMobile?: number;
}

export interface ISectionActionIn {
  id: string | null;
  sectionId: string | null;
  actionId: string | null;
}

export interface IInstanceSectionIn {
  id?: string;
  instanceId: string;
  sortOrder: number;
  templateId: string;
  title: string;
  isHidden: boolean;
  backgroundColor: string | null;
  masterSectionId: string | null;
}

export interface IInstanceSectionLite {
  id: string;
  instanceId: string;
}

export interface ITemplate {
  id: string;
  sections: ISection[];
}

export interface IInstanceTemplate {
  id: string;
  instanceSections: IInstanceSection[];
}

export interface IInstanceSectionsAndComponentsResult {
  instanceSections: IInstanceSection[];
  status: string;
  containsGrading: boolean;
  isGraded: boolean;
}

export interface IInstanceSection {
  id: string;
  sortOrder: number;
  section: ISection;
  instanceSectionComponents: IInstanceSectionComponent[];
  title: string;
  complete?: boolean;
  containsGrading?: boolean;
  gradingComplete?: boolean;
  active?: boolean;
  show?: boolean;
  isHidden?: boolean;
  backgroundColor: string | null;
  masterSectionId: string | null;
}

export interface IInstanceSectionComponent {
  id: string;
  sortOrder: number;
  value: string;
  component: IComponent;
  completed?: boolean;
  instanceSectionId: string;
  instanceSectionComponentRows: IInstanceSectionComponentRows[];
}

export interface IInstanceSectionComponentRows {
  id: string;
  instanceSectionComponentId: string;
  rowId: string;
  sortOrder: number;
  hidden: boolean;
  ungraded: boolean;
}

export interface IInstanceSectionComponentIn {
  id: string | null;
  sortOrder: number;
  value: string;
  instanceSectionId: string;
  builderRowNumber: number;
  colNumber: number;
}
export interface IInstanceSectionComponentValue {
  value: string;
}
export interface IDisplayType {
  id: string;
  name: string;
}

export interface IFileOut {
  asset: FormData;
  type: string;
}

export interface IAsset {
  id: string;
  name: string;
  contentType: string;
  size: number;
  createdDate: string;
  modifiedDate: string;
  containerName: string;
  streamingLocator: string;
  embedCode: string;
  urlUpload: string;
  mediaUploadType: string;
}

export interface IAssetCaptionLanguages {
  language: string;
  languageCode: string;
  vttUrl: null | string | SafeResourceUrl;
}

export interface IAssetIn {
  id?: string;
  name?: string;
  contentType?: string;
  size?: number;
  createdDate?: number;
  containerName?: string;
  streamingLocator?: string;
  embedCode?: string;
  urlUpload?: string;
  mediaUploadType: string;
}

export interface IFeatureIn {
  title: string;
  code: string;
  description: string;
  descriptors: string;
  instanceDescriptors: string;
  featureSlug: string;
  iconAssetId: string;
  coverMediaAssetId: string;
  textureAssetId: string;
  journeyStageId: string;
  roleObjectiveId: string;
  targetAudienceId: string;
  featureTypeId: string;
  continuumTimelineId: string;
  featureCategoryId: string;
  isFullWidth: boolean;
}

export interface IFeatureCommunication {
  id: string;
  authorInstanceCompletion: boolean;
  authorInstanceFeedback: boolean;
  authorInstanceLive: boolean;
  userInstanceCompletion: boolean;
  userAchievement: boolean;
}

export interface IFeatureCommunicationIn {
  authorInstanceCompletion: boolean;
  authorInstanceFeedback: boolean;
  authorInstanceLive: boolean;
  userInstanceCompletion: boolean;
  userAchievement: boolean;
}

export interface ITemplateFieldIn {
  id?: string;
  componentId: string;
  label: string;
  placeHolderText: string;
  helpTitle: string;
  helpDescription: string;
  toolTip: string;
  buttonText: string;
  defaultImageUrl: string;
  defaultText: string;
  tagTreeId?: string;
  systemPropertyId?: string;
  isRequiredField?: boolean;
  isPreviewField?: boolean;
  isBuilderEnabled?: boolean;
  isInherit?: boolean;
  isViewField?: boolean;
  isVariable?: boolean;
  dropDownValues: string[];
  isFilter?: boolean;
  limitTo?: number;
  fileTypeBw?: number;
  minFileSize?: number;
  maxFileSize?: number;
  parentIdSystemPropertyLink?: string;
  isParentSystemPropertyLinkField?: boolean;
  dropDownLinkTypeId?: string;
  communicationBlockId?: string;
  colspan?: number;
  colNumber?: number;
  label1?: string;
  placeHolder1?: string;
  default1?: string;
  caption1?: string;
  description1?: string;
  label2?: string;
  placeHolder2?: string;
  default2?: string;
  caption2?: string;
  description2?: string;
  label3?: string;
  placeHolder3?: string;
  default3?: string;
  caption3?: string;
  description3?: string;
  label4?: string;
  placeHolder4?: string;
  default4?: string;
  caption4?: string;
  description4?: string;
  heightPx?: number;
  showGradient?: boolean;
  customGradient?: string;
  moveToBack?: boolean;
  aspectRatio: string | null;
  isCoverImage?: boolean;
  isRetake?: boolean;
  isButtonBelowText?: boolean;
  stylingDirection?: string;
  darkText?: boolean;
  headingStyle?: string;
  useMaxWidth?: boolean;
}

export interface IRowFilterCriteria {
  field?: string;
  condition?: string;
  value?: string;
  operator?: string;
  sortOrder: number;
  delete: boolean;
}

export interface IRowFiltersBlocks {
  criteria: IRowFilterCriteria[];
  operator?: string;
  sortOrder: number;
}

export interface IRowFilter {
  filtersBlocks: IRowFiltersBlocks[];
}

export interface ITag {
  id: string;
  parentId?: string;
  name: string;
  description?: string;
  assetId?: string;
  treeLevel: number;
  inverseParent?: ITag[];
  parent?: ITag;
  hasInstanceTags: boolean;
  hasUserTags: boolean;
  hasRowTags: boolean;
  asset?: IAsset;
  hasOrganizationTags: boolean;
  tagAncestors?: ITagAncestor[];
  tagRiasecDetails?: ITagRiasecDetail;
}

export interface ITagIn {
  id?: string;
  parentId?: string;
  name?: string;
  description?: string;
  assetId?: string;
  treeLevel?: number;
}

export interface IRowTagIn {
  rowId?: string;
  tagId?: string;
}

export interface ITagAncestor {
  id: string;
  name: string;
}

export interface ITagRiasecDetail {
  id?: string;
  riasecCode?: string;
  onetSocCode?: string;
  implications?: string;
  realistic?: number;
  investigative?: number;
  artistic?: number;
  social?: number;
  enterprising?: number;
  conventional?: number;
}

export interface IInstanceTagIn {
  tagId: string;
  instanceId: string;
  componentId: string;
}

export interface IInstanceTag {
  id: string;
  tagId: string;
  instanceId: string;
  tag: ITag;
}

export interface IOrganization {
  id: string;
  name: string;
  type: string;
  address?: string;
  country?: string;
  province?: string;
  city?: string;
  postalCode?: string;
  designation?: string;
  website?: string;
  logoAssetId?: string;
  status?: string;
  source?: string;
  createdDate: number;
  modifiedDate?: number;
  modifiedBy: string;
  joinCode?: string;
  addressLine1?: string;
  addressLine2?: string;
  isJoinCodeOrganization?: boolean;
  orgUserRoleName?: string;
  panelState?: boolean;
  claimUser?: IOrganizationUser;
  organizationProducts: IUserOrganizationProduct[];
}

export interface IOrganizationIn {
  name: string;
}

export interface ICommunicationIn {
  name: string;
}

export interface INetworkIn {
  name: string;
}

export interface INetwork {
  id: string;
  name: string;
  organizationId: string;
}

export interface INetworkAnalytics {
  id: string;
  networkName: string;
  orgCount: number;
}

export interface INetworkOrganization {
  organizationId: string;
  name: string;
}

export interface IUserRole {
  id: string;
  name: string;
  bitwise: number;
}

export interface IUserTag {
  id: string;
  userId: string;
  tagId: string;
  type: string;
  tag?: ITag;
}

export interface IOrganizationSsoAuthIn {
  id?: string;
  organizationId: string;
  externalSystem: string;
  externalId?: string;
  externalAuthId?: string;
  externalAuthSecret?: string;
  externalUrl: string;
  syncPeople?: boolean;
  syncClasses?: boolean;
  syncCourses?: boolean;
  organizationSsoRoleMapping?: IOrganizationSsoRoleMappingIn[];
}

export interface IOrganizationSsoRoleMappingIn {
  id: string;
  roleId: string;
  isRemoved: boolean;
}

export interface IOrganizationSsoAuth {
  id?: string;
  organizationId: string;
  externalSystem: string;
  externalId?: string;
  externalAuthId?: string;
  externalAuthSecret?: string;
  externalUrl: string;
  syncPeople?: boolean;
  syncClasses?: boolean;
  syncCourses?: boolean;
  organizationSsoroleMappings: IOrganizationSsoRoleMapping[];
  organizationSsoauthExternalUrls: IOrganizationSsoauthExternalUrl[];
}

export interface IOrganizationSsoauthExternalUrl {
  id: string;
  organizationSsoauthId: string | null;
  externalUrl: string | null;
  isEnabled: boolean | null;
}

export interface IOrganizationSsoRoleMapping {
  id: string;
  organizationSsoAuthId: string;
  externalRoleName: string;
  roleId?: string | null;
  roleName: string;
}

export interface IPrivacyType {
  id: string;
  name: string;
  description?: string;
  implications?: string;
}

export interface IOrgPrivacyTypeIn {
  privacyTypeId: string;
  organizationId: string;
}

export interface IProductOrganization {
  id: string;
  orgId?: string;
  productId: string;
  orgName?: string;
  productName: string;
  period?: string;
  expiryDate?: number;
  hasProductFeatures: boolean;
  joinCode?: string;
  instructorJoinCode?: string;
  isJoinCodeProduct?: boolean;
  products: IProduct[];
  privacyTypeId?: string;
  orgUserRoleName?: string;
  productJoinCodeSettings?: IProductJoinCodeSetting[];
  networkName?: string;
  isNetworkProduct?: boolean;
}

export interface IProductHistory {
  id: string;
  productName: string;
  period: string;
  expiryDate: number;
  isNetworkProduct?: boolean;
  expiredByNetwork?: boolean;
}

export interface INetworkProductHistory {
  id: string;
  productName: string;
  period: string;
  expiryDate: number;
  availableLicenses: number | null | undefined;
}

export interface INetworkProductLicenseCount {
  availableLicenses?: number;
  usedLicenses: number;
}

export interface IProductRenew {
  id?: string;
  period: string;
  expiryDate: string;
  availableLicenses?: number;
  unlimitedLicenses?: boolean;
}

export interface INetworkProductLicense {
  availableLicenses?: number;
  unlimitedLicenses?: boolean;
}

export interface IProductFeature {
  id: string;
  title: string;
  description: string;
  isActive: boolean;
  hasChanges?: boolean;
  productFeatureRoles?: IRole[];
}

export interface IProductFeatureInstance {
  id: string;
  title: string;
  description: string;
  isActive: boolean;
}

export interface IProductOrgDomain {
  id?: string;
  productOrganizationId: string;
  domainName: string;
  roleId: string;
  index?: number;
  isDeleted: boolean;
}

export interface IProductSetting {
  id: string;
  title: string;
  description: string;
  isActive?: boolean;
}

export interface IRowProductIn {
  rowId: string;
  productId: string;
}

export interface IAction {
  id: string;
  name: string;
  actionBw: number;
}

export interface IRole {
  id: string;
  name: string;
  productFeatRoles: IProductFeatureRole[];
}

export interface IProductFeatureRole {
  id: string;
  roleId: string;
  actionBw: number;
  productFeatureId: string;
}

export interface IProductFeatureIn {
  id: string;
  isActive: boolean;
}

export interface IProductFeatureRoleIn {
  id: string;
  productFeatureId: string;
  actionBw: number;
}

export interface IProductFeatureInstanceIn {
  id: string;
  instanceId: string;
  isActive: boolean;
}

export interface IProductJoinCodeSetting {
  id: string | null;
  productOrganizationId: string | null;
  isApprovalRequired: boolean | null;
  isLearnerApprovalRequired: boolean | null;
  isGuestCode: boolean | null;
  isMonthlyRegenCode: boolean | null;
  isSelectUserNotificationCode: boolean | null;
  typeName: string | null;
  isActive: boolean;
  users: (string | null)[];
}

export interface IProductIn {
  name: string;
  description?: string;
}

export interface IProductTableUser {
  id: string;
  name: string;
  email: string;
  address: string;
  organizationName: string;
  productName: string;
  roleName: string;
  roleId: string;
  userId: string;
  status: string | undefined;
  personas?: any[];
}

export interface IOrganizationTableUser {
  id: string;
  name: string;
  email: string;
  address: string;
  organizationName: string;
  productName: string;
  roleName: string;
  roleId: string;
  userId: string;
  status: string | undefined;
  personas?: any[];
  userActivity?: IUserActivity;
}

export interface NetworkOrganizationTableData {
  id: string;
  orgId: string;
  orgName: string;
  productOrganizationId: string;
  products?: IProductSearch[];
  orgType?: string;
  status?: string | undefined;
  address: string;
}

export interface OrganizationTableUserLite {
  id: string;
  userId: string;
  status: string | null;
}
export interface IUserActivity {
  userId: string;
  totalEngagement?: number;
  lastActivity?: number;
}
export interface IResults {
  id: string;
  name: string;
  progress: string;
  grade: string;
  contentCount?: number;
  completedContentCount?: number;
}

export interface IPeople {
  id: string;
  name: string;
  userId: string;
  email: string;
  roleName: string;
  status: string;
  address: string;
  grade?: number;
  lastActivity?: Date;
  completedCount?: number;
  totalCount?: number;
  progressPercentage?: number;
  isGraded?: boolean;
  groupSize?: number;
}

export interface IInstanceResult {
  instance: IInstance;
  grade: number;
  status: string;
  isGraded: boolean;
  containsGrading: boolean;
}

export interface IUserClaimSearch {
  id: string;
  roleId: string;
  name: string;
  roleName: string;
  selected?: boolean;
}

export interface INetworkOrganizationSearch {
  id: string;
  name: string;
  selected?: boolean;
}

export interface IBadgeSearch {
  id: string;
  name: string;
  selected?: boolean;
  status?: string;
  published: boolean;
}

export interface IOrganizationSearch {
  id: string;
  name: string;
  country: string;
  selected?: boolean;
}

export interface SelectedOrganizationIn {
  id: string;
}

export interface IOrganizationLite {
  id: string;
  name: string;
  type: string;
  city: string;
  country: string;
  actionBw: number;
}

export interface IOrganizationStatusType {
  id: string;
  name: string;
  typeBw: number;
}

export interface IProductOrganizationUser {
  id: string;
  userId: string;
  roleName: string;
}

export interface IOrganizationUser {
  id: string;
  organizationId: string;
  userId: string;
  roleId: string;
  status: string;
  roleName: string;
  firstName?: string;
  email?: string;
  phoneNumber?: string;
  grade?: string;
  accessTypeBw: number;
}

export interface IProductHistory {
  productId?: string;
  invoiceId: number;
  dueDate: number;
  amount: number;
  status: string;
  orgUserRoleName: string;
}

export interface IProduct {
  id: string;
  name: string;
  sku: string;
  price: string;
  description: string;
  hasRowProduct: boolean | null;
}

export interface IUserOrganizationProduct {
  id: string;
  organizationId: string;
  productId: string;
  productName: string;
  userHasProduct: boolean;
  userRoleId?: string;
  panelState?: boolean;
}

export interface IProductSearch {
  id: string;
  name: string;
  // joinCode?: string;
}

export interface IAddNetworkItem {
  id: string;
  name: string;
  selected?: boolean;
}

export interface IBuilderDashboardFields {
  id: string;
  heading: string;
  propertyId: string;
}

export interface IFeatureSearch {
  id: string;
  title: string;
  description: string;
}

export interface IFeatureSearchLite {
  id: string;
  name: string;
}

export interface IParseContentIn {
  instanceId: string | null;
  rowId: string | null;
  content: string;
  systemProperties: ISystemPropertyValue[];
}

export interface BlobFileDetails {
  reference: string;
  absoluteUrl: string;
  contentType: string;
  name: string;
  thumbUrl: string;
}

export interface Attachment {
  createdDate: string;
  userId: string;
  typeId: string;
  blobUrl: string;
  reference: string;
  fileName: string;
  thumbUrl: string;
}

export interface ISystemPropertyValue {
  id: string;
  key: string;
  value: string;
}

export interface IGoogleMapsAddress {
  addressLine1?: string;
  addressLine2?: string;
  suburb?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  country?: string;
  lat?: string;
  long?: string;
}
export interface IAssetRepo {
  id: string;
  name: string;
  source: string;
  category: string;
  runtime: string;
  feedback: string;
  createdDate: number;
}

export interface IQuestionType {
  id: string;
  name: string;
}

export interface IUserAssessmentFeedback {
  id: string;
  feedback: string | null;
  userId: string | null;
}

export interface IUserAssessmentFeedbackIn {
  id: string | null;
  feedback: string | null;
  userAnswers: IUserAnswerIn[];
  assessmentId: string | null;
  userId: string | null;
  isGraded: boolean | null;
}

export interface IQuestionAnswerIn {
  id: string | null;
  questionId: string;
  tagId: string | null;
  title: string | null;
  value: string | null;
  sortOrder: number;
  assetId: string | null;
  isCorrect: boolean | null;
  assetUrl?: string;
}

export interface IQuestionIn {
  id: string | null;
  title: string | null;
  runtime: number | null;
  questionTypeId: string;
  questionText: string | null;
  questionDescription: string | null;
  weighting: number | null;
  required: boolean | null;
  sortOrder: number | null;
  isDynamicOptions: boolean | null;
  isAuthorEditable: boolean | null;
  isRandom: boolean | null;
  isOtherOption: boolean | null;
  isLimitTo: boolean | null;
  limit: number | null;
  isOptionPhoto: boolean | null;
  scaleStart: number | null;
  scaleEnd: number | null;
  scaleLabelOne: string | null;
  scaleLabelTwo: string | null;
  scaleLabelThree: string | null;
  backgroundImageAssetId: string | null;
  questionAnswers: IQuestionAnswerIn[];
  dynamicOptionsParentId: string | null;
  purpose: string | null;
  featureId: string | null;
  answerText: string | null;
  measuredTagId: string | null;
}

export interface IQuestion {
  id: string;
  title: string | null;
  runtime: number | null;
  questionType: IQuestionType;
  questionText: string | null;
  questionDescription: string | null;
  weighting: number | null;
  required: boolean | null;
  sortOrder: number | null;
  isDynamicOptions: boolean | null;
  isAuthorEditable: boolean | null;
  isRandom: boolean | null;
  isOtherOption: boolean | null;
  isLimitTo: boolean | null;
  limit: number | null;
  isOptionPhoto: boolean | null;
  scaleStart: number | null;
  scaleEnd: number | null;
  scaleLabelOne: string | null;
  scaleLabelTwo: string | null;
  scaleLabelThree: string | null;
  backgroundImageAssetId: string | null;
  questionAnswers: IQuestionAnswer[];
  dynamicOptionsParentId: string | null;
  purpose: string | null;
  dynamicOptions: ITag[] | null;
  featureId: string | null;
  userAnswer: IUserAnswer | null;
  correctAnswersExists: boolean | null;
  answerText: string | null;
  measuredTagId: string | null;
}

export interface IQuestionAnswer {
  id: string | null;
  questionId: string;
  tagId: string | null;
  title: string | null;
  value: string | null;
  sortOrder: number;
  assetId: string | null;
  isCorrect: boolean | null;
  isSelected: boolean | null;
  tag: ITag | null;
}
export interface IUserContext {
  userId?: string;
  canManage: boolean;
  isEfManager: boolean;
  userTrackingId: string;
  fullName?: string;
  city?: string;
  region?: string;
  country: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  hasPersonaTags?: boolean;
  isGuest?: boolean;
  qlikUserId?: string;
}

export interface IGuestContext {
  id: string;
  instanceId: string;
  browsingAs: string;
  impersonate: boolean;
  qlikUserId?: string;
}

export interface DashboardGrid {
  columnNames: string[];
  rows: DashboardGridRow[];
}

export interface NetworkOrganization {
  organizationId?: string;
  type: string;
  name: string;
}

export interface DashboardGridRow {
  tableId: string;
  userId?: string;
  name: string;
  columns: DashboardGridColumn[];
}

export interface DashboardGridColumn {
  columnName: string;
  value: string;
  isMultiple: boolean;
  listItems?: any[];
  id?: string;
  component: IComponent;
}

export interface ICustomContentDetail {
  rowId: string;
  contentId?: string | null;
  isDeleted?: boolean;
  title: string;
  description?: string;
  backgroundImageAssetId?: string;
  buttonText?: string;
  buttonUrl?: string;
  contentOrder: number;
  sameUrlNavigation: boolean | null;
}

export interface IUserAnswerBase {
  instanceId: string;
  answerText: string | null;
  questionId: string | null;
  instanceComponentId: string | null;
  marks: number | null;
  isAutoGraded: boolean;
  isEducatorGraded: boolean;
  feedback: string | null;
  answerStatusTypeBw: number | null;
}

export interface IUserAnswer extends IUserAnswerBase {
  id: string;
  userQuestionAnswers: IUserQuestionAnswer[] | null;
  userId: string;
  gradePercentage: number | null;
  isSubmitted: boolean | null;
  answerStatusTypeBw: number | null;
}

export interface IUserAnswerIn extends IUserAnswerBase {
  id: string | null;
  userQuestionAnswers: IUserQuestionAnswerIn[] | null;
}

export interface IUserQuestionAnswerBase {
  userAnswerId: string | null;
  questionAnswerId: string;
  sortOrder: number | null;
  tagId: string | null;
}

export interface IUserQuestionAnswer extends IUserQuestionAnswerBase {
  id: string;
}

export interface IUserQuestionAnswerIn extends IUserQuestionAnswerBase {
  id: string | null;
}
export interface IPasswordResetIn {
  newPassword: string;
  confirmPassword: string;
  secret?: string;
}

export interface IPersonaOption {
  id: string;
  name: string;
  display: string;
  icon: string;
  children: IPersonaChildren[];
}

export interface IPersonaChildren {
  id: string;
  name: string;
  display: string;
}

export interface IRowAchievementIn {
  id: string | null;
  achievementInstanceId: string | null;
  rowId: string | null;
  instanceId: string | null;
}

export interface IRowAchievement {
  id: string | null;
  achievementInstance: IInstance | null;
  rowId: string | null;
  instanceId: string | null;
}

export interface IInstanceUser {
  id: string;
  userId: string;
  roleId: string;
  instanceId: string;
  rowId: string;
  roleName: string;
  status: string;
  name: string | null;
}

export interface IInstanceUserIn {
  id: string;
  userId: string;
  roleId: string | null;
  instanceId: string | null;
  rowId: string | null;
  roleName: string | null;
  status: string;
  name: string | null;
}

export interface INotification {
  id: string;
  hasRead?: boolean;
  dateCreated?: number;
  notificationText: string;
  notificationTypeBW?: number;
  notificationColor: string;
  notificationIcon: string;
  actionKey: string;
  action: string;
  communication: ICommunication;
}

export interface IEngagementIn {
  instanceId: string;
  organizationId: string;
  instanceSectionComponentId?: string;
  engagementType: number;
  nominalValue: number;
  percentageValue: number;
}

export interface IUserInstanceTracking {
  eventDate: Date;
  itemCount: number;
  itemsCompleted: number;
  isComplete: boolean;
}

export interface IScormRosterIn {
  origin: string;
  userId: string;
  externalAuthSecret?: string;
}

export interface IScormAuthIn {
  origin: string;
  authToken: string;
}

export interface IScormUser {
  userId: string;
}

export interface IScormAuth {
  valid: boolean;
}

export interface IScormFile {
  url: string;
  name: string;
  authToken: string;
  packageTitle: string;
  itemTitle: string;
}

export interface IIntanceQuestionLite {
  id: string;
  sortOrder: number;
}

export interface IUser {
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  organizationId?: string;
  notifyNewUser?: boolean;
}

export interface ILinkedUserEmails {
  email: string;
  userId?: string | null;
  userTrackingId?: string | null;
  idServerLocation?: string | null;
}

export interface IUserSetup {
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  password: string;
  organizationId: string;
  productIds?: string[];
  instanceId?: string;
  instanceIds?: string[];
  emailUser: boolean;
  roleId: string;
  notifyNewUser?: boolean;
}

export interface IAssessmentFilterIn {
  instanceId: string | null;
  isEnabled: boolean;
  required: boolean;
  isBuilder: boolean;
  userId: string | null;
}

export interface ICommunication {
  id: string;
  name: string;
  description?: string;
  featureTypeId?: string;
  purposeId?: string;
  createDate: string;
  modifiedDate?: string;
  createdUserId: string;
  modifiedUserId?: string;
  required: boolean;
  categoryId?: string;
  triggerWorkflowId?: string;
  communicationBlocks?: ICommunicationBlock[];
  userCommunicationPreferenceSetting: IUserCommunicationPreferenceSetting;
}

export interface ICommunicationBlock {
  id: string;
  communicationId: string;
  blockType: string;
  title?: string;
  subTitle?: string;
  message?: string;
  actionTypeId?: string;
  notificationTypeId?: string;
  fromAddress: boolean;
  templateId?: string;
}

export interface IUserCommunicationPreferenceSetting {
  id: string | null;
  userCommunicationPreferenceId: string | null;
  communicationId: string | null;
  shouldReceiveEmail: boolean | null;
  shouldReceiveSms: boolean | null;
  shouldReceivePush: boolean | null;
  shouldReceiveBanner: boolean | null;
}

export interface IUserCommunicationPreference {
  id: string | null;
  preferredEmail: string | null;
  combineNotifications: boolean | null;
  userCommunicationPreferenceSettings: IUserCommunicationPreferenceSetting[] | null;
}

export interface IWorkFlow {
  id: string;
  name: string;
  description?: string;
  typeId?: string;
}

export interface IEarningCriteria {
  id: string;
  instanceId: string;
  sortOrder: number;
  minValue?: number;
  maxValue?: number;
  isDeleted?: boolean;
  earningCriteriaType: IEarningCriteriaType;
  earningCriteriaContent: IEarningCriteriaContent[];
  rowCriteria: IEarningRowCriteria[];
}

export interface IEarningRowCriteria {
  rowId: string;
  instanceId: string;
  title?: string;
  minValue?: number | null;
  earningText?: string;
  earningCriteriaTypeId?: string | null;
}

export interface IEarningCriteriaType {
  id: string;
  name: string;
  selectText?: string;
  earningText?: string;
  completedText?: string;
}

export interface IEarningCriteriaContent {
  id: string;
  earningCriteriaId: string;
  type: string;
  refId: string;
  name: string;
}

export interface IEarningCriteriaIn {
  id?: string;
  minValue?: number;
  maxValue?: number;
  isDeleted?: boolean;
  instanceId: string;
  sortOrder: number;
  earningCriteriaType: IEarningCriteriaType;
  earningCriteriaContent: IEarningCriteriaContentIn[];
}

export interface IEarningCriteriaContentIn {
  id?: string;
  earningCriteriaId?: string;
  refId: string;
  type: string;
  name: string;
  isDeleted?: boolean;
}

export interface IEarningCriteriaContentSearch {
  id: string;
  name: string;
  type: string;
}

export interface IUserDomain {
  hasDomainOrg: boolean;
  organizations: IDomainOrganization[];
  shouldReceiveDomainPopup: boolean;
  domain: string;
}

export interface IDomainOrganization {
  id: string;
  name?: string;
  type?: string;
  city?: string;
  country?: string;
  selected?: boolean;
  domainRoleId: string;
  domainRoleName: string;
}

export interface IRouteParams {
  featureSlug?: string | null;
  instanceSlug?: string | null;
  tabName: string | null;
  viewType: number;
  orgId?: string;
}

export interface IBreadcrumb {
  id: string;
  orgId: string;
  url: string;
  name: string;
  oldName: string;
  featureTypeName: string;
}

export interface IJoinResult {
  joinObjectId: string;
  instanceSlug?: string;
  userId: string;
  instanceId?: string;
  productId?: string;
  orgId?: string;
}
export interface AnalyticResult {
  name?: string;
  description: string;
  headers?: AnalyticTypes[];
  type?: string;
  userBreakdown: any[];
}
export interface AnalyticTypes {
  name: string;
  amount?: number;
}

export interface AnalyticsExpansionPanelHeader {
  title?: string;
  amount?: number;
}

export interface ISidePanelBuilder {
  id: string;
  previousIndex?: number;
  builderType: number;
  heading: string;
}

export interface ITextAndButton {
  heading: string;
  description: string;
  buttonName: string;
  url: string;
  sameUrlNavigation: boolean;
  stylingDirection?: string;
  darkText?: boolean;
}

export interface IButton {
  buttonName: string;
  url: string;
  sameUrlNavigation: boolean;
  stylingDirection?: string;
  tagId?: string;
}

export interface IIconAndText {
  icon: string;
  text: string;
}

export interface IAccordionItem {
  id?: string;
  selected?: boolean;
  heading: string;
  title: string;
  description: string;
  sortOrder: number;
}

export interface IMatchingItem {
  itemPrompts: IMatchingItemPrompt[];
}

export interface IMatchingItemPrompt {
  promptTitle: string;
  answerTitle: string;
  sortOrder: number;
  isDropped?: boolean;
}

export interface IItemAnswer {
  matched?: boolean;
  answerTitle: string;
  sortOrder: number;
}

export interface IItemPrompt {
  matched?: boolean;
  promptTitle: string;
  sortOrder: number;
  promptAnswerSortOrder: number;
  isDropped?: boolean;
}

export interface IBulletList {
  text: string | null;
  sortOrder: number;
}

export interface IMediaAndText {
  sortOrder: number;
  heading: string;
  paragraph: string;
  asset?: string;
  assetType?: string;
  assetUrl?: string;
  buttonText?: string;
  buttonUrl?: string;
  sameUrlNavigation?: boolean;
}

export interface IHeading {
  text: string;
  description: string;
  chipText?: string;
}

export interface IChangeDetection {
  componentId: string;
  updateValue: string;
}

export interface IImageCentered {
  sortOrder: number;
  caption?: string;
  height?: string;
  aspectRatio?: string;
  objectFit?: string;
  noClickPreview?: boolean;
  asset: IAsset;
  assetUrl?: string;
}

export interface ISidePanelBuilder {
  id: string;
  previousIndex?: number;
  builderType: number;
  heading: string;
}

export interface ITextAndButton {
  heading: string;
  description: string;
  buttonName: string;
  url: string;
}

export interface IInstanceInterest {
  heading: string;
  description: string;
  interestNoneText: string;
  interestMediumText: string;
  interestHighText: string;
}

export interface IUserInstanceInterest {
  instanceId?: string;
  eventDate: Date;
  nominalVal: number;
}

export interface IUserOrganizationInterest {
  organizationId?: string;
  eventDate: Date;
  nominalVal: number;
}

export interface IInstanceInterestText {
  text: string;
  sortOrder: number;
}

export interface IAccordionItem {
  id?: string;
  selected?: boolean;
  heading: string;
  title: string;
  description: string;
  sortOrder: number;
}

export interface IMatchingItem {
  description: string;
  itemPrompts: IMatchingItemPrompt[];
}

export interface IMatchingItemPrompt {
  promptTitle: string;
  answerTitle: string;
  sortOrder: number;
}

export interface IItemAnswer {
  matched?: boolean;
  answerTitle: string;
  sortOrder: number;
}

export interface IItemPrompt {
  matched?: boolean;
  promptTitle: string;
  sortOrder: number;
  promptAnswerSortOrder: number;
}

export interface IBulletList {
  text: string | null;
  sortOrder: number;
}

// export interface IMediaAndText {
//   sortOrder: number;
//   heading: string;
//   paragraph: string;
//   asset: IAsset;
//   assetUrl?: string;
// }

export interface ICampaign {
  id: string;
  title: string;
  featureId: string;
  campaignCode: string;
  campaignTagId?: string;
  instanceId?: string;
  productOrganizationId?: string;
  productId?: string;
  iconAssetId?: string;
  productExpirationDate?: Date;
  roleId?: string;
  maxUsers?: number;
  userCount?: number;
}

export interface ICampaignIn {
  title: string;
  featureId: string;
}

export interface Level {
  name: string;
}

export interface ILTIActivityIn {
  instanceId: string;
  featureId: string;
}

export interface ILTSubmit {
  url: string;
  idToken: string;
  state: string;
}

export interface IUserRiasecFinalScore {
  realistic: number;
  investigative: number;
  artistic: number;
  social: number;
  enterprising: number;
  conventional: number;
  riasecCode: string;
}

export interface ICombinedUserRiasecFinalScore {
  initialScore: IUserRiasecFinalScore;
  efDynamicScore: IUserRiasecFinalScore;
}
export interface ILTLinkIn {
  issuer: string;
  subject: string;
  userId?: string;
}
export interface ILTLink {
  userId?: string;
  issuer: string;
  subject: string;
}

export interface ILTIToken {
  id: string;
  token: string;
}

export interface IDashboardGridFilters {
  repoSearchValue: string | null;
  currentAmount: number | null;
  getAmount: number | null;
  sortBy: string | null;
  sortDir: string | null;
  filterField: string | null;
  filterValue: string;
}

export interface IInstanceDetailsValues {
  iconAssetId: string;
  title: string;
  description: string;
}

export interface IQlikSheet {
  id: string;
  title: string;
}

export interface IQlikComponent {
  id: string;
  title: string;
}

export interface IQlikSelection {
  filterType: string | undefined;
  filterValue: string;
  filterOptions: string[];
  bypassWait: boolean;
}

export interface IRowContentById {
  userLocation: IUserContext;
  skip: number;
  limit: number;
  isLoadAll: boolean;
  searchFilter: string | null;
  instanceId: string | null;
  entityId: string | null;
  featureTypeId: string | null;
  selectedUserId: string | null;
}
