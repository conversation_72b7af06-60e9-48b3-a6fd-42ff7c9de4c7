@if (assessmentProficiencyForm) {
  <form [formGroup]="assessmentProficiencyForm">
    <div class="parent-container">
      <ion-row>
        <ion-col size="*" class="heading-col">Complete the assessment with</ion-col>
        <ion-col class="value col-padding" size="1">
          <ion-input (ionChange)="checkPercentageInputLength($event, 'min')" type="number" formControlName="minValue" min="0" max="100"></ion-input>
          <span>%</span>
        </ion-col>
        <ion-col class="col-padding" size="auto"> - </ion-col>
        <ion-col class="value col-padding" size="auto">
          <ion-input (ionChange)="checkPercentageInputLength($event, 'max')" type="number" formControlName="maxValue" min="0" max="100"></ion-input>
          <span>%</span>
        </ion-col>
        <ion-col siz> proficiency. </ion-col>
      </ion-row>
    </div>
  </form>
}
