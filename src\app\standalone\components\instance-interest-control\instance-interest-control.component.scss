.caption {
  color: #ffffff;
  font-family: '<PERSON><PERSON>';
  font-weight: bold;
  font-size: 22px;
  font-style: italic;
  line-height: 1.3;
  letter-spacing: 0.3px;
  text-align: left;
  margin: 0px;
}

.description {
  color: #cccccc;
  font-family: '<PERSON><PERSON>';
  font-weight: 400;
  font-size: 16px;
  line-height: 1.4;
  text-align: left;
  margin: 0px;
}
.parent-container {
  margin: 8px;
  .card-container {
    background-color: #2d2e32;
    padding: 10px;
  }
}

.side-panel-input-padding {
  margin: 0px !important;
  ion-card {
    margin: 0px !important;
  }
}
