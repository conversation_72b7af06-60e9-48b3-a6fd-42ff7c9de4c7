<ion-grid>
  <ion-row class="view-options-row">
    <!-- <div class="top-header">
    <ion-button color="warning">Download</ion-button>
  </div> -->
  </ion-row>
</ion-grid>
<div class="users-table">
  <div class="table-container">
    <table class="table-grid" mat-table [dataSource]="dataSource" matSort #sort="matSort">
      <ng-container matColumnDef="select">
        <th style="border-right: none" mat-header-cell *matHeaderCellDef>
          <mat-checkbox (change)="$event ? masterToggle() : null" [checked]="selection.hasValue() && isAllSelected()" [indeterminate]="selection.hasValue() && !isAllSelected()"> </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let row">
          <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(row) : null" [checked]="selection.isSelected(row)"> </mat-checkbox>
        </td>
      </ng-container>
      <!-- <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
      <td mat-cell *matCellDef="let element">{{ element.userId }}</td>
    </ng-container> -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>NAME</th>
        <td style="color: rgba(250, 167, 0) !important" mat-cell *matCellDef="let element">{{ element.name }}</td>
      </ng-container>
      <ng-container matColumnDef="progress">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>ASSIGNMENT PROGRESS</th>
        <td mat-cell *matCellDef="let element">{{ element.completedContentCount }}/{{ element.contentCount }}</td>
      </ng-container>
      <ng-container matColumnDef="grade">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>AVERAGE ASSIGNMENT GRADE</th>
        <td mat-cell *matCellDef="let element">{{ element.grade }}%</td>
      </ng-container>
      <!-- <ng-container matColumnDef="controls">
    <th style="border-right: none" mat-header-cell *matHeaderCellDef mat-sort-header>CONTROLS</th>
    <td mat-cell *matCellDef="let element">
      <div class="button-container"><ion-button>Results</ion-button><ion-button>Reset</ion-button></div>
    </td>
  </ng-container> -->
      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </div>
  @if (moreResults) {
    <div (click)="getResultsTableById(id, true)" class="load-more">
      <ion-row>
        <ion-col size="12">
          <div>Load More</div>
          <div><ion-icon name="chevron-down-outline"></ion-icon></div>
        </ion-col>
      </ion-row>
    </div>
  }
</div>
