@if (assessmentForm) {
  <form class="assessment-form-container" [formGroup]="assessmentForm">
    <ion-grid>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Block Label'" [placeHolder]="'Add field block label'" formControlName="label"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Help Description'" [placeHolder]="'Add field help description...'" formControlName="helpDescription"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Add field caption...'" formControlName="caption"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Placeholder'"
            [placeHolder]="'Add field placeholder...'"
            formControlName="placeHolderText"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-select-option-control
            [options]="grades"
            [label]="'Grade'"
            [placeHolder]="'Choose grade...'"
            formControlName="grade"
            [toolTip]="'Choose the grade'"
            [backgroundColor]="'#333333'"></app-select-option-control>
        </ion-col>
      </ion-row>
      @if (component?.componentType?.name === 'File Upload') {
        <ion-row>
          <ion-col>
            <div class="required-questions-container">
              <div>
                <ion-input type="number" formControlName="limitTo"></ion-input>
              </div>
              <ion-label style="font-size: small">Prompt character limit</ion-label>
            </div>
          </ion-col>
        </ion-row>
        <ion-row class="file-size-parent-container">
          <ion-col size="12">
            <ion-item #control>
              <ion-label position="stacked" class="label-header">
                File Size
                <span class="reqAsterisk">
                  <ion-icon name="information-circle-outline"></ion-icon>
                </span>
              </ion-label>
              <ion-row>
                <ion-col size="5">
                  <app-text-input-control
                    [isCustom]="true"
                    [label]="'Min file size'"
                    [backgroundColor]="'#292929'"
                    [placeHolder]="'Kb'"
                    formControlName="minFileSize"
                    type="number"></app-text-input-control>
                </ion-col>
                <ion-col size="2"> <h6 style="text-align: center">to</h6> </ion-col>
                <ion-col size="5">
                  <app-text-input-control
                    [isCustom]="true"
                    [label]="'Max file size'"
                    [backgroundColor]="'#292929'"
                    [placeHolder]="'Mb'"
                    formControlName="maxFileSize"
                    type="number"></app-text-input-control>
                </ion-col>
              </ion-row>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-select-option-control
              [multiple]="true"
              [label]="'File Types'"
              [options]="builderService.allFormats"
              [backgroundColor]="'#333333'"
              [disabled]="false"
              formControlName="fileTypeBw"></app-select-option-control>
          </ion-col>
        </ion-row>
      }
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Row Number'" [placeHolder]="'Add field row number...'" formControlName="rowNumber" [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Column Number (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add column number (Out of 12)...'"
            formControlName="colNumber"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Colspan (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add field colspan (Out of 12)...'"
            formControlName="colspan"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Hover Sort Order'"
            [placeHolder]="'Add field hover sort order...'"
            formControlName="hoverSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Instance Sort Order'"
            [placeHolder]="'Add field instance sort order...'"
            formControlName="instanceSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-card class="slide-fields-container-card">
            <ion-card-content>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isBlockRequired">Make this block required for completion</mat-slide-toggle>
              @if (assessmentForm.controls['isBlockRequired'].value === true) {
                <ion-row class="percentage-container-row">
                  <ion-col class="counter-col">
                    <ion-input type="number" (ionChange)="checkPercentageInputLength($event)" min="0" max="100" placeHolder="%" formControlName="percentageToComplete"> </ion-input>
                  </ion-col>
                  <ion-col size="9">Percentage required to complete</ion-col>
                </ion-row>
              }
              <app-field-checkboxes-base [baseForm]="assessmentForm"></app-field-checkboxes-base>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isAuthorRequired">Require author to build out this block</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isRetake">Allow user to retake assessment</mat-slide-toggle>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
