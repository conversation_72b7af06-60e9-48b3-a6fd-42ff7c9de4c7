<ion-content class="parent-container">
  <ion-grid>
    <ion-row class="top-actions-row">
      <ion-col class="back-button-col">
        <ion-button size="small" fill="clear" color="warning" (click)="back()">
          <ion-icon name="chevron-back-outline"></ion-icon>
          <span>Back</span>
        </ion-button>
      </ion-col>
      @if (formValid) {
        <ion-col class="save-button-col">
          <ion-button size="small" color="warning" (click)="save()">Save</ion-button>
        </ion-col>
      }
    </ion-row>
  </ion-grid>

  @if (component.componentType.parentTypeName !== 'Question') {
    <div>
      @switch (component.componentType.name) {
        <!-- <app-text-field-editor *ngSwitchCase="'Text'" [component]="component" (formValidityChanged)="setFormValid($event)"></app-text-field-editor>
        <app-text-field-editor *ngSwitchCase="'Text Area'" [component]="component" (formValidityChanged)="setFormValid($event)"></app-text-field-editor>
        <app-text-field-editor *ngSwitchCase="'Url Upload'" [component]="component" (formValidityChanged)="setFormValid($event)"></app-text-field-editor>
        <app-text-field-editor *ngSwitchCase="'Code Embed'" [component]="component" (formValidityChanged)="setFormValid($event)"></app-text-field-editor>
        <app-text-field-editor *ngSwitchCase="'Feedback Block'" [component]="component" (formValidityChanged)="setFormValid($event)"></app-text-field-editor>
        <app-text-field-editor *ngSwitchCase="'Address Search'" [component]="component" (formValidityChanged)="setFormValid($event)"></app-text-field-editor>
        <app-label-field-editor *ngSwitchCase="'WYSIWYG'" [component]="component" (formValidityChanged)="setFormValid($event)"></app-label-field-editor>
        <app-label-field-editor *ngSwitchCase="'Label'" [component]="component" (formValidityChanged)="setFormValid($event)"></app-label-field-editor>
        <app-organization-field-editor *ngSwitchCase="'Organization'" [component]="component" (formValidityChanged)="setFormValid($event)"></app-organization-field-editor>-->
        @case ('Image Upload Field') {
          <app-image-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-image-field-editor>
        }
        @case ('HTML5 Game') {
          <app-html5-game-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-html5-game-editor>
        }
        @case ('Page Banner') {
          <app-page-banner-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-page-banner-field-editor>
        }
        @case ('Image And Text') {
          <app-media-and-text-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-media-and-text-field-editor>
        }
        @case ('Dropdown') {
          <app-drop-down-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-drop-down-field-editor>
        }
        @case ('Default Row') {
          <app-row-options-editor [component]="component" (formValidityChanged)="setFormValid($event)" (formChange)="rowEditorFormChange($event)"></app-row-options-editor>
        }
        @case ('Smart Row') {
          <app-row-options-editor [component]="component" (formValidityChanged)="setFormValid($event)" (formChange)="rowEditorFormChange($event)"></app-row-options-editor>
        }
        @case ('Download Block') {
          <app-download-block-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-download-block-editor>
        }
        @case ('Media Block') {
          <app-media-block-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-media-block-editor>
        }
        @case ('Instance Header') {
          <app-instance-header-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-instance-header-field-editor>
        }
        @case ('Achievement') {
          <app-achievement-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-achievement-editor>
        }
        @case ('Organization Networks') {
          <app-organization-network-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-organization-network-editor>
        }
        @case ('Network QlikView PDF Download') {
          <app-network-qlik-view-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-network-qlik-view-editor>
        }
        @case ('Row Manager') {
          <app-achievement-bank-editor [component]="component" (formValidityChanged)="setFormValid($event)" (achievementIdsUpdated)="setComponentAchievements($event)"></app-achievement-bank-editor>
        }
        <!-- <app-external-html-block-editor *ngSwitchCase="'External Html Block'" [component]="component" (formValidityChanged)="setFormValid($event)"> </app-external-html-block-editor> -->
        @case ('Banner') {
          <app-banner-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-banner-editor>
        }
        <!--AUTHORING-->
        @case ('Video') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Embedded URL') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Authoring Selector') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Paragraph') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Text & Button') {
          <app-text-and-button-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-text-and-button-field-editor>
        }
        @case ('Button') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Instance Interest') {
          <app-instance-interest-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-instance-interest-field-editor>
        }
        @case ('Numbered Bullet List') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Bullet List') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Heading') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Accordion') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Image Centered') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Media & Text') {
          <app-media-and-text-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-media-and-text-field-editor>
        }
        @case ('Icon & Text') {
          <app-icon-base-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-icon-base-field-editor>
        }
        @case ('Icon & Dropdown') {
          <app-icon-base-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-icon-base-field-editor>
        }
        @case ('Flash Card') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Picture Flash Card') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Attachment') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Listing Details') {
          <app-display-list-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-display-list-field-editor>
        }
        @case ('Phone Number') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Full Name') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Email Chips') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Password') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Contact Info') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Learning Outcomes') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        <!--From Old Buidler Editors-->
        @case ('External Html Block') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Text') {
          <app-text-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-text-field-editor>
        }
        @case ('Text Area') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-authoring-field-editor>
        }
        @case ('Url Upload') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-authoring-field-editor>
        }
        @case ('Code Embed') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-authoring-field-editor>
        }
        @case ('Feedback Block') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-authoring-field-editor>
        }
        @case ('WYSIWYG') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-authoring-field-editor>
        }
        @case ('Label') {
          <app-text-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-text-field-editor>
        }
        @case ('Address Search') {
          <app-google-maps-autocomplete-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-google-maps-autocomplete-field-editor>
        }
        @case ('Organization') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('Website Link') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
        @case ('User RIASEC Score Chart') {
          <app-not-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-not-field-editor>
        }
        @case ('Checkbox') {
          <app-checkbox-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-checkbox-field-editor>
        }
        @case ('Qlik Analytics Type Selector') {
          <app-qlik-analytics-type-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-qlik-analytics-type-editor>
        }
        @case ('Qlik Analytics') {
          <app-qlik-analytics-editor [component]="component" (formValidityChanged)="setFormValid($event)"> </app-qlik-analytics-editor>
        }
        @case ('Criteria Manager') {
          <app-criteria-manager-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-criteria-manager-editor>
        }
        @case ('Spacing') {
          <app-authoring-field-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-authoring-field-editor>
        }
      }
    </div>
  }
  @if (component.componentType.parentTypeName === 'Question') {
    <div>
      <app-assessment-block-editor [component]="component" (formValidityChanged)="setFormValid($event)"></app-assessment-block-editor>
    </div>
  }
</ion-content>
