import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoaderService {
  private loaderVisibleSubject = new BehaviorSubject<boolean>(false);
  private loaderMessageSubject = new BehaviorSubject<string>('Loading...');

  public readonly loaderVisible$: Observable<boolean> = this.loaderVisibleSubject.asObservable();
  public readonly loaderMessage$: Observable<string> = this.loaderMessageSubject.asObservable();

  constructor() {}

  /**
   * Show the loader with an optional message
   * @param message Optional message to display in the loader
   */
  showLoader(message: string = 'Loading...') {
    this.loaderMessageSubject.next(message);
    this.loaderVisibleSubject.next(true);
  }

  /**
   * Hide the loader
   */
  hideLoader() {
    this.loaderVisibleSubject.next(false);
  }
}
