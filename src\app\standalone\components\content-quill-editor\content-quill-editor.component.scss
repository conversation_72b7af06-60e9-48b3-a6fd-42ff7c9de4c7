:host {
  .quill-edit-parent-container {
    width: 100%;
    overflow: hidden;
    .quill-toolbar {
      width: 100%;
      .ql-formats {
        margin-right: 0px !important;

        .custom-button {
          font-size: 15px;
          width: auto;
          color: #fda000;
        }
      }
    }

    .text-counter {
      position: absolute;
      right: 25px;
      bottom: 45px;
      z-index: 2;
      font-size: 12px;
    }

    .ql-toolbar:after {
      display: none;
    }

    .ql-toolbar {
      border: none;
    }

    ::ng-deep .ql-container {
      max-height: 500px;
      border: none;
      min-height: 120px;
      background-color: var(--background-color);
      .ql-editor {
        overflow: hidden;
        font-family: 'Roboto';
        font-size: 1.125em;
        color: #aaa;
        letter-spacing: 0.03em;
        line-height: 1.4;
        font-weight: 400;

        ::ng-deep a {
          background-color: transparent !important;
        }

        ::ng-deep ul li::before {
          color: #ee9907;
        }

        ::ng-deep ul > li:before {
          content: '\25CF';
        }

        ::ng-deep ol > li,
        ul > li {
          padding-bottom: 11px;
        }

        ::ng-deep ol,
        ::ng-deep ul {
          padding-top: 10px;
          padding-bottom: 5px;
        }

        ::ng-deepli:not(.ql-direction-rtl):before {
          margin-right: 0.5em;
          text-align: right;
          font-weight: 600;
        }
      }
    }

    ::ng-deep {
      .ql-snow .ql-editor {
        h1 {
          font-family: 'Roboto';
          font-size: 1.688em;
          color: #fff;
          letter-spacing: 0.03em;
          line-height: 1.1;
          font-weight: 400;
        }

        h2 {
          font-family: 'Roboto';
          font-size: 1.25em;
          color: #fff;
          letter-spacing: 0.03em;
          line-height: 1.1;
          font-weight: 700;
          font-style: italic;
        }
      }
    }

    ::ng-deep #emoji-close-div {
      display: none !important;
    }

    ::ng-deep #emoji-palette {
      top: -270px !important;
      right: 0px !important;
      left: unset !important;
    }

    ::ng-deep.ql-active {
      .ql-stroke {
        stroke: white !important;
      }

      .ql-fill {
        stroke: white !important;
      }
    }

    ::ng-deep.ql-toolbar button:hover {
      .ql-stroke {
        fill: none;
        stroke: #fda000;
      }

      .ql-fill {
        fill: #fda000;
        stroke: none;
      }

      .ql-picker {
        color: #fda000;
      }
    }

    ::ng-deep.ql-picker-options {
      top: -129px !important;
      right: 0px !important;
      left: unset !important;
    }
  }
}
