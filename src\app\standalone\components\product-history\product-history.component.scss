.parent-container {
  
  mat-accordion {
    mat-expansion-panel {
      //background-color: rgba(8, 8, 8);
      background-color: #181818;
      box-shadow: none;

      .expansion-panel-header {
        margin-bottom: 15px;
        height: 100%;
        background-color: rgba(41, 41, 41);
        border-radius: 8px;
        padding: 15px;
        .inner-panel {
          .heading {
            font-weight: bold;
            font-size: 20px;
            color: white;
          }
          .sub-heading {
            margin-bottom: 5px;
            font-style: italic;
            color: rgba(170, 170, 170);
            .expiry-date {
              margin-left: 5px;
              color: white;
            }
          }
        }
        .role-heading {
          color: rgba(170, 170, 170);
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: auto;
          margin-right: 20px;
        }
      }

      .expansion-panel-header:hover {
        background-color: rgba(41, 41, 41) !important;
      }
    }

    .load-more {
      font-size: 16px;
      color: white;
      margin-top: 20px;
      text-align: center;
      display: flex;
      justify-content: center;
      cursor: pointer;
    }
  }
}
