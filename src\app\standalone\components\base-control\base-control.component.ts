import { EventEmitter, Output } from '@angular/core';
import { AbstractControl, ControlValueAccessor, ValidationErrors, Validator, Validators } from '@angular/forms';
import { Subject } from 'rxjs';

export class BaseControlComponent implements ControlValueAccessor, Validator {
  label: string;
  touched = false;
  disabled = false;
  errorMessage: string;
  textValue: string | undefined;
  required = false;
  fieldValueChanged = new EventEmitter<any>();

  // !!!!!!!!!!!!!!!!!!!! Do not remove this all the forms fields stop working if removed
  // eslint-disable-next-line no-unused-vars
  onChanges = (textValue: any) => {
    this.fieldValueChanged.next(textValue);
  };

  onTouched = () => {};
  onValidation = () => {};
  registerOnChange(onChange: any) {
    this.onChanges = onChange;
  }

  setValue(event: any) {
    this.writeValue(event.target.value);
  }

  registerOnTouched(onTouched: any) {
    this.onTouched = onTouched;
  }

  setDisabledState?(isDisabled: boolean) {
    this.disabled = isDisabled;
  }

  markAsTouched() {
    if (!this.touched) {
      this.onTouched();
      this.touched = true;
    }
  }

  forceWriteValue(textValue: any, hideChanges = false) {
    this.textValue = textValue;
    if (!hideChanges) {
      this.onChanges(textValue);
      this.markAsTouched();
    }
  }

  writeValue(textValue: any, hideChanges = false) {
    if (this.textValue === textValue) {
      return;
    }
    this.textValue = textValue;
    if (!hideChanges) {
      this.onChanges(textValue);
      this.markAsTouched();
    }
  }

  validate(control: AbstractControl): ValidationErrors | null {
    if (control.hasValidator(Validators.required) && Validators.required(control)) {
      this.required = true;
      this.errorMessage = this?.label ?? '' + ' is required';
      return control.errors;
    }

    if (control.hasValidator(Validators.email) && Validators.email(control)) {
      this.errorMessage = this.label + ' is not a valid email address';
      return control.errors;
    }

    this.errorMessage = '';
    return null;
  }

  registerOnValidatorChange?(fn: () => void): void {
    this.onValidation = fn;
  }
}
