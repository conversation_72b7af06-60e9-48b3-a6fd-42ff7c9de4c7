.info-container {
  position: relative;
  height: auto;
  display: flex;
  margin: 0px 0px 30px 0px;
  background-color: #232323;
  height: 100%;

  .content {
    align-self: flex-end;
    z-index: 1000;
    width: 100%;
    height: 100%;

    :ng-deep .icon-inner {
      margin-top: 3px;
    }
  }

  .image-header-gradient {
    width: 100%;
    background-image: linear-gradient(to bottom, rgba(30, 30, 30, 0.35) 25%, rgb(30, 30, 30) 75%), var(--background-image);
    background-size: cover;
    border-radius: 11px;
    height: 180px;
    top: -0px;
    left: -0px;
    position: absolute;
  }
}
