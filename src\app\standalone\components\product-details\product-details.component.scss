mat-accordion {
  mat-expansion-panel {
    background-color: transparent;
    .expansion-panel-header {
      margin-bottom: 15px;
      height: 100%;
      background-color: rgba(68, 68, 68);
      border-radius: 8px;
      padding: 15px;
      .inner-panel {
        .heading {
          font-weight: bold;
          font-size: 20px;
          color: white;
        }
        .sub-heading {
          width: 800px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 5px;
          font-style: italic;
          color: rgba(170, 170, 170);
        }
      }

      .toggle-disable-button {
        color: rgba(170, 170, 170);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: auto;
        margin-right: 20px;

        mat-icon {
          color: rgba(250, 167, 0) !important;
        }

        .select-container {
          display: flex;
          align-items: center;
          .toggle {
            margin-left: 10px;
            margin-right: 10px;
          }
        }
      }
    }

    .expansion-panel-header:hover {
      background-color: rgba(92, 92, 92) !important;
    }
  }
}

.load-more {
  font-size: 16px;
  color: white;
  text-align: center;
  display: flex;
  margin-top: 20px;
  justify-content: center;
  cursor: pointer;
}
