import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { IFeature, IFeatureIn } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-feature-repository-overview',
    templateUrl: './overview.component.html',
    styleUrls: ['./overview.component.scss'],
    standalone: false
})
export class FeatureRepositoryOverviewComponent implements OnInit, OnDestroy {
  @Input() feature: IFeature;
  @Output() featureUpdated = new EventEmitter<IFeature>();
  componentDestroyed$: Subject<boolean> = new Subject();
  featureForm: UntypedFormGroup;
  title: UntypedFormControl;
  code: UntypedFormControl;
  description: UntypedFormControl;
  featureDescriptors: UntypedFormControl;
  instanceDescriptors: UntypedFormControl;
  displayObjectCount: UntypedFormControl;
  featureSlug: UntypedFormControl;
  iconAssetId: UntypedFormControl;
  coverMediaAssetId: UntypedFormControl;
  textureAssetId: UntypedFormControl;
  journeyStageId: UntypedFormControl;
  roleObjectiveId: UntypedFormControl;
  targetAudienceId: UntypedFormControl;
  featureTypeId: UntypedFormControl;
  continuumTimelineId: UntypedFormControl;
  featureCategoryId: UntypedFormControl;
  isFullWidth: UntypedFormControl;
  showSave = false;
  journeyStage$: Observable<KeyValue[]>;
  roleObjectives$: Observable<KeyValue[]>;
  targetAudience$: Observable<KeyValue[]>;
  featureTypes$: Observable<KeyValue[]>;
  continuumTimeline$: Observable<KeyValue[]>;
  featureCategory$: Observable<KeyValue[]>;
  featureIn: IFeatureIn;

  constructor(
    private dataService: DataService,
    private instanceService: InstanceService
  ) {}

  ngOnInit() {
    this.createFormControls();
    this.createForm();
    this.initData();
  }

  createFormControls() {
    this.title = new UntypedFormControl(this.feature?.title, [Validators.required]);
    this.code = new UntypedFormControl(this.feature?.code);
    this.description = new UntypedFormControl(this.feature?.description);
    this.featureDescriptors = new UntypedFormControl(this.feature?.descriptors);
    this.instanceDescriptors = new UntypedFormControl(this.feature?.instanceDescriptors);
    this.displayObjectCount = new UntypedFormControl(this.feature?.displayObjectCount);
    this.featureSlug = new UntypedFormControl(this.feature?.featureSlug);
    this.iconAssetId = new UntypedFormControl(this.feature?.iconAssetId);
    this.coverMediaAssetId = new UntypedFormControl(this.feature?.coverMediaAssetId);
    this.textureAssetId = new UntypedFormControl(this.feature?.textureAssetId);
    this.journeyStageId = new UntypedFormControl(this.feature?.journeyStageId);
    this.roleObjectiveId = new UntypedFormControl(this.feature?.roleObjectiveId);
    this.targetAudienceId = new UntypedFormControl(this.feature?.targetAudienceId);
    this.featureTypeId = new UntypedFormControl(this.feature?.featureType?.id);
    this.continuumTimelineId = new UntypedFormControl(this.feature?.continuumTimelineId);
    this.featureCategoryId = new UntypedFormControl(this.feature?.featureCategoryId);
    this.isFullWidth = new UntypedFormControl(this.feature.isFullWidth);
  }

  createForm() {
    this.featureForm = new UntypedFormGroup({
      title: this.title,
      code: this.code,
      description: this.description,
      featureDescriptors: this.featureDescriptors,
      instanceDescriptors: this.instanceDescriptors,
      displayObjectCount: this.displayObjectCount,
      featureSlug: this.featureSlug,
      iconAssetId: this.iconAssetId,
      coverMediaAssetId: this.coverMediaAssetId,
      textureAssetId: this.textureAssetId,
      journeyStageId: this.journeyStageId,
      roleObjectiveId: this.roleObjectiveId,
      targetAudienceId: this.targetAudienceId,
      featureTypeId: this.featureTypeId,
      continuumTimelineId: this.continuumTimelineId,
      featureCategoryId: this.featureCategoryId,
      isFullWidth: this.isFullWidth,
    });

    this.featureForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.featureIn = {
        title: this.title.value,
        code: this.code.value,
        description: this.description.value,
        descriptors: this.featureDescriptors.value,
        instanceDescriptors: this.instanceDescriptors.value,
        displayObjectCount: this.displayObjectCount.value,
        featureSlug: this.featureSlug.value,
        iconAssetId: this.iconAssetId.value,
        coverMediaAssetId: this.coverMediaAssetId.value,
        textureAssetId: this.textureAssetId.value,
        journeyStageId: this.journeyStageId.value,
        roleObjectiveId: this.roleObjectiveId.value,
        targetAudienceId: this.targetAudienceId.value,
        featureTypeId: this.featureTypeId.value,
        continuumTimelineId: this.continuumTimelineId.value,
        featureCategoryId: this.featureCategoryId.value,
        isFullWidth: this.isFullWidth.value,
      } as IFeatureIn;

      if (this.featureForm.valid && this.featureForm.dirty) {
        this.showSave = true;
      }
    });
  }

  initData() {
    this.journeyStage$ = this.dataService.getTagChildren('ED64B075-33C5-4BB7-A488-78FC8F754090').pipe(
      map(tags => {
        return tags.map(t => {
          return { id: t.id, parentId: t.parentId, value: t.name, level: t.treeLevel } as KeyValue;
        });
      })
    );
    this.roleObjectives$ = this.dataService.getTagChildren('1BE81735-8236-4B49-B824-936899903693').pipe(
      map(tags => {
        return tags.map(t => {
          return { id: t.id, parentId: t.parentId, value: t.name, level: t.treeLevel } as KeyValue;
        });
      })
    );
    //Grade.
    this.targetAudience$ = this.dataService.getTagChildren('8FDCB339-5853-4FA7-B88E-9C53156CE73E').pipe(
      map(tags => {
        return tags.map(tag => {
          return { id: tag.id, parentId: tag.parentId, value: tag.name, level: tag.treeLevel, hasChildren: tag.inverseParent ?? false } as KeyValue;
        });
      })
    );
    this.featureTypes$ = this.dataService.getFeatureTypes().pipe(
      map(types => {
        return types.map(t => {
          return { id: t.id, value: t.name } as KeyValue;
        });
      })
    );
    this.continuumTimeline$ = this.dataService.getTagChildren('F933C359-F5E8-4104-A10D-52FE3F7F5C82').pipe(
      map(tags => {
        return tags.map(t => {
          return { id: t.id, parentId: t.parentId, value: t.name, level: t.treeLevel } as KeyValue;
        });
      })
    );
    this.featureCategory$ = this.dataService.getTagChildren('D56770E6-2C23-4C1E-91BD-A4BADDA96617').pipe(
      map(tags => {
        return tags.map(t => {
          return { id: t.id, parentId: t.parentId, value: t.name, level: t.treeLevel } as KeyValue;
        });
      })
    );
  }

  saveFeature() {
    if (this.featureForm.valid && this.featureIn) {
      this.dataService
        .putFeature(this.feature.id, this.featureIn)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(isFeatureUpdated => {
          if (isFeatureUpdated) {
            this.featureUpdated.emit(isFeatureUpdated);
            this.showSave = false;
          }
        });
    }
  }

  checkRoute() {
    this.instanceService.openInstance(this.featureSlug.value);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
