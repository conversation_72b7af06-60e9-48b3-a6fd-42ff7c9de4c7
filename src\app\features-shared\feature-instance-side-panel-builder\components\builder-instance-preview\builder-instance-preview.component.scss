:host {
  .preview-parent-content-container {
    height: 100%;
    border-radius: 11px;
    background-color: rgba(35, 35, 35);
    border: 3px solid rgba(51, 51, 51);

    .edit-button {
      position: absolute;
      right: 25px;
      top: 20px;
      z-index: 9999;
      .inner-container {
        margin: 0px;
        color: black;
        text-transform: none;
        border-radius: 3px;
        font-weight: 500;
        font-size: 18px;
        background-color: #f99e00;
        ion-icon {
          width: 18px;
          height: 18px;
          padding-left: 10px;
          margin-right: 10px;
          color: #000000;
        }
      }

      ion-button.inner-container::part(native) {
        line-height: normal;
        --padding-start: 0 !important;
        --padding-end: 10px !important;
      }
    }

    .image-header-gradient {
      width: 100%;
      background-image: linear-gradient(to bottom, rgba(30, 30, 30, 0.35) 25%, rgb(35, 35, 35) 75%), var(--background-image);
      background-size: cover;
      border-radius: 11px;
      height: 180px;
      top: -0px;
      left: -0px;
      position: absolute;
    }

    .section-container {
      .header {
        position: absolute;
        width: fit-content;
        background: #f99e00;
        color: #000000;
        border-color: #f99e00;
        border-width: 1px;
        border-style: solid;
        border-radius: 3px 3px 0px 0px;
        font-family: 'Roboto';
        font-weight: bold;
        line-height: 1.6;
        letter-spacing: 0.3px;
        text-align: left;
        margin-top: -24px;
        height: 24px;
        display: block;
        z-index: 1000;
      }

      .section-title {
        left: 0px;
        margin-bottom: -1px;
        padding: 2px 11px 16px 11px;
        font-size: 11px;
        z-index: 1000;
      }

      ion-card {
        box-shadow: none;
        --background: none;
        margin: 0;

        .section-headings-col {
          margin-bottom: 10px;
          .section-title {
            display: flex;
            flex-direction: row;
            align-items: center;
          }

          .section-description {
            font-family: Roboto;
            color: rgba(159, 159, 159);
            font-size: 18px;
          }

          .section-index {
            width: 38px;
            height: 36px;
            background: #292929;
            color: #aaaaaa;
            border-color: #393939;
            border-width: 1px;
            border-style: solid;
            border-radius: 26px 26px 26px 26px;
            font-family: 'Roboto';
            font-weight: bold;
            font-size: 16px;
            line-height: 1.3;
            text-align: center;
            z-index: 999;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            align-content: center;
          }

          .section-title-text {
            min-width: 128px;
            width: fit-content;
            height: 24px;
            padding: 7px 12px 4px 43px;
            background: #333333;
            color: #aaaaaa;
            border-color: #454545;
            border-width: 1px;
            border-style: solid;
            border-radius: 11px 11px 11px 11px;
            font-family: 'Roboto';
            font-weight: 400;
            font-size: 12px;
            line-height: 1.1;
            letter-spacing: 0.2px;
            text-align: left;
            z-index: 998;
            margin-left: -20px;
            text-overflow: ellipsis;
          }
        }

        .section-manage-options-col {
          display: flex;
          justify-content: flex-end;
          padding-top: 7px;

          .section-settings {
            min-width: 128px;
            width: fit-content;
            height: 24px;
            background: none;
            color: #aaaaaa;
            font-family: 'Roboto';
            font-weight: 400;
            font-size: 12px;
            line-height: 1.1;
            letter-spacing: 0.2px;
            text-align: left;
            z-index: 998;
            margin-left: -20px;
            text-overflow: ellipsis;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;
            align-content: center;
          }

          ion-reorder {
            z-index: 900;
            .move-icon {
              cursor: pointer;
              height: 16px;

              mat-icon {
                font-size: 16px;
                height: 16px;
                width: 16px;
              }
            }
          }
        }

        .add-component-container {
          margin: 10px;
          border-radius: 10px;
          border: 2px solid rgba(130, 130, 130);
          background-color: rgba(34, 34, 34);
          border-style: dashed;
          .inner-content {
            display: flex;
            justify-content: center;
            padding: 10px;

            .add-button-container {
              .icon-container {
                display: flex;
                justify-content: center;
                margin-bottom: 5px;

                ion-icon {
                  color: rgb(186, 122, 19) !important;
                  font-size: 35px;
                  cursor: pointer;
                }
              }

              .button-heading {
                color: white;
              }
            }
          }
        }
      }

      .section-add-line {
        margin-bottom: 5px;
        .icon-container {
          display: flex;
          justify-content: center;
          align-items: center;
          .start {
            border-top: 3px solid rgb(50, 50, 50);
            border-style: dashed;
            width: 50%;
          }
          ion-icon {
            font-size: 35px;
            color: rgb(186, 122, 19) !important;
            cursor: pointer;
          }
          .end {
            border-top: 3px solid rgb(50, 50, 50);
            border-style: dashed;
            width: 50%;
          }
        }
      }
    }
  }

  .component-overlay {
    z-index: 1001;
    height: 100%;
    width: 100%;
    border-radius: 5px;
    position: absolute;
    cursor: pointer;
  }

  .selected {
    border-color: #f99e00;
    border-width: 1px;
    border-style: solid;
    border-radius: 3px 3px 0px 0px;

    .header {
      position: absolute;
      width: fit-content;
      background: #f99e00;
      color: #000000;
      border-color: #f99e00;
      border-width: 1px;
      border-style: solid;
      border-radius: 3px 3px 0px 0px;
      font-family: 'Roboto';
      font-weight: bold;
      line-height: 1.6;
      letter-spacing: 0.3px;
      text-align: left;
      margin-top: -24px;
      height: 24px;
      display: block;
      z-index: 1000;
    }
  
    .title {
      left: 0px;
      margin-left: -1px;
      padding: 2px 11px 16px 11px;
      font-size: 11px;
      z-index: 1000;
    }
  }

  .preview-parent-container {
    position: unset;
  }
}
