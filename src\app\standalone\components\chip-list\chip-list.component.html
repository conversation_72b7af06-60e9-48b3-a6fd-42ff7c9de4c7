<div class="chip-list-container" fxLayout="row">
  <!--TagDropDown-->
  @if (type === 'Dropdown') {
    <mat-chip-listbox #chipList>
      @for (item of listItems; track item) {
        <mat-chip-option>
          <div class="name">{{ item.name }}</div>
          @if (disableChipDelete !== true) {
            <button matChipRemove (click)="emitRemovedSelected(item, $event)">
              <mat-icon>cancel</mat-icon>
            </button>
          }
        </mat-chip-option>
      }
    </mat-chip-listbox>
  }

  <!--Org Networks-->
  @if (type === 'Organization Networks') {
    @if (parentOrgs.length > 0) {
      <h5>Parent Of:</h5>
      <mat-chip-listbox #chipList>
        @for (item of parentOrgs; track item) {
          <mat-chip-option>
            <div class="name">{{ item.name }}</div>
            @if (disableChipDelete !== true) {
              <button matChipRemove (click)="emitRemovedSelected(item, $event)">
                <mat-icon>cancel</mat-icon>
              </button>
            }
          </mat-chip-option>
        }
      </mat-chip-listbox>
    }
    @if (childOrgs.length > 0) {
      <h5>Child Of:</h5>
      <mat-chip-listbox #chipList>
        @for (item of childOrgs; track item) {
          <mat-chip-option>
            <div class="name">{{ item.name }}</div>
            @if (disableChipDelete !== true) {
              <button matChipRemove (click)="emitRemovedSelected(item, $event)">
                <mat-icon>cancel</mat-icon>
              </button>
            }
          </mat-chip-option>
        }
      </mat-chip-listbox>
    }
  }

  <!--CompleteContent-->
  @if (type === 'CompleteContent') {
    <mat-chip-listbox #chipList>
      @for (item of listItems; track item) {
        <mat-chip-option>
          <div class="complete-content-container">
            <div class="name">{{ item.name }}</div>
            <div class="sub-heading">{{ item.type }}</div>
          </div>
          @if (disableChipDelete !== true) {
            <button matChipRemove (click)="emitRemovedSelected(item, $event)">
              <mat-icon>cancel</mat-icon>
            </button>
          }
        </mat-chip-option>
      }
    </mat-chip-listbox>
  }

  <!--ChipOptionSelect-->
  @if (type === 'ChipOptionSelect') {
    <mat-chip-listbox #chipList>
      @for (item of listItems; track item) {
        <mat-chip-option>
          <div class="name">{{ item.value }}</div>
          @if (disableChipDelete !== true) {
            <button matChipRemove (click)="emitRemovedSelected(item, $event)">
              <mat-icon>cancel</mat-icon>
            </button>
          }
        </mat-chip-option>
      }
    </mat-chip-listbox>
  }
</div>
