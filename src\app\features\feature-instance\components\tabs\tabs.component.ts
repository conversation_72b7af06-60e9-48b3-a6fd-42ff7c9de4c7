import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, signal, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatTabChangeEvent, MatTabGroup } from '@angular/material/tabs';
import { ActivatedRoute } from '@angular/router';
import { IFeatureTab, IInstance, IRouteParams } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { ViewType } from '@app/core/enums/view-type';
import { AlertService } from '@app/core/services/alert-service';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RolesService } from '@app/core/services/roles.service';
import { UnsavedChangesGuard } from '@app/core/services/unsaved-changes.guard';
import { TabFilterPipe } from '@app/shared/pipes/tab-filter';
import { debounceTime, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-tabs',
    templateUrl: './tabs.component.html',
    styleUrls: ['./tabs.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class TabsComponent implements OnInit, OnDestroy {
  @ViewChild('matTabGroup') tabGroup: MatTabGroup;
  @Input() featureTabs: IFeatureTab[];
  @Input() instance: IInstance;
  @Input() routeParams: IRouteParams;
  @Input() selectedIndex = signal(0);
  @Output() reloadSystemProperties = new EventEmitter();
  @Output() emitformChanged = new EventEmitter();
  @Output() instanceChanged = new EventEmitter();

  previousIndex = 0;
  formHasChanged: boolean;
  scrollingNativeElement: HTMLElement;
  componentDestroyed$: Subject<boolean> = new Subject();
  openPlayer = false;
  selectedUserId: string;
  tabChanged$ = new Subject<MatTabChangeEvent>();
  constructor(
    private route: ActivatedRoute,
    public alertService: AlertService,
    private tabFilterPipe: TabFilterPipe,
    private instanceService: InstanceService,
    private eventService: Events,
    private unsavedChangesGuard: UnsavedChangesGuard,
    private rolesService: RolesService
  ) {}

  ngOnInit(): void {
    // This comes from the people table....
    const storageUserId = sessionStorage.getItem('peopleTableSelectedUserId');
    if (storageUserId) {
      this.selectedUserId = storageUserId;
    }

    this.route.fragment.subscribe(fragment => {
      if (fragment) {
        this.selectedIndex.set(this.featureTabs.findIndex(i => i.tab.name.toLowerCase() === fragment!.toLowerCase()));
      }
      //initial load default(builder) for classroom
      else if (this.instance.feature.featureSlug == 'classroom') {
        const tabs = this.getTabs();
        this.selectedIndex.set(tabs.findIndex((x: IFeatureTab) => x.tab && x.tab.name.toLowerCase() === 'assignments'));
        if (tabs.find((x: IFeatureTab) => x.tab && x.tab.name.toLowerCase() === 'assignments')) {
          this.routeParams.tabName = 'assignments';
        }
        this.previousIndex = this.selectedIndex();
      }
    });

    this.instanceService.reload$.pipe(takeUntil(this.componentDestroyed$)).subscribe(tabName => {
      if (this.routeParams.tabName !== tabName && tabName) {
        this.routeParams.tabName = tabName;
        this.selectedIndex.set(this.getTabs().findIndex((x: IFeatureTab) => x.tab && x.tab.name.toLowerCase() === tabName!.toLowerCase()));
      }
      //classroom condition for edit button
      else if (!tabName && this.instance.feature.featureSlug == 'classroom') {
        this.selectedIndex.set(this.getTabs().findIndex((x: IFeatureTab) => x.type && x.type.name === 'Instance Builder'));
      }
      this.previousIndex = this.selectedIndex();
    });

    this.eventService.subscribe('openStudentResults', userId => {
      this.selectedUserId = userId;
      const index = this.getTabs().findIndex((x: IFeatureTab) => x.tab.name!.toLocaleLowerCase() === 'assignments');
      this.routeParams.instanceSlug = 'default';
      this.openPlayer = true;
      this.selectedIndex.set(index);
    });

    this.setFirstBuilderTab();

    this.tabChanged$.pipe(debounceTime(700)).subscribe((changeEvent: MatTabChangeEvent) => {
      this.setTabChangeLogic(changeEvent);
    });
  }

  onPlayer() {
    return this.routeParams.viewType === ViewType.Player;
  }

  setFirstBuilderTab() {
    //Index Default To The First InstanceBuilder Tab.
    if (this.routeParams?.featureSlug !== 'my-journey' && this.routeParams.viewType === ViewType.Builder) {
      const tabs = this.instance.isDefault === true ? this.featureTabs.filter(x => x.isDefaultInstanceTab === true) : this.featureTabs.filter(x => x.isDefaultInstanceTab !== true);
      if (!tabs) {
        return;
      }
      const tabIndex = tabs.findIndex(x => x.showTab === true && x.type?.name === 'Instance Builder');
      this.selectedIndex.set(tabIndex !== -1 ? tabIndex : 0);
      this.previousIndex = this.selectedIndex();
    } else if (this.routeParams.tabName) {
      const tabs = this.instance.isDefault === true ? this.featureTabs.filter(x => x.isDefaultInstanceTab === true) : this.featureTabs.filter(x => x.isDefaultInstanceTab !== true);
      if (!tabs) {
        return;
      }
      const tabIndex = this.getTabs().findIndex((x: any) => x.tab.name?.toLocaleLowerCase()?.replace(' ', '') === this.routeParams.tabName?.toLocaleLowerCase());
      this.selectedIndex.set(tabIndex !== -1 ? tabIndex : 0);
      this.previousIndex = this.selectedIndex();
    }
  }

  formChanged(formChanged: boolean) {
    this.formHasChanged = formChanged;
    this.emitformChanged.emit();
  }

  async onTabChanged(changeEvent: MatTabChangeEvent) {
    this.tabChanged$.next(changeEvent);
  }

  async setTabChangeLogic(changeEvent: MatTabChangeEvent) {
    this.selectedIndex.set(this.previousIndex === 0 && changeEvent.index === 0 ? 0 : changeEvent.index);
    if (changeEvent.index !== this.previousIndex) {
      if (!this.unsavedChangesGuard.canDeactivateVar) {
        this.selectedIndex.set(this.previousIndex);
        await this.alertService.leaveBuilderAlert('Leave the builder?', "Don't worry, your changes have been autosaved and published.").then(() => {
          this.unsavedChangesGuard.canDeactivateVar = true;
          this.selectedIndex.set(changeEvent.index);
          this.reloadSystemProperties.emit();
        });
      } else {
        this.previousIndex = changeEvent.index;
      }

      const selectedTab = this.getTabs()[this.selectedIndex()];
      if (selectedTab?.type?.name !== 'Instance Builder') {
        this.routeParams.viewType = !this.openPlayer ? ViewType.Grid : ViewType.Player;
        this.routeParams.tabName = selectedTab.tab.name;
        this.instanceService.openInstance(
          this.routeParams.featureSlug,
          this.instanceService.isValidGUID(this.routeParams.instanceSlug ?? '') === false ? null : this.routeParams.instanceSlug,
          selectedTab.tab.name,
          !this.openPlayer ? 'grid' : 'player'
        );
      }
      this.openPlayer = false;
    }
  }

  hasEditAccess() {
    const actions = this.instance.feature?.featureTabs?.flatMap(x => x?.featureTabEditActions);
    return !actions || actions?.length === 0 ? this.rolesService.hasFeatureRoleAccess([ActionTypes.Publish]) : this.rolesService.hasFeatureRoleAccess(actions.map(x => x.actionBw));
  }

  getTabs() {
    return this.tabFilterPipe.transform(this.instance?.feature.featureTabs, this.instance?.isDefault ?? false);
  }

  instanceUpdated() {
    this.instanceChanged.emit(null);
  }

  ngOnDestroy() {
    this.eventService.unsubscribe('openStudentResults');
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
