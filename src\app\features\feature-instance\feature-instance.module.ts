import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ComponentRowSelectorModule } from '@app/features-shared/component-row-selector/component-row-selector.module';
import { FeatureInstanceSidePanelBuilderModule } from '@app/features-shared/feature-instance-side-panel-builder/feature-instance-side-panel-builder.module';
import { FeatureInstanceViewerModule } from '@app/features-shared/feature-instance-viewer/feature-instance-viewer.module';
import { RowInstanceModule } from '@app/features-shared/row-instance/row-instance.module';
import { BuilderService } from '../feature-repository-builder/services/builder-service';
import { featureComponents, standaloneComponents } from './feature-instance.declarations';
import { ROUTES } from './feature-instance.routes';
import { ParseContentPipe } from '@app/shared/pipes/parse-content';
import { TabFilterPipe } from '@app/shared/pipes/tab-filter';
import { SharedModule } from '@app/shared/shared.module';

@NgModule({
  declarations: [...featureComponents],
  imports: [...standaloneComponents, SharedModule, RowInstanceModule, ComponentRowSelectorModule, FeatureInstanceViewerModule, FeatureInstanceSidePanelBuilderModule, RouterModule.forChild(ROUTES)],
  exports: [...featureComponents],
  providers: [BuilderService, TabFilterPipe],
})
export class FeatureInstanceModule {}
