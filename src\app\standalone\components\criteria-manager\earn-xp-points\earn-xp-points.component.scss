.parent-container {
  ion-row {
    ion-col {
      display: flex;
      align-items: center;
    }

    .heading-col {
      padding: 10px;
    }

    .min-value {
      ion-input {
        border: 1px solid #4e4e4e;
        border-radius: 5px;
        color: white;
        font-size: 16px;
        --background: #242323;
        text-overflow: ellipsis !important;
      }
    }

    .xp {
      margin-left: 10px;
    }
  }
}
