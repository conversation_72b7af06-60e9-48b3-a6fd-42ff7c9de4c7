import { Component, Input, OnDestroy, OnInit, forwardRef } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, NG_VALIDATORS, NG_VALUE_ACCESSOR, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IBulletList } from '@app/core/contracts/contract';
import { ItemReorderEventDetail, IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { NgClass } from '@angular/common';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';

@Component({
    selector: 'app-bullet-list-control',
    templateUrl: './bullet-list-control.component.html',
    styleUrls: ['./bullet-list-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => BulletListControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => BulletListControlComponent),
        },
    ],
    imports: [NgClass, IonicModule, FormsModule, ReactiveFormsModule]
})
export class BulletListControlComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override label: string;
  @Input() description: string;
  @Input() listType: string;
  @Input() showSelected = false;
  @Input() sidePanelPadding = false;
  itemList: IBulletList[] = [];
  itemListForm: UntypedFormGroup;
  itemFormArray = new FormArray<any>([]);
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private formbuilder: FormBuilder) {
    super();
  }

  ngOnInit() {
    this.fieldValueChanged.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.textValue) {
        this.itemList = JSON.parse(this.textValue) as IBulletList[];
      }

      if (!this.itemList.length) {
        this.itemList.push({ sortOrder: 0, text: '' } as IBulletList);
      }

      this.createForm();
      this.formChanges();
    });
  }

  createForm() {
    this.itemListForm = this.formbuilder.group({
      items: this.itemFormArray,
    });

    for (const item of this.itemList) {
      this.addControlToForm(item);
    }
  }

  addControlToForm(item: IBulletList) {
    this.itemFormArray.push(
      this.formbuilder.group({
        text: [item.text],
        sortOrder: [item.sortOrder],
      })
    );
  }

  handleReorder(event: CustomEvent<ItemReorderEventDetail>) {
    const currentItem = this.itemFormArray.at(event.detail.from);
    this.itemFormArray.removeAt(event.detail.from);
    this.itemFormArray.insert(event.detail.to, currentItem);
    this.reorder();
    event.detail.complete();
  }

  reorder() {
    for (let i = 0; i < this.itemFormArray.length; i++) {
      const item = this.itemFormArray.at(i);
      item.patchValue({ sortOrder: i }, { emitEvent: false });
    }
  }

  add() {
    const nextIndex = this.itemFormArray.length;
    const newItem = { sortOrder: nextIndex, text: `` } as IBulletList;
    this.addControlToForm(newItem);
  }

  remove(index: number) {
    this.itemFormArray.removeAt(index);
    this.reorder();
  }

  formChanges() {
    this.itemListForm.valueChanges.subscribe(() => {
      const reorderedList = this.itemFormArray.getRawValue();
      this.updateList(reorderedList as IBulletList[]);
    });
  }

  updateList(listIn: IBulletList[]) {
    listIn = listIn.filter(x => x.text !== '').map((item, index) => ({ ...item, sortOrder: index + 1 }));
    this.setValue(JSON.stringify(listIn));
  }

  override setValue(value: string) {
    this.writeValue(value);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
