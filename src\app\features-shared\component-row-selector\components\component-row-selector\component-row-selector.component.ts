import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IInstance, IInstanceSection, IInstanceSectionComponent, IRouteParams } from '@app/core/contracts/contract';
import { ComponentType } from '@app/core/enums/component-type.enum';
import { ViewType } from '@app/core/enums/view-type';
import { ActionTypes } from '@app/core/enums/action-types.enum';

@Component({
    selector: 'app-component-row-selector',
    templateUrl: './component-row-selector.component.html',
    styleUrls: ['./component-row-selector.component.scss'],
    standalone: false
})
export class ComponentRowSelectorComponent {
  @Input() instance: IInstance;
  @Input() instanceSectionComponent: IInstanceSectionComponent;
  @Input() instanceSection: IInstanceSection;
  @Input() routeParams: IRouteParams;
  @Input() searchFilter: string;
  @Input() sidePanelPadding = false;
  @Input() isPlayerSidePanel = false;
  @Input() onlyRows = false;
  @Input() selectedUserId: string;
  @Input() isEducator = false;
  @Input() playerSidePanel = false;
  @Input() continuousFeedback: boolean;
  @Input() actionBw: ActionTypes | undefined;
  @Output() updateInstanceComponentValue = new EventEmitter<string>();
  @Output() triggerCompletionCheck = new EventEmitter<any>();
  @Output() rowContentSelected = new EventEmitter<{ event: string; actionBw: ActionTypes | undefined }>();
  componentType = ComponentType;
  viewTypes = ViewType;

  constructor() {}

  setNewValue(valueIn: string) {
    this.updateInstanceComponentValue.emit(valueIn);
  }

  selectedRowContent(rowId: string, actionBw?: ActionTypes) {
    this.rowContentSelected.next({ event: rowId, actionBw: actionBw });
  }

  triggerSectionCompletionCheck() {
    this.triggerCompletionCheck.next(true);
  }
}
