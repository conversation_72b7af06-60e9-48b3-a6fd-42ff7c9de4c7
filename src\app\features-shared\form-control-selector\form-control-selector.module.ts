import { NgModule } from '@angular/core';
import { RowInstanceModule } from '@app/features-shared/row-instance/row-instance.module';
import { SharedModule } from '@app/shared/shared.module';
import { NetworkManagerModule } from '../network-manager/network-manager.module';
import { QuestionBuilderModule } from '../question-builder/question-builder.module';
import { QuestionModule } from '../question/question.module';
import { featureComponents, standaloneComponents } from './form-control-selector.declarations';
import { GradingTableComponent } from '../../standalone/components/grading-table/grading-table.component';

@NgModule({
  declarations: [...featureComponents],
  imports: [...standaloneComponents, SharedModule, RowInstanceModule, QuestionBuilderModule, QuestionModule, QuestionBuilderModule, NetworkManagerModule],
  exports: [...featureComponents],
})
export class FormControlSelectorModule {}
