<ion-row>
  <ion-col size="2.75" class="item">
    <ion-label position="stacked">Text</ion-label>
    <ion-input [(ngModel)]="featureTabButton.buttonText"></ion-input>
  </ion-col>
  <ion-col size="2.75" class="item">
    <ion-label position="stacked">Action</ion-label>
    @if (buttonLinkTypes$ | async; as buttonActions) {
      <ion-select style="--background-color:{{ backgroundColor }};" interface="popover" [(ngModel)]="featureTabButton.buttonLinkType" [compareWith]="linkCompareWith">
        @for (action of buttonActions; track action) {
          <ion-select-option [value]="action">
            {{ action.name }}
          </ion-select-option>
        }
      </ion-select>
    }
  </ion-col>
  <ion-col size="2.75" class="item">
    <ion-label position="stacked">Feature</ion-label>
    <ng-container>
      @if (features$ | async; as features) {
        <ion-select style="--background-color:{{ backgroundColor }};" interface="popover" [(ngModel)]="featureTabButton.referenceId">
          @for (feature of features; track feature) {
            <ion-select-option [value]="feature.id">
              {{ feature.code }}
            </ion-select-option>
          }
        </ion-select>
      }
    </ng-container>
  </ion-col>
  <ion-col size="2.75" class="item">
    <ion-label position="stacked">Action</ion-label>
    @if (actions$ | async; as actions) {
      <ion-select
        style="--background-color:{{ backgroundColor }};"
        interface="popover"
        multiple="true"
        [compareWith]="compareWith"
        (ionChange)="setButtonActions($event)"
        [value]="selectedTabButtonActions">
        @for (item of actions; track item) {
          <ion-select-option [value]="item">
            {{ item.name }}
          </ion-select-option>
        }
      </ion-select>
    }
  </ion-col>
  <ion-col size="1" class="center-col">
    <ion-button fill="clear" color="light" (click)="delete()">
      <ion-icon name="remove-circle-outline"></ion-icon>
    </ion-button>
  </ion-col>
</ion-row>
