import { Component, EventEmitter, Input, Output } from '@angular/core';
import { <PERSON><PERSON>aniti<PERSON>, SafeResourceUrl } from '@angular/platform-browser';
import { IComponent, IInstance, IInstanceSection, IInstanceSectionComponent, IRouteParams, ISystemProperty } from '@app/core/contracts/contract';
import { ComponentType } from '@app/core/enums/component-type.enum';
import { ViewType } from '@app/core/enums/view-type';
import { InstanceService } from '@app/core/services/instance-service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { ActionTypes } from '@app/core/enums/action-types.enum';

@Component({
    selector: 'app-component-selector',
    templateUrl: './component-selector.component.html',
    styleUrls: ['./component-selector.component.scss'],
    standalone: false
})
export class ComponentSelectorComponent {
  @Input() instance: IInstance;
  @Input() instanceSection: IInstanceSection;
  @Input() continuousFeedback: boolean;
  @Input() instanceSectionComponent: IInstanceSectionComponent;
  @Input() routeParams: IRouteParams;
  @Input() searchFilter: string;
  @Input() builderPreviewView = false;
  @Input() selectedUserId: string;
  @Input() isEducator = false;
  @Input() actionBw: ActionTypes | undefined;
  @Output() triggerCompletionCheck = new EventEmitter<any>();
  @Output() updateInstanceComponentValue = new EventEmitter<string>();
  componentType = ComponentType;
  viewTypes = ViewType;

  constructor(
    private builderService: BuilderService,
    private systemPropertyService: SystemPropertiesService,
    private sanitizer: DomSanitizer,
    private instanceService: InstanceService
  ) {}

  getSystemPropertyValue(systemProperty: ISystemProperty | undefined) {
    if (systemProperty) {
      const systemPropertyValue = this.systemPropertyService.getSystemPropertyValue(systemProperty);
      if (systemPropertyValue != null) {
        return systemPropertyValue;
      }
    }

    return null;
  }

  triggerSectionCompletionCheck() {
    this.triggerCompletionCheck.next(true);
  }

  setInheritedComponents() {
    if (this.instanceSectionComponent.component != null) {
      if (this.instanceSectionComponent.component?.templateField?.systemProperty != null) {
        let matchingSystemProperty = this.systemPropertyService.instanceProperties.find(x => x.id === this.instanceSectionComponent.component.templateField.systemProperty?.id)?.value;

        if (this.instanceSectionComponent.component.templateField.systemProperty.type.title === 'User') {
          matchingSystemProperty = this.systemPropertyService.userProperties.find(x => x.id === this.instanceSectionComponent.component.templateField.systemProperty?.id)?.value;
        }

        if (this.instanceSectionComponent.component.templateField.systemProperty.type.title === 'Product') {
          matchingSystemProperty = this.systemPropertyService.productProperties.find(x => x.id === this.instanceSectionComponent.component.templateField.systemProperty?.id)?.value;
        }

        if (this.instanceSectionComponent.component.templateField.systemProperty.type.title === 'Feature') {
          matchingSystemProperty = this.systemPropertyService.featureProperties.find(x => x.id === this.instanceSectionComponent.component.templateField.systemProperty?.id)?.value;
        }

        if (this.instanceSectionComponent.component.templateField.systemProperty.type.title === 'Organization') {
          matchingSystemProperty = this.systemPropertyService.organizationProperties.find(x => x.id === this.instanceSectionComponent.component.templateField.systemProperty?.id)?.value;
        }

        if (this.instanceSectionComponent.component.templateField.systemProperty.type.title === 'Communication') {
          matchingSystemProperty = this.systemPropertyService.communicationProperties.find(x => x.id === this.instanceSectionComponent.component.templateField.systemProperty?.id)?.value;
        }

        if (this.instanceSectionComponent.component.templateField.systemProperty.type.title === 'Network') {
          matchingSystemProperty = this.systemPropertyService.networkProperties.find(x => x.id === this.instanceSectionComponent.component.templateField.systemProperty?.id)?.value;
        }

        if (this.instanceSectionComponent.component.templateField.systemProperty.type.title === 'Campaign') {
          matchingSystemProperty = this.systemPropertyService.campaignProperties.find(x => x.id === this.instanceSectionComponent.component.templateField.systemProperty?.id)?.value;
        }

        this.instanceSectionComponent.component.templateField.systemProperty.value = matchingSystemProperty;
      }
    }
  }

  getSafeUrl(url: string | undefined): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(`${url}`);
  }

  setNewValue(valueIn: string) {
    this.updateInstanceComponentValue.emit(valueIn);
  }

  getId() {
    return this.instanceService.isValidGUID(this.routeParams.instanceSlug ?? '') ? this.routeParams.instanceSlug : this.instance.id;
  }

  componentSelected(comp: IComponent) {
    this.builderService.selectedComponent$.next(comp);
  }
}
