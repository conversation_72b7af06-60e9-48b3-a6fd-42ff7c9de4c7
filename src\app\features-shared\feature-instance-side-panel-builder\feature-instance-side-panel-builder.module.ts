import { DragDropModule } from '@angular/cdk/drag-drop';
import { NgModule } from '@angular/core';
import { SharedModule } from '@app/shared/shared.module';
import { ComponentRowSelectorModule } from '../component-row-selector/component-row-selector.module';
import { ComponentTemplatefieldBuilderModule } from '../component-templatefield-builder/component-templatefield-builder.module';
import { FormControlSelectorModule } from '../form-control-selector/form-control-selector.module';
import { featureComponents, standaloneComponets } from './feature-instance-side-panel-builder.declarations';

@NgModule({
  declarations: [...featureComponents],
  imports: [...standaloneComponets, SharedModule, ComponentRowSelectorModule, FormControlSelectorModule, DragDropModule, ComponentTemplatefieldBuilderModule],
  exports: [...featureComponents],
})
export class FeatureInstanceSidePanelBuilderModule {}
