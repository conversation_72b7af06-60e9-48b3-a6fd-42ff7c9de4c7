.breadcrumb-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;

  ion-breadcrumbs {
    padding-left: 12px;
    background-color: #333;
    border-radius: 6px;
    padding-right: 12px;
    font-size: 0.8em !important;
    letter-spacing: 0.03em;
    border: 0.5px solid #555555;
    margin-top: 10px;
    margin-left: 2px;
    color: #ccc;
    padding-top: 1px;
  }

  ion-breadcrumb {
    cursor: pointer;
    line-height: 1 !important;
  }

  ion-breadcrumb::part(separator) {
    display: none !important;
  }

  ion-breadcrumb::part(native) {
    padding-inline: 0px !important;
  }

  .header-included::part(native) {
    padding-inline: 0px !important;
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .forward-slash {
    margin: 4px 7.5px;
  }

  .back-btn {
    border: 2px solid #848484;
    margin-left: 0px;
    height: 40px;
    width: 40px;
    border-radius: 25px;

    ion-icon {
      width: 20px;
      height: 20px;
    }
  }
  
  .back-btn-large {
    margin-left: 0px;
    width: 40px;
    height: 40px;
    border: 2px solid #848484;
    border-radius: 25px;
    
    ion-icon {
      width: 20px;
      height: 20px;
    }
  }
  
  @media (max-width: 960px) {
    .breadcrum-margin {
      margin-top: 10px;
    }
    
    ion-breadcrumb {
      font-size: 0.8em !important;
    }
  }
  
  @media (max-width: 500px) {
    .breadcrumb-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100px;
    }
  }

  @media (max-width: 400px) {
    .breadcrumb-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50px;
    }
  }
}

@media (max-width: 960px) {
  .breadcrumb-container {
    margin-top: 10px;
  }
}

.no-header-container {
  align-items: center;
  margin-top: 10px;
}

.no-header-breadcrumbs {
  margin-top: 0 !important;
}