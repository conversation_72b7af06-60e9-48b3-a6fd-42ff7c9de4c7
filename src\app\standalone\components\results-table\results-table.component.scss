.view-options-row {
  margin-bottom: 10px;
  justify-content: flex-end;
  align-items: flex-end;
  .top-header {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.users-table {
  .table-container {
    display: flex;
    flex-direction: column;
    align-content: stretch;
    align-items: stretch;

    .table-grid {
      flex: 1;
      width: 100%;
      background-color: #444444;
    }

    .mat-mdc-header-cell {
      color: white;
      background-color: rgba(34, 34, 34) !important;
      border-right: 1px solid rgba(155, 152, 152, 0.5);
      padding-left: 15px;
    }
    .mat-column-select {
      background-color: rgba(42, 42, 42);
      width: 40px;
    }
    .mat-column-id {
      background-color: rgba(42, 42, 42);
      width: 100px;
    }
    .mat-column-name {
      background-color: rgba(42, 42, 42);
      width: 180px;
    }
    .mat-column-progress {
      width: 150px;
    }
    .mat-column-grade {
      width: 150px;
    }
    .mat-column-controls {
      width: 200px;
    }
    .mat-mdc-cell {
      color: white;
      max-width: 200px;
      padding-left: 15px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .button-container {
      ion-button {
        color: black;
        --background: gray;
      }
    }
  }

  .load-more {
    font-size: 16px;
    color: white;
    margin-top: 20px;
    text-align: center;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }
}
