:host {
  height: 100%;
  background-color: #181818;
  color: white;
  position: relative;

  .video-header-min {
    aspect-ratio: 18 / 1 !important;
  }

  .video-header {
    aspect-ratio: 3 / 1;

    .container {
      max-height: 100% !important;
    }

    .bg-video {
      position: absolute;
      width: 100%;
      min-width: 100%;
      left: 0px;
      top: 0px;
    }

    ion-row {
      position: absolute !important;
      bottom: 10px;
    }
  }

  .image-container {
    border-bottom: 2px solid rgb(68, 68, 68);
    overflow: hidden;
    position: relative;
  }

  @media (min-width: 959px) {
    .image-container {
      background-image: linear-gradient(to bottom, rgba(30, 30, 30, 0) 0%, rgba(30, 30, 30, 0.8) 75%, rgba(30, 30, 30, 0.95) 88.5148948598%, #1e1e1e 100%), var(--background-image);
      background-color: #000;
      background-size: cover;
      background-color: #111;
      background-position: center;

      .page-margins {
        height: 100%;
        margin-left: var(--page-margin-left-header);
        margin-right: var(--page-margin-right-header);
        display: flex;
        justify-content: center;
      }

      .page-margins-player {
        height: 100%;
        margin-left: var(--page-margin-left-player-header);
        margin-right: var(--page-margin-right-player-header);
        display: flex;
        justify-content: center;
      }

      .container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        min-height: 25px;
        overflow: hidden;
        margin-top: 8px;

        .icon-col-main-org {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #fff;
          border-radius: 3px;
          margin-right: 2vw;
          margin-bottom: 15px;
        }

        .icon-col-org {
          min-width: 8vw;
          min-height: 8vw;
          max-height: 8vw;
          max-width: 8vw;
          padding: 10px;
          aspect-ratio: 1/7;

          .icon-org {
            object-fit: contain;
            border-radius: 2px;
            max-height: 100%;
          }
        }

        .icon-col-min-org {
          min-width: 4vw;
          min-height: 4vw;
          max-height: 5vw;
          max-width: 5vw;

          .icon-org {
            object-fit: contain;
            border-radius: 2px;
            max-height: 100%;
          }
        }

        .icon-col-main {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #fff;
          border-radius: 3px;
          margin-right: 2vw;
          margin-bottom: 15px;
        }

        .icon-col {
          min-width: 8vw;
          min-height: 8vw;
          max-height: 8vw;
          max-width: 8vw;

          .icon {
            object-fit: contain;
            border-radius: 2px;
          }
        }

        .icon-col-min {
          min-width: 4vw;
          min-height: 4vw;
          max-height: 5vw;
          max-width: 5vw;

          .icon {
            object-fit: contain;
            border-radius: 2px;
          }
        }

        .header-container-parent {
          margin-left: calc(3.5vw - 15px);
        }

        .header-container {
          margin-top: auto;
          padding-bottom: 5px;

          .sub-heading {
            color: #cccccc;
            font-family: 'Roboto';
            font-weight: 400;
            font-size: 16px;
            font-style: italic;
            line-height: var(--general-line-height);
          }
        }

        .org {
          display: flex;
          height: 100%;
          justify-content: flex-end;
          padding-bottom: 15px;
          flex-direction: column;
        }

        ion-breadcrumb::part(native) {
          padding-inline: 0px !important;
        }

        h1 {
          font-size: 35px;
          bottom: 55px;
          left: 20px;
          font-family: 'Exo 2';
          font-weight: 800;
        }

        h6 {
          font-size: 18px;
          left: 20px;
          margin: 0px;
          font-family: 'Roboto';
          font-weight: 500;
          line-height: 1.4;
          color: #ccc;
          width: 80%;
        }
      }
    }

    .content-no-header {
      height: calc(100vh - 92px);
    }
  }

  @media (max-width: 960px) {
    /* place here CSS targeted at iPads and smaller, including iPhones and small hand-held devices */
    .image-container {
      background-image: linear-gradient(to bottom, rgba(30, 30, 30, 0) 0%, rgba(30, 30, 30, 0.8) 75%, rgba(30, 30, 30, 0.95) 88.5148948598%, #1e1e1e 100%), var(--background-image);
      background-color: #000;
      background-size: cover;
      background-color: #111;
      min-height: 50px !important;
      background-position: center;

      .page-margins {
        height: 100%;
        margin-left: var(--page-margin-left-header);
        margin-right: var(--page-margin-right-header);
        display: flex;
        justify-content: center;
      }

      .page-margins-player {
        height: 100%;
        margin-left: var(--page-margin-left-player-header);
        margin-right: var(--page-margin-right-player-header);
        display: flex;
        justify-content: center;
      }

      .container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        max-height: 220px;
        min-height: 25px;
        overflow: hidden;
        margin-top: 8px;

        .icon-col-main-org {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #fff;
          border-radius: 3px;
          margin-right: 2vw;
          margin-bottom: 15px;
        }

        .icon-col-org {
          min-width: 75px;
          min-height: 75px;
          max-height: 75px;
          max-width: 75px;
          padding: 10px;
          aspect-ratio: 1/7;

          .icon-org {
            object-fit: contain;
            border-radius: 2px;
            max-height: 100%;
          }
        }

        .icon-col-min-org {
          min-width: 75px;
          min-height: 75px;
          max-height: 75px;
          max-width: 75px;

          .icon-org {
            object-fit: contain;
            border-radius: 2px;
            max-height: 100%;
          }
        }

        .icon-col-main {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #fff;
          border-radius: 3px;
          margin-right: 2vw;
          margin-bottom: 15px;
        }

        .icon-col {
          min-width: 8vw;
          min-height: 8vw;
          max-height: 8vw;
          max-width: 8vw;

          .icon {
            border-radius: 5px;
            background-size: contain;
          }
        }

        .icon-col-min {
          min-width: 60px;
          min-height: 60px;
          max-height: 8vw;
          max-width: 8vw;

          .icon {
            object-fit: contain;
            border-radius: 2px;
          }
        }

        .header-container-parent {
          margin-left: calc(3.5vw - 15px);
        }

        .header-container {
          margin-top: auto;
          padding-bottom: 5px;

          .sub-heading {
            color: #cccccc;
            font-family: 'Roboto';
            font-weight: 400;
            font-size: 16px;
            font-style: italic;
            line-height: var(--general-line-height);
          }
        }

        .org {
          display: flex;
          height: 100%;
          justify-content: flex-end;
          padding-bottom: 15px;
          flex-direction: column;
        }

        ion-breadcrumb::part(native) {
          padding-inline: 0px !important;
        }

        h1 {
          font-size: 24px;
          bottom: 55px;
          left: 20px;
          font-family: 'Exo 2';
          font-weight: 800;
        }

        h6 {
          font-size: 12px;
          left: 20px;
          margin: 0px;
          font-family: 'Roboto';
          font-weight: 500;
          line-height: 1.4;
          color: #ccc;
        }
      }
    }

    .content-no-header {
      height: calc(100vh - 92px);
    }
  }
}

.grid-view-color {
  background-color: #1e1e1e;
}

.forward-slash {
  margin-left: 15px;
}

ion-content::part(scroll) {
  overflow-y: hidden !important;
}

ion-breadcrumb {
  cursor: pointer;
}

.start-now-button {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.edit-button {
  position: absolute;
  right: 25px;
  top: 8px;
  z-index: 9999;
  display: flex;
  gap: 10px;

  .inner-container {
    margin: 0px;
    color: black;
    text-transform: none;
    border-radius: 3px;
    font-weight: 500;
    font-size: 18px;
    background-color: #f99e00;

    ion-icon {
      width: 18px;
      height: 18px;
      padding-left: 10px;
      margin-right: 10px;
      color: #000000;
    }
  }

  .share-button {
    background-color: white !important;
    color: black !important;
    font-size: 16px !important;
    width: 101px !important;
    font-family: 'Roboto' !important;

    mat-icon {
      color: black !important;
      margin-right: 8px;
      margin-left: 8px;
      width: 20px;
      height: 20px;
    }
  }

  .like-button {
    background-color: white !important;
    color: black !important;
    font-size: 16px !important;
    margin: auto;
    width: 100% !important;
    font-family: 'Roboto' !important;
    
    mat-icon {
      color: black !important;
      margin: auto;
    }
  }

  ion-button.inner-container::part(native) {
    line-height: normal;
    --padding-start: 0 !important;
    --padding-end: 10px !important;
  }
}

@media screen and (max-width: 960px) {
  .edit-button {
    margin-top: 15px;
  }

  .edit-button ion-button {
    min-height: 36px;
    --padding-top: 0px;
    --padding-bottom: 0px;
  }
}

.container-absolute {
  position: absolute;
  width: 100%;
  z-index: 999;
  padding-top: 10px;
  padding-left: 10px;
  display: grid;
}

@media screen and (max-width: 960px) {
  .container-absolute {
    padding-right: 15px;
  }
}

.center-row {
  position: absolute;
  display: flex;
  top: 0px;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  height: 100%;
  width: fit-content;
  justify-self: center;
  max-width: 100%;

  ion-segment {
    display: flex;
    width: fit-content;
    border-radius: 20px;
    background: rgba(68, 68, 68) !important;
    font-size: 16px;
    line-height: 1.1;
    padding-top: 5px;
    padding-bottom: 3px;
    padding-left: 11px;
    padding-right: 11px;
    border: 0.5px solid #333;

    ion-segment-button {
      width: fit-content !important;
      color: #ddd;

      ion-label {
        font-size: 16px;
        color: #ddd;
      }
    }

    ion-segment-button:nth-child(n + 2) {
      border-left: 1px solid #333333;
      border-bottom-left-radius: 0px;
      border-top-left-radius: 0px;
    }

    .segment-button-checked::part(native) {
      background: rgba(68, 68, 68) !important;
    }

    .segment-button-checked::part(indicator) {
      background: rgba(68, 68, 68) !important;
    }

    .segment-button-checked {
      --background: rgba(68, 68, 68) !important;
      --indicator-box-shadow: none !important;

      ion-label {
        font-weight: 900;
        color: var(--ion-color-primary);
      }
    }

    ion-segment-button:hover {
      ion-label {
        color: white !important;
      }
    }
  }
}

.center-row-padding {
  top: 25px;
}

@media screen and (max-width: 960px) {
  .center-row {
    max-width: 90%;
  }

  .center-row-fab {
    max-width: 75%;
  }
}

.like-button {
  ion-icon {
    font-size: 20px;
    margin-right: 5px;
    color: var(--ion-color-primary);
  }

  &.inner-container {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
}
