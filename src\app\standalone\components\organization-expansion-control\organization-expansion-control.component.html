<div class="organization-parent-container">
  <h1 class="component-header">Connected Organizations</h1>
  <ng-container>
    <mat-accordion multi>
      @for (organization of organizations; track organization) {
        <div>
          <mat-expansion-panel (opened)="openGroup(true, organization.id)" (closed)="openGroup(false, organization.id)">
            <mat-expansion-panel-header class="expansion-panel-header">
              <div class="inner-panel">
                <div class="heading">{{ organization.name }}</div>
                <div class="sub-heading">
                  <span>
                    {{ organization.addressLine1 }}
                    @if (organization.addressLine2) {
                      <span>- {{ organization.addressLine2 }}</span>
                    }
                  </span>
                </div>
              </div>
              <div class="inner-panel-role">
                <div class="role-container">
                  <div class="sub-heading">Your Role:</div>
                  <div class="heading">
                    @if (organization.orgUserRoleName) {
                      <span>
                        {{ organization.orgUserRoleName }}
                      </span>
                    }
                  </div>
                </div>
                <div class="button-container">
                  <ion-button color="warning" (click)="routeToOrg(organization.id)">View</ion-button>
                </div>
              </div>
            </mat-expansion-panel-header>
            @if (organization?.panelState === true) {
              <div class="panel-body-container">
                <div class="org-permissions-data-container">
                  <h1>Permissions</h1>
                  <h2>THIS ORGANIZATION CAN:</h2>
                  <ul class="privacy-types-list">
                    <li>Update your personal information.</li>
                    <li>Manage your personal data and privacy settings.</li>
                    <li>Manage the features that you have access to.</li>
                  </ul>
                </div>
                <div class="user-data-parent-container">
                  @if (organization.claimUser) {
                    <div class="user-permissions-data-container">
                      <h2>YOU CAN:</h2>
                      <ul class="privacy-types-list">
                        <li>Access products and features that this organization has purchased.</li>
                        <li>Access content created by other users at the organization.</li>
                        @if (checkRoleAndAccess(organization.claimUser.roleName, organization.claimUser.accessTypeBw)) {
                          <li>Add users at the organization.</li>
                        }
                        @if (checkRoles(organization.claimUser.roleName)) {
                          <li>Manage personal data for users at this organization.</li>
                        }
                        @if (checkRoles(organization.claimUser.roleName)) {
                          <li>Manage the org info and products on behalf of the organization.</li>
                        }
                        @if (checkWriteAccess(organization.claimUser.accessTypeBw)) {
                          <li>Create content on behalf of the organization.</li>
                        }
                        @if (checkPublishAccess(organization.claimUser.accessTypeBw)) {
                          <li>Publish content on behalf of the organization.</li>
                        }
                      </ul>
                    </div>
                    <div class="party-data-container">
                      <h1>Third Party Data</h1>
                      <p>
                        The following data was provided and is managed by
                        <b>{{ organization.name }}</b>
                        .Contact an account admin to update this.
                      </p>
                      <h2>ACCOUNT INFO:</h2>
                      <ul class="account-info-list">
                        @if (organization.claimUser.firstName) {
                          <li>
                            Name: <span class="li-text">{{ organization.claimUser.firstName }}</span>
                          </li>
                        }
                        @if (organization.claimUser.email) {
                          <li>
                            Email: <span class="li-text">{{ organization.claimUser.email }}</span>
                          </li>
                        }
                        @if (organization.claimUser.phoneNumber) {
                          <li>
                            Phone: <span class="li-text">{{ organization.claimUser.phoneNumber }}</span>
                          </li>
                        }
                        @if (organization.claimUser.roleName) {
                          <li>
                            Role: <span class="li-text">{{ organization.claimUser.roleName }}</span>
                          </li>
                        }
                        @if (organization.claimUser.grade) {
                          <li>
                            Grade: <span class="li-text">{{ organization.claimUser.grade }}</span>
                          </li>
                        }
                      </ul>
                    </div>
                  }
                </div>
              </div>
            }
          </mat-expansion-panel>
          @if (moreResults) {
            <div (click)="getOrganizationsById(id, true)" class="load-more">
              <ion-row>
                <ion-col size="12">
                  <div>Load More</div>
                  <div><ion-icon name="chevron-down-outline"></ion-icon></div>
                </ion-col>
              </ion-row>
            </div>
          }
        </div>
      }
    </mat-accordion>
  </ng-container>
</div>
