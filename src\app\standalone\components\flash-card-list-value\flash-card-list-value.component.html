<div class="parent-container">
  @if (flashCardItemList.length > 0) {
    @for (item of flashCardItemList; track item) {
      <ion-card (click)="setSelectedCard(item)" [ngClass]="item.selected ? 'card-selected' : {}">
        @if (type !== 'Picture Flash Card') {
          <div class="inner-container">
            <div class="heading">{{ item.title }}</div>
            @if (item.selected) {
              <div class="description">{{ item.description }}</div>
            }
          </div>
        }
        @if (type === 'Picture Flash Card') {
          <div class="inner-container">
            @if (!item.selected) {
              <img class="image" [src]="setImage(item.title)" />
            }
            @if (item.selected) {
              <div class="description">{{ item.description }}</div>
            }
          </div>
        }
        <footer class="card-footer"><ion-icon name="sync-outline"></ion-icon></footer>
      </ion-card>
    }
  }
</div>
