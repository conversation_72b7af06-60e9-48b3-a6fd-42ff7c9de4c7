import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent, IQuestionIn } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-assessment-block-editor',
    templateUrl: './assessment-block-editor.component.html',
    styleUrls: ['./assessment-block-editor.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class AssessmentBlockEditorComponent implements OnInit, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  @Output() questionUpdated: EventEmitter<IQuestionIn> = new EventEmitter();
  assessmentForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;
  grades: KeyValue[];

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  ngOnInit() {
    if (!this.assessmentForm) {
      this.createForm();
    } else {
      this.setFormValues();
    }
  }

  setTypeBwValues(typeBw: number | undefined) {
    if (typeBw !== undefined) {
      return this.builderService.allFormats.filter(x => Number(x.id) & typeBw).map(x => x.id);
    }
    return null;
  }

  createForm() {
    this.assessmentForm = this.formBuilder.group({
      label: [this.component.templateField.label, Validators.required],
      helpDescription: [this.component.templateField.helpDescription],
      caption: [this.component?.templateField?.helpTitle],
      placeHolderText: [this.component?.templateField?.placeHolderText],
      grade: [],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component.templateField?.isViewField ?? true],
      rowNumber: [this.component?.builderRowNumber],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      isRetake: [this.component.templateField.isRetake],
      isBlockRequired: [this.component?.templateField?.isBlockRequired],
      percentageToComplete: [this.component?.templateField?.percentageToComplete],
      isAuthorRequired: [this.component?.templateField?.isAuthorRequired],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      limitTo: [this.component?.templateField?.limitTo],
      fileTypeBw: [this.setTypeBwValues(this.component?.templateField?.fileTypeBw), this.component.componentType.name === 'File Upload' ? Validators.required : null],
      minFileSize: [this.component?.templateField?.minFileSize],
      maxFileSize: [this.component?.templateField?.maxFileSize],
      useMaxWidth: [this.component?.templateField?.useMaxWidth],
    });
    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.assessmentForm) {
      return;
    }

    this.assessmentForm.controls.label.setValue(this.component.templateField.label);
    this.assessmentForm.controls.helpDescription.setValue(this.component.templateField.helpDescription);
    this.assessmentForm.controls.caption.setValue(this.component.templateField.helpTitle);
    this.assessmentForm.controls.placeHolderText.setValue(this.component.templateField.placeHolderText);
    this.assessmentForm.controls.grade.setValue('');
    this.assessmentForm.controls.isRequiredField.setValue(this.component.templateField?.isRequiredField ?? false);
    this.assessmentForm.controls.isPreviewField.setValue(this.component.templateField?.isPreviewField ?? true);
    this.assessmentForm.controls.isBuilderEnabled.setValue(this.component.templateField?.isBuilderEnabled ?? true);
    this.assessmentForm.controls.isHoverField.setValue(this.component.templateField?.isHoverField ?? false);
    this.assessmentForm.controls.isViewField.setValue(this.component.templateField?.isViewField ?? true);
    this.assessmentForm.controls.rowNumber.setValue(this.component?.builderRowNumber);
    this.assessmentForm.controls.hoverSortOrder.setValue(this.component?.hoverSortOrder);
    this.assessmentForm.controls.instanceSortOrder.setValue(this.component?.instanceSortOrder);
    this.assessmentForm.controls.isRetake.setValue(this.component.templateField.isRetake);
    this.assessmentForm.controls.isBlockRequired.setValue(this.component.templateField.isBlockRequired);
    this.assessmentForm.controls.isAuthorRequired.setValue(this.component.templateField.isAuthorRequired);
    this.assessmentForm.controls.percentageToComplete.setValue(this.component.templateField.percentageToComplete);
    this.assessmentForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.assessmentForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.assessmentForm.controls.limitTo.setValue(this.component?.templateField?.limitTo);
    this.assessmentForm.controls.fileTypeBw.setValue(this.setTypeBwValues(this.component.templateField.fileTypeBw));
    this.assessmentForm.controls.minFileSize.setValue(this.component?.templateField?.minFileSize);
    this.assessmentForm.controls.maxFileSize.setValue(this.component?.templateField?.maxFileSize);
    this.assessmentForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.assessmentForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.assessmentForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.assessmentForm.valid) {
      this.component.templateField.label = this.assessmentForm.controls.label.value;
      this.component.templateField.helpDescription = this.assessmentForm.controls.helpDescription.value;
      this.component.templateField.placeHolderText = this.assessmentForm.controls.placeHolderText.value;
      this.component.templateField.helpTitle = this.assessmentForm.controls.caption.value;
      this.component.templateField.isRequiredField = this.assessmentForm.controls.isRequiredField.value;
      this.component.templateField.isPreviewField = this.assessmentForm.controls.isPreviewField.value;
      this.component.templateField.isBuilderEnabled = this.assessmentForm.controls.isBuilderEnabled.value;
      this.component.templateField.isHoverField = this.assessmentForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.assessmentForm.controls.isViewField.value;
      this.component.templateField.isBlockRequired = this.assessmentForm.controls.isBlockRequired.value;
      this.component.templateField.isAuthorRequired = this.assessmentForm.controls.isAuthorRequired.value;
      this.component.templateField.percentageToComplete = this.assessmentForm.controls.percentageToComplete.value;
      this.component.templateField.colspan = this.assessmentForm.controls.colspan.value;
      this.component.templateField.colNumber = this.assessmentForm.controls.colNumber.value;
      this.component.templateField.limitTo = this.assessmentForm.controls.limitTo.value ?? 0;
      if (Array.isArray(this.assessmentForm.controls.fileTypeBw.value)) {
        this.component.templateField.fileTypeBw = this.assessmentForm.controls.fileTypeBw.value.reduce((a: number, b: number) => a + b, 0);
      } else {
        this.component.templateField.fileTypeBw = this.assessmentForm.controls.fileTypeBw.value;
      }
      this.component.templateField.minFileSize = this.assessmentForm.controls.minFileSize.value;
      this.component.templateField.maxFileSize = this.assessmentForm.controls.maxFileSize.value;

      this.component.builderRowNumber = this.assessmentForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.assessmentForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.assessmentForm.controls.instanceSortOrder.value;
      this.component.templateField.isRetake = this.assessmentForm.controls.isRetake.value;
      this.component.templateField.useMaxWidth = this.assessmentForm.controls.useMaxWidth.value;
    }
  }

  checkPercentageInputLength(event: any) {
    const numberVal = Number(event.detail.value);
    if (numberVal >= 100) {
      this.assessmentForm.controls.percentageToComplete.setValue(100);
    } else if (numberVal < 0) {
      this.assessmentForm.controls.percentageToComplete.setValue(0);
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
