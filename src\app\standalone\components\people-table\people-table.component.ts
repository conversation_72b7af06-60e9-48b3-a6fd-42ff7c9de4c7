import { animate, state, style, transition, trigger } from '@angular/animations';
import { SelectionModel } from '@angular/cdk/collections';
import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatCheckbox } from '@angular/material/checkbox';
import { <PERSON><PERSON><PERSON>, MatSortHeader } from '@angular/material/sort';
import { MatCell, MatCellDef, MatColumnDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatRow, MatRowDef, MatTable, MatTableDataSource } from '@angular/material/table';
import { IHeading, IInstance, IInstanceUserIn, IPeople, IUser, IUserClaimSearch, IUserSetup } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { RolesService } from '@app/core/services/roles.service';
import { AddUserModalComponent } from '@app/standalone/modals/add-user-modal/add-user-modal.component';
import { ConfirmAddUserModalComponent } from '@app/standalone/modals/confirm-add-user-modal/confirm-add-user-modal..component';
import { CreateEntityModalComponent } from '@app/standalone/modals/create-entity-modal/create-entity-modal.component';
import { AlertController, IonicModule, ModalController } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { HeadingValueComponent } from '../heading-value/heading-value.component';
import { PeopleTableUserAssignmentsComponent } from '../people-table-user-assignments/people-table-user-assignments.component';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { NgClass } from '@angular/common';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-people-table',
    templateUrl: './people-table.component.html',
    animations: [
        trigger('detailExpand', [
            state('collapsed,void', style({ height: '0px', minHeight: '0' })),
            state('expanded', style({ height: '*' })),
            transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
        ]),
    ],
    styleUrls: ['./people-table.component.scss'],
    imports: [
        IonicModule,
        MatTable,
        MatSort,
        MatColumnDef,
        MatHeaderCellDef,
        MatHeaderCell,
        MatCheckbox,
        MatCellDef,
        MatCell,
        MatSortHeader,
        MatHeaderRowDef,
        MatHeaderRow,
        MatRowDef,
        MatRow,
        HeadingValueComponent,
        PeopleTableUserAssignmentsComponent,
        NgClass,
    ]
})
export class PeopleTableComponent implements OnInit, OnDestroy {
  @ViewChild('sort', { static: true }) sort: MatSort;
  @ViewChild('parentAddPopover') parentAddPopover: any;
  @Input() id: string | null | undefined;
  @Input() instance: IInstance;
  @Input() name: string | undefined | null;
  @Input() type: string | undefined;
  @Input() orgId: string | undefined;
  dataSource = new MatTableDataSource<IPeople>();
  displayedColumns: string[] = ['select', 'name', 'role', 'lastlogin'];
  currentAmount = 0;
  getAmount = 25;
  selection = new SelectionModel<any>(true, []);
  moreResults = false;
  isParentPopoverOpen = false;
  isLoading = false;
  componentDestroyed$: Subject<boolean> = new Subject();
  hasGrade = false;
  expandedElement: IPeople | null = null;
  heading = { text: 'People', stylingDirection: 'left', darkText: false, description: '' } as IHeading;

  constructor(
    private dataService: DataService,
    private modalController: ModalController,
    private toast: GlobalToastService,
    private rolesService: RolesService,
    private alertController: AlertController,
    private eventService: Events,
    public layoutService: LayoutService,
    private breadcrumbService: BreadcrumbService
  ) { }

  ngOnInit() {
    if (this.id) {
      this.getPeopleTableById(this.id, false);
    }
  }

  setExpandedElement(element: IPeople | null) {
    this.expandedElement = this.expandedElement === element ? null : element;
    if (this.expandedElement) {
      sessionStorage.setItem('peopleTableSelectedUserId', this.expandedElement?.userId);
      sessionStorage.setItem('peopleTableSelectedUserFirstName', this.expandedElement?.name ?? 'User');
    } else {
      sessionStorage.removeItem('peopleTableSelectedUserId');
      sessionStorage.removeItem('peopleTableSelectedUserFirstName');
    }
  }

  getPeopleTableById(id: string, loadMore: boolean) {
    if (id !== undefined) {
      this.isLoading = true;
      if (this.type === 'Accredited Package' || this.type === 'Modifiable Package') {
        if (this.orgId) {
          this.dataService
            .getPeopleTableByRowId(id, this.currentAmount, this.getAmount, this.orgId)
            .pipe(takeUntil(this.componentDestroyed$))
            .subscribe((instanceUsers: IPeople[]) => {
              this.isLoading = false;
              const selectedUserId = sessionStorage.getItem('peopleTableSelectedUserId');

              if (instanceUsers.length > 0) {
                //LoadMoreData
                if (!loadMore) {
                  this.setDataSource(instanceUsers);
                  this.currentAmount += instanceUsers.length;
                } else {
                  instanceUsers.forEach(user => {
                    this.dataSource.data = [...this.dataSource.data, user];
                  });
                  this.currentAmount += instanceUsers.length;
                }

                if (instanceUsers.length < this.getAmount) {
                  this.moreResults = false;
                } else {
                  this.moreResults = true;
                }
              }

              if (selectedUserId) {
                this.expandedElement = this.dataSource.data.find(x => x.userId === selectedUserId) ?? null;
              }
            });
        }
      } else {
        const parentBreadcrumb = this.breadcrumbService.getByfeatureType('Modifiable Learning Container Pages');
        this.dataService
          .getPeopleTableByInstanceId(id, this.currentAmount, this.getAmount, id !== parentBreadcrumb?.id ? parentBreadcrumb?.id : null)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe((instanceUsers: IPeople[]) => {
            this.isLoading = false;
            if (instanceUsers.length > 0) {
              if (!this.displayedColumns.includes('progress')) {
                this.displayedColumns.push('progress');
              }
              if (!this.displayedColumns.includes('grade')) {
                this.displayedColumns.push('grade');
              }
              // this.displayedColumns.push('expand');
              this.hasGrade = true;
              //LoadMoreData
              if (!loadMore) {
                this.setDataSource(instanceUsers);
                this.currentAmount += instanceUsers.length;
              } else {
                instanceUsers.forEach(user => {
                  this.dataSource.data = [...this.dataSource.data, user];
                });
                this.currentAmount += instanceUsers.length;
              }

              if (instanceUsers.length < this.getAmount) {
                this.moreResults = false;
              } else {
                this.moreResults = true;
              }

              if (this.type === 'Modifiable Learning Container Pages') {
                this.heading.description = 'Manage the people enrolled in this class.';
                this.heading.chipText = `${instanceUsers[0].groupSize} people`;
              }
            }
          });
      }
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    if (this.isAllSelected() === false) {
      this.dataSource.data.forEach(row => this.selection.select(row));
    } else {
      this.selection.clear();
    }
  }

  setDataSource(users: IPeople[]) {
    this.dataSource = new MatTableDataSource(users);
    this.dataSource.sort = this.sort;
  }

  presentParentAddPopover(event: Event) {
    this.parentAddPopover.event = event;
    this.isParentPopoverOpen = true;
  }

  async addUserModal() {
    const modal = await this.modalController.create({
      component: AddUserModalComponent,
      componentProps: { id: this.id, instanceId: this.instance.id, type: this.type },
    });

    modal.onDidDismiss().then(value => {
      if (value.data && value.data.length > 0) {
        this.confirmAddOrganizationUserModal(value.data);
      }
    });

    await modal.present();
  }

  async createUserModal() {
    const modal = await this.modalController.create({
      component: CreateEntityModalComponent,
      cssClass: 'create-entity-modal',
      componentProps: { id: this.id, type: this.type, instanceId: this.instance.id, orgId: this.orgId },
    });
    modal.onDidDismiss().then(async value => {
      if (value.data) {
        const user = {
          email: value.data.email,
          username: value.data.username,
          firstName: value.data.firstName,
          lastName: value.data.lastName,
          password: value.data.password,
          organizationId: value.data.organizationId,
          notifyNewUser: value.data.notifyNewUser,
        } as IUser;
        this.addUser(user, value.data as IUserSetup);
      }
    });

    await modal.present();
  }

  async addUser(user: IUser, userSetup: IUserSetup) {
    this.dataService
      .addUser(user)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(id => {
        if (id) {
          this.setupUser(id, userSetup);
          this.presentAlert();
        }
      });
  }

  async presentAlert() {
    const alert = await this.alertController.create({
      header: 'Success!',
      message: 'User successfuly added.',
      buttons: ['OK'],
    });

    await alert.present();
  }

  setupUser(userId: string, userSetup: IUserSetup) {
    this.dataService.setupUser(userId, userSetup).subscribe();
  }

  async confirmAddOrganizationUserModal(selectedUsers: IUserClaimSearch[]) {
    const modal = await this.modalController.create({
      component: ConfirmAddUserModalComponent,
      componentProps: { selectedUsers: selectedUsers, name: this.name, type: this.type },
    });

    modal.onDidDismiss().then(value => {
      this.isParentPopoverOpen = false;
      if (value.data && value.data && value.data.length > 0) {
        this.saveUsers(value.data);
      }
    });

    await modal.present();
  }

  saveUsers(selectedUsers: IUserClaimSearch[]) {
    if (this.id) {
      if (this.type === 'Accredited Package' || this.type === 'Modifiable Package') {
        this.dataService
          .addRowInstanceUsers(this.id, selectedUsers)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe((addedUsers: IPeople[]) => {
            if (addedUsers.length > 0) {
              addedUsers.forEach(user => {
                this.dataSource.data = [...this.dataSource.data, user];
              });
            }
          });
      } else {
        this.dataService
          .addInstanceUsers(this.id, selectedUsers)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe((addedUsers: IPeople[]) => {
            if (addedUsers.length > 0) {
              addedUsers.forEach(user => {
                this.dataSource.data = [...this.dataSource.data, user];
              });
            }
          });
      }
    }
  }

  updateTableUsers(status: string) {
    if (this.selection.selected?.length === 0) {
      return;
    }

    this.selection.selected.forEach(user => {
      const index = this.dataSource.data.findIndex(x => x.id === user.id);
      this.dataSource.data[index].status = status;
      user.status = status;
    });

    const userToUpdate = this.selection.selected.map(x => ({ id: x.id, userId: x.userId, status: status }) as IInstanceUserIn);

    if (this.id) {
      this.dataService
        .updateInstanceUsers(this.id, userToUpdate)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(data => {
          if (data) {
            this.toast.presentToast('Users updated successfully');
          }
        });
    }
  }

  hasAdminAccess() {
    return this.rolesService.hasFeatureRoleAccess([ActionTypes.Manage, ActionTypes.Publish]);
  }

  openResults(person: IPeople) {
    this.eventService.publish('openStudentResults', person.userId);
  }

  timeAgo(date: Date | string): string {
    try {
      const dateObj = date instanceof Date ? date : new Date(date);

      if (isNaN(dateObj.getTime())) {
        return '-';
      }

      const now = new Date();
      const diffInMs = now.getTime() - dateObj.getTime();
      const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) return 'Today';
      if (diffInDays === 1) return 'Yesterday';
      return `${diffInDays} Days Ago`;
    } catch (error) {
      return '-';
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
