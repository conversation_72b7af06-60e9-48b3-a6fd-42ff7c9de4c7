.parent-container {
  .card-content-container {
    margin: 0px !important;
    background-color: transparent;
  }

  ::ng-deep {
    .ql-snow .ql-editor{ 
      h1 {
        font-family: '<PERSON>o';
        font-size: 1.688em;
        color: #FFF;
        letter-spacing: 0.03em;
        line-height: 1.1;
        font-weight: 400;
      }

      h2 {
        font-family: '<PERSON><PERSON>';
        font-size: 1.250em;
        color: #FFF;
        letter-spacing: 0.03em;
        line-height: 1.1;
        font-weight: 700;
      }
    }
  }

  ::ng-deep .ql-editor {
    font-family: 'Roboto';
    font-size: 18px;
    color: #aaa;
    letter-spacing: 0.03em;
    line-height: 1.4;
    font-weight: 400;
    padding-top: 0px;
    padding-left: 0px;
    padding-right: 0px;

    ::ng-deep ol>li,
      ul>li {
        padding-bottom: 11px;
      }

    ::ng-deep ol,
    ::ng-deep ul {
      padding-top: 10px;
      padding-bottom: 5px;
    }

    ::ng-deepli:not(.ql-direction-rtl):before {
      margin-right: 0.5em;
      text-align: right;
      font-weight: 600;
    }

    ::ng-deep ul>li:before {
      content: "\25CF";
    }
  
    ::ng-deep a {
      background-color: transparent !important;
    }
      
    ::ng-deep ul li::before {
      color: #ee9907;
    }
  }

  @media (max-width: 960px) {
    ::ng-deep .ql-editor {
      font-size: 12px;
    }
  }
}
