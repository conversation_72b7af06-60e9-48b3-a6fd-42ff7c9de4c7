.parent-container {
  .caption {
    color: #ffffff;
    font-family: '<PERSON>o';
    font-weight: bold;
    font-size: 22px;
    font-style: italic;
    line-height: 1.3;
    letter-spacing: 0.3px;
    text-align: left;
    margin: 0px;
  }

  .description {
    color: #cccccc;
    font-family: '<PERSON>o';
    font-weight: 400;
    font-size: 16px;
    line-height: 1.4;
    text-align: left;
    margin: 0px;
  }

  .max-width-toggle {
    margin-left: 0px;
    margin-bottom: 10px;
  }

  .inner-container {
    margin: 10px;

    .reorder-container-left {
      float: left;
      margin-right: 10px;

      ion-icon {
        font-size: 20px;
        color: white;
      }

      .trash-icon {
        cursor: pointer;
      }

      .reorder-icon-container {
        margin-top: 40px;
      }
    }

    .card-container {
      padding: 10px;
      border-radius: 3px;
      background-color: rgba(30, 30, 30);

      .upload-container {
        width: 100%;
      }

      .text-input-container {
        width: 100%;
        margin-top: 10px;

        .small-text {
          color: #ffffff;
          font-weight: 500;
          font-size: 16px;
          letter-spacing: 0.4px;
          text-align: left;
          font-family: "Roboto";
          line-height: 1.1;
        }

        .image-button-container {
          display: flex;
          justify-content: space-between;
          width: 100%;

          .button-input-fields {
            width: 49%;
          }
        }
      }
    }

    .styling-container {
      width: 100%;
      border-top: 2px solid #1d1d1d;
      color: #ffffff;
      font-family: 'Roboto';
      font-weight: bold;

      .styling-heading {
        font-size: 16px !important;
      }

      .styles-container {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        gap: 5px;

        .styling-img-container {
          width: 49%;

          .image-container {
            background-size: contain;
            width: 100%;
            object-fit: contain;
            background-position: left;
            background-repeat: no-repeat;
            border-radius: 5px;
            justify-content: center;
            display: flex;
          }

          .selected {
            border: 2px solid #f99e00;
            border-radius: 8px;
          }

          img {
            border-radius: 8px;
            max-width: 300px;
            width: 100%;
          }

          .image-text {
            display: flex;
            justify-content: center;
          }
        }
      }
    }
  }

  .section-add-line {
    margin: 20px 0px;
    position: relative;

    .icon-container {
      display: flex;
      justify-content: center;
      align-items: center;

      ion-icon {
        font-size: 35px;
        color: rgb(186, 122, 19) !important;
        cursor: pointer;
      }
    }
  }
}