.header-parent-container {
  width: 100%;

  .image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    .row-icon {
      object-fit: cover;
      border-radius: 2px;
    }
  }

  .inner-html {
    margin-top: 5px;
    letter-spacing: 0.01em;
    line-height: 1.3;
    font-weight: 500;
    color: #ccc;
    font-family: '<PERSON>o';
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  ::ng-deep .instance-descriptors {
    p {
      padding-bottom: 0px !important;
    }
  }

  @media (min-width: 1501px) {
    .description {
      margin-top: 5px;
    }
  }

  @media (min-width: 769px) and (max-width: 1500px) {
    .description {
      margin-top: 5px;
    }
  }

  @media (min-width: 480px) and (max-width: 768px) {
    .description {
      margin-top: 3px;
    }
  }

  @media (max-width: 480px) {
    .description {
      margin-top: 3px;
    }
  }

  .title-container {
    text-align: left;
    font-family: 'Roboto';

    .status-heading-container {
      margin-bottom: 5px;
      .inner-container {
        padding: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: white;
        font-size: 12px;
        border-radius: 5px;

        ion-icon {
          margin-right: 5px;
          font-size: 16px;
        }
      }
    }
  }

  @media (min-width: 1501px) {
    .title-centered {
      margin-top: -3px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    .row-icon {
      height: 50px;
      margin: 11px 0px 0px 11px;
    }

    .title-container {
      margin: 19px 11px 0px 15px;
      line-height: 1.1;
      letter-spacing: 0.01em;

      .title {
        color: #fff;
        font-weight: 900;
        font-size: 1.25em;
        margin-bottom: 1px;
      }

      .title-property {
        color: #ccc;
        font-weight: 500;
        font-size: 0.875em;
        font-style: italic;
      }
    }
  }

  @media (min-width: 1281px) and (max-width: 1500px) {
    .title-centered {
      margin-top: -4px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    .row-icon {
      height: 50px;
      margin: 11px 0px 0px 11px;
    }

    .title-container {
      margin: 19px 11px 0px 15px;
      line-height: 1.1;
      letter-spacing: 0.01em;

      .title {
        color: #fff;
        font-weight: 900;
        font-size: 1.125em;
        margin-bottom: 1px;
      }

      .title-property {
        color: #ccc;
        font-weight: 500;
        font-size: 0.875em;
        font-style: italic;
      }
    }
  }

  @media (min-width: 993px) and (max-width: 1280px) {
    .title-centered {
      margin-top: -4px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    .row-icon {
      height: 50px;
      margin: 11px 0px 0px 11px;
    }

    .title-container {
      margin: 19px 11px 0px 15px;
      line-height: 1.1;
      letter-spacing: 0.01em;

      .title {
        color: #fff;
        font-weight: 900;
        font-size: 1.063em;
        margin-bottom: 1px;
      }

      .title-property {
        color: #ccc;
        font-weight: 500;
        font-size: 0.875em;
        font-style: italic;
      }
    }
  }
  @media (min-width: 1501px) {
    .mat-expansion-panel-header {
      margin-bottom: 11px;
    }
  }

  @media (min-width: 769px) and (max-width: 992px) {
    .title-centered {
      margin-top: -4px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    .row-icon {
      height: 50px;
      margin: 11px 0px 0px 11px;
    }

    .title-container {
      margin: 19px 11px 0px 11px;
      line-height: 1.1;
      letter-spacing: 0.01em;

      .title {
        color: #fff;
        font-weight: 900;
        font-size: 1.063em;
        margin-bottom: 1px;
      }

      .title-property {
        color: #ccc;
        font-weight: 500;
        font-size: 0.875em;
        font-style: italic;
      }
    }
  }

  @media (min-width: 480px) and (max-width: 768px) {
    .title-centered {
      margin-top: -5px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    .row-icon {
      height: 40px;
      margin: 9px 0px 0px 11px;
    }

    .title-container {
      margin: 19px 11px 0px 9px;
      line-height: 1.1;
      letter-spacing: 0.01em;

      .title {
        color: #fff;
        font-weight: 900;
        font-size: 1.063em;
        margin-bottom: 1px;
      }

      .title-property {
        color: #ccc;
        font-weight: 500;
        font-size: 0.875em;
        font-style: italic;
      }
    }
  }
  @media (max-width: 480px) {
    .title-centered {
      margin-top: -3px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    .row-icon {
      height: 40px;
      margin: 9px 0px 0px 11px;
    }

    .title-container {
      margin: 13px 11px 0px 9px;
      line-height: 1.1;
      letter-spacing: 0.01em;

      .title {
        color: #fff;
        font-weight: 900;
        font-size: 1.063em;
        margin-bottom: 1px;
      }

      .title-property {
        color: #ccc;
        font-weight: 500;
        font-size: 0.875em;
        font-style: italic;
      }
    }
  }
}

@media (min-width: 1501px) {
  .header-parent-container {
    margin-bottom: 11px;
  }
}

@media (min-width: 1281px) and (max-width: 1500px) {
  .header-parent-container {
    margin-bottom: 11px;
  }
}

@media (min-width: 480px) and (max-width: 1280px) {
  .header-parent-container {
    margin-bottom: 9px;
  }
}

@media (max-width: 480px) {
  .header-parent-container {
    margin-bottom: 7px;
  }
}
