.parent-container {
  ion-text {
    color: red;
    font-style: italic;
    font-size: 14px;
  }

  ion-button {
    margin-right: 10px;
  }

  ion-label {
    cursor: help !important;
  }

  ion-icon {
    cursor: help !important;
  }

  .identifier {
    background-color: #7f550c;
    border-radius: 0.2em;
    color: black;
    font-weight: bold;
    padding: 0.3em;
    margin-left: 0.4em;
  }

  .reqAsterisk {
    color: #7f550c;
    font-size: 28px;
  }

  .no-padding {
    --inner-padding-end: 0;
    --padding-start: 0;
  }

  .no-border {
    ion-input {
      height: 100%;
      width: 100%;
      border: none !important;
    }
  }

  .side-panel-input-padding {
    --inner-padding-end: 10px;
    --padding-start: 10px;
  }
}

.normal {
  ion-item {
    --border-color: transparent; // default underline color
    --color: white;
    font-size: 20px;
    font-family: 'Exo 2';
    --background: var(--background);
  }

  ion-input {
    border: 1px solid #4e4e4e;
    border-radius: 5px;
    margin-top: 0.2em;
    color: white;
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    --padding-start: 8px !important;
    caret-color: #7f550c;
    background-color: var(--background-color);
  }

  .label-header {
    margin-bottom: 0.2em;
    padding-bottom: 0.04em;
    font-weight: 500;
    letter-spacing: 0.03em;
  }
}

.selected {
  .label-header {
    width: fit-content;
    padding: 2px 11px 16px 11px;
    background: #f99e00;
    color: #000000;
    border-color: #f99e00;
    border-width: 1px;
    border-style: solid;
    border-radius: 3px 3px 0px 0px;
    font-family: 'Roboto';
    font-weight: bold;
    font-size: 16px;
    line-height: 1.1;
    letter-spacing: 0.3px;
    text-align: left;
  }

  .inner-container {
    --border-color: transparent;
    --color: white;
    font-size: 20px;
    font-family: 'Roboto';
    --background: var(--background);
    width: 100%;
    --inner-padding-end: 0;
    --padding-start: 0;
  }

  ion-input {
    border: 1px solid #f99e00;
    border-radius: 0px 3px 3px 3px;
    --padding-start: 8px !important;
    caret-color: #7f550c;
    background-color: var(--background-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 20px 11px 9px 20px;
    box-shadow: 1px 1px 14px rgba(249, 158, 0, 0.15);
    background: #222;
    color: #aaaaaa;
    font-family: 'Roboto';
    font-weight: 400;
    font-size: 16px;
    line-height: 1.3;
    letter-spacing: 0.5px;
    text-align: left;
  }
}

.password-container {
  display: flex;
  width: 100%;
  align-items: center;
  border: 1px solid #4e4e4e;
  border-radius: 0px 3px 3px 3px;
  margin-top: 5px;

  ion-input {
    border: none !important
  }

  .type-toggle {
    padding-inline-start: 0.5rem;
    margin-right: 10px;
    color: #7a7a7a !important;
    cursor: pointer !important;

    .show-option,
    .hide-option {
      font-size: 1.2rem;
      display: block;
    }
  }
}

@media screen and (max-width: 960px) {
  .label-header {
    overflow: hidden;
    text-wrap: nowrap;
    text-overflow: ellipsis;
  }
}