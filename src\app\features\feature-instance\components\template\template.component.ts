import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { IFeatureTab, IInstance, IInstanceTemplate, IRouteParams } from '@app/core/contracts/contract';
import { ComponentType } from '@app/core/enums/component-type.enum';
import { ViewType } from '@app/core/enums/view-type';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { Observable, Subject, map, takeUntil } from 'rxjs';

@Component({
    selector: 'app-template',
    templateUrl: './template.component.html',
    styleUrls: ['./template.component.scss'],
    standalone: false
})
export class TemplateComponent implements OnInit, OnDestroy {
  @Input() templateId: string;
  @Input() instance: IInstance;
  @Input() featureTab: IFeatureTab;
  @Input() disabled = true;
  @Input() routeParams: IRouteParams;
  @Input() onlyContent = false;
  @Input() selectedUserId: string;
  @Output() formChanged = new EventEmitter<boolean>();
  @Output() instanceChanged = new EventEmitter();
  template$: Observable<IInstanceTemplate>;
  componentType = ComponentType;
  componentDestroyed$: Subject<boolean> = new Subject();
  viewTypes = ViewType;

  constructor(
    private dataService: DataService,
    private instanceService: InstanceService
  ) {}

  ngOnInit(): void {
    this.dataService.reload$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setTemplate();
    });

    if (
      this.featureTab.type?.name === 'Instance Builder' &&
      this.routeParams.tabName !== 'default' &&
      (this.instance.feature.featureType.name.toLocaleLowerCase().indexOf('manager') !== -1 ||
        this.instance.feature.featureType.name.toLocaleLowerCase().indexOf('organization') !== -1 ||
        this.instance.feature.featureType.name.toLocaleLowerCase().indexOf('learning container pages') !== -1)
    ) {
      if (this.routeParams.viewType !== ViewType.Builder) {
        this.routeParams.viewType = ViewType.Builder;
        this.instanceService.openInstance(this.routeParams.featureSlug, this.routeParams.instanceSlug, this.featureTab.tab.name, 'builder');
      }
    }

    this.setTemplate();
  }

  getId() {
    return this.instanceService.isValidGUID(this.routeParams.instanceSlug ?? '') ? this.routeParams.instanceSlug : this.instance.id;
  }

  setTemplate() {
    this.template$ = this.dataService
      .getInstanceTemplate(
        this.instance.id,
        this.templateId,
        this.featureTab.type?.name !== 'Instance Builder' || this.routeParams.viewType !== ViewType.Builder,
        this.featureTab.type?.name === 'Instance Builder' && this.routeParams.viewType === ViewType.Builder
      )
      .pipe(
        map(res => {
          return res;
        })
      );
  }

  formChanges(formchanged: boolean) {
    this.formChanged.emit(formchanged);
  }

  instanceUpdated() {
    this.instanceChanged.emit(null);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
