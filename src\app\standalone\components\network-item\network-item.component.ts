import { Component, Input, OnDestroy } from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { MatProgressBar } from '@angular/material/progress-bar';
import { INetworkAnalytics } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import saveAs from 'file-saver';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-network-item',
    templateUrl: './network-item.component.html',
    styleUrl: './network-item.component.scss',
    imports: [MatIcon, MatProgressBar]
})
export class NetworkItemComponent implements OnDestroy {
  @Input() network: INetworkAnalytics;
  started?: boolean = false;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(private dataService: DataService) {}

  downloadPdf(networkId: string) {
    this.started = true;
    this.dataService
      .getAnalyticsObjectQlikPdf(networkId, 'Network')
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe({
        next: (data: any) => {
          if (data) {
            const byteCharacters = atob(data.fileContent);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'application/pdf' });
            saveAs(blob, data.fileName);
            this.started = false;
          } else {
            this.started = false;
          }
        },
        error: e => {
          this.started = false;
        },
      });
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
