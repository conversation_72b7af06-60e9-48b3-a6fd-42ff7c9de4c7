@for (instanceSection of instanceSections; track instanceSection) {
  <div>
    @if (instanceSection?.section?.title && instanceSection?.section?.title?.length > 0 && instanceSection?.section?.showTitleOnPlayer) {
      <h4 class="side-panel-title">
        {{ instanceSection?.section?.title }}
      </h4>
    }
    <div>
      <ion-grid>
        @for (groupedComponents of instanceSection.instanceSectionComponents | groupBy: 'component.builderRowNumber' | values; track groupedComponents) {
          <ion-row>
            @for (instanceSectionComponent of groupedComponents | orderBy: 'component.templateField.colNumber'; track instanceSectionComponent) {
              <ion-col size="12">
                <app-component-row-selector
                  [routeParams]="routeParams"
                  [instanceSectionComponent]="instanceSectionComponent"
                  [instance]="instance"
                  [searchFilter]="searchFilter"
                  [isPlayerSidePanel]="true"
                  [selectedUserId]="selectedUserId"
                  [onlyRows]="true"
                  [playerSidePanel]="true"
                  (rowContentSelected)="selectedRowContent($event.event, $event.actionBw)"></app-component-row-selector>
              </ion-col>
            }
          </ion-row>
        }
      </ion-grid>
    </div>
  </div>
}
