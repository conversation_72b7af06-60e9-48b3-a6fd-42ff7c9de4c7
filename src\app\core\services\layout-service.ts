import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { EventEmitter, Injectable } from '@angular/core';
import { distinctUntilChanged } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LayoutService {
  currentScreenSize: string = 'lg';
  peopleTableCompact: boolean = false;
  isTablet: boolean = false;
  compactModeChanged = new EventEmitter<boolean>();

  displayNameMap = new Map([
    [Breakpoints.XSmall, 'xs'],
    ['(min-width: 600px) and (max-width: 769px)', 'xs'],
    ['(min-width: 769px) and (max-width: 992px)', 'xs'], // For Tablet
    ['(min-width: 993px) and (max-width: 1250px)', 'lg'],
    ['(min-width: 1250px) and (max-width: 1279.98px)', 'lg'],
    [Breakpoints.Large, 'lg'],
    [Breakpoints.XLarge, 'lg'],
  ]);

  constructor(breakpointObserver: BreakpointObserver) {
    breakpointObserver
      .observe([
        Breakpoints.XSmall,
        '(min-width: 600px) and (max-width: 769px)',
        '(min-width: 769px) and (max-width: 992px)', // For Tablet
        '(min-width: 993px) and (max-width: 1250px)',
        '(min-width: 1250px) and (max-width: 1279.98px)',
        Breakpoints.Large,
        Breakpoints.XLarge,
      ])
      .pipe(distinctUntilChanged())
      .subscribe(result => {
        for (const query of Object.keys(result.breakpoints)) {
          if (result.breakpoints[query]) {
            this.currentScreenSize = this.displayNameMap.get(query) ?? 'lg';
            this.isTablet = query === '(min-width: 769px) and (max-width: 992px)';

            const newCompactValue =
              query === Breakpoints.XSmall ||
              query === '(min-width: 600px) and (max-width: 769px)' ||
              query === '(min-width: 769px) and (max-width: 992px)' ||
              query === '(min-width: 993px) and (max-width: 1250px)';

            if (this.peopleTableCompact !== newCompactValue) {
              this.peopleTableCompact = newCompactValue;
              this.compactModeChanged.emit(this.peopleTableCompact);
            }
          }
        }
      });
  }
}
