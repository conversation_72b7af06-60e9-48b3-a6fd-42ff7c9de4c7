import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { IInstanceInterest, IInstanceInterestText, IInstanceSectionComponent, IUserInstanceInterest } from '@app/core/contracts/contract';
import { InstanceInterestTypes } from '@app/core/enums/instance-interest-types.enum';
import { DataService } from '@app/core/services/data-service';
import { LayoutService } from '@app/core/services/layout-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { HeadingValueComponent } from '../heading-value/heading-value.component';
import { DynamicTextValueComponent } from '../dynamic-text-value/dynamic-text-value.component';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-instance-interest-value',
    templateUrl: './instance-interest-value.component.html',
    styleUrls: ['./instance-interest-value.component.scss'],
    imports: [IonicModule, HeadingValueComponent, DynamicTextValueComponent, MatIcon]
})
export class InstanceInterestValueComponent implements OnInit, OnDestroy {
  @Input() instanceId: string;
  @Input() instanceComponent: IInstanceSectionComponent | undefined;
  instanceInterest: IInstanceInterest;
  selectedIcon: InstanceInterestTypes;
  componentDestroyed$: Subject<boolean> = new Subject();
  interestText: IInstanceInterestText[] = [];
  instanceInterestTypes = InstanceInterestTypes;

  constructor(
    private layoutService: LayoutService,
    private dataService: DataService
  ) { }

  get mobileScreen() {
    return this.layoutService.currentScreenSize === 'xs';
  }

  ngOnInit() {
    this.extractButtonText();

    if (this.instanceComponent?.value) {
      this.instanceInterest = JSON.parse(this.instanceComponent?.value) as IInstanceInterest;
      if (this.instanceInterest.heading === '' && this.instanceComponent?.component?.templateField?.default1) {
        this.instanceInterest.heading = this.instanceComponent?.component?.templateField?.default1;
      }
      if (this.instanceInterest.description === '' && this.instanceComponent?.component?.templateField?.default2) {
        this.instanceInterest.description = this.instanceComponent?.component?.templateField?.default2;
      }
      if (this.instanceInterest.interestNoneText === '' && this.getInterestText(0)) {
        this.instanceInterest.interestNoneText = this.getInterestText(0);
      }
      if (this.instanceInterest.interestMediumText === '' && this.getInterestText(1)) {
        this.instanceInterest.interestMediumText = this.getInterestText(1);
      }
      if (this.instanceInterest.interestHighText === '' && this.getInterestText(2)) {
        this.instanceInterest.interestHighText = this.getInterestText(2);
      }
    } else {
      this.instanceInterest = {
        heading: this.instanceComponent?.component?.templateField?.default1 ?? 'Heading',
        description: this.instanceComponent?.component?.templateField?.default2 ?? 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
        interestNoneText: this.instanceInterest?.interestNoneText ?? this.getInterestText(0),
        interestMediumText: this.instanceInterest?.interestMediumText ?? this.getInterestText(1),
        interestHighText: this.instanceInterest?.interestHighText ?? this.getInterestText(2),
      } as IInstanceInterest;
    }

    this.dataService.getInstanceInterests().subscribe((interests: IUserInstanceInterest[]) => {
      this.selectedIcon = interests.find(x => x.instanceId === this.instanceId)?.nominalVal ?? -1;

      if (this.instanceComponent) {
        this.instanceComponent.completed = this.selectedIcon !== this.instanceInterestTypes.none;
      };
    });
  }

  extractButtonText() {
    if (this.instanceComponent?.component?.templateField?.default3) {
      this.interestText = JSON.parse(this.instanceComponent.component.templateField.default3) as IInstanceInterestText[];
    }
  }

  getInterestText(sortOrder: number) {
    return this.interestText?.find(x => x.sortOrder === sortOrder)?.text ?? '';
  }

  setSelectedIcon(value: InstanceInterestTypes) {
    if (value === this.selectedIcon) {
      this.selectedIcon = this.instanceInterestTypes.none;
    } else {
      this.selectedIcon = value;
    }
    this.addInterest();
  }

  addInterest() {
    this.dataService.addInstanceInterest(this.instanceId, this.selectedIcon).pipe(takeUntil(this.componentDestroyed$)).subscribe();

    if (this.instanceComponent) {
      if (this.instanceComponent) { this.instanceComponent.completed = this.selectedIcon !== this.instanceInterestTypes.none };
    }
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
