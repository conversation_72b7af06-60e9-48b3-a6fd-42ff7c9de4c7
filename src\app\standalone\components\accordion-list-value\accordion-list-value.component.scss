.accordion-parent-container {
  .accordion-inner-group-container {
    padding-bottom: 25px;

    ion-accordion {
      ion-item {
        --background: rgb(50, 50, 50);
        border-top: 1px solid #222;
      }

      .inner-content {
        background-color: rgba(50, 50, 50);
        border-left: 3px solid #f99e00;
        font-size: 20px;
        line-height: 1.4;
        color: #aaa;
        letter-spacing: 0.02em;
        padding-top: 0px;
        margin-top: 0px;
        padding-bottom: 20px;
        border-bottom: 1px solid #222;
      }
    }
  }
}


:host ::ng-deep ion-accordion-group {
  ion-accordion {
    ion-item[slot="header"] {
      font-size: 18px;
      font-weight: 900;
      color: #fff;
      letter-spacing: 0.03em;
    }

    .ion-accordion-toggle-icon {
      color: #fff;
      width: 22px;
      height: 22px;
    }
  }
}