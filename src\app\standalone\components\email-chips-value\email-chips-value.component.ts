import { Component, OnDestroy, OnInit } from '@angular/core';
import { ILinkedUserEmails } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';

@Component({
    selector: 'app-email-chips-value',
    templateUrl: './email-chips-value.component.html',
    styleUrls: ['./email-chips-value.component.scss'],
    imports: [IonicModule]
})
export class EmailChipsValueComponent implements OnInit, OnDestroy {
  listItems: ILinkedUserEmails[];
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.dataService
      .getUserLinkedEmails()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((linkedEmails: any) => {
        this.listItems = linkedEmails;
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
