import { <PERSON>tt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, mergeMap, retry, retryWhen, switchMap } from 'rxjs/operators';
import { GlobalToastService } from '../services/global-toast.service';
import { AuthService } from '../services/auth-service';
import { MonitoringService } from '../services/monitoring-service';

@Injectable()
export class ServerErrorsInterceptor implements HttpInterceptor {
  retryBlacklist: string[] = ['https://centralus-2.in.applicationinsights.azure.com/v2/track'];
  maxRetries = 1;
  silentRefreshBusy = false;
  constructor(
    private toast: GlobalToastService,
    private monitoringService: MonitoringService,
    private authService: AuthService
  ) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
    return this.handleRequest(request, next);
  }

  private handleRequest(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<any> {
    if (this.retryBlacklist.indexOf(request.url) === -1 && request.url.includes('tracking')) {
      return next.handle(request).pipe(
        retryWhen(error => {
          return error.pipe(
            mergeMap((error, index) => {
              if (index < this.maxRetries && this.silentRefreshBusy === false) {
                if (error.status === 401) {
                  this.silentRefreshBusy = true;
                  return this.authService.silentRefresh().pipe(
                    switchMap(() => {
                      this.silentRefreshBusy = false;
                      return of(error);
                    })
                  );
                } else {
                  return of(error);
                }
              } else {
                this.errorHandler(error);
              }
              throw error;
            })
          );
        })
      );
    } else if (this.retryBlacklist.indexOf(request.url) === -1 && !request.url.includes('tracking')) {
      // If the call fails, retry first before throwing an error
      return next.handle(request).pipe(
        retry(1),
        catchError(error => this.errorHandler(error))
      );
    } else {
      return next.handle(request).pipe(catchError(error => this.errorHandler(error, true)));
    }
  }

  private errorHandler(response: any, skipMonitoringService = false): Observable<HttpEvent<any>> {
    if (skipMonitoringService !== true) {
      this.monitoringService.logException(response);
    }
    if (response.status === 0) {
      response.message = 'Your network is unavailable. Check your data or wifi connection.';
    } else if (response.status === 401) {
      response.message = 'Your session has expired please login again.';
    } else if (response.status === 403) {
      response.message = 'Request permission from your administrator to perform this action.';
    } else if (response?.error?.Message?.indexOf('Slug') !== -1) {
      //response.message = response.error?.Message;
    } else {
      response.message = 'An error occurred while processing your request. Please try again.';
    }
    this.toast.presentToast(response?.error?.message ? response?.error?.message : response?.message);
    //this.toast.presentToast(response.message);
    throw response;
  }
}
