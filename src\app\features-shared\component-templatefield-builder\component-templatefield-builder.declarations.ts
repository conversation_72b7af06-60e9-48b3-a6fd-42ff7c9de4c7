import { ContentQuillEditorComponent } from '@app/standalone/components/content-quill-editor/content-quill-editor.component';
import { FileUploadControlComponent } from '@app/standalone/components/file-upload-control/file-upload-control.component';
import { SelectOptionControlComponent } from '@app/standalone/components/select-option-control/select-option-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { AchievementBankEditorComponent } from './components/achievement-bank-editor/achievement-bank-editor.component';
import { AchievementEditorComponent } from './components/achievement-editor/achievement-editor.component';
import { AssessmentBlockEditorComponent } from './components/assessment-block-editor/assessment-block-editor.component';
import { AssessmentQuestionFieldComponent } from './components/assessment-question-field/assessment-question-field.component';
import { AuthoringFieldEditorComponent } from './components/authoring-field-editor/authoring-field-editor.component';
import { BannerEditorComponent } from './components/banner-editor/banner-editor.component';
import { CheckboxFieldEditorComponent } from './components/checkbox-field-editor/checkbox-field-editor.component';
import { ComponentAchievementsFieldComponent } from './components/component-achievements-field/component-achievements-field.component';
import { ComponentEditorComponent } from './components/component-editor/component-editor.component';
import { DisplayListFieldEditorComponent } from './components/display-list-field-editor/display-list-field-editor.component';
import { DownloadBlockEditorComponent } from './components/download-block-editor/download-block-editor.component';
import { DropDownFieldEditorComponent } from './components/drop-down-field-editor/drop-down-field-editor.component';
import { ExternalHtmlBlockEditorComponent } from './components/external-html-block-editor/external-html-block-editor.component';
import { FeedbackBlockEditorComponent } from './components/feedback-block-editor/feedback-block-editor.component';
import { FieldCheckboxesBaseComponent } from './components/field-checkboxes-base/field-checkboxes-base.component';
import { FieldEditorBaseComponent } from './components/field-editor-base/field-editor-base.component';
import { GoogleMapsAutocompleteFieldEditorComponent } from './components/google-maps-autocomplete-field-editor/google-maps-autocomplete-field-editor.component';
import { Html5GameEditorComponent } from './components/html5-game-editor/html5-game-editor.component';
import { IconBaseFieldEditorComponent } from './components/icon-base-field-editor/icon-base-field-editor.component';
import { ImageFieldEditorComponent } from './components/image-field-editor/image-field-editor.component';
import { InstanceHeaderFieldEditorComponent } from './components/instance-header-field-editor/instance-header-field-editor.component';
import { InstanceInterestFieldEditorComponent } from './components/instance-interest-field-editor/instance-interest-field-editor.component';
import { LabelFieldEditorComponent } from './components/label-field-editor/label-field-editor.component';
import { MediaAndTextFieldEditorComponent } from './components/media-and-text-field-editor/media-and-text-field-editor.component';
import { MediaBlockEditorComponent } from './components/media-block-editor/media-block-editor.component';
import { CliqviewEditorComponent } from './components/network-qlik-view-editor/network-qlik-view-editor.component';
import { NotFieldEditorComponent } from './components/not-field-editor/not-field-editor.component';
import { OrganizationFieldEditorComponent } from './components/organization-field-editor/organization-field-editor.component';
import { OrganizationNetworkEditorComponent } from './components/organization-network-editor/organization-network-editor.component';
import { PageBannerEditorComponent } from './components/page-banner-field-editor/page-banner-field-editor.component';
import { RowOptionsEditorComponent } from './components/row-options-editor/row-options-editor.component';
import { SystemPropertySelectorComponent } from './components/system-property-selector/system-property-selector.component';
import { TagTreeFieldComponent } from './components/tag-tree-field/tag-tree-field.component';
import { TextAndButtonFieldEditorComponent } from './components/text-and-button-field-editor/text-and-button-field-editor.component';
import { TextFieldEditorComponent } from './components/text-field-editor/text-field-editor.component';
import { QlikAnalyticsEditor } from './components/qlik-analytics-editor/qlik-analytics-editor';
import { QlikAnalyticsTypeEditorComponent } from './components/qlik-analytics-type-editor/qlik-analytics-type-editor.component';
import { CriteriaManagerEditorComponent } from './components/criteria-manager-editor/criteria-manager-editor.component';

export const featureComponents: any[] = [
  ComponentEditorComponent,
  TextFieldEditorComponent,
  LabelFieldEditorComponent,
  DropDownFieldEditorComponent,
  SystemPropertySelectorComponent,
  FieldEditorBaseComponent,
  TagTreeFieldComponent,
  RowOptionsEditorComponent,
  OrganizationFieldEditorComponent,
  AssessmentBlockEditorComponent,
  MediaBlockEditorComponent,
  DownloadBlockEditorComponent,
  ImageFieldEditorComponent,
  FeedbackBlockEditorComponent,
  AssessmentQuestionFieldComponent,
  InstanceHeaderFieldEditorComponent,
  AchievementEditorComponent,
  AchievementBankEditorComponent,
  ComponentAchievementsFieldComponent,
  OrganizationNetworkEditorComponent,
  ExternalHtmlBlockEditorComponent,
  BannerEditorComponent,
  AuthoringFieldEditorComponent,
  FieldCheckboxesBaseComponent,
  DisplayListFieldEditorComponent,
  MediaAndTextFieldEditorComponent,
  TextAndButtonFieldEditorComponent,
  IconBaseFieldEditorComponent,
  PageBannerEditorComponent,
  NotFieldEditorComponent,
  CheckboxFieldEditorComponent,
  CliqviewEditorComponent,
  InstanceInterestFieldEditorComponent,
  GoogleMapsAutocompleteFieldEditorComponent,
  Html5GameEditorComponent,
  QlikAnalyticsEditor,
  QlikAnalyticsTypeEditorComponent,
  CriteriaManagerEditorComponent,
];

export const standaloneComponents: any[] = [TextInputControlComponent, SelectOptionControlComponent, FileUploadControlComponent, ContentQuillEditorComponent];
