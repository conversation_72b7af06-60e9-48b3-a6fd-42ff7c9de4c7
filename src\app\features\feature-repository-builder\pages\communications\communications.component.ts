import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { IFeature } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-feature-repository-communications',
    templateUrl: './communications.component.html',
    styleUrls: ['./communications.component.scss'],
    standalone: false
})
export class FeatureRepositoryCommunicationsComponent implements OnInit, OnDestroy {
  @Input() feature: IFeature;
  componentDestroyed$: Subject<boolean> = new Subject();
  communicationsForm: UntypedFormGroup;
  authorInstanceCompletion: UntypedFormControl;
  authorInstanceFeedback: UntypedFormControl;
  authorInstanceLive: UntypedFormControl;
  userInstanceCompletion: UntypedFormControl;
  userAchievement: UntypedFormControl;
  showSave = false;
  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.createFormControls();
    this.createForm();
    this.communicationsForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.communicationsForm.valid) {
        this.showSave = true;
      }
    });
  }

  createFormControls() {
    this.authorInstanceCompletion = new UntypedFormControl(this.feature.featureCommunication.authorInstanceCompletion, [Validators.required]);
    this.authorInstanceFeedback = new UntypedFormControl(this.feature.featureCommunication.authorInstanceFeedback, [Validators.required]);
    this.authorInstanceLive = new UntypedFormControl(this.feature.featureCommunication.authorInstanceLive, [Validators.required]);
    this.userInstanceCompletion = new UntypedFormControl(this.feature.featureCommunication.userInstanceCompletion, [Validators.required]);
    this.userAchievement = new UntypedFormControl(this.feature.featureCommunication.userAchievement, [Validators.required]);
  }

  createForm() {
    this.communicationsForm = new UntypedFormGroup({
      authorInstanceCompletion: this.authorInstanceCompletion,
      authorInstanceFeedback: this.authorInstanceFeedback,
      authorInstanceLive: this.authorInstanceLive,
      userInstanceCompletion: this.userInstanceCompletion,
      userAchievement: this.userAchievement,
    });
  }

  saveCommunication() {
    if (this.communicationsForm.valid) {
      this.dataService
        .putFeatureCommunications(this.feature.id, this.communicationsForm.value)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.showSave = false;
        });
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
