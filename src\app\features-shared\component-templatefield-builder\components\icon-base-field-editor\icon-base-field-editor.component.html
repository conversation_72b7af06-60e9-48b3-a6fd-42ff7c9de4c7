@if (iconAndTextForm) {
  <form class="parent-form-container" [formGroup]="iconAndTextForm">
    <ion-grid>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="header">Edit {{ component.componentType.name }}</div>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Personalize Component</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Label'"
            [placeHolder]="'Section Name'"
            [toolTip]="'Label'"
            formControlName="label"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Placeholder'"
            [placeHolder]="'e.g. Section 1'"
            [toolTip]="'Placeholder'"
            formControlName="placeHolderText"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Default Text'"
            [placeHolder]="'Section Name'"
            [toolTip]="'Default Text'"
            formControlName="defaultText"></app-text-input-control>
        </ion-col>
      </ion-row>
      @if (componentType === 'Icon & Dropdown') {
        <div>
          <ion-row>
            <ion-col>
              <app-select-option-control
                [toolTip]="'Link Type'"
                [label]="'Link Type'"
                [options]="dropDownLinkTypes"
                [backgroundColor]="'#333333'"
                [disabled]="false"
                formControlName="dropDownLinkType"></app-select-option-control>
            </ion-col>
          </ion-row>
          @if (getDropDownLinkValue()?.title === 'Tags' || getDropDownLinkValue()?.title === 'Organization Tags' || getDropDownLinkValue()?.title === 'Campaign User Tags') {
            <ion-row>
              <ion-col>
                <app-tag-tree-field [templateField]="component.templateField" (valueChanged)="tagSelectionChanged($event)"></app-tag-tree-field>
              </ion-col>
            </ion-row>
          }
        </div>
      }
      <ion-row>
        <ion-col>
          <app-file-upload-control
            [componentType]="component?.componentType?.name"
            [fileTypeBw]="component?.templateField?.fileTypeBw"
            formControlName="defaultImageUrl"
            [label]="'Default icon'"
            [component]="component"
            [toolTip]="'Default icon'">
          </app-file-upload-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Caption'"
            [placeHolder]="'Start typing...'"
            [toolTip]="'Caption'"
            formControlName="caption"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Description'"
            [placeHolder]="'Start typing...'"
            [toolTip]="'Description'"
            formControlName="description"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col class="row-number-col">
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Row Number'"
            [placeHolder]="'Add field row number...'"
            formControlName="rowNumber"
            [toolTip]="'Row Number'"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Column Number (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add column number (Out of 12)...'"
            formControlName="colNumber"
            [toolTip]="'Column Number'"
            [type]="'number'">
          </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Colspan (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add field colspan (Out of 12)...'"
            formControlName="colspan"
            [toolTip]="'Colspan'"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Settings</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-card>
            <ion-card-content>
              <app-field-checkboxes-base [baseForm]="iconAndTextForm"></app-field-checkboxes-base>
              <!--relation to dropDownLinkType-->
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isTag">IsTag</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isInherit">System Property</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVisibleRepository">Is Visible in Repository</mat-slide-toggle>
              @if (isInheritControl.value) {
                <app-system-property-selector [templateField]="component.templateField" [formGroup]="iconAndTextForm"></app-system-property-selector>
              }
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
