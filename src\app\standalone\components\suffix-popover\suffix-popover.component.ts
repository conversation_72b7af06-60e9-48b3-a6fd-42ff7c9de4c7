import { Component, Input } from '@angular/core';
import { IonicModule, PopoverController } from '@ionic/angular';

@Component({
    selector: 'app-suffix-popover',
    templateUrl: './suffix-popover.component.html',
    styleUrls: ['./suffix-popover.component.scss'],
    imports: [IonicModule]
})
export class SuffixPopoverComponent {
  @Input() options: Array<{ value: string, label: string }>;
  @Input() selected: string;

  constructor(private popoverController: PopoverController) {}

  selectOption(option: string) {
    this.popoverController.dismiss(option);
  }
}