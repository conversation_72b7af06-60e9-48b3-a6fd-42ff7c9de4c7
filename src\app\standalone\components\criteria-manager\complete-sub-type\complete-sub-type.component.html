@if (completeSubTypeForm) {
  <form [formGroup]="completeSubTypeForm">
    <div class="parent-container">
      <ion-row>
        <ion-col size="auto" class="heading-col">Complete Block:</ion-col>
        <ion-col class="component-type-col" size="2">
          <ion-select formControlName="refId" (ionChange)="setSelectedContent($event)" interface="popover">
            <ng-container>
              @for (type of componentTypes; track type) {
                <ion-select-option [value]="type.id">
                  {{ type.name }}
                </ion-select-option>
              }
            </ng-container>
          </ion-select>
        </ion-col>
      </ion-row>
    </div>
  </form>
}
