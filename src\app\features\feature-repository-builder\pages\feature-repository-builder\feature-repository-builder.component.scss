.repository {
  flex: 1;
  display: flex;
  align-items: stretch;
  align-content: stretch;
  flex-direction: column;
  color: white;

  margin-left: var(--page-margin-left-player);
  margin-right: var(--page-margin-right-player);

  .block-view {
    margin-left: 0px !important;
    margin-right: 0px !important;
  }

  .header-container {
    z-index: 1000;
    height: 80px;
    padding-top: 8px;
    padding-left: var(--page-margin-left-player);
    padding-right: var(--page-margin-right-player);
    background-color: #111;
    background-image: var(--background-image);
    background-size: cover;
    h1 {
      font-family: 'Exo 2';
      text-shadow: 2px 2px #000;
      margin: 0;
      font-weight: 800;
      align-self: flex-end;
      font-size: 30px;
    }
    h6 {
      font-family: 'Exo 2';
      text-shadow: 2px 2px #000;
      margin: 4px 4px 4px 0px;
      font-weight: 800;
      align-self: flex-end;
      font-size: 1em;
      font-style: italic;
      font-weight: 500;
    }
    p {
      bottom: 0px;
      font-family: 'Roboto';
      text-shadow: 2px 2px #000;
    }
  }

  .content {
    flex: 1;
    display: flex;
    align-items: stretch;
    align-content: stretch;
    flex-direction: column;
  }

  .mat-mdc-tab-group {
    flex: 1;
  }

  .mat-mdc-tab-body-wrapper {
    display: flex;
    flex-grow: 1;
  }

  .mat-mdc-tab-body-wrapper {
    display: flex;
    flex-grow: 1;
  }

  .mat-mdc-tab {
    margin-right: 35px;
    padding: 0px;
    justify-content: flex-start;
    min-width: 0px;
    width: auto;
    color: #ffffff;
    border-bottom: #f99e00;
    font-size: 1em;

    div#mat-mdc-tab-0-0 {
      padding: 0px 0px 0px 0px !important;
      width: auto !important;
      margin-left: 0px !important;
    }

    // .header-less-tabs .mat-tab-header {
    //   display: none;
    // }

    div#mat-mdc-tab-0-0 {
      padding: 0px 0px 0px 0px !important;
      width: auto !important;
      margin-left: 0px !important;
    }
  }

  .mdc-tab__text-label {
    color: #aaa !important;
  }

  .mdc-tab--active {
    color: #f99e00;
    .mdc-tab__text-label {
      color: #f99e00 !important;
    }
  }
}
