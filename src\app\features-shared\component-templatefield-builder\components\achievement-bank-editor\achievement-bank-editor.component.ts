import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent, IComponentAchievement, IInstanceAchievement } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-achievement-bank-editor',
    templateUrl: './achievement-bank-editor.component.html',
    styleUrls: ['./achievement-bank-editor.component.scss'],
    standalone: false
})
export class AchievementBankEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  @Output() achievementIdsUpdated: EventEmitter<string[]> = new EventEmitter();

  componentAchievements: IComponentAchievement[];
  selectedAchievementIds: string[] = [];
  componentDestroyed$: Subject<boolean> = new Subject();
  achievementForm: UntypedFormGroup;

  constructor(
    private dataService: DataService,
    private formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit() {
    this.getAchievementData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      this.getAchievementData();
    }
  }

  getAchievementData() {
    this.dataService.getComponentAchievements(this.component.id).subscribe(data => {
      this.selectedAchievementIds = data ? data.map(x => (({ ...x }) as IInstanceAchievement).achievementInstance.id) : [];

      if (!this.achievementForm) {
        this.createForm();
      } else {
        this.setFormValues();
      }
    });
  }

  createForm() {
    this.achievementForm = this.formBuilder.group({
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      rowNumber: [this.component?.builderRowNumber],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.achievementForm) {
      return;
    }

    this.achievementForm.controls.isRequiredField.setValue(this.component?.templateField?.isRequiredField ?? false);
    this.achievementForm.controls.isPreviewField.setValue(this.component?.templateField?.isPreviewField ?? true);
    this.achievementForm.controls.isBuilderEnabled.setValue(this.component?.templateField?.isBuilderEnabled ?? true);
    this.achievementForm.controls.isHoverField.setValue(this.component?.templateField?.isHoverField ?? false);
    this.achievementForm.controls.isViewField.setValue(this.component?.templateField?.isViewField ?? true);
    this.achievementForm.controls.rowNumber.setValue(this.component?.builderRowNumber);
    this.achievementForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.achievementForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.achievementForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth ?? false);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.achievementForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.achievementForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.achievementForm.valid) {
      this.component.templateField.isRequiredField = this.achievementForm.controls.isRequiredField.value;
      this.component.templateField.isPreviewField = this.achievementForm.controls.isPreviewField.value;
      this.component.templateField.isBuilderEnabled = this.achievementForm.controls.isBuilderEnabled.value;
      this.component.templateField.isHoverField = this.achievementForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.achievementForm.controls.isViewField.value;
      this.component.builderRowNumber = this.achievementForm.controls.rowNumber.value;
      this.component.templateField.colspan = this.achievementForm.controls.colspan.value;
      this.component.templateField.colNumber = this.achievementForm.controls.colNumber.value;
      this.component.templateField.useMaxWidth = this.achievementForm.controls.useMaxWidth.value;
    }

    this.achievementIdsUpdated.next(this.selectedAchievementIds);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
