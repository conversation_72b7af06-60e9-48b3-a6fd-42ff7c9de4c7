import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { VersionModalComponent } from './version.component';

@Component({
    selector: 'app-global-version-component',
    changeDetection: ChangeDetectionStrategy.OnPush,
    template: '',
    standalone: false
})
export class GlobalVersionComponent {
  constructor(public modalController: ModalController) {}

  async presentModal() {
    const modal = await this.modalController.create({
      component: VersionModalComponent,
      cssClass: '',
    });
    await modal.present();
  }
}
