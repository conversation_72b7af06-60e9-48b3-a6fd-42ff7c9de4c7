<div
  cdkDropList
  [cdkDropListData]="section"
  (cdkDropListDropped)="addFieldDropped($event)"
  class="section"
  [style]="section.backgroundColor ? 'background:' + section.backgroundColor : 'background-color:none'">
  <div class="controls">
    <span class="sectionType">( {{ section.typeBw === 1 ? 'Static' : 'Dynamic' }} )</span>
    <div class="remove-section" (click)="removeSection(section.id)">
      <ion-icon [title]="'Remove Section'" color="primary" name="trash-outline"></ion-icon>
    </div>
    <div class="edit-section" (click)="selectSection(section)">
      <ion-icon [title]="'Edit Section'" color="primary" name="pencil-outline"></ion-icon>
    </div>
    <div class="reorder-section">
      @if (section.sortOrder !== 0) {
        <ion-icon name="chevron-up-outline" (click)="reorderSection(true, section)"></ion-icon>
      }
      @if (section.sortOrder !== template.sections.length - 1) {
        <ion-icon name="chevron-down-outline" (click)="reorderSection(false, section)"></ion-icon>
      }
    </div>
  </div>
  @if (templateForm) {
    <form [formGroup]="templateForm">
      <ng-container [formGroupName]="section.id">
        <div style="display: none">
          <app-text-input-control formControlName="sortOrder"></app-text-input-control>
        </div>
        <app-text-input-control formControlName="title" [label]="'Section title'"></app-text-input-control>
        <app-text-area-input-control formControlName="description" [label]="'Section description'"></app-text-area-input-control>
        <!-- <ion-row class="toggle-row">
        <ion-col class="toggle-col">
          <mat-slide-toggle color="primary" formControlName="hideBackground" class="toggle-switch"></mat-slide-toggle>
          <ion-label class="toggle-label">Hide background</ion-label>
        </ion-col>
      </ion-row> -->
      </ng-container>
      <!-- Component -->
      <div
        cdkDropList
        [cdkDropListData]="{ components: section.components, index: index }"
        (cdkDropListDropped)="reorderDropped(section.id, $event)"
        [ngClass]="{
          draggingOver: draggingOver,
        }">
        <!-- Input -->
        @for (groupedComponents of section.components | groupBy: 'builderRowNumber' | values; track groupedComponents) {
          <ion-row cdkDrag (mouseenter)="dragEntered($event)" (mouseleave)="dragEntered($event)">
            @for (component of groupedComponents | orderBy: 'templateField.colNumber'; track component) {
              <ion-col col-12 col-md-6 col-lg-4 col-xl-3>
                <div [ngClass]="{ 'drag-component': activeComponentId === component.id }">
                  <div>
                    @if (activeComponentId === component.id) {
                      <ion-icon name="apps-sharp" class="cdk-drag-handle" cdkDragHandle></ion-icon>
                      <div class="option-buttons">
                        <ion-icon class="left-icon" color="light" stytle="font-size:larger" (click)="deleteComponentConfirmation(section, component)" name="trash-outline"></ion-icon>
                        @if (component?.isLocked === true && section.typeBw === 2) {
                          <ion-icon color="light" stytle="font-size:larger" (click)="toggleComponentLocked(section.id, component, false)" name="lock-closed-outline"></ion-icon>
                        }
                        @if (component?.isLocked !== true && section.typeBw === 2) {
                          <ion-icon color="light" stytle="font-size:larger" (click)="toggleComponentLocked(section.id, component, true)" name="lock-open-outline"></ion-icon>
                        }
                      </div>
                    }
                    @if (activeComponentId !== component.id) {
                      @if (component?.isLocked === true && section.typeBw === 2 && activeComponentId !== component.id) {
                        <ion-icon class="lock-icon" color="light" stytle="font-size:larger" name="lock-closed-outline"></ion-icon>
                      }
                    }
                    <app-form-control-selector
                      [viewType]="3"
                      (click)="componentSelected(component)"
                      [component]="component"
                      [instance]="instance"
                      [featureId]="featureId"
                      [formGroup]="templateForm"
                      [formGroupName]="section.id"
                      [showPlaceHolder]="section.typeBw === sectionTypeEnums.Dynamic || component?.componentType?.name === 'Listing Details'"></app-form-control-selector>
                  </div>
                </div>
              </ion-col>
            }
          </ion-row>
        }
      </div>
    </form>
  }
</div>
