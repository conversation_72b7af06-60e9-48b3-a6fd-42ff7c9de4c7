import { Component, Input, OnInit } from '@angular/core';
import { IOrganizationLite } from '@app/core/contracts/contract';
import { ModalController } from '@ionic/angular';

@Component({
    selector: 'app-add-organization',
    templateUrl: './add-organization.component.html',
    styleUrls: ['./add-organization.component.scss'],
    standalone: false
})
export class AddOrganizationComponent implements OnInit {
  @Input() availableUserOrganizations: IOrganizationLite[];
  filteredOrganizations: IOrganizationLite[];
  selectedOrgId: string;

  constructor(private modalController: ModalController) {}

  ngOnInit() {
    this.filteredOrganizations = Object.assign(this.availableUserOrganizations);
  }

  updateAssignedList(orgId: string) {
    this.selectedOrgId = orgId;
  }

  search(value: any): void {
    this.filteredOrganizations = Object.assign(this.availableUserOrganizations);
    this.filteredOrganizations = this.filteredOrganizations.filter(val => val.name.toLowerCase().includes(value.detail.value.toLowerCase()));
  }

  onAddTo() {
    this.modalController.dismiss(this.selectedOrgId);
  }

  onClose() {
    this.modalController.dismiss();
  }
}
