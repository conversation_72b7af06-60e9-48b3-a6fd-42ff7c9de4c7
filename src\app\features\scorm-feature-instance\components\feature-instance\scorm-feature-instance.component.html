@if (instance && !scormLoading) {
  <app-page-header
    [scrollPosition]="scrollPosition"
    [viewType]="routeParams?.viewType ?? 0"
    [instance]="instance"
    [content]="content"
    [routeParams]="routeParams"
    [onlyContent]="instance?.isDefault !== true && instance?.feature?.featureType?.name !== 'Accredited Learning Container Pages'"></app-page-header>
  <ng-template #content>
    <app-scorm-template
      class="no-tabs"
      [templateId]="getTab()?.tab?.templateId"
      [instance]="instance"
      [featureTab]="getTab()"
      [routeParams]="routeParams"
      (instanceChanged)="setData()"></app-scorm-template>
  </ng-template>
}
@if (scormLoading) {
  <ion-grid class="scormloader">
    <ion-row class="ion-align-items-center">
      <ion-col size="12">
        <img class="img" src="assets/images/EdgeFactor-EF_rings-2018-white_small_png.png" />
        <br />
        <ion-spinner color="primary" name="dots"></ion-spinner>
      </ion-col>
    </ion-row>
  </ion-grid>
}
