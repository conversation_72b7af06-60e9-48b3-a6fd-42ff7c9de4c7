.container {
  display: flex;
  justify-content: center;

  .grid-container {
    .view-options-row {
      display: flex;
      flex-direction: row;
      padding: 0px;
      padding-right: 8px;
      color: white;
      align-items: center;
      border-bottom: 1px solid #333333;
      margin-bottom: 10px;
      padding-top: 10px;

      .view-options-col {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        padding: 0px;

        ion-button {
          --background: #333333 !important;
          border-radius: 5px;
          color: white;
          font-size: 1em;
          text-transform: none;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 0.5em;
          height: 42px;
          font-weight: 400;
          margin-left: 0px !important;

          .button-text {
            font-weight: 400;
            font-size: 1em !important;
          }
        }

        ion-button:focus {
          border: 1px solid #f99e00;
        }

        ion-searchbar {
          --ion-background-color: #333333;
          --icon-color: white;
          --color: white;
          --box-shadow: 0;
          --border-radius: 5px;
          max-width: 300px;
          margin-left: 0px !important;
          padding-left: 0px !important;
        }
      }

      .view-options-col-right {
        justify-content: flex-end;

        .view-options {
          font-size: 1.3em;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
        }

        .view-options:hover {
          color: #f99e00;
          cursor: pointer;
        }

        .view-options-padding {
          padding-right: 16px;
        }

        .button-container {
          margin-left: 16px;

          ion-button {
            width: auto;
            float: right;
            padding-right: 0px;
            margin-right: 0px !important;
            height: 42px;

            background: var(--ion-color-base);
            color: var(--ion-color-contrast);
            font-size: 1.125em;
            font-weight: 500;
          }
        }
      }
    }

    ion-label {
      font-size: 0.875em;
      font-weight: 500;
      padding-left: 5px;
    }

    mat-icon {
      height: 30px;
      width: 30px;
    }

    .active.active {
      color: #f99e00;
    }

    .text-description {
      margin-left: 5px;
      font-size: 1em;
    }
  }

  .user-search-custom {
    display: flex;
    flex-direction: row;
    width: fit-content;
    margin-top: -7px !important;
    height: 50px;
    background-color: #555555;
    border: 1px solid #4E4E4E;
    border-radius: 5px;

    ion-label {
      line-height: 50px;
      font-size: 16px !important;
    }

    app-select-option-control {
      margin-top: -8px !important;
      width: 250px;
    }
  }
}

.page-margins {
  margin-left: var(--page-margin-left);
  margin-right: var(--page-margin-right);
}

.page-margins-player {
  padding-left: var(--page-margin-left-player);
  padding-right: var(--page-margin-right-player);
}