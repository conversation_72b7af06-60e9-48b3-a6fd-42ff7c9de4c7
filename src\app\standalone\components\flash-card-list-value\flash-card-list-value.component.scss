.parent-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  align-items: stretch;
  height: 100%;
  width: 103%;
  padding-bottom: 10px;
  padding-left: 0px;

  @media (min-width: 1280px) {
    ion-card {
      flex: 0 23%;

      .inner-container {
        .heading {
          font-size: 18px;
        }
        .description {
          font-size: 16px;
        }
      }
    }
  }

  @media (min-width: 769px) and (max-width: 1280px) {
    ion-card {
      flex: 0 30.5%;

      .inner-container {
        .heading {
          font-size: 16px;
        }
        .description {
          font-size: 14px;
        }
      }
    }
  }

  @media (min-width: 480px) and (max-width: 769px) {
    ion-card {
      flex: 0 46.5%;

      .inner-container {
        .heading {
          font-size: 16px;
        }
        .description {
          font-size: 14px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    ion-card {
      flex: 0 45.25%;

      .inner-container {
        .heading {
          font-size: 14px;
        }
        .description {
          font-size: 12px;
        }
      }
    }
  }
  ion-card {
    padding: 10px;
    border-radius: 11px;
    background-color: rgb(50, 50, 50);
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid black;
    margin-left: 0px;
    max-width: none;
    height: none;
    aspect-ratio: 5/6;
    width: 200px;

    .inner-container {
      .heading {
        color: white;
        text-align: center;
        font-weight: 900;
        letter-spacing: 0.03em;
        padding-top: 5px;
        margin-bottom: 5px;
        line-height: 1;
      }

      .image {
        width: 100%;
        height: 100%;
        min-width: 100%;
      }

      .description {
        color: rgb(170, 170, 170);
        text-align: center;
        line-height: 1.1;
      }
    }
    .card-footer {
      position: absolute;
      right: 10px;
      bottom: 10px;
      ion-icon {
        color: white;
        font-size: 20px;
      }
    }
  }

  .card-selected {
    border: 2px solid #6f4d1a !important;
    background-color: #1d1d1d;
  }
}
