import { Injectable } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { IComponent, ISection } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { Subject } from 'rxjs';

@Injectable()
export class BuilderService {
  public acceptedImageFormats: KeyValue[] = [
    { id: 1, value: 'image/*' },
    { id: 2, value: '.jpeg' },
    { id: 4, value: '.gif' },
    { id: 8, value: '.png' },
  ];
  acceptedVideoFormats: KeyValue[] = [
    { id: 1, value: 'video/*' },
    { id: 2, value: '.avi' },
    { id: 4, value: '.mov' },
    { id: 8, value: '.mp4' },
  ];
  acceptedFileFormats: KeyValue[] = [
    { id: 1, value: 'application/*' },
    { id: 2, value: '.pdf' },
    { id: 4, value: '.ppt' },
    { id: 8, value: '.pptx' },
    { id: 16, value: '.doc' },
    { id: 32, value: '.docx' },
    { id: 64, value: '.xlsx' },
    { id: 128, value: '.msword' },
    { id: 256, value: '.txt' },
    { id: 512, value: '.jpeg' },
    { id: 1024, value: '.png' },
  ];
  public acceptedImageAndVideoFormats: KeyValue[] = [
    { id: 1, value: 'image/*' },
    { id: 2, value: '.jpeg' },
    { id: 4, value: '.gif' },
    { id: 8, value: '.png' },
    { id: 16, value: 'video/*' },
    { id: 32, value: '.avi' },
    { id: 64, value: '.mov' },
    { id: 128, value: '.mp4' },
  ];
  allFormats: KeyValue[] = [
    { id: 1, value: 'application/*' },
    { id: 2, value: '.pdf' },
    { id: 4, value: '.ppt' },
    { id: 8, value: '.pptx' },
    { id: 16, value: '.doc' },
    { id: 32, value: '.docx' },
    { id: 64, value: '.xlsx' },
    { id: 128, value: '.msword' },
    { id: 256, value: '.txt' },
    { id: 512, value: 'video/*' },
    { id: 1024, value: '.avi' },
    { id: 2048, value: '.mov' },
    { id: 4096, value: '.mp4' },
    { id: 8192, value: 'image/*' },
    { id: 16384, value: '.jpeg' },
    { id: 32768, value: '.gif' },
    { id: 65536, value: '.png' },
  ];
  acceptedHtml5Formats: KeyValue[] = [{ id: 1, value: 'application/zip' }];

  selectedComponent$: Subject<IComponent | null> = new Subject();
  selectedSection$: Subject<ISection | null> = new Subject();
  templateFormCreated$: Subject<UntypedFormGroup> = new Subject();
  sectionUpdated$: Subject<ISection> = new Subject();
  imageContentTypes = ['image/avif', 'image/bmp', 'image/gif', 'image/vnd.microsoft.icon', 'image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml', 'image/tiff', 'image/webp'];
  videoContentTypes = ['application/octet-stream', 'video/mp4', 'video/x-flv', 'application/x-mpegURL', 'video/MP2T', 'video/3gpp', 'video/quicktime', 'video/x-msvideo', 'video/x-ms-wmv'];
  fileContentTyps = ['text/plain', 'multipart/form-data', 'text/xml', 'text/csv', 'text/plain', 'application/xml', 'application/zip', 'application/pdf'];
}
