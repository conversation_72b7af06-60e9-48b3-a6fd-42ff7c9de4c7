import { Component, DoCheck, Input, OnInit } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { BaseValueComponent } from '@app/standalone/components/base-control/base-value.component';


@Component({
    selector: 'app-spacing-value',
    templateUrl: './spacing-value.component.html',
    styleUrls: ['./spacing-value.component.scss'],
    imports: [IonicModule]
})
export class SpacingValueComponent extends BaseValueComponent implements OnInit {
  oldValue: any;

  constructor() {
    super()
  }

  ngOnInit(): void {
    this.setData();
  }

  override setData(): void {
    if (this.instanceComponent?.value != this.oldValue) {
      this.oldValue = this.instanceComponent?.value;
    }
  }
}
