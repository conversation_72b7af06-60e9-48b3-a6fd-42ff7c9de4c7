import { Injectable, signal } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { content } from 'html2canvas/dist/types/css/property-descriptors/content';

@Injectable({
  providedIn: 'root',
})
export class MetaTagService {
  constructor(
    private meta: Meta,
    private titleService: Title
  ) {}

  setInitialMetaTags() {
    this.titleService.setTitle('Edge Factor');
    this.meta.addTag({ name: 'color-scheme', content: 'light dark' });
    this.meta.addTag({ name: 'viewport', content: 'viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no' });
    this.meta.addTag({ name: 'format-detection', content: 'telephone=no' });
    this.meta.addTag({ name: 'msapplication-tap-highlight', content: 'no' });

    const defaultDescription =
      "Edge Factor's smart platform and story-driven tools inspire people to explore, prepare and connect on their career journey." +
      ' Together with schools, companies and service providers, we empower learners of all ages to discover careers and get connected to real-world opportunities.';

    this.meta.addTag({ name: 'description', content: defaultDescription });

    // Add default Open Graph tags programmatically
    this.meta.addTag({ property: 'og:type', content: 'website' });
    this.meta.addTag({ property: 'og:url', content: window.location.href });
    this.meta.addTag({ property: 'og:title', content: 'Edge Factor' });
    this.meta.addTag({ property: 'og:description', content: defaultDescription });
    this.meta.addTag({ property: 'og:image', content: window.location.origin + '/assets/images/EFLogo_Black.png' });

    // Add default Twitter Card tags
    this.meta.addTag({ name: 'twitter:card', content: 'summary_large_image' });
    this.meta.addTag({ name: 'twitter:url', content: window.location.href });
    this.meta.addTag({ name: 'twitter:title', content: 'Edge Factor' });
    this.meta.addTag({ name: 'twitter:description', content: defaultDescription });
    this.meta.addTag({ name: 'twitter:image', content: window.location.origin + '/assets/images/EFLogo_Black.png' });

    // add to homescreen for ios
    this.meta.addTag({ name: 'apple-mobile-web-app-capable', content: 'yes' });
    this.meta.addTag({ name: 'apple-mobile-web-app-status-bar-style', content: 'black' });
    this.meta.addTag({ name: 'viewport', content: 'viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no' });
    this.meta.addTag({ name: 'format-detection', content: 'telephone=no' });
    this.meta.addTag({ name: 'msapplication-tap-highlight', content: 'no' });
    this.meta.addTag({ name: 'theme-color', content: '#f99e00' });
  }

  updateMetaTags(title: string, description: string, iconUrl: string): void {
    this.meta.updateTag({ name: 'description', content: description });

    // Add default Open Graph tags programmatically
    this.meta.updateTag({ property: 'og:type', content: 'website' });
    this.meta.updateTag({ property: 'og:url', content: window.location.href });
    this.meta.updateTag({ property: 'og:title', content: title });
    this.meta.updateTag({ property: 'og:description', content: description });
    this.meta.updateTag({ property: 'og:image', content: iconUrl });

    // Add default Twitter Card tags
    this.meta.updateTag({ name: 'twitter:card', content: 'summary_large_image' });
    this.meta.updateTag({ name: 'twitter:url', content: window.location.href });
    this.meta.updateTag({ name: 'twitter:title', content: title });
    this.meta.updateTag({ name: 'twitter:description', content: description });
    this.meta.updateTag({ name: 'twitter:image', content: iconUrl });
  }
}
