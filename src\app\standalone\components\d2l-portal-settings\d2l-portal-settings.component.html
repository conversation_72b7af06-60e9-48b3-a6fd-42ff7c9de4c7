<ion-card class="card-container">
  @if (dataLoaded) {
    <form [formGroup]="orgSsoAuthGroupForm">
      <div class="top-form-container">
        <h4>Input your OAuth 2.0 tokens to authenticate Edge Factor to connect with Brightspace</h4>
        <ion-row>
          <ion-col>
            <app-text-input-control formControlName="externalId" [backgroundColor]="'#292929'" [label]="'Tenant ID'" [placeHolder]="'Add Tenant Id'"></app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control formControlName="externalAuthId" [backgroundColor]="'#292929'" [label]="'Client ID'" [placeHolder]="'Add Client Id'"></app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control formControlName="externalAuthSecret" [backgroundColor]="'#292929'" [label]="'Secret ID'" [placeHolder]="'Add Secret Id'"></app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control formControlName="externalUrl" [backgroundColor]="'#292929'" [label]="'Base Brightspace URL'" [placeHolder]="'Add Base Brightspace URL'"></app-text-input-control>
          </ion-col>
        </ion-row>
      </div>
      <div class="middle-line"><hr /></div>
      <div class="slider-form-container">
        <h4>Manage the D2L data that is synced to Edge Factor.</h4>
        <ion-card class="card-container">
          <ion-row>
            <ion-col class="heading-col">
              <div>
                <div class="heading">People</div>
                <div class="sub-heading">
                  <span>Auto-roster your educators and students into the platform</span>
                </div>
              </div>
            </ion-col>
            <ion-col size="{{ layoutService.currentScreenSize === 'lg' ? 1 : 3 }}" class="slider-col">
              <mat-slide-toggle formControlName="syncPeople" color="primary"></mat-slide-toggle>
            </ion-col>
          </ion-row>
        </ion-card>
        <ion-card class="card-container">
          <ion-row>
            <ion-col class="heading-col">
              <div>
                <div class="heading">Classes</div>
                <div class="sub-heading">
                  <span>Load your D2L classes into the Classroom feature</span>
                </div>
              </div>
            </ion-col>
            <ion-col size="{{ layoutService.currentScreenSize === 'lg' ? 1 : 3 }}" class="slider-col">
              <mat-slide-toggle formControlName="syncClasses" color="primary"></mat-slide-toggle>
            </ion-col>
          </ion-row>
        </ion-card>
        <ion-card class="card-container">
          <ion-row>
            <ion-col class="heading-col">
              <div>
                <div class="heading">Courses</div>
                <div class="sub-heading">
                  <span>Load your D2L classes into the Courses feature</span>
                </div>
              </div>
            </ion-col>
            <ion-col size="{{ layoutService.currentScreenSize === 'lg' ? 1 : 3 }}" class="slider-col">
              <mat-slide-toggle formControlName="syncCourses" color="primary"></mat-slide-toggle>
            </ion-col>
          </ion-row>
        </ion-card>
      </div>
      <div class="middle-line"><hr /></div>
      <div class="role-form-container">
        @if (orgSsoAuth?.organizationSsoroleMappings !== null && orgSsoAuth.organizationSsoroleMappings.length > 0) {
          <h4>Manage the default role that people will receive when they login to Edge Factor. You can always change their role on the Users tab.</h4>
          @for (d2lRole of orgSsoAuth.organizationSsoroleMappings; track d2lRole) {
            <ion-card class="card-container">
              <ion-row>
                <ion-col size="9">
                  <div class="inner-container">
                    People with the D2L role of
                    <div class="static-input-container">
                      <ion-input disabled="true" [value]="d2lRole.externalRoleName" type="text"></ion-input>
                    </div>
                    will receive
                    <div class="drop-down-container">
                      <ion-select [value]="d2lRole.roleId" (ionChange)="manageChange($event, d2lRole)" placeholder="Role" interface="popover">
                        <ion-select-option value="" hidden="hidden">None</ion-select-option>
                        @for (role of userRoles; track role) {
                          <ion-select-option [value]="role.id"> {{ role.name }} </ion-select-option>
                        }
                      </ion-select>
                    </div>
                  </div>
                </ion-col>
              </ion-row>
            </ion-card>
          }
        }
      </div>
    </form>
  }
</ion-card>
