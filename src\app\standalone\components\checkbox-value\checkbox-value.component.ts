import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Subject } from 'rxjs';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-checkbox-value',
  templateUrl: './checkbox-value.component.html',
  styleUrls: ['./checkbox-value.component.scss'],
  imports: [IonicModule],
})
export class CheckboxValueComponent implements OnInit, OnChanges {
  @Input() value?: string;
  @Input() inheritedPropertyValue: string | null;
  @Input() labelPlacement = 'start';
  @Input() justify = 'start';
  @Input() indeterminate = false;
  @Input() label!: string;
  @Output() checkboxChanged = new EventEmitter();
  checkboxValue = false;

  componentDestroyed$: Subject<boolean> = new Subject();

  constructor() {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['inheritedPropertyValue'] || changes['value']) {
      this.setCheckboxValue();
    }
  }

  ngOnInit() {
    this.setCheckboxValue();
  }

  setCheckboxValue() {
    if (this.inheritedPropertyValue && this.inheritedPropertyValue !== '') {
      this.checkboxValue = this.inheritedPropertyValue.toLowerCase() === 'true';
    } else if (this.value && this.value !== '') {
      this.checkboxValue = this.value.toLowerCase() === 'true';
    }
  }

  checkboxInteraction(event: any) {
    this.checkboxChanged.emit(event.detail.checked.toString());
  }
}
