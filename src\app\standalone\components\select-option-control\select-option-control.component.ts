import { <PERSON><PERSON><PERSON>, NgStyle } from '@angular/common';
import { AfterViewInit, Component, EventEmitter, forwardRef, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { AddSearchModalComponent } from '@app/standalone/modals/add-search-modal/add-search-modal.component';
import { IonicModule, PopoverController } from '@ionic/angular';
import { debounceTime, map, Subject, takeUntil } from 'rxjs';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';

@Component({
    selector: 'app-select-option-control',
    templateUrl: './select-option-control.component.html',
    styleUrls: ['./select-option-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => SelectOptionControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => SelectOptionControlComponent),
        },
    ],
    imports: [NgClass, IonicModule, NgStyle]
})
export class SelectOptionControlComponent extends BaseControlComponent implements AfterViewInit, OnInit, OnDestroy {
  @ViewChild('select') selectEl: HTMLIonSelectElement;
  @Input() toolTip!: string;
  @Input() name!: string;
  @Input() override label!: string;
  @Input() override textValue: string | null;
  @Input() placeHolder!: string;
  @Input() options!: KeyValue[];
  @Input() backgroundColor = '#181818';
  @Input() identifierText: string;
  @Input() multiple = false;
  @Input() isCustom = false;
  @Input() linkTypeName: string | undefined;
  @Input() criteriaType: string | undefined;
  @Input() showLabel = true;
  @Input() showSelected = false;
  @Input() noPadding = false;
  @Input() noMargin = false;
  @Input() noBorder = false;
  @Input() sidePanelPadding = false;
  @Input() floating = false;
  @Input() allowClear = false;
  @Output() emitSelected: EventEmitter<any> = new EventEmitter();
  @Output() valueChanged = new EventEmitter<string>();
  isSearchDropDownModal = false;
  displayValue: string;

  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private dataService: DataService,
    private popoverController: PopoverController
  ) {
    super();
  }

  ngOnInit(): void {
    if (this.linkTypeName === 'Features' || this.linkTypeName === 'Organizations' || this.linkTypeName === 'Educators' || this.linkTypeName === 'Instances') {
      this.isSearchDropDownModal = true;
      this.setDisplayValue();
    } else if (
      this.linkTypeName === 'Users' ||
      this.linkTypeName === 'Feature Types' ||
      this.linkTypeName === 'Products' ||
      this.linkTypeName === 'Notification Trigger' ||
      this.linkTypeName === 'Section Types'
    ) {
      this.setDisplayValue();
    } else if (this.options?.length <= 0 && !this.linkTypeName) {
      this.defaultData();
    } else if (this.textValue) {
      this.setDisplayValue();
    }
  }

  ngAfterViewInit(): void {
    // Pass a custom class to each select interface for styling
    const selects: any = document.querySelectorAll('.custom-options');
    for (let i = 0; i < selects.length; i++) {
      selects[i].interfaceOptions = {
        cssClass: 'my-custom-interface',
      };
    }
  }

  clear() {
    this.selectEl.value = undefined;
    this.valueChange({ target: { value: null } });
  }

  valueChange(event: any) {
    this.setValue(event);
    this.valueChanged.emit(event.target.value);
  }

  defaultData() {
    this.dataService
      .getTagChildren(null)
      .pipe(
        map(tags => {
          this.options = tags.map(t => {
            return { id: t.id, value: t.name } as KeyValue;
          });
        })
      )
      .subscribe();
  }

  async addSearchModalOpen(event: any) {
    if (this.isSearchDropDownModal) {
      const popover = await this.popoverController.create({
        component: AddSearchModalComponent,
        cssClass: 'add-search-modal',
        componentProps: { linkTypeName: this.linkTypeName, criteriaType: this.criteriaType, options: this.options },
        event: event,
        side: 'bottom',
      });

      popover.onDidDismiss().then(result => {
        if (result.data) {
          this.displayValue = result.data.name;
          this.writeValue(result.data.id);
          this.emitSelected.emit(result.data);
        }
      });

      await popover.present();
    }
  }

  setDisplayValue() {
    if (this.textValue) {
      switch (this.linkTypeName) {
        case 'Organizations':
          this.dataService
            .getOrganizationById(this.textValue)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe(organization => {
              this.displayValue = organization.name;
            });
          break;
        case 'Features':
          this.dataService
            .getFeatureById(this.textValue)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe(feature => {
              this.displayValue = feature.title;
            });
          break;
        case 'Instances':
          this.dataService
            .getInstance(this.textValue)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe(instance => {
              this.displayValue = instance.title;
            });
          break;
        case 'Products':
          this.dataService
            .getProductById(this.textValue)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe(product => {
              this.displayValue = product.name;
            });
          break;
        default:
          this.displayValue = this.options?.find(x => x.id === this.textValue)?.value ?? '';
      }
    }
  }

  identify(index: number, item: KeyValue) {
    return item.value;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
