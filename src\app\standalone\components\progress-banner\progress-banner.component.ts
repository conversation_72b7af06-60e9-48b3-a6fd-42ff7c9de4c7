import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { BannerService, BannerType } from '@app/core/services/banner.service';
import { InstanceService } from '@app/core/services/instance-service';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-progress-banner',
  standalone: true,
  imports: [CommonModule, IonicModule],
  templateUrl: './progress-banner.component.html',
  styleUrls: ['./progress-banner.component.scss'],
})
export class ProgressBannerComponent {
  @Input() screenSize = 'lg';

  BannerType = BannerType; // Make enum available in template

  constructor(
    public bannerService: BannerService,
    public instanceService: InstanceService
  ) {}

  /**
   * Dismiss the banner
   */
  dismiss(): void {
    this.bannerService.hideBanner();
  }

  /**
   * Handle continue button click
   */
  onContinue(): void {
    // Store the current instance before hiding the banner
    const currentState = this.bannerService.bannerState();
    const instance = currentState.instance;

    // Hide the banner first
    this.bannerService.hideBanner();

    // Only navigate if we have a valid instance
    if (instance) {
      this.instanceService.openInstance(
        'instance',
        instance.slug || instance.id, // Use slug if available, otherwise use id
        null,
        null,
        null,
        true
      );
    }
  }
}
