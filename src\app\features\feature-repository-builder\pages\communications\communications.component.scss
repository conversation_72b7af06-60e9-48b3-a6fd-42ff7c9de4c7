:host {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  align-content: stretch;

  ion-card {
    margin-inline: 0px;
  }

  p {
    color: #aaa;
  }

  ion-card {
    background-color: #444;
    ion-card-title {
      color: white;
    }
    ion-card-subtitle {
      color: #aaa;
    }

    .mat-mdc-slide-toggle{
      margin: 4px;
    }
  }

  .ion-padding-start {
    padding-left: unset;
    padding-inline-start: var(--ion-padding, 0px);
  }
}
