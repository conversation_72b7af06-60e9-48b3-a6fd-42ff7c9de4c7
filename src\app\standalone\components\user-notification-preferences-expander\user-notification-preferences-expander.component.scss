:host {
  mat-expansion-panel {
    background-color: rgba(41, 41, 41);
    box-shadow: none;
    margin-bottom: 15px !important;
    margin: 0vw 1vw 0vw 0.9vw;

    ion-card{
      margin: 0px;
      margin-bottom: 10px;
    }

    ion-col{
      padding-left: 10px;
    }

    .expansion-panel-header {
      height: 100%;
      border-radius: 8px;
      padding: 5px 15px 15px 15px;

      .inner-panel {
        .heading {
          font-style: italic;
          font-weight: bold;
          font-size: 20px;
          color: white;
          margin-top: 10px;
        }

        .sub-heading {
          // font-style: italic;
          color: rgba(170, 170, 170);

          .expiry-date {
            margin-left: 5px;
            color: white;
          }
        }

        .sub-heading-margin {
          margin-top: 10px;
          margin-bottom: 5px;
        }
      }

      .role-heading {
        color: rgba(170, 170, 170);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: auto;
        margin-right: 20px;
      }
    }

    .expansion-panel-header:hover {
      background-color: rgba(41, 41, 41) !important;
    }

    ::ng-deep .mat-expansion-indicator::after,
    .mat-expansion-panel-header-description {
      border-color: white;
    }

    .center-col {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .header-row{
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }
}
