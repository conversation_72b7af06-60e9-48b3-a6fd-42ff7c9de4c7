<ng-container>
  <div class="field-container">
    <div style="margin: 8px">
      <ion-label position="stacked" style="font-size: 14px"> Select Questions </ion-label>
      <ion-input placeholder="Find a Question" [(ngModel)]="query" (ngModelChange)="search()"><ion-icon style="margin: 5px" name="search-outline"></ion-icon></ion-input>
    </div>
    @if (questions) {
      <div class="radio-group-container">
        @for (question of questions; track question) {
          <div class="properties-container">
            <div class="property">
              <div class="property-left">
                <ion-checkbox (ionChange)="checkboxChanged($event, question.id)" [checked]="isSelected(question.id)"></ion-checkbox>
                <div style="display: flex; flex-direction: column">
                  <div class="heading">{{ question?.title }}</div>
                  <div class="sub-heading">
                    <!-- <span>{{assessmentQuestion?.question?.category}}</span>
                    <span>&#xb7;</span> -->
                    <span>{{ question?.questionType?.name }}</span>
                    <!-- <span>&#xb7;</span> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        }
      </div>
    }
  </div>
</ng-container>
