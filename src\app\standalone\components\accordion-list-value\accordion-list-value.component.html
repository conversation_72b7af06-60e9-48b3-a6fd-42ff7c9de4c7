<div class="accordion-parent-container">
  @if (accordionItemList.length > 0) {
    <ion-accordion-group expand="inset" class="accordion-inner-group-container" (ionChange)="change()">
      @for (item of accordionItemList; track item) {
        <ion-accordion class="ion-no-margin" toggleIcon="chevron-down-outline" toggleIconSlot="end">
          <ion-item slot="header">
            <ion-label>{{ item.title }}</ion-label>
          </ion-item>
          <div class="inner-content ion-padding" slot="content">
            {{ item.description }}
          </div>
        </ion-accordion>
      }
    </ion-accordion-group>
  }
</div>
