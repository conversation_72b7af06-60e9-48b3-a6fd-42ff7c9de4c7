:root {
  $overflow-override: auto;
}

.content-no-header {
  display: contents;
  height: 100%;
  --padding-left: 1.25vw !important;
  --padding-right: 1.25vw !important;
}

.full-container{
    position: relative;
    min-height: 100%;
}

ion-content::part(scroll) {
  overflow-y: overlay !important;
}

:host {
  background-color: #1e1e1e;
  color: white;

  ion-grid {
    height: 100%;
    min-height: 100%;
  }

  ion-row {
    height: 100%;
  }

  @media screen and (max-width: 960px) {
    .vertical-row {
      gap: 20px;
      flex-direction: column;
    }
  }

  .description {
    margin-bottom: 25px;
    margin-left: var(--page-margin-left-player);
  }

  .section {
    flex: 1;
    position: relative;
    overflow: visible;
  }

  .section-no-background {
    flex: 1;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
    display:contents;
  }
}

::ng-deep .mdc-tab__content {
  overflow: hidden;
}

.player-outer-container {
  margin-right: var(--page-margin-right-player);
  margin-left: var(--page-margin-left-player);
  overflow-y: auto;
  height: calc(100% - 1px);
  .player-container {
    border: 1px solid #f99e00;
    background-color: #393939;
    border-radius: 11px;
  }
}

.player-side-panel-container {
  overflow-y: auto;
  height: calc(100% - 249px);
  width: 20%;
  background-color: #1e1e1e;
  margin-top: 5px;
  margin-right: 5px;
  border-radius: 10px;
}

.top-border {
  border-color: #171717;
  border-width: 1px;
  border-style: solid;

  width: 100%;
}

.player-inner-container {
  width: 100%;
  float: left;
}

ion-col {
  min-height: 0px !important;
}
