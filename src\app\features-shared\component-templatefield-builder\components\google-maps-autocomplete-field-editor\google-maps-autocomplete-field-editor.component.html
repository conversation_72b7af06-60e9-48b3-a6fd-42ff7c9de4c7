@if (form) {
  <form class="parent-form-container" [formGroup]="form">
    <ion-grid>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="header">Edit {{ component.componentType.name }}</div>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Add field label...'" formControlName="label"> </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Description'"
            [placeHolder]="'Add field description...'"
            formControlName="description"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Add field caption...'" formControlName="caption"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Placeholder'"
            [placeHolder]="'Add field placeholder...'"
            formControlName="placeHolderText"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Instance Sort Order'"
            [placeHolder]="'Add field instance sort order...'"
            formControlName="instanceSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="row-number-col">
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Row Number'"
            [placeHolder]="'Add field row number...'"
            formControlName="rowNumber"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="col-number-col">
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Column Number (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add column number (Out of 12)...'"
            formControlName="colNumber"
            [type]="'number'">
          </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="col-span-col">
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Colspan (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add field colspan (Out of 12)...'"
            formControlName="colspan"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="bottom-sliders-col">
          <ion-card>
            <ion-card-content>
              <mat-slide-toggle class="full-width" color="primary" formControlName="showMap">Show map</mat-slide-toggle>
              <app-field-checkboxes-base [baseForm]="form"></app-field-checkboxes-base>
              <mat-slide-toggle class="full-width" color="primary" formControlName="isVisibleRepository">Is Visible in Repository</mat-slide-toggle>
              <mat-slide-toggle class="full-width" color="primary" formControlName="isInherit">System Property</mat-slide-toggle>
              @if (isInheritControl.value) {
                <app-system-property-selector [templateField]="component.templateField" [formGroup]="form"></app-system-property-selector>
              }
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
