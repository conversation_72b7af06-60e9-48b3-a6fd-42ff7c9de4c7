import { ChipListComponent } from '@app/standalone/components/chip-list/chip-list.component';
import { DynamicTextInputControlComponent } from '@app/standalone/components/dynamic-text-input-control/dynamic-text-input-control.component';
import { FileUploadControlComponent } from '@app/standalone/components/file-upload-control/file-upload-control.component';
import { PersonaChipListComponent } from '@app/standalone/components/persona-chip-list/persona-chip-list.component';
import { SectionEditComponent } from '@app/standalone/components/section-edit/section-edit.component';
import { SelectOptionControlComponent } from '@app/standalone/components/select-option-control/select-option-control.component';
import { TagPopoverComponent } from '@app/standalone/components/tag-popover/tag-popover.component';
import { TagSelectOptionControlComponent } from '@app/standalone/components/tag-select-option-control/tag-select-option-control.component';
import { TextAreaInputControlComponent } from '@app/standalone/components/text-area-input-control/text-area-input-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { InstancePublishButtonComponent } from './instance-publish-button/instance-publish-button.component';
import { OrgPublishButtonComponent } from './org-publish-button/org-publish-button.component';
import { FeatureRepositoryAnalyticsComponent } from './pages/analytics/analytics.component';
import { FeatureRepositoryCommunicationsComponent } from './pages/communications/communications.component';
import { AddMediaComponent } from './pages/dashboard/add-media/add-media.component';
import { AddOrganizationComponent } from './pages/dashboard/add-organization/add-organization.component';
import { RepositoryDashboardEditorDialogComponent } from './pages/dashboard/repository-dashboard-editor-dialog/repository-dashboard-editor-dialog.component';
import { RepositoryDashboardTableComponent } from './pages/dashboard/repository-dashboard-table/repository-dashboard-table.component';
import { FeatureRepositoryDashboardComponent } from './pages/dashboard/template/feature-repository-dashboard/feature-repository-dashboard.component';
import { MediaManagerRepositoryComponent } from './pages/dashboard/template/media-manager/dashboard.component';
import { FeatureRepositoryBuilderComponent } from './pages/feature-repository-builder/feature-repository-builder.component';
import { FeatureRepositoryBuilderFieldsComponent } from './pages/template/builder-fields/builder-fields.component';
import { SectionComponent } from './pages/template/builder-fields/section/section.component';
import { TabButtonActionComponent } from './pages/template/builder-fields/tab-button-action/tab-button-action.component';
import { TabButtonActionsComponent } from './pages/template/builder-fields/tab-button-actions/tab-button-actions.component';
import { TabSettingsComponent } from './pages/template/builder-fields/tab-settings/tab-settings.component';
import { TabsComponent } from './pages/template/builder-fields/tabs/tabs.component';
import { TemplateFieldComponent } from './pages/template/builder-fields/template-field/template-field.component';
import { TemplateComponent } from './pages/template/builder-fields/template/template.component';
import { FeatureRepositoryOverviewComponent } from './pages/template/overview/overview.component';
import { FeatureRepositoryTemplateComponent } from './pages/template/template.component';
import { BuilderService } from './services/builder-service';
import { CheckboxValueComponent } from '@app/standalone/components/checkbox-value/checkbox-value.component';

export const sharedDialogs: any[] = [RepositoryDashboardEditorDialogComponent];

export const featureComponents: any[] = [
  FeatureRepositoryBuilderComponent,
  FeatureRepositoryCommunicationsComponent,
  FeatureRepositoryAnalyticsComponent,
  FeatureRepositoryTemplateComponent,
  FeatureRepositoryBuilderFieldsComponent,
  FeatureRepositoryOverviewComponent,
  FeatureRepositoryDashboardComponent,
  TemplateComponent,
  SectionComponent,
  TabsComponent,
  TemplateFieldComponent,
  MediaManagerRepositoryComponent,
  RepositoryDashboardTableComponent,
  RepositoryDashboardEditorDialogComponent,
  TabSettingsComponent,
  TabButtonActionComponent,
  TabButtonActionsComponent,
  AddOrganizationComponent,
  AddMediaComponent,
  OrgPublishButtonComponent,
  InstancePublishButtonComponent,
];

export const featureServices: any[] = [BuilderService];

export const standaloneComponents: any[] = [
  SelectOptionControlComponent,
  ChipListComponent,
  TagPopoverComponent,
  PersonaChipListComponent,
  TextInputControlComponent,
  TextAreaInputControlComponent,
  SectionEditComponent,
  DynamicTextInputControlComponent,
  FileUploadControlComponent,
  TagSelectOptionControlComponent,
  CheckboxValueComponent,
];
