:host {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  align-content: stretch;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Exo 2';
    margin: 0;
    color: white;
  }

  p {
    font-family: 'Roboto';
    color: #aaa;
    margin: 0;
  }
  .drag-component {
    border-radius: 8px;
    border: 1px #4c3820 solid;
    border-left: 16px #d28227 solid;
    padding-bottom: 8px;
    margin: 16px 0;
    position: relative;
  }

  .cdk-drag-handle {
    font-size: 16px;
    color: white;
    top: 45%;
    left: -14px;
    z-index: 2;
    background-color: transparent;
    cursor: move;
  }

  .option-buttons {
    padding: 0 4px;
    position: absolute;
    top: -12px;
    right: 4px;
    z-index: 2;
    background-color: #d28227;
    border-radius: 4px;
    cursor: pointer;
    font-size: large;

    .left-icon {
      margin-right: 10px;
    }
  }

  .section {
    flex: 1;
    border-radius: 5px;
    margin: 0 16px 16px;
    background-color: #111111;
    padding-bottom: 16px;

    .toggle-row {
      padding-top: 20px;
      .toggle-col {
        display: flex;
        flex-direction: row;
        padding-left: 18px;
        align-items: center;
        .toggle-switch {
          vertical-align: middle;
          padding-right: 10px;
        }
        .toggle-label {
          font-size: 15px;
          color: white;
        }
      }
    }
  }

  .new-section {
    flex: 1;
    border-radius: 5px;
    border: 1px #aaa dashed;
    margin: 16px 32px;
    padding-top: 8px;
    text-align: center;
    cursor: pointer;
    ion-icon {
      margin: 16px 0;
      font-size: 1.5em;
    }
  }

  .cdk-drag-preview {
    display: none;
  }

  .cdk-drag-placeholder {
    background-color: #d28227;
    color: gray;
    border: 1px solid #d28227;
    border-radius: 8px;
  }
}

ion-label {
  cursor: help;
}

.controls {
  position: relative;
  display: flex;
  right: 0px;
  justify-content: flex-end;
  .remove-section {
    margin: 0.5em;
    display: flex;
    justify-content: center;
    align-items: center;
    ion-icon {
      font-size: 20px;
      color: #d28227;
      cursor: pointer;
    }
  }

  .edit-section {
    margin: 0.5em;
    display: flex;
    justify-content: center;
    align-items: center;
    ion-icon {
      font-size: 20px;
      color: #d28227;
      cursor: pointer;
    }
  }

  .reorder-section {
    margin: 0.5em;
    display: flex;
    justify-content: center;
    align-items: center;
    ion-icon {
      font-size: 28px;
      color: #d28227;
      cursor: pointer;
    }
  }

  .reorder-section:hover {
    cursor: pointer;
  }

  .sectionType {
    color: #565656;
    margin-top: auto;
    margin-bottom: auto;
  }
}

.lock-icon {
  right: 10px;
  position: absolute;
}

.draggingOver {
  border: 1px solid #d28227 !important;
  border-radius: 8px;
}
