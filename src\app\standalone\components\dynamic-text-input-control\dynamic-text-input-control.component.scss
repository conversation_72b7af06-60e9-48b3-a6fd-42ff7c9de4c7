.parent-container {
  .reqAsterisk {
    color: #7f550c;
    font-size: 28px;
  }

  .normal {
    margin: 10px 16px 0px 16px;

    ion-label {
      color: white;
      font-size: 16px;
    }

    .quill-container {
      border-radius: 5px;
      border: 1px solid rgb(78, 78, 78);
      background-color: var(--background-color);
      padding-bottom: 10px;

      ion-text {
        font-size: 14px;
        padding-left: 10px;
        padding-bottom: 10px;
        color: red;
        font-style: italic;
      }
    }
  }

  .selected {
    ion-label {
      width: fit-content;
      padding: 4px 11px 2px 11px;
      background: #f99e00;
      color: #000000;
      border-color: #f99e00;
      border-width: 1px;
      border-style: solid;
      border-radius: 5px 5px 0px 0px;
      font-family: 'Roboto';
      font-weight: bold;
      font-size: 12px;
      line-height: 1.1;
      letter-spacing: 0.3px;
      text-align: left;
    }

    .quill-container {
      border: 1px #fda000 solid;
      border-radius: 0px 3px 3px 3px;
      color: #fff;
      margin-top: 2px;
      padding-bottom: 10px;

      ion-text {
        padding-left: 10px;
        color: red;
        font-style: italic;
        font-size: 14px;
      }
    }
  }

  .no-padding {
    margin: 0px;
  }

  .side-panel-input-padding {
    margin-left: 10px;
    margin-right: 10px;
  }

  .no-border {
    .quill-container {
      border: none;
    }
  }
}