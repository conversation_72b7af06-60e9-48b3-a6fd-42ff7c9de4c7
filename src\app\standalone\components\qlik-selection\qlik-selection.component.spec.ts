import { ComponentFixture, TestBed } from '@angular/core/testing';

import { QlikSelectionComponent } from './qlik-selection.component';

describe('QlikSelectionComponent', () => {
  let component: QlikSelectionComponent;
  let fixture: ComponentFixture<QlikSelectionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [QlikSelectionComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(QlikSelectionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
