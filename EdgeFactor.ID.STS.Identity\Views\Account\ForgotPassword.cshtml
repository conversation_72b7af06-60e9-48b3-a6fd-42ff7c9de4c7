@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Options
@inject IViewLocalizer Localizer
@model EdgeFactor.ID.STS.Identity.ViewModels.Account.ForgotPasswordViewModel
@inject IOptions<IdentityOptions> IdentityOptions

<div class="login-page">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@600;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Exo+2:wght@500;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Exo:wght@700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,700&display=swap" rel="stylesheet">
    <style>
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        textarea:-webkit-autofill,
        textarea:-webkit-autofill:hover,
        textarea:-webkit-autofill:focus,
        select:-webkit-autofill,
        select:-webkit-autofill:hover,
        select:-webkit-autofill:focus {
            border: 1px solid transparent;
            -webkit-text-fill-color: #F1F1F1;
            -webkit-box-shadow: unset;
            background: transparent;
            transition: background-color 5000s ease-in-out 0s;
            width: calc(100% - 20px);
            margin-right: 20px;
            box-sizing: border-box;
            font-family: 'Montserrat', Arial, sans-serif;
        }
        .logo {
            width: 258px;
            height: 57px;
            position: fixed;
            left: 56px;
            top: 42px;
        }
        .outer-container {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .main-container {
            width: 475px;
            height: 601px;
            border-radius: 4px;
            position: relative;
            background-color: #28282A;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        .main-container h2 {
            margin-bottom: 10px;
        }
        .forgot-desc {
            color: #cccccc;
            font-family: 'Roboto', Arial, sans-serif;
            font-size: 16px;
            font-weight: 400;
            margin-left: 36px;
            margin-right: 36px;
            margin-top: 50px;
            margin-bottom: 12px;
        }
        .InputBox {
            height: 56px;
            background: #1A191B;
            width: calc(100% - 72px);
            margin-top: 17.48px;
            margin-left: 36px;
            margin-right: 36px;
            border-bottom: 2px solid #D4D3DD;
            position: relative;
        }
        .InputLabel {
            margin-left: 8px;
            line-height: 22px;
            font-weight: 400;
            color: #96959D;
            font-family: 'Montserrat';
            font-size: 10px;
        }
        .Username {
            all: unset;
            width: calc(100% - 10px);
            background: #1A191B;
            margin-left: 8px;
            font-family: 'Montserrat', Arial, sans-serif;
            font-size: 14px;
        }
        .login-btn {
            margin-top: 32px;
            width: calc(100% - 72px);
            margin-left: 36px;
            margin-right: 36px;
            height: 40px;
            border: none;
            border-radius: 4px;
            font-family: 'Montserrat', Arial, sans-serif;
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            letter-spacing: 0;
            color: #1A191B;
            background: linear-gradient(90deg, #F99E00 0%, #FDE734 100%);
            cursor: pointer;
            transition: box-shadow 0.2s;
            box-shadow: none;
            text-align: center;
        }
        .login-btn:active,
        .login-btn:focus {
            outline: none;
            box-shadow: 0 0 0 2px #FDE73455;
        }
        .signup-prompt {
            margin-top: 20px;
            text-align: center;
            margin-bottom: 24px;
        }
        .main-container {
            display: flex;
            flex-direction: column;
        }
        .main-container .spacer {
            flex: 1 1 auto;
        }
        .signup-text-normal {
            font-family: 'Roboto', Arial, sans-serif;
            font-size: 16px;
            font-weight: 400;
            color: #cccccc;
        }
        .signup-link {
            font-family: 'Roboto', Arial, sans-serif;
            font-size: 16px;
            font-weight: 600;
            color: #F99E00;
            text-decoration: none;
            margin-left: 5px;
        }
        .signup-link:hover {
            text-decoration: underline;
        }
        h2 {
            height: 39px;
            color: #ffffff;
            font-family: 'Exo', Arial, sans-serif;
            font-weight: 700;
            font-size: 36px;
            line-height: 1.3;
            text-align: left;
            margin: 0;
            padding: 0;
            margin-left: 36px;
            margin-top: 26px;
        }
    </style>

    @await Html.PartialAsync("_ValidationSummary")
    <img class="logo" src="~/images/EFLogo_White.png" />
    <div class="outer-container">
        <div class="main-container">
            <h2>Forgot Password</h2>
            <div class="forgot-desc">Enter your Email Address, and we'll send you a link to reset your password.</div>
            <form asp-controller="Account" asp-action="ForgotPassword" method="post">
                <div class="InputBox">
                    <span class="InputLabel">Email</span>
                    <input class="Username" placeholder="Email" asp-for="Email" aria-label="Email" autocomplete="off">
                </div>
                <button type="submit" class="login-btn">Send Link</button>
            </form>
            <div class="spacer"></div>
            <div class="signup-prompt">
                <span class="signup-text-normal">Don't have an account ?</span>
                <a href="/Account/Register" class="signup-link">SIGN UP</a>
            </div>
        </div>
    </div>
</div>