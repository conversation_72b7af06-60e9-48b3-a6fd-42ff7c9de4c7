import { Component, OnDestroy } from '@angular/core';
import { environment } from '@env/environment';
import { format } from 'date-fns';

import { Subject } from 'rxjs';

@Component({
    selector: 'app-link-footer',
    templateUrl: './link-footer.component.html',
    styleUrls: ['./link-footer.component.scss'],
    standalone: false
})
export class LinkFooterComponent implements OnDestroy {
  componentDestroyed$: Subject<boolean> = new Subject();
  environment = environment;
  year: string = format(new Date(), 'yyyy');
  constructor() {}

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
