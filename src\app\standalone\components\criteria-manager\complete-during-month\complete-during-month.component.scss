.parent-container {
  ion-row {
    ion-col {
      display: flex;
      align-items: center;
    }

    .heading-col {
      padding: 10px;
    }
  }
  mat-form-field {
    mat-date-range-input {
      display: flex;
    }

    ::ng-deep .mat-focused .mat-mdc-floating-label{
      /*change color of label*/
      color: white !important;
    }

    ::ng-deep.mdc-line-ripple {
      /*change color of underline*/
      background-color: white !important;
    }


    ::ng-deep .mat-mdc-floating-label {
      /*change color of label*/
      color: white !important;
    }

    mat-datepicker-toggle {
      color: white !important;
    }

    ::ng-deep .mat-mdc-floating-label {
      bottom: 0;
    }
    ::ng-deep .mat-mdc-text-field-wrapper {
      padding: 0;
    }

    ::ng-deep .mat-mdc-text-field-wrapper {
      display: flex;
      padding-bottom: 0;
    }

    ::ng-deep .mat-mdc-form-field-flex {
      border-radius: 5px;
      padding-right: 0;
      border: 1px solid #4e4e4e;
      border-radius: 5px;
      color: white;
      font-size: 16px;
      background: #242323;
      text-overflow: ellipsis !important;
      min-width: 260px;
    }

    ::ng-deep .mat-date-range-input-separator {
      color: white;
    }
  }
}
