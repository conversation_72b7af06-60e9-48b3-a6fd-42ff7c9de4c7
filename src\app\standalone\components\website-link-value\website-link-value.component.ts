import { Component, Input } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { TextValueComponent } from '../text-value/text-value.component';

@Component({
    selector: 'app-website-link-value',
    templateUrl: './website-link-value.component.html',
    styleUrls: ['./website-link-value.component.scss'],
    imports: [IonicModule, TextValueComponent]
})
export class WebsiteLinkValueComponent {
  @Input() link!: string | undefined;

  constructor() {}

  open(event: any) {
    event?.stopPropagation();
    window.open(`${this.link}`, '_blank');
  }
}
