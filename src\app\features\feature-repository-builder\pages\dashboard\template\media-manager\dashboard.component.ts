import { SelectionModel } from '@angular/cdk/collections';
import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IAssetRepo, IFeature } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { merge, Subject } from 'rxjs';
import { debounceTime, map, startWith, switchMap, takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-media-manager-repository',
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.scss'],
    standalone: false
})
export class MediaManagerRepositoryComponent implements OnInit, OnDestroy {
  @Input() feature: IFeature;
  @ViewChild(MatPaginator, { static: true }) paginator!: MatPaginator;
  @ViewChild('filter', { static: true }) filter: ElementRef;
  @ViewChild('sort', { static: true }) sort: MatSort;
  selection = new SelectionModel<IAssetRepo>(true, []);
  componentDestroyed$: Subject<boolean> = new Subject();
  displayedColumns: string[] = ['name', 'source', 'category', 'runtime', 'status', 'date'];
  dataSource = new MatTableDataSource<IAssetRepo>();
  searchString = '';
  searchEmitter = new Subject<string>();
  resultsLength = 0;

  constructor(
    private dataService: DataService,
    private instanceService: InstanceService
  ) {}

  ngOnInit() {
    this.getDashboard();
  }

  getDashboard() {
    this.paginator.pageSize = 25;
    merge(this.searchEmitter, this.paginator.page)
      .pipe(
        startWith({}),
        debounceTime(700),
        switchMap(() => {
          return this.dataService.getMediaDashboard(this.searchString, this.paginator.pageIndex, this.paginator.pageSize);
        }),
        map(data => {
          this.resultsLength = data.paging.totalRecordCount;
          return data.data;
        })
      )
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.dataSource = new MatTableDataSource(data);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      });
  }

  masterHeaderToggle() {
    if (this.selectAll() === false) {
      this.dataSource.data.forEach(row => this.selection.select(row));
    } else {
      this.selection.clear();
    }
  }

  selectAll() {
    const selectedAmount = this.selection.selected.length;
    const rowAmount = this.dataSource.data.length;
    return selectedAmount === rowAmount;
  }

  openInstanceBuilder(assetId: string) {
    this.instanceService.openInstance('media-manager', assetId);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
