.parent-container {
  .inner-container {
    .icon-col {
      display: flex;
      align-items: top;
      justify-content: center;
      ion-icon {
        color: white;
        font-size: 20px;
      }
    }

    .chiplist-content-col {
      display: flex;
      align-items: center;

      padding-left: 10px;
    }
  }
}

mat-chip-listbox {
  mat-chip-option {
    background-color: #181818;
    color: white;
    // min-width: 200px;
    min-height: 50px;
    width: auto;
    border-radius: 10px;
    border: 1px solid black;
    padding: 5px 11px;
  }
}

img {
  max-width: 50px;
  max-height: 50px;
  border-radius: 10px;
}

.image-column {
  display: flex;
  justify-content: center;
  align-items: center;
}

.parent-tag {
  color: #aaaaaa;
  font-family: '<PERSON><PERSON>';
  font-weight: bold;
  font-size: 0.75em;
  line-height: var(--general-line-height);
  text-align: left;
  text-transform: uppercase;
  white-space: nowrap;
  letter-spacing: 0.01em;
}

.child-tag {
  color: #ffffff;
  font-family: '<PERSON><PERSON>';
  font-weight: 400;
  font-size: 1em;
  line-height: var(--general-line-height);
  letter-spacing: 0.01em;
  text-align: left;
}
