import { Component, forwardRef, Input, OnInit } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { NgClass } from '@angular/common';
import { ContentQuillEditorComponent } from '../content-quill-editor/content-quill-editor.component';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';
import { IComponent } from '@app/core/contracts/contract';

@Component({
  selector: 'app-qlikview-control',
  templateUrl: './qlikview-control.component.html',
  styleUrl: './qlikview-control.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: forwardRef(() => QlikviewControlComponent),
    },
    {
      provide: NG_VALIDATORS,
      multi: true,
      useExisting: forwardRef(() => QlikviewControlComponent),
    },
  ],
  standalone: true,
  imports: [IonicModule, NgClass, ContentQuillEditorComponent],
})
export class QlikviewControlComponent extends BaseControlComponent implements OnInit {
  @Input() component: IComponent;
  @Input() toolTip!: string;
  @Input() placeHolder!: string;
  @Input() defaultValue: string | undefined;
  @Input() override label!: string;
  @Input() backgroundColor = '#181818';
  @Input() itemBackgroundColor = '';
  @Input() hideQuillPersonalize = false;
  @Input() noPadding = false;
  @Input() noBorder = false;
  @Input() sidePanelPadding = false;
  @Input() showSelected = false;

  constructor(
    private signalService: ComponentUpdateSignalService
  ) {
    super();
  }

  ngOnInit() {
    this.setDefaultValue();
  }

  setDefaultValue() {
    setTimeout(() => {
      if (this.defaultValue && !this.textValue) {
        this.textValue = this.defaultValue;
      }
    }, 1);
  }

  override setValue(input: string): void {
    this.writeValue(input);
    if(this.component.id)
    {
      this.signalService.triggerSignal({ componentId: this.component.id, updateValue: input })
    }
  }
}
