import { Component, Input, OnInit } from '@angular/core';
import { IBulletList, IInstanceSectionComponent } from '@app/core/contracts/contract';
import { NgClass } from '@angular/common';

@Component({
    selector: 'app-bullet-list-value',
    templateUrl: './bullet-list-value.component.html',
    styleUrls: ['./bullet-list-value.component.scss'],
    imports: [NgClass]
})
export class BulletListValueComponent implements OnInit {
  @Input() instanceComponent: IInstanceSectionComponent | undefined;
  @Input() numbered = false;
  bulletList: IBulletList[] = [];

  constructor() {}

  ngOnInit() {
    if (this.instanceComponent?.value) {
      this.bulletList = JSON.parse(this.instanceComponent?.value) as IBulletList[];
    }
    if (this.bulletList.length < 1) {
      this.bulletList = [{ text: this.numbered ? 'Numbered bullet list' : 'Bullet list', sortOrder: 0 }] as IBulletList[];
    }
  }
}
