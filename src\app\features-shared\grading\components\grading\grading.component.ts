import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { IPeople } from '@app/core/contracts/contract';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { DataService } from '@app/core/services/data-service';
import { map, Observable, Subject } from 'rxjs';

@Component({
  selector: 'app-grading',
  templateUrl: './grading.component.html',
  styleUrls: ['./grading.component.scss'],
})
export class GradingComponent implements OnInit, OnDestroy {
  @Input() instanceId: string;
  componentDestroyed$: Subject<boolean> = new Subject();
  users$: Observable<IPeople[]>;
  selectedUserId: string;
  constructor(
    private dataService: DataService,
    private breadcrumbService: BreadcrumbService
  ) {}

  ngOnInit() {
    this.getUsers();
  }

  getUsers() {
    const parentBreadcrumb = this.breadcrumbService.getByfeatureType('Modifiable Learning Container Pages');
    this.users$ = this.dataService.getPeopleTableByInstanceId(this.instanceId, 0, 100, this.instanceId !== parentBreadcrumb?.id ? parentBreadcrumb?.id : null).pipe(
      map(data => {
        if (data) {
          this.selectedUserId = data[0]?.userId;
        }

        return data;
      })
    );
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
