.parent-container {
  .view-options-row {
    margin-bottom: 10px;
    justify-content: space-between;
    align-items: center;

    .top-header {
      display: flex;
      justify-content: center;
      align-items: center;

      ion-item {
        border-radius: 4px;
        display: flex;
        height: 36px;
      }
    }
  }
}

.custom-table {
  width: 100%;
  border-collapse: separate !important;
  border-spacing: 0 10px;
  background-color: transparent;
  background: transparent;
  /* Space between rows */

  .example-element-row {
    border-radius: 5px;
    background-color: #2E2E2E;
    /* Green row background */
    color: white;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
    margin-top: 5px;
    // border-top: 5px solid #222222;
  }

  .in-progress-row {
    background-color: #433B2C !important;
  }

  .completed-row {
    background-color: #343F30 !important;
  }

  .grading-required-row {
    background-color: #3D2A2A !important;
    /* Dark red background for items that need grading */
  }

  .mat-cell {
    color: white;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .mat-cell,
  .mat-header-cell {
    border-bottom: none;
    overflow: hidden;
    color: white;
    /* Remove default Material border */
  }

  .mat-header-cell {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .mat-mdc-header-row {
    color: white;
    font-weight: bold;
    border-bottom: 1px solid #424242;
    height: 35px;
    min-height: 30px;
    background: #333;
    border-radius: 5px;
    overflow: hidden;
  }

  .example-detail-expanded-row {
    background: #2E2E2E;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    overflow: hidden;
  }

  .example-element-detail {
    overflow: hidden;
    display: flex;
  }

  .example-expanded-row {
    border-bottom-right-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
    border-bottom: 1px solid #5A5653 !important;
  }

  .mat-column-select {
    padding-left: 0px;
    max-width: 40px;
    padding-right: 0px;
  }

  .mat-column-name {
    padding-left: 0px;
  }

  .mat-column-grade {
    max-width: 280px;
  }

  .mat-column-icon {
    padding-left: 40px;
    max-width: fit-content;
  }

  .mat-column-progress {
    max-width: 200px;
  }

  .mat-column-grade-status {
    max-width: 360;
  }

  .name-column {
    display: flex;
    flex-direction: column;
  }

  .white-name {
    color: white;
    font-weight: bold;
  }

  .subheading {
    font-style: italic;
    color: var(--ion-color-medium);
    font-size: 12px;
  }

  .row-space-between {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    align-items: center;

    ion-button {
      --background: #CCCCCC;

      &.grade-button {
        --background: #C75050;
        --color: white;
      }
    }
  }

  .icon {
    object-fit: cover;
    border-radius: 5px;
    border: 1px solid #333;
    width: 40px;
    height: 40px;
  }

  // Status badge styles
  .status-badge-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .status-badge {
    display: flex;
    align-items: center;
    padding: 0px 5px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid rgba(255, 255, 255, 0.2);
  }

  .status-badge ion-icon {
    margin-right: 4px;
    font-size: 14px;
  }

  .in-progress {
    background-color: #FFF4D8;
    color: black;
    border-color: #D89B45;
  }

  .completed {
    background-color: #DFFCE9;
    color: black;
    border-color: #4E9961;
  }

  .not-started {
    background-color: #F3F3F7;
    color: black;
    border-color: #A7A7A7;
  }

  .grading-required {
    background-color: #FFE8E8;
    color: black;
    border-color: #C75050;
  }
}

.progress-indicator {
  display: flex;
  align-items: center;

  .progress-circle {
    position: relative;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .circular-chart {
    display: block;
    width: 100%;
    height: 100%;
  }

  .circle-bg {
    fill: none;
    stroke: transparent;
    stroke-width: 2.8;
  }

  .circle {
    fill: none;
    stroke: #4caf50;
    stroke-width: 2.8;
    stroke-linecap: round;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
  }

  .progress-text {
    margin-left: 10px;
    display: flex;
    align-items: center;
  }

  .items-count {
    font-size: 12px;
    white-space: nowrap;
  }

  .completed-count {
    font-weight: bold;
    color: white;
  }

  .remaining-text {
    font-weight: normal;
    color: #aaaaaa;
  }
}
