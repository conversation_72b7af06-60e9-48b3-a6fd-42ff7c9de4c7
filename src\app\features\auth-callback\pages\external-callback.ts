import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthService } from '@app/core/services/auth-service';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-external-callback',
    styleUrls: ['./external-callback.scss'],
    templateUrl: './external-callback.html',
    standalone: false
})
export class ExternalCallbackComponent implements OnInit, OnDestroy {
  componentDestroyed$: Subject<boolean> = new Subject();
  returnUrl = '/my-journey';
  constructor(
    private route: ActivatedRoute,
    private authService: AuthService
  ) {}

  ngOnInit() {
    const joinCode = this.route.snapshot.queryParamMap.get('joinCode') as string;
    if (joinCode) {
      sessionStorage.setItem('returnUrl', '/user/joincode/' + joinCode);
    } else {
      sessionStorage.setItem('returnUrl', this.returnUrl);
    }

    this.authService
      .setUserContext(false)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.authService.init();
        this.authService
          .startAuthenticationExternal()
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(() => {});
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
