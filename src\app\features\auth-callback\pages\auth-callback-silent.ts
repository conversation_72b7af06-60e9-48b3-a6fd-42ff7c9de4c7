import { Component, OnDestroy, OnInit } from '@angular/core';
import { AuthService } from '@app/core/services/auth-service';
import { NavController } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';

//callback after silent-callback.html returned
@Component({
    selector: 'app-auth-callback-silent',
    styleUrls: ['./auth-callback-silent.scss'],
    templateUrl: './auth-callback-silent.html',
    standalone: false
})
export class AuthCallbackSilentComponent implements OnInit, OnDestroy {
  componentDestroyed$: Subject<boolean> = new Subject();
  returnUrl: string;
  constructor(
    private authService: AuthService,
    private nav: NavController
  ) {}

  ngOnInit() {
    if (sessionStorage.getItem('returnUrl') && sessionStorage.getItem('returnUrl') !== '/') {
      this.returnUrl = sessionStorage.getItem('returnUrl') ?? '/';
    }
    this.authService
      .completeAuthentication()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        if (this.authService.user) {
          // this.notificationService.startConnection(this.authService.user.access_token);
          // this.notificationService.loadNotifications();
        }
        this.nav.navigateRoot(this.returnUrl).then(() => {
          const refresh = new BroadcastChannel('refresh');
          refresh.postMessage('refresh');
        });
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
