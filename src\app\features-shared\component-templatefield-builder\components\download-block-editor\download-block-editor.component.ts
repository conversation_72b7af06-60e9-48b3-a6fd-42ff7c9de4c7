import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-download-block-editor',
    templateUrl: './download-block-editor.component.html',
    styleUrls: ['./download-block-editor.component.scss'],
    standalone: false
})
export class DownloadBlockEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  downloadBlockForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.downloadBlockForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  setTypeBwValues(typeBw: number | undefined) {
    if (typeBw !== undefined) {
      return this.builderService.acceptedFileFormats.filter(x => Number(x.id) & typeBw).map(x => x.id);
    }
    return null;
  }

  createForm() {
    this.downloadBlockForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      placeHolderText: [this.component?.templateField?.placeHolderText, Validators.required],
      rowNumber: [this.component?.builderRowNumber ?? 0, Validators.required],
      hoverSortOrder: [this.component?.hoverSortOrder ?? 0, Validators.required],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0, Validators.required],
      buttonText: [this.component?.templateField?.buttonText ?? 'Upload', Validators.required],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      fileTypeBw: [this.setTypeBwValues(this.component?.templateField?.fileTypeBw), Validators.required],
      minFileSize: [this.component?.templateField?.minFileSize],
      maxFileSize: [this.component?.templateField?.maxFileSize],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.downloadBlockForm) {
      return;
    }

    this.downloadBlockForm.controls.label.setValue(this.component.templateField.label);
    this.downloadBlockForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.downloadBlockForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.downloadBlockForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.downloadBlockForm.controls.buttonText.setValue(this.component.templateField.buttonText);
    this.downloadBlockForm.controls.placeHolderText.setValue(this.component.templateField.placeHolderText);
    this.downloadBlockForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.downloadBlockForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.downloadBlockForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.downloadBlockForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.downloadBlockForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.downloadBlockForm.controls.fileTypeBw.setValue(this.component.templateField.fileTypeBw);
    this.downloadBlockForm.controls.minFileSize.setValue(this.component.templateField.minFileSize);
    this.downloadBlockForm.controls.maxFileSize.setValue(this.component.templateField.maxFileSize);
    this.downloadBlockForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.downloadBlockForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.downloadBlockForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.downloadBlockForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.downloadBlockForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.downloadBlockForm.valid) {
      this.component.templateField.label = this.downloadBlockForm.controls.label.value;
      this.component.builderRowNumber = this.downloadBlockForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.downloadBlockForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.downloadBlockForm.controls.instanceSortOrder.value;
      this.component.templateField.buttonText = this.downloadBlockForm.controls.buttonText.value;
      this.component.templateField.placeHolderText = this.downloadBlockForm.controls.placeHolderText.value;
      this.component.templateField.isRequiredField = this.downloadBlockForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.downloadBlockForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.downloadBlockForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.downloadBlockForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.downloadBlockForm.controls.isViewField.value;
      this.component.templateField.fileTypeBw = this.downloadBlockForm.controls.fileTypeBw.value.reduce((a: number, b: number) => a + b, 0);
      this.component.templateField.minFileSize = this.downloadBlockForm.controls.minFileSize.value;
      this.component.templateField.maxFileSize = this.downloadBlockForm.controls.maxFileSize.value;
      this.component.templateField.colspan = this.downloadBlockForm.controls.colspan.value;
      this.component.templateField.colNumber = this.downloadBlockForm.controls.colNumber.value;
      this.component.templateField.useMaxWidth = this.downloadBlockForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
