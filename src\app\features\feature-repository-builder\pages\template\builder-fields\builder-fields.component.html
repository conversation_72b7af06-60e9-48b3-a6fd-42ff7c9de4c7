<ion-grid class="parent-container">
  <ion-row cdkDropListGroup style="height: 100%">
    <ion-col class="edit-col" size="3">
      @if (!component && !section) {
        <ion-content>
          <div class="inner-content">
            <p>These are the fields that a user sees in the builder. Click on a field to customize the field to this feature.</p>
            @if (componentTypes$ | async; as componentTypes) {
              <h5>Template fields</h5>
              <ion-grid class="fields-container" cdkDropList>
                @for (ct of componentTypes; track ct) {
                  <ion-row cdkDrag [cdkDragData]="ct">
                    <ion-col size="12">
                      <app-feature-repository-template-field [componentType]="ct"></app-feature-repository-template-field>
                    </ion-col>
                  </ion-row>
                }
              </ion-grid>
            }
          </div>
        </ion-content>
      }
      @if (component) {
        <app-component-editor [component]="component" (componentChanged)="componentChanged()"></app-component-editor>
      }
      @if (section) {
        <app-section-edit [section]="section" [templateForm]="templateForm" [persistSortOrder]="true"></app-section-edit>
      }
    </ion-col>
    <ion-col size="9">
      <app-feature-repository-builder-tabs [featureId]="feature.id" [featureTabs]="feature.featureTabs" (showDefaultComponents)="setComponentList($event)"></app-feature-repository-builder-tabs>
    </ion-col>
  </ion-row>
</ion-grid>
