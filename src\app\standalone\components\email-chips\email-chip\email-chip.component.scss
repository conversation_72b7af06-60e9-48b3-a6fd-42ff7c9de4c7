.parent-container {
  margin: 8px;
  .card-content-container {
    .inner-container {
      width: 100%;
      --ion-item-background: #2d2e32;
      --border-color: transparent;
      --color: white;
      --padding-start: 8px;
      --inner-padding-end: 8px;
      border-radius: 5px;

      mat-chip-listbox {
        padding: 10px;
        padding-left: 5px;

        mat-chip-option {
          background-color: #484848;
          color: white;
          width: auto;
          height: 40px;
          border-radius: 5px;
          border: 1px solid black;

          .text-value {
            max-width: 20em;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          button {
            color: white;
          }
        }

        .form-container {
          background: rgb(30, 30, 30);
          margin: 4px;
          border-radius: 5px;
          input {
            border-radius: 5px;
            border: 1px solid #4e4e4e;
            background-color: rgb(30, 30, 30);
            color: white;
            font-size: 16px;
            padding: 10px;
            width: 140px;
            height: 40px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin: 0px;
          }

          ::placeholder {
            color: rgb(255, 255, 255, 0.8);
          }
        }
      }
    }
  }
}

.no-container {
  margin: 8px 0px !important;
  ion-card {
    margin: 8px 0px !important;
    .inner-container {
      border: 1px solid #4e4e4e;
    }
  }
}

.side-panel-input-padding {
  margin: 0px !important;
  ion-card {
    margin: 0px !important;
    .inner-container {
      border: transparent !important;
    }
  }
}
