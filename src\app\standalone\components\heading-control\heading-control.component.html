<div class="parent-container">
  <div class="inner-container">
    <form [formGroup]="headingForm">
      <ion-card class="card-container">
        <div class="text-input-container">
          <app-text-input-control
            [options]="options"
            [selectedOption]="headingForm.controls.headingStyle.value"
            [component]="component"
            [backgroundColor]="'#1E1E1E'"
            [noPadding]="true"
            [placeHolder]="'Start typing here'"
            [label]="'Header'"
            (optionsValueChanged)="onHeadingStyleChanged($event)"
            formControlName="text"
            [itemBackgroundColor]="'#292929'"></app-text-input-control>
        </div>
        <div class="text-input-container">
          <app-text-area-input-control
            [noPadding]="true"
            [backgroundColor]="'#1E1E1E'"
            formControlName="description"
            [label]="'Description'"
            [placeHolder]="'Start typing here'"></app-text-area-input-control>
        </div>
        <mat-slide-toggle style="width: 100%" color="primary" formControlName="darkText">Dark text</mat-slide-toggle>
        <app-alignment-control formControlName="stylingDirection"></app-alignment-control>
      </ion-card>
    </form>
  </div>
</div>
