<div class="tab-container">
  @if (featureTabs) {
    <mat-tab-group
      [selectedIndex]="selectedIndex()"
      (selectedIndexChange)="selectedIndex.set($event)"
      style="height: 100%"
      color="primary"
      #matTabGroup
      mat-stretch-tabs="false"
      mat-align-tabs="start"
      [ngClass]="{ 'header-less-tabs': featureTabs.length <= 1 || instance.feature.isFullWidth || onPlayer() }"
      (selectedTabChange)="onTabChanged($event)">
      @for (featureTab of getTabs(); track featureTab) {
        <mat-tab class="tabs" [label]="featureTab.tab.name">
          <ng-template matTabContent>
            <app-template
              [templateId]="featureTab.tab.templateId"
              [instance]="instance"
              [featureTab]="featureTab"
              [routeParams]="routeParams"
              [selectedUserId]="selectedUserId"
              (formChanged)="formChanged($event)"
              (instanceChanged)="instanceUpdated()"></app-template>
          </ng-template>
        </mat-tab>
      }
    </mat-tab-group>
  }
</div>
