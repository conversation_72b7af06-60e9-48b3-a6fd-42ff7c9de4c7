<div class="parent-container" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }">
  <ion-card class="card-container">
    <form [formGroup]="form">
      <app-text-input-control
        [disabled]="disabled"
        [noPadding]="true"
        [backgroundColor]="'#1E1E1E'"
        [placeHolder]="'Start typing here'"
        [label]="'Height'"
        formControlName="height"
        [itemBackgroundColor]="'#292929'"></app-text-input-control>
    </form>
  </ion-card>
</div>
