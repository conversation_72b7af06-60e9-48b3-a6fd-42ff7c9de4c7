<div class="network-container">
  <div>
    <div class="icon-and-text-container">
      <div class="icon-container">
        <mat-icon svgIcon="network_analytics"></mat-icon>
      </div>
      <div>
        <div class="network-name">
          {{ network.networkName }}
        </div>
        <div class="org-count">{{ network.orgCount }} Organizations</div>
      </div>
    </div>
  </div>
  @if (started) {
    <div class="tooltip">
      <span class="progress-bar">
        <span>Initializing Download</span>
        <mat-progress-bar mode="buffer"></mat-progress-bar>
      </span>
    </div>
  } @else {
    <mat-icon class="download-icon" (click)="downloadPdf(network.id)" svgIcon="download"></mat-icon>
  }
</div>
