import { NgClass } from '@angular/common';
import { Component, forwardRef } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';

@Component({
    selector: 'app-alignment-control',
    templateUrl: './alignment-control.component.html',
    styleUrls: ['./alignment-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => AlignmentControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => AlignmentControlComponent),
        },
    ],
    imports: [IonicModule, NgClass]
})
export class AlignmentControlComponent extends BaseControlComponent {
  stylingDirection: string;

  constructor() {
    super();
  }

  isSelected(styling: string) {
    return this.textValue ? this.textValue === styling : false;
  }

  checkboxChanged(styling: string) {
    this.writeValue(styling);
  }
}
