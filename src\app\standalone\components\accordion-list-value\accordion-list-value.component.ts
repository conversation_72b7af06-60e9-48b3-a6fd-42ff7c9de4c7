import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { IAccordionItem, IEngagementIn, IInstanceSectionComponent } from '@app/core/contracts/contract';
import { EngagementTypes } from '@app/core/enums/engagment-types.enum';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';

@Component({
    selector: 'app-accordion-list-value',
    templateUrl: './accordion-list-value.component.html',
    styleUrls: ['./accordion-list-value.component.scss'],
    imports: [IonicModule]
})
export class AccordionListValueComponent implements OnInit, OnDestroy {
  @Input() instanceId: string;
  @Input() instanceComponent: IInstanceSectionComponent | undefined;
  accordionItemList: IAccordionItem[] = [];
  componentDestroyed$: Subject<boolean> = new Subject();
  progress = 0;
  constructor(
    private dataService: DataService,
    private eventsService: Events
  ) {}

  ngOnInit() {
    if (this.instanceComponent?.value && this.instanceComponent?.value?.length > 0) {
      this.accordionItemList = JSON.parse(this.instanceComponent?.value) as IAccordionItem[];
    } else {
      this.accordionItemList = [{ heading: 'First Item', title: 'First Item', description: '', sortOrder: 0 } as IAccordionItem];
    }
  }

  change() {
    if (this.progress === 0) {
      this.progress = 100;
      this.completeInstanceComponent();
      this.addInstanceSectionComponentEngagement(100);
    }
  }

  completeInstanceComponent() {
    if (this.instanceComponent?.component.templateField.isRequiredField) {
      this.addInstanceSectionComponentEngagement(100);
      this.dataService
        .addInstanceSectionComponentCompletion(this.instanceComponent?.id ?? '', this.instanceId ?? '', 100)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.eventsService.publish('instanceSectionComponentCompleted', this.instanceComponent?.id);
          if (this.instanceComponent) {
            this.instanceComponent.completed = true;
          }
        });
    }
  }

  addInstanceSectionComponentEngagement(progress: number) {
    this.dataService
      .addInstanceSectionComponentEngagement({
        instanceId: this.instanceId,
        instanceSectionComponentId: this.instanceComponent?.id,
        engagementType: EngagementTypes.Click,
        percentageValue: progress !== undefined ? Math.round(progress) : 0,
        nominalValue: 1,
      } as IEngagementIn)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {});
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
