.content-card {
  background-color: rgba(41, 41, 41);
  .inner-container {
    display: flex;
    justify-content: center;
    justify-content: flex-start;
    align-items: center;
    font-size: 16px;
    padding: 10px;
    color: rgba(255, 255, 255, 0.8);
    .user-roles-container {
      margin-left: 15px;
      margin-right: 15px;
      ion-select {
        --padding-start: 12px;
        --padding-end: 12px;
        height: 30px;
        width: 120px;
        border-radius: 5px;
        border: 1px solid;
      }
    }

    .input-container {
      margin-left: 15px;
      margin-right: 15px;
      ion-input {
        --padding-start: 12px;
        --padding-end: 12px;
        height: 30px;
        width: 120px;
        border: 1px solid #4e4e4e;
        border-radius: 5px;
      }
    }
  }

  .remove-button-col {
    justify-content: center;
    display: flex;
    .remove-button-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: auto;
      margin-right: 20px;
      cursor: pointer;

      mat-icon {
        color: rgba(250, 167, 0) !important;
      }
    }
  }
}

.add-button-col {
  justify-content: flex-start;
  display: flex;
  .add-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;

    mat-icon {
      cursor: pointer;
      margin-right: 10px;
      color: rgba(250, 167, 0) !important;
      min-width: 25px;
    }

    .button-text {
      color: rgba(170, 170, 170);
    }
  }
}
