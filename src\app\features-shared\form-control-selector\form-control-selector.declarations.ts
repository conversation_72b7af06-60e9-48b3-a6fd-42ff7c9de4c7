import { AccordionComponent } from '@app/standalone/components/accordion/accordion.component';
import { AchievementControlComponent } from '@app/standalone/components/achievement-control/achievement-control.component';
import { AlignmentControlComponent } from '@app/standalone/components/alignment-control/alignment-control.component';
import { AuthoringHeaderComponent } from '@app/standalone/components/authoring-header/authoring-header.component';
import { BannerComponent } from '@app/standalone/components/banner/banner.component';
import { BulletListControlComponent } from '@app/standalone/components/bullet-list-control/bullet-list-control.component';
import { ButtonControlComponent } from '@app/standalone/components/button-control/button-control.component';
import { CheckboxControlComponent } from '@app/standalone/components/checkbox-control/checkbox-control.component';
import { CommunicationManagerComponent } from '@app/standalone/components/communication-manager/communication-manager.component';
import { ContactInfoComponent } from '@app/standalone/components/contact-info/contact-info.component';
import { CriteriaManagerComponent } from '@app/standalone/components/criteria-manager/criteria-manager.component';
import { DynamicTextInputControlComponent } from '@app/standalone/components/dynamic-text-input-control/dynamic-text-input-control.component';
import { EmailChipComponent } from '@app/standalone/components/email-chips/email-chip/email-chip.component';
import { EmbeddedUrlComponent } from '@app/standalone/components/embedded-url/embedded-url.component';
import { FileUploadControlComponent } from '@app/standalone/components/file-upload-control/file-upload-control.component';
import { FullNameComponent } from '@app/standalone/components/full-name/full-name.component';
import { GoogleMapsAutcompleteComponent } from '@app/standalone/components/google-maps/google-maps-autocomplete.component';
import { GradingTableComponent } from '@app/standalone/components/grading-table/grading-table.component';
import { HeadingControlComponent } from '@app/standalone/components/heading-control/heading-control.component';
import { IconAndTextComponent } from '@app/standalone/components/icon-and-text/icon-and-text.component';
import { ImageCenteredComponent } from '@app/standalone/components/image-centered/image-centered.component';
import { InstanceDetailsControlComponent } from '@app/standalone/components/instance-details-control/instance-details-control.component';
import { InstanceHeaderControlComponent } from '@app/standalone/components/instance-header-control/instance-header-control.component';
import { InstanceInterestControlComponent } from '@app/standalone/components/instance-interest-control/instance-interest-control.component';
import { MediaAndTextComponent } from '@app/standalone/components/media-and-text/media-and-text.component';
import { MediaBlockControlComponent } from '@app/standalone/components/media-block-control/media-block-control.component';
import { NetworkOrganizationExpansionAccordionComponent } from '@app/standalone/components/network-organization-expansion-accordion/network-organization-expansion-accordion.component';
import { OrganizationExpansionControlComponent } from '@app/standalone/components/organization-expansion-control/organization-expansion-control.component';
import { PasswordControlComponent } from '@app/standalone/components/password-control/password-control.component';
import { PeopleTableComponent } from '@app/standalone/components/people-table/people-table.component';
import { PersonaTagPopoverComponent } from '@app/standalone/components/persona-tag-popover/persona-tag-popover.component';
import { PhoneNumberComponent } from '@app/standalone/components/phone-number/phone-number.component';
import { ProductActionRolesComponent } from '@app/standalone/components/product-action-roles/product-action-roles.component';
import { ProductBillingComponent } from '@app/standalone/components/product-billing/product-billing.component';
import { ProductExpansionAccordionComponent } from '@app/standalone/components/product-expansion-accordion/product-expansion-accordion.component';
import { ProductHistoryComponent } from '@app/standalone/components/product-history/product-history.component';
import { SelectOptionControlComponent } from '@app/standalone/components/select-option-control/select-option-control.component';
import { SpacingControlComponent } from '@app/standalone/components/spacing-control/spacing-control.component';
import { TagPopoverComponent } from '@app/standalone/components/tag-popover/tag-popover.component';
import { TagSelectOptionControlComponent } from '@app/standalone/components/tag-select-option-control/tag-select-option-control.component';
import { TextAndButtonControlComponent } from '@app/standalone/components/text-and-button-control/text-and-button-control.component';
import { TextAreaInputControlComponent } from '@app/standalone/components/text-area-input-control/text-area-input-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { UserNotificationPreferencesComponent } from '@app/standalone/components/user-notification-preferences/user-notification-preferences.component';
import { UsersTableComponent } from '@app/standalone/components/users-table/users-table.component';
import { VideoComponent } from '@app/standalone/components/video/video.component';
import { FormControlSelectorComponent } from './components/form-control-selector.component';

export const featureComponents: any[] = [FormControlSelectorComponent];

export const standaloneComponents: any[] = [
  AuthoringHeaderComponent,
  TextInputControlComponent,
  DynamicTextInputControlComponent,
  TextAreaInputControlComponent,
  SelectOptionControlComponent,
  TagSelectOptionControlComponent,
  TagPopoverComponent,
  PersonaTagPopoverComponent,
  PasswordControlComponent,
  FileUploadControlComponent,
  MediaBlockControlComponent,
  EmailChipComponent,
  ProductExpansionAccordionComponent,
  ProductHistoryComponent,
  ProductBillingComponent,
  ProductActionRolesComponent,
  UsersTableComponent,
  PeopleTableComponent,
  NetworkOrganizationExpansionAccordionComponent,
  OrganizationExpansionControlComponent,
  CommunicationManagerComponent,
  GoogleMapsAutcompleteComponent,
  InstanceHeaderControlComponent,
  AchievementControlComponent,
  CriteriaManagerComponent,
  UserNotificationPreferencesComponent,
  BannerComponent,
  VideoComponent,
  EmbeddedUrlComponent,
  BulletListControlComponent,
  TextAndButtonControlComponent,
  InstanceInterestControlComponent,
  AccordionComponent,
  ImageCenteredComponent,
  MediaAndTextComponent,
  HeadingControlComponent,
  IconAndTextComponent,
  InstanceDetailsControlComponent,
  FullNameComponent,
  PhoneNumberComponent,
  ContactInfoComponent,
  CheckboxControlComponent,
  ButtonControlComponent,
  SpacingControlComponent,
  AlignmentControlComponent,
  GradingTableComponent,
];
