<!-- NB! THIS RETURNS A COMPONENT FOR VIEWING - ROWS ARE NOT HERE BECAUSE THAT CREATES AN INFINITE LOOP IN SOME INSTANCES -->
@if (instanceSectionComponent?.component?.templateField || instanceSectionComponent?.component?.componentType?.name === 'Assessment Block') {
  @if (instanceSectionComponent?.component?.componentType?.parentTypeName === 'Question') {
    <!-- ASSESSMENT BLOCK -->
    <div [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
      <app-question
        [builderPreviewView]="builderPreviewView"
        [component]="instanceSectionComponent.component"
        [instance]="instance"
        [instanceSection]="instanceSection"
        [completed]="instanceSectionComponent?.component?.completed ?? ''"
        [instanceComponentValue]="instanceSectionComponent.value ?? ''"
        [instanceComponent]="instanceSectionComponent"
        [selectedUserId]="selectedUserId"
        [isEducator]="isEducator"
        (triggerCompletionCheck)="triggerSectionCompletionCheck()"
        [continuousFeedback]="continuousFeedback"></app-question>
    </div>
  } @else {
    <!-- TEXT INPUT -->
    <!-- LABEL FIELD -->
    <!-- TEXT AREA FIELD -->
    @switch (instanceSectionComponent?.component?.componentType?.name) {
      @case (
        instanceSectionComponent?.component?.componentType?.name === 'Text' || instanceSectionComponent?.component?.componentType?.name === 'Text Area'
          ? instanceSectionComponent?.component?.componentType?.name
          : ''
      ) {
        <app-text-value
          [instanceComponent]="instanceSectionComponent"
          [instanceId]="instance.id"
          [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"
          [defaultValue]="instanceSectionComponent?.component?.templateField?.label"></app-text-value>
      }
      <!-- WYSIWYG FIELD -->
      @case (
        instanceSectionComponent?.component?.componentType?.name === 'WYSIWYG' || instanceSectionComponent?.component?.componentType?.name === 'Paragraph'
          ? instanceSectionComponent?.component?.componentType?.name
          : ''
      ) {
        <div [ngClass]="{ 'max-width': (instance?.feature?.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-dynamic-text-value
            [builderPreviewView]="builderPreviewView"
            [instanceComponent]="instanceSectionComponent"
            [instanceId]="instance.id"
            [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"
            [defaultValue]="instanceSectionComponent?.component?.templateField?.defaultImageUrl"></app-dynamic-text-value>
        </div>
      }
      <!-- Network QlikView PDF Download -->
      @case ('Network QlikView PDF Download') {
        <app-network-qlik-view-download
          [defaultText]="instanceSectionComponent?.component?.templateField?.defaultText"
          [instanceComponent]="instanceSectionComponent"
          [id]="routeParams.instanceSlug ?? ''">
        </app-network-qlik-view-download>
      }
      <!-- LABEL FIELD -->
      @case ('Label') {
        <app-label
          [instanceComponent]="instanceSectionComponent"
          [instanceId]="instance.id"
          [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"></app-label>
      }
      <!-- DROP DOWNS -->
      <!--SINGLE SELECTOR VALUES-->
      @case ('Dropdown') {
        @if (!instanceSectionComponent?.component?.templateField?.isFilter && !instanceSectionComponent?.component?.templateField?.isTag) {
          <app-select-option-value
            [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"
            [instanceComponent]="instanceSectionComponent"
            [templateField]="instanceSectionComponent?.component?.templateField"
            [instance]="instance">
          </app-select-option-value>
        }
        <!--ISTAG CHIPLIST MULTIPLE SELECTOR VALUES-->
        @else if (instanceSectionComponent?.component?.templateField?.isTag) {
          <div [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
            <app-tag-values
              [dropDownLinkType]="instanceSectionComponent?.component?.templateField?.dropDownLinkType?.title"
              [instanceId]="instance.id"
              [componentId]="instanceSectionComponent.component.id"
              [campaignId]="getId()"
              [systemPropertyType]="instanceSectionComponent?.component?.templateField?.systemProperty?.type?.title">
            </app-tag-values>
          </div>
        }
      }
      <!---->
      <!--ISTAG CHIPLIST MULTIPLE SELECTOR VALUES-->
      @case ('Persona Selector') {
        <div [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-persona-tag-popover [selectedUserId]="selectedUserId" [component]="instanceSectionComponent.component"> </app-persona-tag-popover>
        </div>
      }
      <!---->
      <!-- IMAGE UPLOAD -->
      @case ('Image Upload Field') {
        <div [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-image-viewer-value
            [instance]="instance"
            [defaultImageUrl]="instanceSectionComponent?.component?.templateField?.defaultImageUrl ?? null"
            [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"
            [instanceSectionComponent]="instanceSectionComponent"></app-image-viewer-value>
        </div>
      }
      <!-- MY ORGANIZATION -->
      @case ('Organization') {
        <app-organization-expansion-control [id]="getId() ?? ''" [featureType]="instance.feature.featureType?.name ?? ''"></app-organization-expansion-control>
      }

      <!-- MEDIA BLOCK -->
      @case ('Media Block') {
        <div [ngClass]="{ 'max-width-media': instance?.feature?.isFullWidth === true || routeParams?.viewType === viewTypes.Player }">
          @if (!instanceSectionComponent?.component?.templateField?.isSrcEmbedCode) {
            @defer (on immediate) {
              <app-video-player
                [component]="instanceSectionComponent.component"
                [featureName]="instance.feature.featureType?.name"
                [systemPropertyValue]="getSystemPropertyValue(instanceSectionComponent.component.templateField.systemProperty)"
                [assetId]="instanceSectionComponent.value"
                [instanceSectionComponent]="instanceSectionComponent"
                [instanceId]="instance.id"
                [builderPreviewView]="builderPreviewView"></app-video-player>
            }
          } @else {
            @defer (on immediate) {
              <app-video-player
                [component]="instanceSectionComponent.component"
                [featureName]="instance.feature.featureType?.name"
                [url]="instanceSectionComponent.value"
                [instanceSectionComponent]="instanceSectionComponent"
                [instanceId]="instance.id"
                [builderPreviewView]="builderPreviewView"></app-video-player>
            }
          }
        </div>
      }
      <!-- DOWNLOAD BLOCK -->
      @case (
        instanceSectionComponent?.component?.componentType?.name === 'Download Block' || instanceSectionComponent?.component?.componentType?.name === 'Attachment'
          ? instanceSectionComponent?.component?.componentType?.name
          : ''
      ) {
        <div [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-file-download-control
            [assetId]="instanceSectionComponent.value"
            [instanceSectionComponent]="instanceSectionComponent"
            [instanceId]="instance.id"
            [isPlayer]="routeParams?.viewType === viewTypes.Player"
            [isBuilder]="builderPreviewView"></app-file-download-control>
        </div>
      }
      <!-- FEEDBACK BLOCK -->
      @case ('Feedback Block') {
        <img alt src="/assets/images/media-block-placeholder.png" />
      }
      <!-- EMAIL CHIPS -->
      @case ('Email Chips') {
        <app-email-chips-value></app-email-chips-value>
      }
      <!-- PRODUCTS -->
      <!---->
      <!-- NETWORK LICENSE MANAGER -->
      @case ('Network License Manager') {
        <app-network-license-manager [searchFilter]="searchFilter" [featureType]="instance.feature.featureType?.name ?? ''" [id]="getId()"></app-network-license-manager>
      }
      <!-- PRODUCT SETTINGS -->
      @case ('Product Settings') {
        @if (instance.feature.featureType?.name === 'Network Manager') {
          <app-network-product-expansion-accordion [searchFilter]="searchFilter" [featureType]="instance.feature.featureType?.name ?? ''" [networkId]="getId() ?? ''">
          </app-network-product-expansion-accordion>
        } @else {
          <app-product-expansion-accordion [searchFilter]="searchFilter" [featureType]="instance.feature.featureType?.name ?? ''" [id]="getId()"> </app-product-expansion-accordion>
        }
      }
      <!-- PRODUCT HISTORY -->
      @case ('Product History') {
        <app-product-history [searchFilter]="searchFilter" [featureType]="instance.feature.featureType?.name ?? ''" [id]="getId()"> </app-product-history>
      }
      <!-- PRODUCT BILLING -->
      @case ('Product Billing') {
        <app-product-billing> </app-product-billing>
      }
      <!--PRODUCT ACCESS/ACTIONS-->
      @case ('Product Actions') {
        <app-product-action-roles [type]="instance.feature.featureType?.name ?? ''" [id]="getId()"> </app-product-action-roles>
      }
      <!--USERS TABLE-->
      @case ('Users Table') {
        <app-users-table
          [instanceSectionComponent]="instanceSectionComponent"
          [type]="instance.feature.featureType?.name ?? ''"
          [name]="instance.title | parsePipe: instance?.id | async"
          [id]="getId()">
        </app-users-table>
      }
      <!-- Analytics -->
      @case ('Analytics') {
        <app-analytics [id]="getId() ?? ''" [componentId]="instanceSectionComponent?.component?.id ?? ''" [templateField]="instanceSectionComponent?.component?.templateField"></app-analytics>
      }
      <!--PEOPLE TABLE-->
      @case ('People Table') {
        <app-people-table [orgId]="instance?.organizationId" [instance]="instance" [type]="instance.feature.featureType?.name" [name]="instance.title | parsePipe: instance?.id | async" [id]="getId()">
        </app-people-table>
      }
      <!--GRADING TABLE-->
      @case ('Grading Table') {
        <app-grading-table
          [orgId]="instance?.organizationId"
          [instance]="instance"
          [type]="instance.feature.featureType?.name"
          [name]="instance.title | parsePipe: instance?.id | async"
          [id]="getId()">
        </app-grading-table>
      }
      @case ('External Html Block') {
        <app-external-html-block-value
          [resourceUrl]="instanceSectionComponent?.component?.templateField?.defaultImageUrl ?? instanceSectionComponent?.value"
          [openExternal]="instanceSectionComponent?.component?.templateField?.openExternal"
          [builderPreviewView]="builderPreviewView">
        </app-external-html-block-value>
      }
      <!-- COMMUNICATION MANAGER -->
      @case ('Communication Manager') {
        <app-communication-manager [communicationId]="routeParams.instanceSlug"> </app-communication-manager>
      }
      <!--ORGANIZATION SETTINGS-->
      @case ('Organization Settings') {
        <div class="page-margins">
          <app-organization-settings [organizationId]="routeParams.instanceSlug"> </app-organization-settings>
        </div>
      }
      <!-- ORGANIZATION NETWORKS -->
      @case ('Organization Networks') {
        <app-network-organization-expansion-accordion [type]="instance.feature.featureType?.name ?? ''" [id]="getId()"> </app-network-organization-expansion-accordion>
      }
      <!-- NETWORK ORGANIZATIONS -->
      @case ('Network Organizations') {
        <app-network-organizations [type]="instance.feature.featureType?.name ?? ''" [networkId]="getId()" [name]="instance.feature.title"> </app-network-organizations>
      }
      <!-- NOTIFICATION PREFERENCES -->
      @case ('Notification Preferences') {
        <div class="page-margin">
          <app-user-notification-preferences></app-user-notification-preferences>
        </div>
      }
      <!--CRITERIA-->
      @case ('Criteria Manager') {
        <div>
          <app-criteria-manager-component [instanceId]="getId()"> </app-criteria-manager-component>
        </div>
      }
      <!-- BANNER -->
      @case ('Banner') {
        <div class="page-margin">
          <app-banner [component]="instanceSectionComponent.component"></app-banner>
        </div>
      }
      <!--GOOGLE ADDRESS SEARCH-->
      @case ('Address Search') {
        <app-google-maps-autocomplete-value [featureType]="instance?.feature?.featureType?.name" [templateField]="instanceSectionComponent?.component?.templateField">
        </app-google-maps-autocomplete-value>
      }
      <!--AUTHORING TOOLS-->
      <!------>
      <!--HEADING-->
      @case ('Heading') {
        <div class="static-container" [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-heading-value
            [defaultText]="instanceSectionComponent?.component?.templateField?.defaultText"
            [instanceComponent]="instanceSectionComponent"
            [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"></app-heading-value>
        </div>
      }
      <!--MEDIA AND TEXT-->
      @case ('Media & Text') {
        <div
          class="media-text"
          [class.grid-full-width]="instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid"
          [class.use-max-width]="instanceSectionComponent?.component?.templateField?.useMaxWidth"
          [ngClass]="{
            'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player,
            'page-margin': instance.feature.isFullWidth !== true && routeParams?.viewType !== viewTypes.Player,
          }">
          <app-media-and-text-value
            [instanceComponent]="instanceSectionComponent"
            [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"></app-media-and-text-value>
        </div>
      }
      <!--ICON AND TEXT-->
      @case ('Icon & Text') {
        <ng-container class="page-margin">
          <div [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
            <app-icon-and-text-value [templateField]="instanceSectionComponent.component?.templateField" [instanceComponent]="instanceSectionComponent"></app-icon-and-text-value>
          </div>
        </ng-container>
      }
      @case ('Icon & Dropdown') {
        <ng-container class="page-margin">
          <div [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
            <app-icon-and-dropdown-value
              [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"
              [instanceSectionComponent]="instanceSectionComponent"
              [templateField]="instanceSectionComponent?.component?.templateField"
              [instance]="instance"></app-icon-and-dropdown-value>
          </div>
        </ng-container>
      }
      <!--IMAGE CENTERED-->
      @case ('Image Centered') {
        <div [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <ng-container class="page-margin">
            <app-image-centered-value
              [instanceComponent]="instanceSectionComponent"
              [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"></app-image-centered-value>
          </ng-container>
        </div>
      }
      <!--TEXTANDBUTTON-->
      @case ('Text & Button') {
        <div
          class="full-parent-height static-container"
          [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-text-and-button-value [instanceId]="instance.id" [instanceComponent]="instanceSectionComponent"></app-text-and-button-value>
        </div>
      }
      <!--BUTTON-->
      @case ('Button') {
        <div
          class="full-parent-height static-container"
          [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-button-value [instanceId]="instance.id" [instanceComponent]="instanceSectionComponent"></app-button-value>
        </div>
      }
      <!--INSTANCE INTEREST-->
      @case ('Instance Interest') {
        <div
          class="full-parent-height static-container"
          [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-instance-interest-value [instanceId]="instance.id" [instanceComponent]="instanceSectionComponent"></app-instance-interest-value>
        </div>
      }
      <!--NUMBERED BULLET LIST-->
      @case ('Numbered Bullet List') {
        <div class="static-container" [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-bullet-list-value [numbered]="true" [instanceComponent]="instanceSectionComponent"></app-bullet-list-value>
        </div>
      }
      <!--BULLETLIST-->
      @case ('Bullet List') {
        <div class="static-container" [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-bullet-list-value [instanceComponent]="instanceSectionComponent"></app-bullet-list-value>
        </div>
      }
      <!--ACCORDION-->
      @case ('Accordion') {
        <div class="static-container" [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-accordion-list-value [instanceId]="instance.id" [instanceComponent]="instanceSectionComponent"></app-accordion-list-value>
        </div>
      }
      <!--FLASH CARD-->
      @case ('Flash Card') {
        <div class="static-container" [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-flash-card-list-value [type]="'Flash Card'" [instanceId]="instance.id" [instanceSectionComponent]="instanceSectionComponent"> </app-flash-card-list-value>
        </div>
      }
      <!--PICTURE FLASH CARD-->
      @case ('Picture Flash Card') {
        <div class="static-container" [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-flash-card-list-value [type]="'Picture Flash Card'" [instanceId]="instance.id" [instanceSectionComponent]="instanceSectionComponent"> </app-flash-card-list-value>
        </div>
      }
      <!--INSTANCEDETAILS-->
      @case ('Listing Details') {
        <div
          class="static-container listing-details"
          [class.grid-full-width]="instance?.feature?.isFullWidth === true && routeParams?.viewType === viewTypes.Grid"
          [class.player-with-gradient]="routeParams?.viewType === viewTypes.Player && instanceSectionComponent.component?.templateField?.showGradient === true"
          [class.use-max-width]="instanceSectionComponent?.component?.templateField?.useMaxWidth"
          [ngClass]="{
            'max-width': (instance?.feature?.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player,
          }">
          <app-instance-details-value
            [instanceSectionComponent]="instanceSectionComponent"
            [defaultIconAssetId]="instanceSectionComponent?.component?.templateField?.defaultImageUrl"
            [instance]="instance"
            [id]="getId() ?? ''"
            [actionBw]="actionBw"
            [viewType]="routeParams?.viewType ?? 1">
          </app-instance-details-value>
        </div>
      }
      <!--PHONE NUMBER-->
      @case ('Phone Number') {
        <div class="static-container">
          <app-phone-number-value></app-phone-number-value>
        </div>
      }
      <!--FULL NAME-->
      @case ('Full Name') {
        <div class="static-container">
          <app-full-name-value [instanceSectionComponent]="instanceSectionComponent"></app-full-name-value>
        </div>
      }
      <!--PASSWORD-->
      @case ('Password') {
        <div class="static-container">
          <app-password-value [instanceSectionComponent]="instanceSectionComponent"></app-password-value>
        </div>
      }
      <!--CONTACT INFO-->
      @case ('Contact Info') {
        <div class="static-container">
          <app-contact-info-value></app-contact-info-value>
        </div>
      }
      <!--WEBSITE LINK-->
      @case ('Website Link') {
        <div class="static-container">
          <app-website-link-value [link]="instanceSectionComponent?.value"></app-website-link-value>
        </div>
      }
      <!--EMBEDDEDURL-->
      <!--TODO!-->
      @case (
        instanceSectionComponent?.component?.componentType?.name === 'Video' || instanceSectionComponent.component?.componentType?.name === 'Embedded URL'
          ? instanceSectionComponent?.component?.componentType?.name
          : ''
      ) {
        <div class="static-container">
          <app-authoring-header [componentName]="instanceSectionComponent.component.componentType.name"></app-authoring-header>
        </div>
      }
      <!-- Learning Outcomes -->
      @case ('Learning Outcomes') {
        <app-dynamic-text-value
          [instanceComponent]="instanceSectionComponent"
          [instanceId]="instance.id"
          [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"
          [defaultValue]="instanceSectionComponent?.component?.templateField?.defaultText"></app-dynamic-text-value>
      }
      <!-- Campaign Code -->
      @case ('Text') {
        @if (instanceSectionComponent?.component?.templateField?.systemProperty?.property === 'Campaign.CampaignCode') {
          <app-qr-code [qrCodeData]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"></app-qr-code>
        }
      }
      <!-- Page Banner -->
      @case ('Page Banner') {
        <app-page-banner
          [instance]="instance"
          [instanceSectionComponent]="instanceSectionComponent"
          [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"
          [defaultImageUrl]="instanceSectionComponent?.component?.templateField?.defaultImageUrl ?? null"
          [routeParams]="routeParams"></app-page-banner>
      }
      <!-- User Risec Score Chart -->
      @case ('User RIASEC Score Chart') {
        <app-user-riasec-score-chart></app-user-riasec-score-chart>
      }
      @case ('HTML5 Game') {
        <div [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-html5-game [instanceSectionComponent]="instanceSectionComponent" [instance]="instance"></app-html5-game>
        </div>
      }
      @case ('Checkbox') {
        <app-checkbox-value
          [value]="instanceSectionComponent?.value"
          [inheritedPropertyValue]="getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty)"
          [label]="instanceSectionComponent?.component?.templateField?.label ?? ''">
        </app-checkbox-value>
      }
      @case ('Qlik Analytics Type Selector') {
        <app-qlik-analytics-type-selector [id]="routeParams.instanceSlug" [templateField]="instanceSectionComponent?.component?.templateField"></app-qlik-analytics-type-selector>
      }
      @case ('Qlik Analytics') {
        <app-qlik-analytics [id]="routeParams.instanceSlug" [featureSlug]="routeParams.featureSlug" [templateField]="instanceSectionComponent?.component?.templateField"></app-qlik-analytics>
      }
      <!--SPACING-->
      @case ('Spacing') {
        <div
          class="full-parent-height static-container"
          [ngClass]="{ 'max-width': (instance.feature.isFullWidth === true && routeParams?.viewType === viewTypes.Grid) || routeParams?.viewType === viewTypes.Player }">
          <app-spacing-value [instanceComponent]="instanceSectionComponent"></app-spacing-value>
        </div>
      }
    }
  }
}
