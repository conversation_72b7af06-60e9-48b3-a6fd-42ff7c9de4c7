import { Component, forwardRef, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IAsset, IEngagementIn, IInstanceSectionComponent } from '@app/core/contracts/contract';
import { EngagementTypes } from '@app/core/enums/engagment-types.enum';
import { DataService } from '@app/core/services/data-service';
import { LayoutService } from '@app/core/services/layout-service';
import { environment } from '@env/environment';
import { saveAs } from 'file-saver';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { IonicModule } from '@ionic/angular';
import { MatIcon } from '@angular/material/icon';
import { AsyncPipe } from '@angular/common';
import { FileSizePipe } from '@app/shared/pipes/file-size-display';

@Component({
    selector: 'app-file-download-control',
    templateUrl: './file-download-control.component.html',
    styleUrls: ['./file-download-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => FileDownloadControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => FileDownloadControlComponent),
        },
    ],
    imports: [IonicModule, MatIcon, AsyncPipe, FileSizePipe]
})
export class FileDownloadControlComponent extends BaseControlComponent implements OnDestroy, OnInit {
  @Input() assetId: string | undefined;
  @Input() instanceId: string;
  @Input() instanceSectionComponent?: IInstanceSectionComponent;
  @Input() isPlayer = false;
  @Input() isBuilder = false;
  controlBackground = 'none';
  fileFormat = '*';
  componentDestroyed$: Subject<boolean> = new Subject();
  contentUrl = environment.contentUrl;
  file: File | null = null;
  progress = new BehaviorSubject<number>(0);
  $asset: Observable<IAsset>;
  isDisabled = false;
  itemBackgroundColor: string;
  fileExtension: string;

  constructor(
    private dataService: DataService,
    public layoutService: LayoutService
  ) {
    super();
  }

  ngOnInit(): void {
    if (this.assetId && this.assetId.length > 0) {
      this.$asset = this.dataService.getAssetDetailsById(this.assetId);
      this.$asset.subscribe(x => (this.fileExtension = x.name.substring(x.name.lastIndexOf('.') + 1)));
    } else {
      this.isDisabled = true;
    }
  }

  validateFileFormat(file: File): boolean {
    const matches = file.type.match(this.fileFormat);
    if (matches && matches.length > 0) {
      return true;
    }
    return false;
  }

  deleteFile() {
    this.file = null;
    this.dataService.deleteAsset(this.textValue, null).pipe(takeUntil(this.componentDestroyed$)).subscribe();
    this.textValue = '';
  }

  downloadAsset(fileName: string) {
    if (this.assetId) {
      this.dataService
        .downloadBlobFile(this.assetId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(blob => {
          if (blob) {
            saveAs(blob, fileName);
            if (this.instanceSectionComponent) {
              this.dataService
                .addInstanceSectionComponentCompletion(this.instanceSectionComponent?.id ?? '', this.instanceId ?? '')
                .pipe(takeUntil(this.componentDestroyed$))
                .subscribe(() => {});
            }
          }
        });

      if (this.instanceSectionComponent) {
        this.dataService
          .addInstanceSectionComponentEngagement({
            instanceId: this.instanceId,
            instanceSectionComponentId: this.instanceSectionComponent?.id,
            engagementType: EngagementTypes.Download,
            nominalValue: 1,
            percentageValue: 100,
          } as IEngagementIn)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(() => {});
      }
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
