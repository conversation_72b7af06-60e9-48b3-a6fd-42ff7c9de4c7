<!--EDIT-->
@if (templateForm && steps) {
  <div class="builder-side-panel" #content>
    @if (steps && steps.length > 0 && templateForm && (editPanelBuilderIn?.builderType === builderType.EditComponent || editPanelBuilderIn?.builderType === builderType.EditSection)) {
      <form class="stepper-form-container" [formGroup]="templateForm">
        <div [style]="'height: Calc(100vh - ' + contentHeight + 'px);'" class="inner-content-container">
          <div class="top-stepper-heading">
            <div class="text-container"></div>
            <div class="actions-container">
              <div class="actions">
                <ion-button class="close-button" size="small" fill="clear" color="medium" (click)="closeMenu()">
                  <ion-icon name="close-circle-outline"></ion-icon>
                </ion-button>
              </div>
            </div>
          </div>
          <div class="stepper-container" cdkScrollable>
            @for (step of steps; track step; let i = $index) {
              <ng-template matStepperIcon="i">
                <mat-icon>create</mat-icon>
              </ng-template>
            }
            <mat-stepper #stepper orientation="vertical" (click)="setIndexDirect()" [linear]="false" [selectedIndex]="selectedIndex" cdkStepper>
              @for (step of steps; track step; let i = $index) {
                <mat-step completed="false" [stepControl]="getFormControl(step.section.id, step.component?.id)" [state]="getState(step)">
                  <ng-template matStepLabel>
                    <div [ngClass]="{ section: step.typeBw !== stepTypes.Component }">
                      <div class="text-container">
                        @if (step.typeBw === stepTypes.Component) {
                          <div class="text">STEP {{ step.componentIndex }}</div>
                        }
                        @if (step.typeBw !== stepTypes.Component) {
                          <div class="text">{{ step.section?.title }}</div>
                        }
                      </div>
                      <div class="text-and-icon-container">
                        @if (step.typeBw === stepTypes.Component && step.component) {
                          <app-builder-side-panel-heading [text]="step?.component?.templateField?.helpTitle ?? ''"></app-builder-side-panel-heading>
                        }
                        @if (step.typeBw !== stepTypes.Component && step.section) {
                          <app-builder-side-panel-heading [isSectionHeading]="true" [text]="step.section.caption ?? 'Section'"></app-builder-side-panel-heading>
                          @if (step.typeBw === stepTypes.TemplateSection) {
                            @if (section?.description ?? section?.description?.length > 0) {
                              <app-builder-side-panel-description [text]="section?.description" [noPadding]="true"> </app-builder-side-panel-description>
                            }
                          }
                        }
                        @if (step.typeBw === stepTypes.Component && step.component) {
                          <div class="icon-container">
                            @if (i === selectedIndex) {
                              <ion-icon name="chevron-up"></ion-icon>
                            }
                            @if (i !== selectedIndex) {
                              <ion-icon name="chevron-down"></ion-icon>
                            }
                          </div>
                        }
                      </div>
                    </div>
                  </ng-template>
                  <ng-template matStepContent>
                    <div class="step-snap-container" [id]="step.component ? step.component.id + '-left' : step.section.id + '-left'">
                      <!-- COMPONENT -->
                      @if (step.typeBw === stepTypes.Component && step.component) {
                        @if (step.component?.templateField?.helpDescription) {
                          <app-builder-side-panel-description [text]="step.component.templateField.helpDescription" [noPadding]="true"></app-builder-side-panel-description>
                        }
                        @if (step?.typeBw === stepTypes.Component) {
                          <div class="header-row">
                            <ion-label class="header title">{{ step.component?.templateField?.label ?? step.component?.componentType?.name }}</ion-label>
                            @if (step.section.typeBw === sectionTypes.Dynamic && step.component.isLocked !== true) {
                              <div class="header buttons">
                                @if (step.section.typeBw === sectionTypes.Dynamic && hasAdminAccess()) {
                                  <div (click)="editComponentSetup()">
                                    <ion-icon name="settings"></ion-icon>
                                  </div>
                                }
                                @if (allowSectionComponentMoveUp(step)) {
                                  <div (click)="moveSectionComponent(step, true)">
                                    <ion-icon name="chevron-up"></ion-icon>
                                  </div>
                                }
                                @if (allowSectionComponentMoveDown(step)) {
                                  <div (click)="moveSectionComponent(step, false)">
                                    <ion-icon name="chevron-down"></ion-icon>
                                  </div>
                                }
                                <div (click)="deleteSectionComponent()">
                                  <ion-icon name="trash"></ion-icon>
                                </div>
                              </div>
                            }
                          </div>
                        }
                        <div class="step-component-container selected">
                          <app-form-control-selector
                            [component]="step.component"
                            [instance]="instance"
                            [formGroupName]="step.section.id"
                            [formGroup]="templateForm"
                            [showSelected]="true"
                            [id]="id"
                            [isSidePanelBuilder]="true"
                            [rowBuilderView]="step.section.typeBw === sectionTypes.Dynamic"
                            [routeParams]="routeParams"
                            (questionUpdated)="updateQuestionValue($event, step)">
                          </app-form-control-selector>
                        </div>
                      }
                      <!-- SECTION -->
                      @if (step.typeBw !== stepTypes.Component && step.section) {
                        <div class="header-row">
                          @if (step.section.typeBw === sectionTypes.Dynamic || step?.section?.isEditable === true) {
                            <ion-label class="header title"> {{ step.section.title ?? 'Section' }}</ion-label>
                          }
                          @if (step?.section?.isEditable === true || allowSectionMoveUp(step) || allowSectionMoveDown(step) || !step.section.templateId) {
                            <div class="header buttons">
                              @if (step.section.typeBw === sectionTypes.Dynamic) {
                                @if (allowSectionMoveUp(step)) {
                                  <div (click)="moveSection(step.section, true)">
                                    <ion-icon name="chevron-up"></ion-icon>
                                  </div>
                                }
                                @if (allowSectionMoveDown(step)) {
                                  <div (click)="moveSection(step.section, false)">
                                    <ion-icon name="chevron-down"></ion-icon>
                                  </div>
                                }
                                @if (!step.section.templateId) {
                                  <div (click)="deleteSection()">
                                    <ion-icon name="trash"></ion-icon>
                                  </div>
                                }
                              }
                              @if (step?.section?.isEditable === true) {
                                @if (step.instanceSection.isHidden !== true && hasAdminAccess()) {
                                  <div (click)="toggleShowInstanceSection(step.instanceSection)">
                                    <ion-icon name="eye-outline"></ion-icon>
                                  </div>
                                }
                                @if (step.instanceSection.isHidden === true && hasAdminAccess()) {
                                  <div (click)="toggleShowInstanceSection(step.instanceSection)">
                                    <ion-icon name="eye-off-outline"></ion-icon>
                                  </div>
                                }
                              }
                            </div>
                          }
                        </div>
                        <div [ngClass]="step.typeBw === stepTypes.AuthToolSection || step?.section?.isEditable === true ? 'section-container selected' : 'section-container'">
                          @if (step.typeBw === stepTypes.TemplateSection) {
                            @if (step?.section?.isEditable === true) {
                              <app-instance-section-edit
                                [panelBuilder]="editPanelBuilderIn"
                                [instanceSection]="step.instanceSection"
                                [instanceId]="instance.id"
                                [templateId]="template.id"
                                (instanceSectionUpdated)="instanceSectionUpdated($event, step.instanceSection.id)"></app-instance-section-edit>
                            }
                          }
                          @if (step.typeBw === stepTypes.AuthToolSection) {
                            <app-section-edit
                              [templateId]="template.id"
                              [panelBuilder]="editPanelBuilderIn"
                              [section]="step.section"
                              [templateForm]="templateForm"
                              [backGroundColor]="step.instanceSection?.backgroundColor ?? step.section?.backgroundColor ?? null"
                              (sectionColorChanged)="updateInstanceSectionColor($event, step.instanceSection)"></app-section-edit>
                          }
                        </div>
                      }
                    </div>
                  </ng-template>
                </mat-step>
              }
              <ng-template matStepperIcon="empty"> </ng-template>
              <ng-template matStepperIcon="section" let-index="index">
                {{ getStepEditIcon(index) }}
              </ng-template>
              <ng-template matStepperIcon="edit" let-index="index">
                {{ getStepEditIcon(index) }}
              </ng-template>
            </mat-stepper>
          </div>
        </div>
        <footer class="stepper-footer">
          <div class="back-button">
            @if (stepper.selectedIndex > 0) {
              <ion-button fill="clear" class="inner-container" (click)="goBack()">
                <mat-icon svgIcon="chevron-arrow-left"></mat-icon>
                Back
              </ion-button>
            }
          </div>
          <div class="next-button">
            <ion-button class="inner-container" (click)="goForward()">
              @if (stepper.steps.length > stepper.selectedIndex + 1) {
                <span style="color: black">Next</span>
              }
              @if (stepper.steps.length === stepper.selectedIndex + 1) {
                <span style="color: black">Finish</span>
              }
            </ion-button>
          </div>
        </footer>
      </form>
    }
    @if (editPanelBuilderIn?.builderType === builderType.AddComponent) {
      <div [style]="'height: Calc(100vh - ' + sectionHeight + 'px);'" class="static-container">
        <app-dynamic-section-components
          [section]="getMasterSection(section.id)"
          [panelBuilder]="editPanelBuilderIn"
          (sectionComponentsChanged)="sectionComponentsUpdated($event)"></app-dynamic-section-components>
      </div>
    }
    @if (editPanelBuilderIn?.builderType === builderType.AddSection) {
      <div [style]="'height: Calc(100vh - ' + sectionHeight + 'px);'" class="static-container">
        <app-dynamic-section-selector [template]="template" [panelBuilder]="editPanelBuilderIn" (sectionSelected)="setSectionBuilder($event)"></app-dynamic-section-selector>
      </div>
    }
    @if (editPanelBuilderIn?.builderType === builderType.EditComponentSetup && steps[selectedIndex].component) {
      <div [style]="'height: Calc(100vh - ' + sectionHeight + 'px);'" class="static-container">
        <app-component-editor [component]="steps[selectedIndex].component" (componentChanged)="componentChanged(true)" (closeClicked)="componentChanged(false)"></app-component-editor>
      </div>
    }
  </div>
}
