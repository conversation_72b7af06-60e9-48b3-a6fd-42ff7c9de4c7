.parent-container {
  margin-top: 2px;
  margin-bottom: 10px;
  border-radius: 3px;
  padding: 0px 10px 10px 10px;

  .header-spacing {
    margin-top: 60px;
  }
}

.suffix-input {
  background-color: #292929;
  --background: #292929;
  border-radius: 3px;
  outline: 1px solid #4e4e4e;
}

ion-input {
  --highlight-color-valid: #f99e00;
}

ion-button {
  background-color: #292929;
}

ion-select {
  --highlight-color-valid: #f99e00;

  margin-top: 8px;
  --color: #ffffff;
  --placeholder-color: #999999;

  // Add these properties for the options
  ion-select-option {
    color: #ffffff;
  }

  &::part(text) {
    color: #ffffff;
  }

  &::part(label) {
    color: #ffffff;
  }
}