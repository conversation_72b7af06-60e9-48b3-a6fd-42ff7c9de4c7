import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { IInstanceTemplate, ISection, ISidePanelBuilder } from '@app/core/contracts/contract';
import { SectionTypes } from '@app/core/enums/section-types.enum';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';

@Component({
    selector: 'app-dynamic-section-selector',
    templateUrl: './dynamic-section-selector.component.html',
    styleUrls: ['./dynamic-section-selector.component.scss'],
    standalone: false
})
export class DynamicSectionSelectorComponent implements OnInit {
  @Input() panelBuilder: ISidePanelBuilder;
  @Input() template: IInstanceTemplate;
  @Output() sectionSelected = new EventEmitter<ISection>();
  selectedSection: ISection;
  sections: ISection[];
  sectionTypes = SectionTypes;

  constructor(private builderService: BuilderService) {}

  ngOnInit() {
    this.sections = this.template.instanceSections.filter(x => x.section.typeBw === this.sectionTypes.Dynamic && x.section.templateId).map(x => x.section);
  }

  isSelected(sectionId: string) {
    return this.selectedSection?.id === sectionId;
  }

  checkboxChanged(section: ISection) {
    this.selectedSection = section;
  }

  back() {
    this.builderService.selectedSection$.next(null);
  }

  save() {
    this.sectionSelected.emit(this.selectedSection);
  }
}
