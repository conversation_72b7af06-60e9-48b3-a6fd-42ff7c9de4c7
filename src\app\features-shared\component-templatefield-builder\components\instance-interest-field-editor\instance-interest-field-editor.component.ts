import { Component, EventEmitter, Input, OnChang<PERSON>, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent, IInstanceInterestText } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-instance-interest-field-editor',
    templateUrl: './instance-interest-field-editor.component.html',
    styleUrls: ['./instance-interest-field-editor.component.scss'],
    standalone: false
})
export class InstanceInterestFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  instanceInterestForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;
  interestText: IInstanceInterestText[] = [];

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.instanceInterestForm.controls.isInherit;
  }

  ngOnInit() {
    if (this.component?.templateField?.default3) {
      this.interestText = JSON.parse(this.component.templateField.default3) as IInstanceInterestText[];
    }
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }

      this.setFormValues();
    }
  }

  createForm() {
    this.instanceInterestForm = this.formBuilder.group({
      rowNumber: [this.component?.builderRowNumber ?? 0],
      hoverSortOrder: [this.component?.hoverSortOrder ?? 0],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      label1: [this.component?.templateField?.label1, Validators.required],
      placeHolder1: [this.component?.templateField?.placeHolder1],
      default1: [this.component?.templateField?.default1],
      caption1: [this.component?.templateField?.caption1],
      description1: [this.component?.templateField?.description1],
      label2: [this.component?.templateField?.label2, Validators.required],
      placeHolder2: [this.component?.templateField?.placeHolder2],
      default2: [this.component?.templateField?.default2],
      caption2: [this.component?.templateField?.caption2],
      description2: [this.component?.templateField?.description2],
      interestText: [this.component?.templateField?.default3],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.instanceInterestForm) {
      return;
    }

    this.instanceInterestForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.instanceInterestForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.instanceInterestForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.instanceInterestForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.instanceInterestForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.instanceInterestForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.instanceInterestForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.instanceInterestForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.instanceInterestForm.controls.colspan.setValue(this.component?.templateField?.colspan);

    this.instanceInterestForm.controls.label1.setValue(this.component?.templateField?.label1);
    this.instanceInterestForm.controls.placeHolder1.setValue(this.component?.templateField?.placeHolder1);
    this.instanceInterestForm.controls.default1.setValue(this.component?.templateField?.default1);
    this.instanceInterestForm.controls.caption1.setValue(this.component?.templateField?.caption1);
    this.instanceInterestForm.controls.description1.setValue(this.component?.templateField?.description1);

    this.instanceInterestForm.controls.labe2.setValue(this.component?.templateField?.label2);
    this.instanceInterestForm.controls.placeHolder2.setValue(this.component?.templateField?.placeHolder2);
    this.instanceInterestForm.controls.default2.setValue(this.component?.templateField?.default2);
    this.instanceInterestForm.controls.caption2.setValue(this.component?.templateField?.caption2);
    this.instanceInterestForm.controls.description2.setValue(this.component?.templateField?.description2);
    this.instanceInterestForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth ?? false);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.instanceInterestForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.instanceInterestForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.instanceInterestForm.valid) {
      this.component.builderRowNumber = this.instanceInterestForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.instanceInterestForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.instanceInterestForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.instanceInterestForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.instanceInterestForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.instanceInterestForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.instanceInterestForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.instanceInterestForm.controls.isViewField.value;
      this.component.templateField.colspan = this.instanceInterestForm.controls.colspan.value;
      this.component.templateField.colNumber = this.instanceInterestForm.controls.colNumber.value;
      this.component.templateField.label1 = this.instanceInterestForm.controls.label1.value;
      this.component.templateField.placeHolder1 = this.instanceInterestForm.controls.placeHolder1.value;
      this.component.templateField.default1 = this.instanceInterestForm.controls.default1.value;
      this.component.templateField.caption1 = this.instanceInterestForm.controls.caption1.value;
      this.component.templateField.description1 = this.instanceInterestForm.controls.description1.value;
      this.component.templateField.label2 = this.instanceInterestForm.controls.label2.value;
      this.component.templateField.placeHolder2 = this.instanceInterestForm.controls.placeHolder2.value;
      this.component.templateField.default2 = this.instanceInterestForm.controls.default2.value;
      this.component.templateField.caption2 = this.instanceInterestForm.controls.caption2.value;
      this.component.templateField.description2 = this.instanceInterestForm.controls.description2.value;
      this.component.templateField.default3 = this.instanceInterestForm.controls.interestText.value;
      this.component.templateField.useMaxWidth = this.instanceInterestForm.controls.useMaxWidth.value;
    }
  }

  getInterestText(sortOrder: number) {
    return this.interestText?.find(x => x.sortOrder === sortOrder)?.text ?? '';
  }

  setInterestText(text: string, sortOrder: number) {
    const interestText: IInstanceInterestText = { text, sortOrder };

    const index = this.interestText?.findIndex(x => x.sortOrder === sortOrder) ?? -1;
    if (index > -1) {
      this.interestText.splice(index, 1);
    }

    this.interestText = this.interestText ?? [];
    this.interestText.push(interestText);

    this.instanceInterestForm.controls.interestText.setValue(JSON.stringify(this.interestText));
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
