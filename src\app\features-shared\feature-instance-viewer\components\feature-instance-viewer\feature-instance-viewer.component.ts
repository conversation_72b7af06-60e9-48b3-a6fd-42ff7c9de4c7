import { Component, ElementRef, HostListener, Input, On<PERSON>hang<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, SimpleChanges, computed, signal } from '@angular/core';
import { Router } from '@angular/router';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import {
  IComponent,
  IFeature,
  IFeatureTab,
  IFeatureTabButton,
  IInstance,
  IInstanceIn,
  IInstanceRowLite,
  IInstanceSection,
  IInstanceSectionComponent,
  IInstanceSectionComponentRows,
  IInstanceTemplate,
  IKeyValue,
  IOrganizationIn,
  IProductIn,
  IQuestionIn,
  IRouteParams,
  IRowIn,
} from '@app/core/contracts/contract';
import { AddToDialogActionTypes } from '@app/core/enums/add-to-dialog-action-types';
import { ViewType } from '@app/core/enums/view-type';
import { AuthService } from '@app/core/services/auth-service';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';
import { LayoutService } from '@app/core/services/layout-service';
import { AddMediaComponent } from '@app/features/feature-repository-builder/pages/dashboard/add-media/add-media.component';
import { AddSearchModalComponent } from '@app/standalone/modals/add-search-modal/add-search-modal.component';
import { AddToDialogComponent } from '@app/standalone/modals/add-to-dialog/add-to-dialog.component';
import { OptionSelectorDialogComponent } from '@app/standalone/modals/option-selector-dialog/option-selector-dialog.component';
import { environment } from '@env/environment';
import { AlertController, ModalController, PopoverController } from '@ionic/angular';
import { OverlayEventDetail } from '@ionic/core';
import { map, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-feature-instance-viewer',
    templateUrl: './feature-instance-viewer.component.html',
    styleUrls: ['./feature-instance-viewer.component.scss'],
    standalone: false
})
export class FeatureInstanceViewerComponent implements OnInit, OnChanges, OnDestroy {
  @Input() containerTop: any;
  @Input() template: IInstanceTemplate;
  @Input() instance: IInstance;
  @Input() featureTab: IFeatureTab;
  @Input() routeParams: IRouteParams;
  @Input() selectedUserId: string;
  searchFilter: string;
  instanceRowsInLite: IInstanceRowLite[] = [];

  viewTypes = ViewType;
  componentDestroyed$: Subject<boolean> = new Subject();
  assetUrl: string;
  minimized = false;
  customFeature: IFeature | null;
  isScorm = false;
  height = 0;
  windowResize = false;
  searchBarAvailable: boolean = false;
  scrollTop = signal(0);
  savedScrollTop = signal(0);
  scrollElement = signal<any>(null);
  maxWidthRowsMap: Map<string, number[]> = new Map();

  getMaxWidthStyle(section: any) {
    if (!section || (!section.webMaxWidth && !section.mobileMaxWidth)) {
      return { 'max-width': '100%' };
    }

    const maxWidth = this.layoutService.currentScreenSize === 'xs' ? section.mobileMaxWidth : section.webMaxWidth;

    if (!maxWidth || maxWidth === 'auto') {
      return { 'max-width': '100%' };
    }

    // Format the maxWidth value
    let formattedMaxWidth = maxWidth;
    if (!maxWidth.includes('px') && !maxWidth.includes('vw')) {
      formattedMaxWidth = `${maxWidth}px`;
    }

    return {
      'max-width': formattedMaxWidth,
      'margin-left': 'auto',
      'margin-right': 'auto',
    };
  }

  getSectionMaxWidthStyle(section: any, sectionId: string, rowNumber: number) {
    if (!section) {
      return {};
    }

    // Get the maxWidthRowNumbers for this section
    const maxWidthRowNumbers = this.maxWidthRowsMap.get(sectionId) || [];

    // Only apply max width if this row number is in the maxWidthRowNumbers array
    if (maxWidthRowNumbers.includes(rowNumber)) {
      return section.webMaxWidth || section.mobileMaxWidth ? this.getMaxWidthStyle(section) : {};
    }

    return {};
  }

  getBackgroundStyle(instanceSection: any) {
    if (!instanceSection || !instanceSection.section) {
      return 'background-color:none';
    }

    if (instanceSection.section.hideBackground === true) {
      return 'background-color:none';
    }

    if (instanceSection.section.backgroundColor || instanceSection.backgroundColor) {
      const backgroundColor = instanceSection.backgroundColor && instanceSection.backgroundColor.length > 0 ? instanceSection.backgroundColor : instanceSection.section.backgroundColor || 'none';

      return `background: ${backgroundColor}`;
    }

    return 'background-color:none';
  }

  constructor(
    private dataService: DataService,
    private router: Router,
    private alertController: AlertController,
    private instanceService: InstanceService,
    public layoutService: LayoutService,
    private toast: GlobalToastService,
    private popoverController: PopoverController,
    private authService: AuthService,
    private breadcrumbService: BreadcrumbService,
    private eventsService: Events,
    private modalController: ModalController
  ) {}

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.windowResize = true;
  }

  ngOnInit() {
    this.isScorm = this.router.url.startsWith('/scorm/');
    if (this.instance?.feature?.coverMediaAssetId) {
      this.assetUrl = `${environment.contentUrl}asset/${this.instance?.feature?.coverMediaAssetId}/content?height=1000`;
    } else {
      this.assetUrl = 'assets/images/defaultbackgroundgradient.png';
    }
    if (this.routeParams?.viewType === ViewType.Player) {
      this.minimized = true;
    }

    this.eventsService.subscribe('changeViewType', (viewtype: number) => {
      this.setSelectedViewType(viewtype);
    });

    this.initializeMaxWidthRows();
  }

  initializeMaxWidthRows() {
    if (!this.template?.instanceSections) {
      return;
    }

    // Process each section
    this.template.instanceSections.forEach(section => {
      if (!section.instanceSectionComponents) {
        return;
      }

      // Group components by builderRowNumber
      interface RowGroup {
        [key: number]: any[];
      }

      const rowGroups: RowGroup = section.instanceSectionComponents.reduce((acc, comp) => {
        const rowNumber = comp.component?.builderRowNumber;
        if (rowNumber !== undefined && rowNumber !== null) {
          if (!acc[rowNumber]) {
            acc[rowNumber] = [];
          }
          acc[rowNumber].push(comp);
        }
        return acc;
      }, {} as RowGroup);

      // Filter for rows where all components have useMaxWidth=true
      const maxWidthRowNumbers = Object.entries(rowGroups)
        .filter(([_, components]) => components.every(comp => comp.component?.templateField?.useMaxWidth === true))
        .map(([rowNumber]) => Number(rowNumber));

      // Store the result in the map
      this.maxWidthRowsMap.set(section.id, maxWidthRowNumbers);
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.windowResize || changes['containerTop']?.currentValue) {
      this.calculateHeight();
      this.windowResize = false;
    }
  }

  setSearchBarAvailable(condition) {
    this.searchBarAvailable = condition;
  }

  getElementRef(parentDiv: HTMLDivElement): ElementRef {
    return new ElementRef(parentDiv);
  }

  calculateHeight() {
    let windowSpaceUsed = 0;

    if (this.searchBarAvailable) {
      if (this.layoutService.currentScreenSize === 'xs') {
        if (this.containerTop < 80) {
          this.containerTop = this.containerTop + 85;
        } else {
          this.containerTop = this.containerTop + 145;
        }
      } else if (this.containerTop > 80) {
        this.containerTop = this.containerTop + 75;
      }
    }

    windowSpaceUsed = (this.containerTop / window.innerHeight) * 100;
    this.height = 100 - windowSpaceUsed;
  }

  setSelectedViewType(viewType: number) {
    this.routeParams.viewType = viewType;
    this.searchFilter = '';
    this.instanceService.openInstance(this.routeParams.featureSlug, this.routeParams.instanceSlug, this.routeParams.tabName, ViewType[viewType]);
  }

  checkScroll(event: any) {
    this.scrollTop.set(event.detail.scrollTop);
    this.scrollElement.set(event.target);

    if (this.scrollTop() - this.savedScrollTop() > 1000) {
      this.scrollElement().scrollToPoint(0, this.savedScrollTop());
    } else {
      this.savedScrollTop.set(this.scrollTop());
    }
  }

  addItem(event: Event) {
    this.dataService
      .getFeatureTypes()
      .pipe(
        takeUntil(this.componentDestroyed$),
        map(types => {
          return types.map(t => ({ key: t.id, value: t.name }) as IKeyValue<string, string>);
        })
      )
      .subscribe(async (featureTypes: IKeyValue<string, string>[]) => {
        const popover = await this.popoverController.create({
          component: OptionSelectorDialogComponent,
          cssClass: 'feature-type-select-popover',
          componentProps: {
            header: 'Please select a feature type:',
            options: featureTypes,
          },
          event: event,
          side: 'bottom',
        });

        popover.onDidDismiss().then(async (result: any) => {
          if (result.data) {
            this.dataService
              .createFeature(result.data.key)
              .pipe(takeUntil(this.componentDestroyed$))
              .subscribe(featureId => {
                this.instanceService.openInstance(`repository/builder`, featureId);
              });
          }
        });
        await popover.present();
      });
  }

  rowFiltered(filter: string) {
    this.searchFilter = filter;
  }

  setNewValue(valueIn: string, instanceSectionComponentId: string) {
    const instanceSectionIndex = this.template.instanceSections.findIndex(x => x.instanceSectionComponents?.some(y => y.id === instanceSectionComponentId));
    const instanceSectionCompIndex = this.template.instanceSections[instanceSectionIndex].instanceSectionComponents.findIndex(x => x.id === instanceSectionComponentId);
    if (instanceSectionCompIndex !== -1) {
      this.template.instanceSections[instanceSectionIndex].instanceSectionComponents[instanceSectionCompIndex].value = valueIn;
    }
  }

  ngOnDestroy() {
    this.eventsService.unsubscribe('changeViewType');
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }

  buttonClicked(event: any) {
    if (!this.featureTab?.featureTabButtons) {
      return;
    }
    if (this.featureTab?.featureTabButtons.length === 1) {
      this.optionSelected(this.featureTab.featureTabButtons[0], event);
    } else {
      this.openButtonOptions(event);
    }
  }

  async openButtonOptions(event: any) {
    if (this.featureTab?.featureTabButtons) {
      const popover = await this.popoverController.create({
        component: OptionSelectorDialogComponent,
        cssClass: 'question-type-popover',
        componentProps: {
          options: this.featureTab?.featureTabButtons.map(x => ({ key: x.id, value: x.buttonText }) as IKeyValue<string, string>),
        },
        event: event,
        side: 'bottom',
      });

      popover.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
        if (overlayEventDetail.data) {
          const button = this.featureTab?.featureTabButtons.find(x => x.id === overlayEventDetail.data.key);
          this.optionSelected(button, event);
        }
      });

      await popover.present();
    }
  }

  optionSelected(button: IFeatureTabButton | undefined, event: any) {
    this.customFeature = null;
    if (button?.referenceId && button.referenceId !== '') {
      this.dataService.getFeatureById(button.referenceId).subscribe(feature => {
        switch (feature.featureType?.name) {
          case 'Product Manager':
            this.addProduct(feature);
            break;
          case 'Question Manager':
            this.openSelectQuestionType(feature, event);
            break;
          case 'Organization Manager':
            this.addOrganization(feature);
            break;
          case 'Media Manager':
            this.addMedia(event);
            break;
          case 'Modifiable Learning Container Pages':
            if (
              (this.instance.feature.featureType.name === 'Modifiable Learning Container Pages' || this.instance.feature.featureType.name === 'Accredited Learning Container Pages') &&
              this.instance.isDefault === true
            ) {
              this.addClassroom();
            } else if (this.instance.feature.featureType.name === 'Modifiable Learning Container Pages' || this.instance.feature.featureType.name === 'Accredited Learning Container Pages') {
              this.addAssignment();
            } else if (this.template.instanceSections.find(x => x.instanceSectionComponents?.some(c => c.component.componentType.name === 'Row Manager'))) {
              this.openSelectRowContent(event);
            } else {
              this.addClassroom();
            }
            break;
          case 'Accredited Learning Container Pages':
            // this.addClassroom(event);
            break;
          case 'Feature Manager':
            this.addItem(event);
            break;
          default:
            this.addInstance(event);
            break;
        }
      });
    } else if (this.template.instanceSections.find(x => x.instanceSectionComponents?.some(c => c.component.componentType.name === 'Row Manager'))) {
      this.openSelectRowContent(event);
    }
  }

  async addClassroom() {
    const modal = await this.modalController.create({
      component: AddToDialogComponent,
      componentProps: { selectedInstance: this.instance, featureTypeName: this.instance?.feature?.featureType?.name, actionType: AddToDialogActionTypes.CreateClass, reload: true },
      cssClass: 'my-instances-dialog',
    });

    modal.onDidDismiss().then(value => {
      if (value.data === true) {
        this.dataService.reload$.next(null);
      }
    });

    await modal.present();
  }

  async openSelectQuestionType(feature: IFeature, event: any) {
    this.dataService.getQuestionTypes().subscribe(async data => {
      if (data) {
        const popover = await this.popoverController.create({
          component: OptionSelectorDialogComponent,
          cssClass: 'question-type-popover',
          componentProps: {
            options: data.map(x => ({ key: x.id, value: x.name }) as IKeyValue<string, string>),
          },
          event: event,
          side: 'bottom',
        });

        popover.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
          if (overlayEventDetail.data) {
            this.createQuestion(feature, overlayEventDetail.data.key);
          }
        });

        await popover.present();
      }
    });
  }

  async openSelectRowContent(event: any) {
    if (this.featureTab?.featureTabRowTypes?.length === 1) {
      this.addRowToRowManager(this.featureTab.featureTabRowTypes[0].rowType.id);
    } else if (this.featureTab?.featureTabRowTypes?.length > 1) {
      const popover = await this.popoverController.create({
        component: OptionSelectorDialogComponent,
        cssClass: 'question-type-popover',
        componentProps: {
          options: this.featureTab.featureTabRowTypes.map(x => ({ key: x.rowType.id, value: x.rowType.name }) as IKeyValue<string, string>),
        },
        event: event,
        side: 'bottom',
      });

      popover.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
        if (overlayEventDetail.data) {
          this.addRowToRowManager(overlayEventDetail.data.key);
        }
      });

      await popover.present();
    } else {
      this.dataService.getRowTypes().subscribe(async data => {
        if (data) {
          const popover = await this.popoverController.create({
            component: OptionSelectorDialogComponent,
            cssClass: 'question-type-popover',
            componentProps: {
              options: data.map(x => ({ key: x.id, value: x.name }) as IKeyValue<string, string>),
            },
            event: event,
            side: 'bottom',
          });

          popover.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
            if (overlayEventDetail.data) {
              this.addRowToRowManager(overlayEventDetail.data.key);
            }
          });

          await popover.present();
        }
      });
    }
  }

  async addAssignment() {
    const modal = await this.modalController.create({
      component: AddToDialogComponent,
      componentProps: {
        selectedInstance: this.instance,
        instanceId: this.instance.id,
        featureTypeName: this.instance?.feature?.featureType?.name,
        actionType: AddToDialogActionTypes.CreateAssignment,
        reload: true,
      },
      cssClass: 'my-instances-dialog',
    });

    modal.onDidDismiss().then(value => {
      if (value.data === true) {
        this.dataService.reload$.next(null);
      }
    });

    await modal.present();
  }

  addAllPeopleToAssignment(rowId: string) {
    this.dataService
      .addAllPeopleToAssignment(this.instance.id, rowId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        if (data) {
          this.toast.presentToast('Active users added to assignment');
        }
      });
  }

  addRowToRowManager(typeId: string) {
    this.dataService
      .createRow({ title: 'New Assignment', rowTypeId: typeId, status: 'Published' } as IRowIn)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((rowId: string) => {
        if (rowId && rowId !== '') {
          const sectionIndex = this.template.instanceSections.findIndex(x => x.instanceSectionComponents?.some(c => c.component.componentType.name === 'Row Manager'));
          const instanceSectionComponent = this.template.instanceSections[sectionIndex]?.instanceSectionComponents?.find(c => c.component.componentType.name === 'Row Manager') ?? null;
          if (instanceSectionComponent) {
            const compIndex = this.template.instanceSections[sectionIndex].instanceSectionComponents.findIndex(x => x.id === instanceSectionComponent.id);

            if (instanceSectionComponent.instanceSectionComponentRows) {
              //UpdateSortOrder New To Top.

              this.template.instanceSections[sectionIndex].instanceSectionComponents[compIndex].instanceSectionComponentRows = [
                { id: '', instanceSectionComponentId: instanceSectionComponent.id, rowId: rowId, sortOrder: 0, hidden: false } as IInstanceSectionComponentRows,
              ].concat(
                instanceSectionComponent.instanceSectionComponentRows.map(
                  x => ({ id: x.id, instanceSectionComponentId: x.instanceSectionComponentId, rowId: x.rowId, sortOrder: x.sortOrder + 1, hidden: false }) as IInstanceSectionComponentRows
                )
              );

              this.instanceRowsInLite = this.template.instanceSections[sectionIndex].instanceSectionComponents[compIndex].instanceSectionComponentRows.map(
                x => ({ id: x.rowId, sortOrder: x.sortOrder }) as IInstanceRowLite
              );
            } else {
              this.template.instanceSections[sectionIndex].instanceSectionComponents[compIndex].instanceSectionComponentRows = [
                { id: '', instanceSectionComponentId: instanceSectionComponent.id, rowId: rowId, sortOrder: 0, hidden: false } as IInstanceSectionComponentRows,
              ];
              this.instanceRowsInLite = this.template.instanceSections[sectionIndex].instanceSectionComponents[compIndex].instanceSectionComponentRows.map(
                x => ({ id: x.rowId, sortOrder: x.sortOrder }) as IInstanceRowLite
              );
            }

            const instanceRowLiteNewIn = JSON.stringify(this.instanceRowsInLite);
            this.template.instanceSections[sectionIndex].instanceSectionComponents[compIndex].value = instanceRowLiteNewIn;

            this.dataService
              .updateInstanceSectionComponent(instanceSectionComponent.id, instanceSectionComponent.value)
              .pipe(takeUntil(this.componentDestroyed$))
              .subscribe(() => {
                if (this.instance.feature.featureType.name === 'Modifiable Learning Container Pages') {
                  this.addAllPeopleToAssignment(rowId);
                }
                this.forceChangeDetection();
              });
          }
        }
      });
  }

  forceChangeDetection() {
    this.template.instanceSections = [
      ...this.template.instanceSections.map(
        x =>
          ({
            ...x,
            instanceSectionComponents: x.instanceSectionComponents.map(
              y =>
                ({
                  ...y,
                  component: { ...y.component } as IComponent,
                  instanceSectionComponentRows: [...y.instanceSectionComponentRows],
                }) as IInstanceSectionComponent
            ),
          }) as IInstanceSection
      ),
    ];
    this.template = { ...this.template } as IInstanceTemplate;
  }

  createQuestion(feature: IFeature, typeId: string) {
    this.dataService
      .createQuestion({ id: null, questionTypeId: typeId } as IQuestionIn)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(question => {
        if (question) {
          this.openInstanceBuilder(feature, question);
        }
      });
  }

  openInstanceBuilder(feature: IFeature, id: string) {
    if (feature.featureType != null) {
      if (
        feature.featureType?.name === 'Product Manager' ||
        feature.featureType?.name === 'Organization Manager' ||
        feature.featureType?.name === 'Question Manager' ||
        feature.featureType?.name === 'Network Manager'
      ) {
        this.instanceService.openInstance(feature.featureSlug, id);
      } else {
        this.instanceService.openInstance('instance', id, null, 'builder', null, true);
      }
    }
  }

  getSectionComponents(instanceSection: IInstanceSection) {
    return instanceSection.instanceSectionComponents.map(x => x.component);
  }

  private addInstance(event: any) {
    this.dataService
      .getMyOrganizations(this.instance.feature.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(async data => {
        if (data.length < 1) {
          await this.presentAlert();
          return;
        }
        if (data.length > 1 || this.authService.userContext?.canManage === true) {
          this.addInstanceToOrganization(event);
        } else {
          this.saveInstance(data[0].id);
        }
      });
  }

  private populateNewInstanceIn(orgId: string) {
    const newInstance: IInstanceIn = {
      title: this.customFeature?.title ?? this.instance?.feature?.title,
      featureId: this.customFeature?.id ?? this.instance?.feature?.id,
      organizationId: orgId,
      status: this.instance.feature.featureType.name === 'Modifiable Learning Container Pages' ? 'public' : 'private',
    };
    return newInstance;
  }

  private async addInstanceToOrganization(event: any) {
    const popover = await this.popoverController.create({
      component: AddSearchModalComponent,
      cssClass: 'add-search-modal',
      componentProps: { linkTypeName: 'Organizations', criteriaType: null, options: null },
      event: event,
      side: 'bottom',
    });

    popover.onDidDismiss().then((result: any) => {
      if (result.data) {
        this.saveInstance(result.data.id);
      }
    });

    await popover.present();
  }

  private saveInstance(orgId: string) {
    const newInstance = this.populateNewInstanceIn(orgId);
    this.dataService
      .createInstance(newInstance)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(instance => {
        this.breadcrumbService.hardRefresh = true;
        this.instanceService.openInstance('instance', instance.id, null, 'builder');
      });
  }

  private async addMedia(event: any) {
    const popover = await this.popoverController.create({
      component: AddMediaComponent,
      cssClass: 'add-media-popover',
      event: event,
      side: 'bottom',
    });

    await popover.present();
  }

  private async presentAlert() {
    const alert = await this.alertController.create({
      cssClass: '',
      header: 'Please Note:',
      message: 'User not linked to an organization.',
      buttons: ['OK'],
    });

    await alert.present();

    await alert.onDidDismiss();
  }

  private addProduct(feature: IFeature) {
    const newProduct: IProductIn = {
      name: this.instance?.feature?.title + Math.floor(1000 + Math.random() * 9000),
      description: this.instance?.feature?.description as string,
    };

    this.dataService
      .addProduct(newProduct)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(product => {
        this.openInstanceBuilder(feature, product.id);
      });
  }

  private addOrganization(feature: IFeature) {
    const newOrganization: IOrganizationIn = {
      name: this.instance?.feature?.title + Math.floor(1000 + Math.random() * 9000),
    };

    this.dataService
      .addOrganization(newOrganization)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(org => {
        if (org) {
          this.openInstanceBuilder(feature, org.id);
        }
      });
  }
}
