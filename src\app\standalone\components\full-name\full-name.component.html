<div class="parent-container" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }">
  <ion-card class="card-content-container">
    @if (fullNameForm) {
      <form class="inner-container" [formGroup]="fullNameForm">
        <ion-grid>
          <ion-row>
            <ion-col>
              <app-text-input-control
                [disabled]="disabled"
                [backgroundColor]="'#1E1E1E'"
                [label]="'Full Name'"
                [sidePanelPadding]="true"
                [placeHolder]="'Enter your full name'"
                [toolTip]="''"
                formControlName="fullName"></app-text-input-control>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col size="6">
              <app-text-input-control
                [disabled]="disabled"
                [backgroundColor]="'#1E1E1E'"
                [label]="'First Name'"
                [sidePanelPadding]="true"
                [placeHolder]="'Enter your first name'"
                [toolTip]="''"
                formControlName="firstName"></app-text-input-control>
            </ion-col>
            <ion-col size="6">
              <app-text-input-control
                [disabled]="disabled"
                [backgroundColor]="'#1E1E1E'"
                [label]="'Last Name'"
                [sidePanelPadding]="true"
                [placeHolder]="'Enter your last name'"
                [toolTip]="''"
                formControlName="lastName"></app-text-input-control>
            </ion-col>
          </ion-row>
        </ion-grid>
      </form>
    }
  </ion-card>
</div>
