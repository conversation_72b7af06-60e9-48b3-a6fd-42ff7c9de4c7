import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IFeature } from '@app/core/contracts/contract';

@Component({
    selector: 'app-feature-repository-template',
    templateUrl: './template.component.html',
    styleUrls: ['./template.component.scss'],
    standalone: false
})
export class FeatureRepositoryTemplateComponent {
  @Input() feature: IFeature;
  @Output() featureUpdated = new EventEmitter<IFeature>();
  constructor() {}

  updateFeature(isUpdatedFeature: IFeature) {
    this.featureUpdated.emit(isUpdatedFeature);
  }
}
