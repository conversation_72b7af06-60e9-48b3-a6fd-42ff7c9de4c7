import { Component, Input, OnDestroy } from '@angular/core';
import { IEarningCriteria, IInstance } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-page-enroll',
  templateUrl: './page-enroll.component.html',
  styleUrls: ['./page-enroll.component.scss'],
  standalone: true,
  imports: [IonicModule],
})
export class PageEnrollComponent implements OnDestroy {
  @Input() instance: IInstance;
  @Input() earningCriteria: IEarningCriteria;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private dataService: DataService) {}

  join() {
    this.dataService
      .addInstanceBadgeUser(this.instance?.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(enrolled => {
        this.instance.isEnrolled = enrolled;
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
