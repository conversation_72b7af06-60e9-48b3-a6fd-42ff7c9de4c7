import { Component, EventEmitter, Input, OnChanges, OnDestroy, Output, SimpleChanges } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { IComponent, IComponentEditIn, ITemplateFieldIn } from '@app/core/contracts/contract';
import { ComponentType } from '@app/core/enums/component-type.enum';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Observable, Subject, forkJoin } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-component-editor',
    templateUrl: './component-editor.component.html',
    styleUrls: ['./component-editor.component.scss'],
    standalone: false
})
export class ComponentEditorComponent implements OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() componentChanged = new EventEmitter<void>();
  @Output() closeClicked = new EventEmitter<void>();
  componentDestroyed$: Subject<boolean> = new Subject();
  componentType = ComponentType;
  formValid = false;
  rowEditorForm: UntypedFormGroup;
  componentAchievementIds: string[];

  constructor(
    private builderService: BuilderService,
    private dataService: DataService
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      this.formValid = false;
    }
  }

  back() {
    this.closeClicked.emit();
    this.builderService.selectedComponent$.next(null);
  }

  setFormValid(value: boolean) {
    this.formValid = value;
  }

  rowEditorFormChange(formGroup: UntypedFormGroup) {
    this.rowEditorForm = formGroup;
  }

  save() {
    const requests: Observable<any>[] = [];
    const editComponent = this.dataService.editComponent({
      id: this.component.id,
      builderRowNumber: this.component?.builderRowNumber ? +this.component?.builderRowNumber : 0,
      hoverSortOrder: this.component?.hoverSortOrder ? +this.component?.hoverSortOrder : 0,
      instanceSortOrder: this.component?.instanceSortOrder ? +this.component?.instanceSortOrder : 0,
    } as IComponentEditIn);
    requests.push(editComponent);
    if (this.component.componentType.name === 'Default Row' || this.component.componentType.name === 'Smart Row') {
      const addRowType = this.dataService.putRowType(this.component.rowId, this.rowEditorForm.value.rowType);
      requests.push(addRowType);

      //create temaplete field if undefined
      if (!this.component.templateField) {
        this.dataService.createTemplateField(this.component.id, this.component.componentType.name).subscribe();
      } else {
        const updateTemplateField = this.dataService.updateTemplateField({
          ...this.component.templateField,
          systemPropertyId: this.component.templateField.systemProperty?.id,
          parentIdSystemPropertyLink: this.component.templateField.parentIdSystemPropertyLink?.id,
          dropDownValues: this.component.templateField.dropDownValues ? this.component.templateField.dropDownValues.map(x => x.id) : null,
          dropDownLinkTypeId: this.component.templateField?.dropDownLinkType?.id,
          communicationBlockId: this.component.templateField?.communicationBlock?.id,
        } as ITemplateFieldIn);
        requests.push(updateTemplateField);
      }
    } else {
      if (this.component.componentType.name === 'Row Manager') {
        const updateAchievements = this.dataService.updateComponentAchievements(this.component.id, this.componentAchievementIds);
        requests.push(updateAchievements);
      }
      if (!this.component.templateField) {
        this.dataService.createTemplateField(this.component.id, this.component.componentType.name).subscribe();
      } else {
        const updateTemplateField = this.dataService.updateTemplateField({
          ...this.component.templateField,
          systemPropertyId: this.component.templateField.systemProperty?.id,
          parentIdSystemPropertyLink: this.component.templateField.parentIdSystemPropertyLink?.id,
          dropDownValues: this.component.templateField.dropDownValues ? this.component.templateField.dropDownValues.map(x => x.id) : null,
          dropDownLinkTypeId: this.component.templateField?.dropDownLinkType?.id,
          communicationBlockId: this.component.templateField?.communicationBlock?.id,
        } as ITemplateFieldIn);
        requests.push(updateTemplateField);
      }
    }
    forkJoin(requests)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.componentChanged.emit();
        this.back();
      });
  }

  setComponentAchievements(ids: string[]) {
    this.componentAchievementIds = ids;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
