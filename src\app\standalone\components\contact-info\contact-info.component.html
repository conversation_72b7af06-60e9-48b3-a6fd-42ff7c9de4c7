<div class="parent-container" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }">
  <ion-card class="card-content-container">
    @if (contactInfoForm) {
      <form [formGroup]="contactInfoForm">
        <ion-grid>
          <ion-row>
            <ion-col class="primary-email-col">
              <app-text-input-control
                [backgroundColor]="'#1E1E1E'"
                [noPadding]="true"
                [label]="'Primary Email'"
                [placeHolder]="'Enter your primary email'"
                [toolTip]="''"
                [disabled]="disabled"
                formControlName="primaryEmail"></app-text-input-control>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <app-email-chip [noContainer]="true" [disabled]="disabled" [id]="id"> </app-email-chip>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <app-phone-number [noPadding]="true" [disabled]="disabled" [placeHolder]="'(___) ___-____'" [label]="'Mobile Phone'" formControlName="phoneNumber"> </app-phone-number>
            </ion-col>
          </ion-row>
        </ion-grid>
      </form>
    }
  </ion-card>
</div>
