<div class="parent-container" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }">
  <ion-card class="card-container">
    <form class="form-container" [formGroup]="instanceInterestForm">
      @if (component?.templateField?.caption1) {
        <p class="caption">{{ component?.templateField?.caption1 }}</p>
      }
      @if (component?.templateField?.description1) {
        <p class="description">{{ component?.templateField?.description1 }}</p>
      }
      <app-text-input-control
        [noPadding]="true"
        [backgroundColor]="'#1E1E1E'"
        [placeHolder]="component?.templateField?.placeHolder1 ?? 'Start typing here'"
        [label]="component?.templateField?.label1 ?? 'Title'"
        formControlName="heading"
        [itemBackgroundColor]="'#292929'"></app-text-input-control>

      @if (component?.templateField?.caption2) {
        <p class="caption">{{ component?.templateField?.caption2 }}</p>
      }
      @if (component?.templateField?.description2) {
        <p class="description">{{ component?.templateField?.description2 }}</p>
      }
      <app-dynamic-text-input-control
        [placeHolder]="component?.templateField?.placeHolder2 ?? 'Start typing here'"
        [label]="component?.templateField?.label2 ?? 'Description'"
        [sidePanelPadding]="true"
        [backgroundColor]="'#1E1E1E'"
        formControlName="description"></app-dynamic-text-input-control>

      @if (component?.templateField?.caption3) {
        <p class="caption">{{ component?.templateField?.caption3 }}</p>
      }
      @if (component?.templateField?.description3) {
        <p class="description">{{ component?.templateField?.description3 }}</p>
      }
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Text for interest none'"
            [placeHolder]="'Type here...'"
            formControlName="interestNoneText"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Text for interest medium'"
            [placeHolder]="'Type here...'"
            formControlName="interestMediumText"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Text for interest high'"
            [placeHolder]="'Type here...'"
            formControlName="interestHighText"></app-text-input-control>
        </ion-col>
      </ion-row>
    </form>
  </ion-card>
</div>
