import { Component } from '@angular/core';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { IonicModule } from '@ionic/angular';
import { EmailChipsValueComponent } from '../email-chips-value/email-chips-value.component';
import { PhoneNumberValueComponent } from '../phone-number-value/phone-number-value.component';
import { TextValueComponent } from '../text-value/text-value.component';

@Component({
    selector: 'app-contact-info-value',
    templateUrl: './contact-info-value.component.html',
    styleUrls: ['./contact-info-value.component.scss'],
    imports: [IonicModule, TextValueComponent, EmailChipsValueComponent, PhoneNumberValueComponent]
})
export class ContactInfoValueComponent {
  constructor(private systemPropertiesService: SystemPropertiesService) {}

  getPrimaryEmail() {
    if (!this.systemPropertiesService.userProperties) {
      return undefined;
    }

    const email = this.systemPropertiesService.userProperties.find(x => x.key.indexOf('email') !== -1)?.value;
    return email;
  }
}
