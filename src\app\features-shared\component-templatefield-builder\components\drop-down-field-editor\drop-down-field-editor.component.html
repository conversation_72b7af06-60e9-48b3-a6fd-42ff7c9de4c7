@if (dropDownFieldForm) {
  <form [formGroup]="dropDownFieldForm">
    <ion-grid>
      <ion-row>
        <ion-col>
          <app-select-option-control
            [label]="'Link Type'"
            [options]="dropDownLinkTypes"
            [backgroundColor]="'#333333'"
            [disabled]="false"
            formControlName="dropDownLinkType"></app-select-option-control>
        </ion-col>
      </ion-row>
      @if (getDropDownLinkValue()?.title === 'Tags' || getDropDownLinkValue()?.title === 'Organization Tags' || getDropDownLinkValue()?.title === 'Campaign User Tags') {
        <ion-row>
          <ion-col>
            <app-tag-tree-field [templateField]="component.templateField" (valueChanged)="tagSelectionChanged($event)"></app-tag-tree-field>
          </ion-col>
        </ion-row>
      }
      <app-field-editor-base [fieldForm]="dropDownFieldForm"></app-field-editor-base>
      <ion-row>
        <ion-col>
          <ion-card>
            <ion-card-content>
              <app-field-checkboxes-base [baseForm]="dropDownFieldForm"></app-field-checkboxes-base>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isInherit">System Property</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isTag">Is Tag</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVariable">Is Variable</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVisibleRepository">Is Visible in Repository</mat-slide-toggle>
              @if (isInheritControl.value) {
                <app-system-property-selector [templateField]="component.templateField" [formGroup]="dropDownFieldForm"></app-system-property-selector>
              }
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isParentSystemPropertyLinkField">Parent Tree System Property</mat-slide-toggle>
              @if (isParentSystemPropertyLinkControl.value) {
                <app-system-property-selector [templateField]="component.templateField" [formGroup]="dropDownFieldForm" [isParentSystemPropertyLinkField]="true"></app-system-property-selector>
              }
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
