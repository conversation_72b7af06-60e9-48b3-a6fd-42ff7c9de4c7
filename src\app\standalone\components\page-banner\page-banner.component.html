@if (iconUrl && iconUrl !== '') {
  @if (!isIconVideo) {
    <div
      id="page-banner-first"
      [ngClass]="{ absolute: templateField?.moveToBack === true, 'page-image-header-gradient': templateField?.showGradient === true, 'page-image-header': templateField?.showGradient !== true }"
      [style.height]="templateField?.heightPx && templateField?.heightPx > 0 ? templateField?.heightPx + 'px' : 'auto'"
      [style.min-height]="templateField?.isCoverImage ? '220px' : 'auto'"
      [style.aspect-ratio]="templateField?.aspectRatio ?? 'auto'"
      [ngStyle]="{
        'background-position': templateField?.backgroundPosition || undefined,
        'background-size': templateField?.backgroundSize || undefined,
        'background-image': templateField?.customGradient
          ? iconUrl
            ? templateField?.customGradient + ', url(' + iconUrl + ')'
            : templateField?.customGradient + ', url(assets/images/no-image.png)'
          : iconUrl
            ? 'url(' + iconUrl + ')'
            : undefined,
      }"
      [style.backgroundImage]="iconUrl ? templateField?.customGradient + ', url(' + iconUrl + ')' : gradientStyle + ', url(assets/images/no-image.png)'"></div>
  } @else {
    <video
      id="autoplay-vid"
      class="bg-video"
      [ngClass]="{ absolute: templateField?.moveToBack === true }"
      [style.height]="templateField?.heightPx && templateField?.heightPx > 0 ? templateField?.heightPx + 'px' : 'auto'"
      [style.min-height]="templateField?.isCoverImage ? '220px' : 'auto'"
      [style.aspect-ratio]="templateField?.aspectRatio ?? 'auto'"
      [ngStyle]="{
        'background-position': templateField?.backgroundPosition || undefined,
        'background-size': templateField?.backgroundSize || undefined,
      }"
      autoplay
      loop
      muted
      preload="metadata"
      playsinline>
      <source [src]="iconUrl" type="video/mp4" />
    </video>
  }
  @if (templateField?.showGradient === true) {
    <div [style.background]="gradientStyle" class="gradient-overlay"></div>
  }
} @else if (!iconUrl || iconUrl === '') {
  <div
    [ngClass]="{ absolute: templateField?.moveToBack === true, 'page-image-header-gradient': templateField?.showGradient === true, 'page-image-header': templateField?.showGradient !== true }"
    [style.height]="templateField?.heightPx && templateField?.heightPx > 0 ? templateField?.heightPx + 'px' : 'auto'"
    [style.min-height]="templateField?.isCoverImage ? '220px' : 'auto'"
    [style.aspect-ratio]="templateField?.aspectRatio ?? 'auto'"
    [ngStyle]="{
      'background-position': templateField?.backgroundPosition || undefined,
      'background-size': templateField?.backgroundSize || undefined,
    }"
    [style]="'--background-image:url(assets/images/no-image.png);'"></div>
}
