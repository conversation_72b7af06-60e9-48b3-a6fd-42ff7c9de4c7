@if (headerFieldForm) {
  <form [formGroup]="headerFieldForm">
    <ion-grid>
      <ion-row>
        <ion-col>
          <ion-card>
            <ion-card-content>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="showOnHover">Visible on hover</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVisible">Preview in builder</mat-slide-toggle>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Hover Sort Order'"
            [placeHolder]="'Add field hover sort order...'"
            formControlName="hoverSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Instance Sort Order'"
            [placeHolder]="'Add field instance sort order...'"
            formControlName="instanceSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
