import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ICommunicationBlock, IComponent } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-banner-editor',
    templateUrl: './banner-editor.component.html',
    styleUrls: ['./banner-editor.component.scss'],
    standalone: false
})
export class BannerEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  bannerForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;
  dropDownValues: KeyValue[];
  communicationBlocks: ICommunicationBlock[];

  constructor(
    private formBuilder: UntypedFormBuilder,
    private dataService: DataService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.bannerForm.controls.isInherit;
  }

  get isParentSystemPropertyLinkControl(): AbstractControl {
    return this.bannerForm.controls.isParentSystemPropertyLinkField;
  }

  ngOnInit() {
    this.getCommunicationBlocks();
  }

  getCommunicationBlocks() {
    this.dataService
      .getCommunicationBlocks(null, 'Banner')
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.dropDownValues = data.map(x => ({ id: x.id, value: x.title }) as KeyValue);
        this.communicationBlocks = data;
        this.createForm();
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.bannerForm = this.formBuilder.group({
      communicationBlockId: [this.component.templateField?.communicationBlock?.id, Validators.required],
      rowNumber: [this.component?.builderRowNumber],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.bannerForm) {
      return;
    }

    this.bannerForm.controls.communicationBlockId.setValue(this.component.templateField?.communicationBlock?.id);
    this.bannerForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.bannerForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.bannerForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.bannerForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.bannerForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.bannerForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.bannerForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.bannerForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.bannerForm.controls.isVisibleRepository.setValue(this.component?.templateField?.isVisibleRepository);
    this.bannerForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.bannerForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.bannerForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.bannerForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.bannerForm.valid);
      this.setValues();
    });
  }

  tagSelectionChanged(tagChanged: boolean) {
    this.formValidityChanged.emit(tagChanged);
  }

  setValues() {
    if (this.bannerForm.valid) {
      this.component.templateField.communicationBlock = this.getCommunicationBlockalue();
      this.component.builderRowNumber = this.bannerForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.bannerForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.bannerForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.bannerForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.bannerForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.bannerForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.bannerForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.bannerForm.controls.isViewField.value;
      this.component.templateField.isVisibleRepository = this.bannerForm.controls.isVisibleRepository.value;
      this.component.templateField.colspan = this.bannerForm.controls.colspan.value;
      this.component.templateField.colNumber = this.bannerForm.controls.colNumber.value;
      this.component.templateField.useMaxWidth = this.bannerForm.controls.useMaxWidth.value;
    }
  }

  getCommunicationBlockalue() {
    const link = this.communicationBlocks.find(x => x.id === this.bannerForm?.controls?.communicationBlockId?.value);

    return link;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
