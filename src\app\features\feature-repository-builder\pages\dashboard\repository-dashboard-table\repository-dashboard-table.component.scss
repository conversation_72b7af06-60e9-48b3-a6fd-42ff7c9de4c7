.table-container {
  height: 100%;
  width: fit-content;
  .id-route:hover {
    color: orange;
    text-decoration: underline;
  }

  mat-table {
    background-color: rgb(24, 24, 24) !important;

    .mat-mdc-header-cell {
      color: white;
      background-color: rgba(34, 34, 34) !important;
      padding: 10px;
      font-size: 16px;
      border-width: 0px 1px 1px 0px;
      border-right-style: solid;
      border-right-color: black;
      border-bottom-color: gray;
      border-left-color: rgba(34, 34, 34);
      box-sizing: border-box;
    }

    .mat-mdc-cell {
      background-color: rgba(42, 42, 42);
      color: white;
      padding: 10px;
      border-width: 0px 1px 1px 0px;
      border-style: solid solid;
      border-color: black;
      box-sizing: border-box;

      .inner-container {
        max-height: 150px;
        min-height: 30px;
        display: flex;
        align-items: center;
        overflow: auto;
      }

      .image-container {
        width: 100%;
        height: 150px;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .cdk-column-Sync-to-Credential-Engine {
      min-width: 270px;
    }

    .cdk-column-Status {
      min-width: 340px;
    }
  }

  table::-webkit-scrollbar-thumb {
    background-color: gray;
  }
}

.scroll-container {
  position: absolute;
  bottom: 0px;
  width: 100px;
  height: 100px;
  right: 0px;
}

.scroll-box {
  height: calc(100% - 60px);
  overflow-y: scroll;
  white-space: nowrap;
  position: relative;
}