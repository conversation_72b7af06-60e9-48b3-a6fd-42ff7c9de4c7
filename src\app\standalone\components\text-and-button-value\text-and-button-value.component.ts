import { Async<PERSON>ip<PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { ITextAndButton } from '@app/core/contracts/contract';
import { AuthService } from '@app/core/services/auth-service';
import { LayoutService } from '@app/core/services/layout-service';
import { ParseContentPipe } from '@app/shared/pipes/parse-content';
import { IonicModule } from '@ionic/angular';
import { first } from 'rxjs';
import { QuillViewComponent } from 'ngx-quill';
import { BaseValueComponent } from '@app/standalone/components/base-control/base-value.component';


@Component({
    selector: 'app-text-and-button-value',
    templateUrl: './text-and-button-value.component.html',
    styleUrls: ['./text-and-button-value.component.scss'],
    imports: [IonicModule, NgClass, QuillViewComponent, NgTemplateOutlet, AsyncPipe, ParseContentPipe]
})
export class TextAndButtonValueComponent extends BaseValueComponent implements OnInit {
  @Input() instanceId: string;
  textAndButton: ITextAndButton;
  isButtonBelowText: boolean;
  stylingDirection: string;
  oldValue: any;

  constructor(
    private authService: AuthService,
    private layoutService: LayoutService,
    private parseContentPipe: ParseContentPipe
  ) {
    super()
  }

  get mobileScreen() {
    return this.layoutService.currentScreenSize === 'xs';
  }

  ngOnInit() {
    this.setData();
  }

  override setData() {
    this.isButtonBelowText = this.instanceComponent?.component?.templateField?.isButtonBelowText ?? false;

    if (this.instanceComponent?.value != this.oldValue) {
      this.textAndButton = JSON.parse(this.instanceComponent?.value!) as ITextAndButton;
      if (this.textAndButton.heading === '' && this.instanceComponent?.component?.templateField?.default1) {
        this.textAndButton.heading = this.instanceComponent?.component?.templateField?.default1;
      }
      if (this.textAndButton.description === '' && this.instanceComponent?.component?.templateField?.default2) {
        this.textAndButton.description = this.instanceComponent?.component?.templateField?.default2;
      }
      if (!this.textAndButton.buttonName && this.instanceComponent?.component?.templateField?.default3) {
        this.textAndButton.buttonName = this.instanceComponent?.component?.templateField?.default3;
      }
      if (!this.textAndButton.url && this.instanceComponent?.component?.templateField?.default4) {
        this.textAndButton.url = this.instanceComponent?.component?.templateField?.default4;
      }
      if (this.textAndButton?.stylingDirection) {
        this.stylingDirection = this.textAndButton?.stylingDirection;
      } else {
        this.stylingDirection = 'Bottom';
      }
    } else {
      this.stylingDirection = 'Bottom';
      this.textAndButton = {
        heading: this.instanceComponent?.component?.templateField?.default1 ?? 'Heading',
        description: this.instanceComponent?.component?.templateField?.default2 ?? 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
        buttonName: this.instanceComponent?.component?.templateField?.default3 ?? 'button',
        url: this.instanceComponent?.component?.templateField?.default4 ?? '',
        stylingDirection: 'Bottom',
      } as ITextAndButton;
    }
  }

  open() {
    this.parseContentPipe
      .transform(this.textAndButton.url, this.instanceId, null, true)
      .pipe(first())
      .subscribe((result: string) => {
        if (result === 'login') {
          this.authService.setUserContext(false).subscribe(() => {
            this.authService.startAuthentication();
          });
        } else {
          if (this.layoutService.currentScreenSize === 'xs') {
            window.open(result.startsWith('http') === true ? result : `https://${result}`, '_self');
          } else if (this.textAndButton.sameUrlNavigation !== true) {
            window.open(result.startsWith('http') === true ? result : `https://${result}`, '_blank');
          } else {
            window.open(result.startsWith('http') === true ? result : `https://${result}`, '_self');
          }
        }
      });
  }
}
