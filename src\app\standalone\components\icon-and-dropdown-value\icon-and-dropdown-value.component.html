<ion-grid class="parent-container">
  <ng-container>
    <div class="icon-and-text-container">
      <div class="icon-container">
        <img class="icon" [src]="setIcon()" />
      </div>
      <div class="text">
        @if (!instanceSectionComponent?.component?.templateField?.isTag) {
          <app-select-option-value
            [inheritedPropertyValue]="inheritedPropertyValue"
            [instanceComponent]="instanceSectionComponent"
            [templateField]="templateField"
            [instance]="instance"
            [noPadding]="true">
          </app-select-option-value>
        } @else {
          <app-tag-values
            [dropDownLinkType]="instanceSectionComponent?.component?.templateField?.dropDownLinkType?.title"
            [instanceId]="instance.id"
            [componentId]="componentId"
            [textOnly]="true"
            [isTag]="templateField?.isTag"
            [systemPropertyType]="instanceSectionComponent?.component?.templateField?.systemProperty?.type?.title">
          </app-tag-values>
        }
      </div>
    </div>
  </ng-container>
</ion-grid>
