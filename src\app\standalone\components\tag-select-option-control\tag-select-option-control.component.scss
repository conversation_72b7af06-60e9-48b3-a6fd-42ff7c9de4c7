.parent-container {
  .inner-container {
    --background-hover: var(--background);
    --border-color: transparent;
    --color: white;
    font-size: 20px;
    font-family: 'Exo 2';
    --background: var(--background);
    width: 100%;

    .reqAsterisk {
      color: #7f550c;
      font-size: 28px;
    }

    .identifier {
      background-color: #7f550c;
      border-radius: 0.2em;
      color: black;
      font-weight: bold;
      padding: 0.3em;
      margin-left: 0.4em;
    }

    ion-input {
      border: 1px solid #4e4e4e;
      border-radius: 5px;
      margin-top: 0.5em;
      color: white;
      font-size: 16px;
      text-overflow: ellipsis !important;
      --padding-start: 8px !important;
    }
  }

  .selected {
    ion-input {
      margin-top: 0px !important;
    }

    .parent-container {
      --inner-padding-end: 0px !important;
      --padding-start: 0px !important;
    }
  }

  .chiplist-inner-container {
    margin: 16px;
    padding: 10px;
    background-color: #232323;
    border: #444444 1px solid;
    border-radius: 3px;

    .chiplist-col {
      .label-container {
        margin-bottom: 5px;
        color: white;
      }
    }

    .pop-over-trigger-col {
      display: flex;
      justify-content: flex-end;

      .trigger {
        display: flex;
        align-items: flex-start;
        padding-left: 5px;
        color: white;
      }

      ion-icon {
        font-size: 20px;
        cursor: pointer;
      }
    }
  }
}

.filter-row-container {
  .chiplist-inner-container {
    margin: 0px !important;
  }

  ion-input {
    font-size: 14px !important;
    margin-top: 0px !important;
    background-color: #232323 !important;
  }
}

#option-value {
  cursor: pointer;
}

.no-padding {
  --inner-padding-end: 0;
  --padding-start: 0;
}

ion-button {
  position: absolute;
  right: 20px;
  top: 45px;
  z-index: 50;
}