.tab-builder-container {
  height: 100%;
  display: flex;
  flex: 1;

  .settings-grid {
    margin: 0 16px 0 16px;
  }

  .italic-text {
    font-style: italic;
    font-size: 10px;
    text-transform: uppercase;
    padding-left: 0px;
  }

  .tab-header {
    text-align: start;
  }

  .mdc-tab__content {
    width: 100% !important;
  }

  .mat-mdc-tab-body-wrapper {
    // margin-right: -10px;
    overflow: hidden;
  }

  .mdc-tab__text-label {
    color: #aaa !important;
  }

  .mat-mdc-tab-header {
    background-color: #292929 !important;
  }

  .mdc-tab--active {
    color: #f99e00;
    .mdc-tab__text-label {
      color: #f99e00 !important;
    }
  }

  .mat-mdc-tab {
    margin-right: 0px !important;
    padding: 0px !important;
    justify-content: flex-start !important;
    min-width: 0px !important;
    width: auto !important;
    color: #ffffff;
    border-bottom: #f99e00;
    font-size: 1em;
  }

  .mat-mdc-tab.mat-mdc-tab-disabled {
    opacity: 1 !important;
  }

  ion-icon {
    height: 16px;
  }

  ion-input {
    min-height: 0px !important;
    --padding-top: 0px;
    --padding-bottom: 0px;
  }

  ion-tab-button {
    min-width: 48px;
  }

  .align-right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;

    ion-button {
      margin-right: 16px;
      margin-bottom: 16px;
      margin-top: 16px;
    }
  }

  .content-container {
    width: 100%;
  }

  .add-tab {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;

    ion-button {
      margin-right: 16px;
      pointer-events: auto !important;
    }
  }
}
