<mat-tab-group mat-stretch-tabs="true" mat-align-tabs="start" (selectedTabChange)="tabChanged($event)" color="primary" class="tab-builder-container">
  @for (featureTab of featureTabs; track featureTab; let i = $index) {
    <mat-tab>
      <ng-template matTabLabel>
        <div class="tab-header">
          <ion-label class="italic-text">{{ featureTab?.isDefaultInstanceTab ? 'Feature' : 'Instance' }}</ion-label>
          <ion-input (change)="updateTab($event, i)" [value]="featureTab?.tab?.name"></ion-input>
        </div>
        <ion-tab-button fill="clear" (click)="removeTabConfirmation(i)" [disabled]="tabIndex !== i">
          @if (tabIndex === i) {
            <ion-icon color="medium" name="trash"></ion-icon>
          }
        </ion-tab-button>
      </ng-template>
      <ng-template matTabContent>
        <div class="content-container">
          <ion-grid class="settings-grid">
            <ion-row>
              <ion-col class="align-right">
                <ion-button color="light" id="default" (click)="openSettings(featureTab)"> SETTINGS </ion-button>
              </ion-col>
            </ion-row>
          </ion-grid>
          <ion-content [scrollEvents]="true" (ionScroll)="logScrolling($event)" style="flex: 1; height: calc(100vh - 390px)">
            <app-feature-repository-builder-template
              [featureTab]="featureTab"
              [templateId]="featureTab.tab.templateId"
              [instance]="instance"
              (sectionChanged)="scrollToPrevPosition()"></app-feature-repository-builder-template>
          </ion-content>
        </div>
      </ng-template>
    </mat-tab>
  }
  <mat-tab disabled>
    <ng-template matTabLabel>
      <div class="add-tab">
        <ion-button color="light" id="default" (click)="isPopoverOpen = !isPopoverOpen">
          ADD
          <ion-icon name="chevron-down-outline"></ion-icon>
        </ion-button>
      </div>
    </ng-template>
  </mat-tab>
</mat-tab-group>
<ion-popover [isOpen]="isPopoverOpen">
  <ng-template>
    <ion-list>
      <ion-item button (click)="addTab(true, true)">FEATURE</ion-item>
      <ion-item button (click)="addTab(true, false)">INSTANCE</ion-item>
    </ion-list>
  </ng-template>
</ion-popover>
