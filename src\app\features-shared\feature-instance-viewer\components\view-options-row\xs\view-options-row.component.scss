.container {
  display: flex;

  .main-btn-container {
    float: right;
    justify-content: flex-end;
    width: 100%;
    display: flex;
    align-items: center;

    .btn-container {
      ion-button {
        background: var(--ion-color-base);
        color: var(--ion-color-contrast);
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  .view-select {
    border: 1px solid #444;
    border-radius: 5px;
    justify-content: flex-end;
    height: 42px;
    margin-right: 1px;
  }
  .grid-container {
    .view-options-row {
      display: flex;
      flex-direction: row;
      padding: 0px;
      color: white;
      align-items: center;
      border-bottom: 1px solid #333333;
      justify-content: space-between;
      width: 100%;

      ion-button {
        --background: #333333 !important;
        border-radius: 5px;
        color: white;
        height: 42px;
      }

      .view-options-col {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        padding: 0px;

        ion-button {
          //--background: #333333 !important;
          border-radius: 5px;
          //color: white;
          font-size: 1em;
          text-transform: none;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 0.5em;
          height: 42px;
          font-weight: 400;
          margin-left: 0px !important;
        }

        ion-button:focus {
          border: 1px solid #f99e00;
        }
        ion-searchbar {
          --ion-background-color: #333333;
          --icon-color: white;
          --color: white;
          --box-shadow: 0;
          --border-radius: 5px;
          width: 70vw;
          max-width: 300px;
          min-width: 100px;
          margin-left: 0px !important;
          padding-left: 0px !important;
          padding-right: 4px !important;

          ::ng-deep .searchbar-input.sc-ion-searchbar-md {
            padding-inline-end: 30px !important;
            padding-inline-start: 40px !important;
          }
        }
      }
    }

    .right {
      justify-content: flex-end;
    }

    .button-row {
      justify-content: flex-end;
      border: none !important;
    }

    mat-icon {
      height: 30px;
      width: 30px;
    }

    .active.active {
      color: #f99e00;
    }

    .text-description {
      margin-left: 5px;
      font-size: 1em;
    }
  }
}

.page-margins {
  margin-left: var(--page-margin-left);
  margin-right: var(--page-margin-right);
}

.page-margins-player {
  padding-left: var(--page-margin-left-player);
  padding-right: var(--page-margin-right-player);
}
