import { PageHeaderComponent } from '@app/standalone/components/page-header/page-header.component';
import { ScormFeatureInstanceComponent } from './components/feature-instance/scorm-feature-instance.component';
import { PlayerViewInformationLgComponent } from './components/player-view-information/lg/player-view-information.component';
import { PlayerViewInformationComponent } from './components/player-view-information/player-view-information.component';
import { PlayerViewInformationXsComponent } from './components/player-view-information/xs/player-view-information.component';
import { PlayerViewSidePanelScormComponent } from './components/player-view-side-panel/player-view-side-panel-scorm.component';
import { PlayerViewComponent } from './components/player-view/player-view.component';
import { PlayerViewScormComponent } from './components/player-view/scorm/player-view.component';
import { TemplateComponent } from './components/template/template.component';

export const featureComponents: any[] = [
  ScormFeatureInstanceComponent,
  TemplateComponent,
  PlayerViewComponent,
  PlayerViewScormComponent,
  PlayerViewInformationComponent,
  PlayerViewInformationLgComponent,
  PlayerViewInformationXsComponent,
  PlayerViewSidePanelScormComponent,
];

export const standaloneComponents: any[] = [PageHeaderComponent];
