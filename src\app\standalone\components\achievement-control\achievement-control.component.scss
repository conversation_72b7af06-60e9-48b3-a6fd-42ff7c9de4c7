$hex-width: calc(20vw/5.4);
$hex-between: calc($hex-width/10);
$hex-height:  calc($hex-width/1.73);
$hex-margin:  calc($hex-width/2);
$hex-border:  calc($hex-margin/1.73);
$row-shift:   calc($hex-width/2 + $hex-between/2);
$row-height:  calc($hex-width - $hex-border/2);
$hex-color: #cccccc;
$hex-color-hover: darken($hex-color, 4%);

.hex-grid {
  margin: 10px;
}

.hex {
  background-color: $hex-color;
  color: transparent;
  width: $hex-width;
  height: $hex-height;
  margin: $hex-border 0;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-repeat: no-repeat;
  background-size: auto calc($hex-width/1.1);
  background-position: center;
}
.hex::after,
.hex::before {
  border-left: $hex-margin solid transparent;
  border-right: $hex-margin solid transparent;
  content: '';
  left: 0;
  position: absolute;
  width: 0;
}
.hex::after {
  border-top: $hex-border solid $hex-color;
  top: 100%;
  width: 0;
}
.hex::before {
  border-bottom: $hex-border solid $hex-color;
  bottom: 100%;
}

.hex:nth-child(8n + 5),
.hex:nth-child(8n + 6),
.hex:nth-child(8n + 7),
.hex:nth-child(8n + 8) {
  margin-left: $row-shift;
}

.title-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  margin-top: 20px;
}

.image {
  width: 60%;
  height: 100%;
  background-size: cover;
  border-radius: 5px;
}

.title {
  font-size: 16px;
  font-family: 'Roboto';
  color: #fff;
}

.sub-title {
  color: #cccccc;
  font-family: 'Roboto';
  font-weight: 400;
  font-size: 12px;
  font-style: italic;
  line-height: 1;
  color: #cccccc;
}

.issued {
  color: #f89e06;
  font-family: 'Roboto';
  font-weight: 400;
  font-size: 12px;
  font-style: italic;
  line-height: 1;
}

.description {
  margin-top: 10px;
  color: #ebebeb;
  font-family: 'Roboto';
  font-weight: 400;
  font-size: 12px;
  line-height: 1.4;
}

.card-container {
  max-width: 300px;
  padding-right: 10px;
  padding-bottom: 10px;
}
