.parent-container {
  margin: 8px;
  .card-content-container {
    background-color: #2d2e32;
    padding: 10px;

    .inner-item {
      --padding-start: 0px !important;
      --background: var(--background);
      --border-color: transparent;
      --color: white;
      font-size: 20px;
      font-family: 'Exo 2';

      ion-label {
        margin-bottom: 0.2em;
      }

      ion-grid {
        width: 100%;
      }

      .disabled {
        pointer-events: none;
        opacity: 2;
      }

      .button-container {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #4b3919;
        border-radius: 5px;
        color: var(--ion-color-primary);
        border: 1px solid var(--ion-color-primary);
        height: 60px;
        line-height: 60px;
        margin: 5px;

        ion-button {
          cursor: pointer;
        }
      }
    }
  }
}

.side-panel-input-padding {
  margin: 0px !important;
  ion-card {
    margin: 0px !important;
  }
}
