import { KeyValue } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IInstance, IParseContentIn, ISystemPropertyValue } from '@app/core/contracts/contract';
import { environment } from '@env/environment';
import { forkJoin, map, of, Subject } from 'rxjs';

@Injectable()
export class ParseService {
  orgCoverPhotoUrl: string;
  orgIconUrl: string;
  orgAssetsUpdated$: Subject<string> = new Subject();
  existingParsedProperties: KeyValue<string, string>[] = [];

  constructor(private http: HttpClient) {}

  public parse(parseContentIn: IParseContentIn) {
    if (!parseContentIn.content || parseContentIn.content?.indexOf('{{') === -1) {
      // Return the cached value as an observable
      return of({ value: parseContentIn.content });
    }

    const key = this.getParseContentKey(parseContentIn);

    // Check if the value already exists in `existingParsedProperties`
    const existingProperty = this.existingParsedProperties.find(y => y.key === key);

    if (existingProperty) {
      if (parseContentIn?.systemProperties?.length > 0) {
        const existingPropertyValueRef = this.replaceCurlyBracketsContent(parseContentIn.content, parseContentIn.systemProperties);

        if (existingPropertyValueRef === (existingProperty.value as any).value) {
          // Return the cached value as an observable
          return of({ value: (existingProperty.value as any).value });
        }
      } else {
        // Return the cached value as an observable
        return of({ value: (existingProperty.value as any).value });
      }
    }

    // Only send needed system properties
    if (parseContentIn?.systemProperties?.length > 0) {
      const keys = this.getAllContentsInsideCurlyBrackets(parseContentIn.content);
      parseContentIn.systemProperties = parseContentIn.systemProperties.filter(x => keys?.some(y => y === x.key));
    }

    return this.http.post<any>('parse/content', parseContentIn).pipe(
      map(x => {
        const key = this.getParseContentKey(parseContentIn);

        if (!this.existingParsedProperties?.some(y => y.key == key)) {
          this.addToExistingProperties({ key: key, value: x } as KeyValue<string, string>);
        } else {
          const index = this.existingParsedProperties.findIndex(x => x.key == key);
          this.existingParsedProperties[index].value = x;
        }

        return x;
      })
    );
  }

  public clearOrganizationAssets() {
    this.orgCoverPhotoUrl = '';
    this.orgIconUrl = '';
  }

  public populateOrganizationAssets(instance: IInstance, orgSystemProperties: ISystemPropertyValue[]) {
    const logoParseContent = {
      instanceId: instance.id,
      content: '{{Organization.LogoAssetId}}',
      systemProperties: orgSystemProperties,
    } as IParseContentIn;

    const coverPhotoParseContent = {
      instanceId: instance.id,
      content: '{{Organization.CoverPhotoAssetId}}',
      systemProperties: orgSystemProperties,
    } as IParseContentIn;

    forkJoin([this.parse(logoParseContent), this.parse(coverPhotoParseContent)]).subscribe(([logoRes, coverPhotoRes]) => {
      if (logoRes?.value !== '' && !logoRes?.value.toString().startsWith('https')) {
        this.orgIconUrl = `${environment.contentUrl}asset/${logoRes.value}/content`;
      } else {
        this.orgIconUrl = logoRes.value;
      }

      if (coverPhotoRes?.value !== '' && !coverPhotoRes?.value.toString().startsWith('https')) {
        this.orgCoverPhotoUrl = `${environment.contentUrl}asset/${coverPhotoRes.value}/content`;
      } else if (coverPhotoRes?.value !== '' && coverPhotoRes?.value.toString().startsWith('https')) {
        this.orgCoverPhotoUrl = coverPhotoRes?.value;
      } else if (instance.feature.coverMediaAssetId) {
        this.orgCoverPhotoUrl = `${environment.contentUrl}asset/${instance.feature.coverMediaAssetId}/content`;
      } else {
        this.orgCoverPhotoUrl = coverPhotoRes?.value;
      }

      this.orgAssetsUpdated$.next('');
    });
  }

  private getAllContentsInsideCurlyBrackets(sentence: string): string[] {
    // Use regex to find all occurrences of the pattern
    const regex = /{{\s*([^{}]*)\s*}}/g; // Match anything inside {{ }}
    const matches: string[] = [];
    let match;

    // Loop through all matches
    while ((match = regex.exec(sentence)) !== null) {
      // Add the content found inside the curly brackets
      matches.push(match[1].trim()); // Trim to remove any leading/trailing whitespace
    }

    return matches;
  }

  private getParseContentKey(parseContentIn: IParseContentIn) {
    const wordcount = this.getWordCount(parseContentIn.content);
    let key = wordcount > 3 ? this.getFirstLettersAndLastWord(parseContentIn.content) : parseContentIn.content;
    key = this.removeSpaces(key);
    return this.removeSpecialCharacters(key) + parseContentIn.instanceId;
  }

  private addToExistingProperties(property: KeyValue<string, string>) {
    this.existingParsedProperties.push(property);
  }

  private getFirstLettersAndLastWord(sentence: string): string {
    if (!sentence || sentence.trim() === '') {
      return sentence;
    }

    const words: string[] = sentence.split(' ');
    let firstLetters: string = '';

    for (const word of words) {
      if (word) {
        firstLetters += word[0];
      }
    }

    const lastWord: string | undefined = words[words.length - 1];

    return firstLetters + (lastWord ? lastWord.substring(1) : '');
  }

  private removeSpecialCharacters(value: string, keepQuestionOrExclamationMark: boolean = false): string {
    if (!value || value.trim() === '') {
      return value;
    }

    // Replace all characters that are not alphanumeric, space, underscore, or hyphen
    let cleanString = value.replace(/[^0-9A-Za-z _-]/g, '');

    if (keepQuestionOrExclamationMark) {
      const lastIndexOfHtmlTagStart = value.lastIndexOf('<');
      const lastCharacter = lastIndexOfHtmlTagStart > 0 ? value[lastIndexOfHtmlTagStart - 1] : value[value.length - 1];

      if (lastCharacter === '?' || lastCharacter === '!') {
        cleanString += lastCharacter === '?' ? 'qm' : lastCharacter;
      }
    }

    return cleanString;
  }

  private removeSpaces(value: string): string {
    return value.replace(/ /g, '');
  }

  private getWordCount(sentence: string): number {
    return sentence && sentence.trim() !== '' ? sentence.split(/\s+/).length : 0;
  }

  private replaceCurlyBracketsContent(sentence: string, systemProperties: ISystemPropertyValue[]): string {
    // Regex to find all occurrences of the pattern {{...}}
    const regex = /{{\s*([^{}]*)\s*}}/g;

    // Replace function
    return sentence.replace(regex, (match, p1) => {
      // Find the item in the list with the matching id
      const item = systemProperties.find(i => i.key === p1.trim());
      return item ? item.value : match; // Replace with item value or keep the original if not found
    });
  }
}
