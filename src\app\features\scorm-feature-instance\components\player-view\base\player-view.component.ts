import { Directive, EventEmitter, Input, Output } from '@angular/core';
import { IFeatureTab, IInstance, IInstanceTemplate, IRouteParams } from '@app/core/contracts/contract';
import { ViewType } from '@app/core/enums/view-type';

@Directive()
export class PlayerViewBaseComponent {
  @Input() template: IInstanceTemplate;
  @Input() instance: IInstance;
  @Input() featureTab: IFeatureTab;
  @Input() routeParams: IRouteParams;
  @Output() buttonChanged = new EventEmitter<number>();
  @Output() optionSelected = new EventEmitter<number>();
  openMobileMenu = false;
  viewTypes = ViewType;

  constructor() {}

  buttonClicked(option: any) {
    this.buttonChanged.emit(option);
  }

  setSelectedRowType(option: any) {
    this.optionSelected.emit(option);
  }
}
