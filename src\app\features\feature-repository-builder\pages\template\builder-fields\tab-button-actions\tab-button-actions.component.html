<ion-grid>
  @for (button of featureTab.featureTabButtons; track button) {
    <app-tab-button-action
      [featureTabButton]="button"
      [buttonLinkTypes$]="buttonLinkTypes$"
      [actions$]="actions$"
      [features$]="features$"
      (deleteClicked)="removeButton(button)"></app-tab-button-action>
  }
  <ion-button (click)="addButton()"> Add Bullet<mat-icon svgIcon="add"></mat-icon> </ion-button>
</ion-grid>
