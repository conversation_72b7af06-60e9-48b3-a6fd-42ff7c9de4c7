import { Injectable } from '@angular/core';
import { map, Subject } from 'rxjs';
import { ISystemProperty, ISystemPropertyValue } from '../contracts/contract';
import { SystemPropertyType } from '../enums/system-property-type.enum';
import { DataService } from './data-service';

@Injectable({
  providedIn: 'root',
})
export class SystemPropertiesService {
  reload$ = new Subject<any>();
  propertyChanged$ = new Subject<ISystemProperty>();
  organizationProperties: ISystemPropertyValue[];
  featureProperties: ISystemPropertyValue[];
  userProperties: ISystemPropertyValue[];
  instanceProperties: ISystemPropertyValue[];
  productProperties: ISystemPropertyValue[];
  questionProperties: ISystemPropertyValue[];
  assetProperties: ISystemPropertyValue[];
  communicationProperties: ISystemPropertyValue[];
  networkProperties: ISystemPropertyValue[];
  campaignProperties: ISystemPropertyValue[];
  credentialEngineBadge: ISystemPropertyValue[];

  constructor(private dataService: DataService) {}

  getAllSystemProperties(): ISystemPropertyValue[] {
    let properties = this.instanceProperties ?? [];

    if (this.featureProperties) {
      properties = properties.concat(this.featureProperties);
    }

    if (this.organizationProperties) {
      properties = properties.concat(this.organizationProperties);
    }

    if (this.productProperties) {
      properties = properties.concat(this.productProperties);
    }

    if (this.questionProperties) {
      properties = properties.concat(this.questionProperties);
    }

    if (this.userProperties) {
      properties = properties.concat(this.userProperties);
    }

    if (this.assetProperties) {
      properties = properties.concat(this.assetProperties);
    }

    if (this.communicationProperties) {
      properties = properties.concat(this.communicationProperties);
    }

    if (this.networkProperties) {
      properties = properties.concat(this.networkProperties);
    }

    if (this.campaignProperties) {
      properties = properties.concat(this.campaignProperties);
    }

    if (this.credentialEngineBadge) {
      properties = properties.concat(this.credentialEngineBadge);
    }
    return properties;
  }

  getSystemPropertyValues(type: SystemPropertyType, id: string) {
    return this.dataService.getSystemPropertyContextValues(id, type).pipe(
      map(values => {
        if (values) {
          switch (type) {
            case SystemPropertyType.Feature:
              this.featureProperties = values;
              break;
            case SystemPropertyType.Organization:
              this.organizationProperties = values;
              break;
            case SystemPropertyType.User:
              this.userProperties = values;
              break;
            case SystemPropertyType.Instance:
              this.instanceProperties = values;
              break;
            case SystemPropertyType.Product:
              this.productProperties = values;
              break;
            case SystemPropertyType.Question:
              this.questionProperties = values;
              break;
            case SystemPropertyType.Media:
              this.assetProperties = values;
              break;
            case SystemPropertyType.Communication:
              this.communicationProperties = values;
              break;
            case SystemPropertyType.Network:
              this.networkProperties = values;
              break;
            case SystemPropertyType.Campaign:
              this.campaignProperties = values;
              break;
            case SystemPropertyType.CredentialEngineBadge:
              this.credentialEngineBadge = values;
              break;
            default:
              break;
          }
        }
      })
    );
  }

  persistSystemPropertyValues(type: SystemPropertyType, id: string) {
    let properties: ISystemPropertyValue[] = [];
    switch (type) {
      case SystemPropertyType.Feature:
        properties = this.featureProperties;
        break;
      case SystemPropertyType.Organization:
        properties = this.organizationProperties;
        break;
      case SystemPropertyType.User:
        properties = this.userProperties;
        break;
      case SystemPropertyType.Instance:
        properties = this.instanceProperties;
        break;
      case SystemPropertyType.Product:
        properties = this.productProperties;
        break;
      case SystemPropertyType.Question:
        properties = this.questionProperties;
        break;
      case SystemPropertyType.Media:
        properties = this.assetProperties;
        break;
      case SystemPropertyType.Communication:
        properties = this.communicationProperties;
        break;
      case SystemPropertyType.Network:
        properties = this.networkProperties;
        break;
      case SystemPropertyType.Campaign:
        properties = this.campaignProperties;
        break;
      case SystemPropertyType.CredentialEngineBadge:
        properties = this.credentialEngineBadge;
        break;
      default:
        break;
    }

    return this.dataService.setSystemPropertyContextValues(id, type, properties);
  }

  getSystemPropertyValue(systemProperty: ISystemProperty) {
    if (!systemProperty) {
      return '';
    }

    const keyArray = systemProperty.property.split('.');
    switch (systemProperty?.type?.typeBw) {
      case SystemPropertyType.Feature:
        return this.featureProperties?.find(x => x.key.includes(keyArray[1]))?.value;
      case SystemPropertyType.Organization:
        return this.organizationProperties?.find(x => x.key.includes(keyArray[1]))?.value;
      case SystemPropertyType.User:
        return this.userProperties?.find(x => x.key.includes(keyArray[1]))?.value;
      case SystemPropertyType.Instance:
        return this.instanceProperties?.find(x => x.key.includes(keyArray[1]))?.value;
      case SystemPropertyType.Product:
        return this.productProperties?.find(x => x.key.includes(keyArray[1]))?.value;
      case SystemPropertyType.Question:
        return this.questionProperties?.find(x => x.key.includes(keyArray[1]))?.value;
      case SystemPropertyType.Media:
        return this.assetProperties?.find(x => x.key.includes(keyArray[1]))?.value;
      case SystemPropertyType.Communication:
        return this.communicationProperties?.find(x => x.key.includes(keyArray[1]))?.value;
      case SystemPropertyType.Network:
        return this.networkProperties?.find(x => x.key.includes(keyArray[1]))?.value;
      case SystemPropertyType.Campaign:
        return this.campaignProperties?.find(x => x.key.includes(keyArray[1]))?.value;
      case SystemPropertyType.CredentialEngineBadge:
        return this.credentialEngineBadge?.find(x => x.key.includes(keyArray[1]))?.value;
      default:
        return null;
    }
  }

  setAssetSystemPropertyValueByKey(key: string, value: string) {
    if (this.assetProperties.find(x => x.key === key)) {
      const index = this.assetProperties.findIndex(x => x.key === key);
      this.assetProperties[index].value = value?.toString();
    }

    if (this.questionProperties.find(x => x.key === key)) {
      const index = this.questionProperties.findIndex(x => x.key === key);
      this.questionProperties[index].value = value?.toString();
    }
  }

  setSystemPropertyValue(systemProperty: ISystemProperty, value: string) {
    const keyArray = systemProperty?.property?.split('.');
    let index = 0;
    let prevValue = '';
    switch (systemProperty?.type?.typeBw) {
      case SystemPropertyType.Feature:
        index = this.featureProperties.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.featureProperties[index].value;
        this.featureProperties[index].value = value?.toString();
        break;
      case SystemPropertyType.Organization:
        index = this.organizationProperties.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.organizationProperties[index].value;
        this.organizationProperties[index].value = value?.toString();
        break;
      case SystemPropertyType.User:
        index = this.userProperties.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.userProperties[index].value;
        this.userProperties[index].value = value?.toString();
        break;
      case SystemPropertyType.Instance:
        index = this.instanceProperties.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.instanceProperties[index].value;
        this.instanceProperties[index].value = value?.toString();
        break;
      case SystemPropertyType.Product:
        index = this.productProperties.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.productProperties[index].value;
        this.productProperties[index].value = value?.toString();
        break;
      case SystemPropertyType.Question:
        index = this.questionProperties.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.questionProperties[index].value;
        this.questionProperties[index].value = value?.toString();
        break;
      case SystemPropertyType.Media:
        index = this.assetProperties.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.assetProperties[index].value;
        this.assetProperties[index].value = value?.toString();
        break;
      case SystemPropertyType.Communication:
        index = this.communicationProperties.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.communicationProperties[index].value;
        this.communicationProperties[index].value = value?.toString();
        break;
      case SystemPropertyType.Network:
        index = this.networkProperties.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.networkProperties[index].value;
        this.networkProperties[index].value = value?.toString();
        break;
      case SystemPropertyType.Campaign:
        index = this.campaignProperties.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.campaignProperties[index].value;
        this.campaignProperties[index].value = value?.toString();
        break;
      case SystemPropertyType.CredentialEngineBadge:
        index = this.credentialEngineBadge.findIndex(x => x.key.includes(keyArray[1]));
        prevValue = this.credentialEngineBadge[index].value;
        this.credentialEngineBadge[index].value = value?.toString();
        break;
      default:
        break;
    }

    if (prevValue !== value) {
      systemProperty.value = value;
      this.propertyChanged$.next(systemProperty);
    }
  }

  clearAllSystemProperties() {
    this.organizationProperties = [];
    this.featureProperties = [];
    this.userProperties = [];
    this.instanceProperties = [];
    this.productProperties = [];
    this.questionProperties = [];
    this.assetProperties = [];
    this.communicationProperties = [];
    this.networkProperties = [];
    this.campaignProperties = [];
    this.credentialEngineBadge = [];
  }
}
