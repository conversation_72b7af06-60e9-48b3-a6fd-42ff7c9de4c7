@for (domain of productOrgDomains; track domain) {
  <ion-card class="content-card">
    @if (!domain.isDeleted) {
      <ion-row>
        <ion-col size="9">
          <div class="inner-container">
            People with the domain &#64;
            <div class="input-container">
              <ion-input placeholder="Domain" (ionChange)="manageChange($event, domain, 'domain')" [value]="domain.domainName" type="text"></ion-input>
            </div>
            will receive the role
            <div class="user-roles-container">
              <ion-select placeholder="Role" (ionChange)="manageChange($event, domain, 'role')" [value]="domain.roleId" interface="popover">
                @for (role of filteredRoles; track role) {
                  <ion-select-option [value]="role.id"> {{ role.name }} </ion-select-option>
                }
              </ion-select>
            </div>
          </div>
        </ion-col>
        <ion-col size="3" class="remove-button-col">
          <div class="remove-button-container">
            <mat-icon (click)="removeProductDomain(domain)">remove_circle_outline</mat-icon>
          </div>
        </ion-col>
      </ion-row>
    }
  </ion-card>
}
<ion-col size="3" class="add-button-col">
  <div class="add-button-container">
    <mat-icon (click)="addNewProductDomain()">add_circle_outline</mat-icon>
    <div class="button-text">Add domain</div>
  </div>
</ion-col>
