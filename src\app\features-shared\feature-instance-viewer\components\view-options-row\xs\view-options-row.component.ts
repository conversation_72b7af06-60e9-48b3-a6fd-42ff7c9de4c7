import { Component, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { DataService } from '@app/core/services/data-service';
import { LayoutService } from '@app/core/services/layout-service';
import { RolesService } from '@app/core/services/roles.service';
import { ScormService } from '@app/core/services/scorm.service';
import { StorageService } from '@app/core/services/storage-service';
import { ViewOptionsRowBaseComponent } from '../base/view-options-row-base.component';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { PendingRequestsInterceptor } from '@app/core/interceptors/pending-requests-interceptor';

@Component({
    selector: 'app-view-options-row-xs',
    templateUrl: './view-options-row.component.html',
    styleUrls: ['./view-options-row.component.scss'],
    standalone: false
})
export class ViewOptionsRowXsComponent extends ViewOptionsRowBaseComponent {
  @Input() mobileMenuClosed = false;
  constructor(
    dataService: DataService,
    storageService: StorageService,
    dialog: MatDialog,
    activatedRoute: ActivatedRoute,
    layoutService: LayoutService,
    scormService: ScormService,
    breadcrumbService: BreadcrumbService,
    pendingRequestsInterceptor: PendingRequestsInterceptor

  ) {
    super(dataService, storageService, dialog, activatedRoute, layoutService, scormService, breadcrumbService, pendingRequestsInterceptor);
  }
}
