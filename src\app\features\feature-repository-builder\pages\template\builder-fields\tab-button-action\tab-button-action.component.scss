:host {
  ion-select {
    border: 1px solid #4e4e4e;
    border-radius: 5px;
    color: white;
    background-color: #232323;
    font-size: 14px;
  }

  ion-input {
    border: 1px solid #4e4e4e;
    border-radius: 5px;
    color: white;
    background-color: #232323;
    font-size: 14px;
  }

  ion-row {
    background-color: #181818 !important;
    border-radius: 5px;
    margin-top: 4px;
    margin-bottom: 4px;
  }

  ion-col {
    padding: 4px;
  }

  ion-item {
    margin: 0px;
    padding: 0px;
  }

  .label-stacked.sc-ion-label-md-h {
    -webkit-transform: translateY(30%) scale(0.75) !important;
    transform: translateY(30%) scale(0.75) !important;
    font-style: italic;
    color: white;
  }

  .center-col {
    align-items: center;
    align-content: center;
    justify-content: center;
    display: flex;
  }
}

::ng-deep .select-interface-option::part(native) {
  background-color: #181818 !important;
  color: white;
}
