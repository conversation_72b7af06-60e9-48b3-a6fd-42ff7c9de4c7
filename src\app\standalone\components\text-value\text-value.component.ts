import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { IHover, IInstanceSectionComponent } from '@app/core/contracts/contract';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { NgClass, AsyncPipe } from '@angular/common';
import { ParseContentPipe } from '../../../shared/pipes/parse-content';

@Component({
    selector: 'app-text-value',
    templateUrl: './text-value.component.html',
    styleUrls: ['./text-value.component.scss'],
    imports: [NgClass, AsyncPipe, ParseContentPipe]
})
export class TextValueComponent implements OnInit, OnChanges {
  @Input() textType: string | undefined;
  @Input() inheritedPropertyValue: string | null;
  @Input() instanceComponent: IInstanceSectionComponent | undefined;
  @Input() instanceId: string;
  @Input() defaultValue: string | undefined;
  @Input() displayShowMore = false;
  @Input() onlyHover = false;
  @Input() hoverDetails: IHover;
  @Input() noMargin = false;
  textValue = '';

  displayMoreComponent = false;
  displayMoreProperty = false;

  constructor(public systemPropertiesService: SystemPropertiesService) {}

  ngOnInit() {
    this.setTextValue();
    if ((this.instanceComponent?.value?.length ?? 0) < 100 && this.displayShowMore) {
      this.displayShowMore = false;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['inheritedPropertyValue'] || changes['instanceComponent'] || changes['defaultValue']) {
      this.setTextValue();
    }
  }

  setTextValue() {
    if (this.inheritedPropertyValue && this.inheritedPropertyValue !== '') {
      this.textValue = this.inheritedPropertyValue;
    } else if (this.instanceComponent?.value && this.instanceComponent.value !== '') {
      this.textValue = this.instanceComponent.value;
    } else if (this.defaultValue && this.defaultValue !== '') {
      this.textValue = this.defaultValue;
    }
  }

  trimString(string: any, length: number) {
    return string.length > length ? string.substring(0, length) + '...' : string;
  }
}
