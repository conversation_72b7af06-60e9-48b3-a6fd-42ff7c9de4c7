import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, AbstractControl, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-network-qlik-view-editor',
    templateUrl: './network-qlik-view-editor.component.html',
    styleUrl: './network-qlik-view-editor.component.scss',
    standalone: false
})
export class CliqviewEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  textFieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(private formBuilder: UntypedFormBuilder) {}

  get isInheritControl(): AbstractControl {
    return this.textFieldForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.textFieldForm = this.formBuilder.group({
      title: [this.component?.templateField?.label, Validators.required],
      description: [this.component?.templateField?.helpDescription],
      rowNumber: [this.component?.builderRowNumber],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.textFieldForm) {
      return;
    }

    this.textFieldForm.controls.title.setValue(this.component.templateField.label);
    this.textFieldForm.controls.description.setValue(this.component.templateField.helpDescription);
    this.textFieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.textFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.textFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.textFieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.textFieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.textFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.textFieldForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.textFieldForm.valid) {
      this.component.templateField.label = this.textFieldForm.controls.title.value;
      this.component.templateField.helpDescription = this.textFieldForm.controls.description.value;
      this.component.builderRowNumber = this.textFieldForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.textFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.textFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.colspan = this.textFieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.textFieldForm.controls.colNumber.value;
    }
  }

  setDefaultValue(quillData: any) {
    this.textFieldForm.controls.defaultText.setValue(quillData);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
