import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { InstanceService } from './instance-service';

@Injectable()
export class InstanceStatusGuard {
  constructor(
    private instanceService: InstanceService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): void {
    const instanceSlug = route.params['instanceslug'];
    const view = route.params['view'];

    if (!instanceSlug || !view) {
      return;
    }

    this.instanceService.getInstanceStatus(instanceSlug).subscribe((published: boolean) => {
      let currentUrl = state.url;
      if (!published && !currentUrl.endsWith('/unpublished')) {
        currentUrl = currentUrl + '/unpublished';
      } else if (published && currentUrl.endsWith('/unpublished')) {
        currentUrl = currentUrl.replace('/unpublished', '');
      }
      this.router.navigateByUrl(currentUrl);
    });
  }
}
