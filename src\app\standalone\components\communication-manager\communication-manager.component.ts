import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { ICommunicationBlock } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { BannerBlockComponent } from './banner-block/banner-block.component';
import { BellAlertBlockComponent } from './bell-alert-block/bell-alert-block.component';
import { EmailBlockComponent } from './email-block/email-block.component';
import { PushBlockComponent } from './push-block/push-block.component';
import { SmsBlockComponent } from './sms-block/sms-block.component';

@Component({
    selector: 'app-communication-manager',
    templateUrl: './communication-manager.component.html',
    styleUrls: ['./communication-manager.component.scss'],
    imports: [IonicModule, BannerBlockComponent, BellAlertBlockComponent, Email<PERSON>lock<PERSON>omponent, PushBlockComponent, SmsBlockComponent]
})
export class CommunicationManagerComponent implements OnInit, OnDestroy {
  @Input() communicationId: string | null | undefined;
  @Output() communicationBlockUpdated: EventEmitter<string | null> = new EventEmitter();
  communicationBlocks: ICommunicationBlock[] = [];
  communicationBlocksIn: ICommunicationBlock[] = [];
  communicationBlockTypes: string[] = [];
  isLoaded = false;

  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.setStaticList();
    this.getCommunicationBlocksById();
  }

  setStaticList() {
    this.communicationBlockTypes = ['Banner', 'Bell', 'Email', 'Push', 'SMS'];
  }

  getCommunicationBlocksById() {
    if (this.communicationId) {
      this.dataService
        .getCommunicationBlocks(this.communicationId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((communicationBlocks: ICommunicationBlock[]) => {
          if (communicationBlocks) {
            this.communicationBlocks = communicationBlocks;
          }
          this.isLoaded = true;
        });
    }
  }

  getCommunicationBlock(type: string) {
    if (this.communicationBlocks.length > 0) {
      const existingType = this.communicationBlocks.find(x => x.blockType === type);
      if (existingType) {
        return existingType;
      }
    }

    return null;
  }

  setCommunicationBlockUpdated(communicationBlockIn: ICommunicationBlock) {
    const index = this.communicationBlocksIn.findIndex(x => x.blockType === communicationBlockIn.blockType);
    if (index === -1) {
      this.communicationBlocksIn.push(communicationBlockIn);
    } else {
      this.communicationBlocksIn[index] = communicationBlockIn;
    }

    this.communicationBlockUpdated.emit(JSON.stringify(this.communicationBlocksIn));
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
