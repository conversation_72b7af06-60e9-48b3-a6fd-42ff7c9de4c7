<ng-container>
  <div class="field-container">
    <div style="margin: 8px">
      <ion-label position="stacked" style="font-size: 14px"> Select Achievements </ion-label>
      <ion-input placeholder="Find a Achievement" [(ngModel)]="query" (ngModelChange)="search()"><ion-icon style="margin: 5px" name="search-outline"></ion-icon></ion-input>
    </div>
    @if (instances) {
      <div class="radio-group-container">
        @for (instance of instances; track instance) {
          <div class="properties-container">
            <div class="property">
              <div class="property-left">
                <ion-checkbox (ionChange)="checkboxChanged($event, instance.id)" [checked]="isSelected(instance.id)"></ion-checkbox>
                <div style="display: flex; flex-direction: column">
                  <div class="heading">{{ instance?.title }}</div>
                  <div class="sub-heading">
                    <!-- <span>{{assessmentQuestion?.question?.category}}</span>
                    <span>&#xb7;</span> -->
                    <span>{{ instance?.feature?.title }}</span>
                    <!-- <span>&#xb7;</span> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        }
      </div>
    }
  </div>
</ng-container>
