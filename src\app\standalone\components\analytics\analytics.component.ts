import { Component, HostListener, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { AnalyticResult, ITemplateField } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { environment } from '@env/environment';
import { Subject, forkJoin, takeUntil } from 'rxjs';
import { NgClass, NgOptimizedImage } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { MatIcon } from '@angular/material/icon';
import { AnalyticsTableComponent } from '../analytics-table/analytics-table.component';

@Component({
    selector: 'app-analytics',
    templateUrl: './analytics.component.html',
    styleUrls: ['./analytics.component.scss'],
    imports: [NgClass, IonicModule, NgOptimizedImage, MatIcon, AnalyticsTableComponent]
})
export class AnalyticsComponent implements OnInit, OnDestroy {
  @Input() id: string;
  @Input() templateField: ITemplateField | undefined;
  @Input() componentId: string;
  currentAmount = 0;
  getAmount = 25;
  moreResults = false;
  isParentPanelClosed = true;
  cardWidth: string;
  iconUrl: string;
  assetUrl: string;
  analytics: AnalyticResult[] = [];
  screenWidth: number;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private dataService: DataService) {}

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.setCardWidth();
  }

  ngOnInit(): void {
    this.setIconUrl();
    this.setBackgroundImageUrl();
    this.getAnalytics();
  }

  openGroup(panelState: boolean) {
    this.isParentPanelClosed = panelState;
  }

  sortAnalytics() {
    this.analytics.map(x =>
      x.userBreakdown?.sort((a, b) => {
        if (a.totalCount !== undefined && b.totalCount !== undefined) {
          return (b?.totalCount || 0) - (a?.totalCount || 0);
        } else {
          return b.totalSessions - a.totalSessions;
        }
      })
    );
  }

  setCardWidth() {
    const itemCount = this.getItemCount();
    this.cardWidth = (100 / itemCount).toString().concat('%');
  }

  getItemCount() {
    this.screenWidth = window.innerWidth;
    const itemCount = this.analytics.length === 1 ? 2 : this.analytics.length;
    if (this.screenWidth >= 1501) {
      return itemCount;
    } else if (itemCount >= 2 && this.screenWidth <= 1500 && this.screenWidth >= 993) {
      return 2;
    }
    return 1;
  }

  setIconUrl() {
    if (this.templateField?.iconAssetId) {
      this.iconUrl = `${environment.contentUrl}asset/${this.templateField?.iconAssetId}/content`;
    }
    return;
  }

  setBackgroundImageUrl() {
    if (this.templateField?.backgroundAssetId) {
      this.assetUrl = `${environment.contentUrl}asset/${this.templateField?.backgroundAssetId}/content`;
    }
    return;
  }

  sortAndFilter(userBreakdown: any, takeAmount: number) {
    userBreakdown.forEach((element: any) => {
      element.users = element.users.slice(0, takeAmount);
    });
    return userBreakdown;
  }

  getAnalytics() {
    switch (this.componentId) {
      case '6ab7eb96-9464-4779-9c05-da7cc097ee93':
        //Organization Overview Section
        forkJoin({
          overview: this.dataService.getAnalyticsForOrganizationOverview(this.id ?? '', 'organization'),
          productLicenses: this.dataService.getAnalyticsForProductLicenses(this.id ?? '', 'organization'),
        })
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(({ overview, productLicenses }) => {
            this.analytics.push({
              description: 'An overview of who at your organization is using the platform.',
              headers: [
                { name: 'Users', amount: overview.totalUsers },
                { name: 'Active Users', amount: overview.activeUsers },
                { name: 'Total Sessions', amount: overview.totalSessions },
              ],
              type: 'Overview',
              userBreakdown: this.sortAndFilter(overview.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Your organization currently holds these products.',
              headers: [{ name: 'Product Licenses', amount: productLicenses.totalCount }],
              type: 'Product Licenses',
              userBreakdown: productLicenses.licensesDetails,
            });
            this.sortAnalytics();
            this.setCardWidth();
          });
        break;

      case '389b6d31-f192-42b6-b8d4-fe6b9b5e5837':
        //User Setup Section
        this.dataService.getAnalyticsForUserSetup(this.id ?? '', 'organization').subscribe(data => {
          this.analytics.push({
            description: 'Identify users who have configured their account by adding a persona.',
            headers: [
              { name: 'Total Users', amount: data.totalUsers },
              { name: 'Have selected a persona', amount: data.totalUsers - data.totalMissingInformation },
            ],
            type: 'User Setup',
            userBreakdown: this.sortAndFilter(data.userBreakdown, 10),
          });
          this.sortAnalytics();
          this.setCardWidth();
        });
        break;

      case 'ea5200cd-07e3-40bf-bbe8-2e43fd30108d':
        //Total Usage Section
        forkJoin({
          impressions: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'impressions'),
          opens: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'opens'),
          engagements: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'engagements'),
        })
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(({ impressions, opens, engagements }) => {
            this.analytics.push({
              description: 'Times that content was displayed to people.',
              headers: [{ name: 'Impressions', amount: impressions.totalCount }],
              type: 'Impressions',
              userBreakdown: this.sortAndFilter(impressions.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Times that people opened and saw content.',
              headers: [{ name: 'Opens', amount: opens.totalCount }],
              type: 'Opens',
              userBreakdown: this.sortAndFilter(opens.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Times that people have interacted with content.',
              headers: [{ name: 'Engagements', amount: engagements.totalCount }],
              type: 'Engagements',
              userBreakdown: this.sortAndFilter(engagements.userBreakdown, 10),
            });
            this.sortAnalytics();
            this.setCardWidth();
          });
        break;

      case '7070024d-3f75-4a5a-bf8c-894a34ab1ada':
        //Inspiration Section
        forkJoin({
          impressions: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'impressions', null, 'F79E5DF2-3FAE-448A-9CA2-D6E6B0AD94F8'),
          opens: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'opens', null, 'F79E5DF2-3FAE-448A-9CA2-D6E6B0AD94F8'),
          engagements: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'engagements', null, 'F79E5DF2-3FAE-448A-9CA2-D6E6B0AD94F8'),
        })
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(({ impressions, opens, engagements }) => {
            this.analytics.push({
              description: 'Times content was displayed to people.',
              headers: [{ name: 'Impressions', amount: impressions.totalCount }],
              type: 'Impressions',
              userBreakdown: this.sortAndFilter(impressions.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Times people opened and saw content.',
              headers: [{ name: 'Opens', amount: opens.totalCount }],
              type: 'Opens',
              userBreakdown: this.sortAndFilter(opens.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Times people have interacted with content.',
              headers: [{ name: 'Engagements', amount: engagements.totalCount }],
              type: 'Engagements',
              userBreakdown: this.sortAndFilter(engagements.userBreakdown, 10),
            });
            this.sortAnalytics();
            this.setCardWidth();
          });
        break;

      case '23306e0f-e350-4991-b27b-911bdf8fe39b':
        //Exploration Section
        forkJoin({
          impressions: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'impressions', null, 'ACB47F1F-CEFE-4353-887C-7176EB1F58E4'),
          opens: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'opens', null, 'ACB47F1F-CEFE-4353-887C-7176EB1F58E4'),
          engagements: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'engagements', null, 'ACB47F1F-CEFE-4353-887C-7176EB1F58E4'),
        })
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(({ impressions, opens, engagements }) => {
            this.analytics.push({
              description: 'Times content was displayed to people.',
              headers: [{ name: 'Impressions', amount: impressions.totalCount }],
              type: 'Impressions',
              userBreakdown: this.sortAndFilter(impressions.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Times people opened and saw content.',
              headers: [{ name: 'Opens', amount: opens.totalCount }],
              type: 'Opens',
              userBreakdown: this.sortAndFilter(opens.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Times people have interacted with content.',
              headers: [{ name: 'Engagements', amount: engagements.totalCount }],
              type: 'Engagements',
              userBreakdown: this.sortAndFilter(engagements.userBreakdown, 10),
            });
            this.sortAnalytics();
            this.setCardWidth();
          });
        break;

      case '2f63c972-58f2-4930-b929-2e051a3a58e9':
        //Preparation Section
        forkJoin({
          impressions: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'impressions', null, 'EB357F52-EA29-4EE1-B318-6CC11F8C7FB2'),
          opens: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'opens', null, 'EB357F52-EA29-4EE1-B318-6CC11F8C7FB2'),
          engagements: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'engagements', null, 'EB357F52-EA29-4EE1-B318-6CC11F8C7FB2'),
        })
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(({ impressions, opens, engagements }) => {
            this.analytics.push({
              description: 'Times content was displayed to people.',
              headers: [{ name: 'Impressions', amount: impressions.totalCount }],
              type: 'Impressions',
              userBreakdown: this.sortAndFilter(impressions.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Times people opened and saw content.',
              headers: [{ name: 'Opens', amount: opens.totalCount }],
              type: 'Opens',
              userBreakdown: this.sortAndFilter(opens.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Times people have interacted with content.',
              headers: [{ name: 'Engagements', amount: engagements.totalCount }],
              type: 'Engagements',
              userBreakdown: this.sortAndFilter(engagements.userBreakdown, 10),
            });
            this.sortAnalytics();
            this.setCardWidth();
          });
        break;

      case '731165c8-76f7-4f31-bd9a-9cd8847fb77a':
        //Connection Section
        forkJoin({
          impressions: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'impressions', null, 'F0C60E0E-1046-4C27-86D8-9F91A24EBA22'),
          opens: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'opens', null, 'F0C60E0E-1046-4C27-86D8-9F91A24EBA22'),
          engagements: this.dataService.getAnalyticsForIEPCbytype(this.id ?? '', 'organization', 'engagements', null, 'F0C60E0E-1046-4C27-86D8-9F91A24EBA22'),
        })
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(({ impressions, opens, engagements }) => {
            this.analytics.push({
              description: 'The number of times an item has been displayed on the screen to someone.',
              headers: [{ name: 'Impressions', amount: impressions.totalCount }],
              type: 'Impressions',
              userBreakdown: this.sortAndFilter(impressions.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'The number of times an item has been opened on by a user.',
              headers: [{ name: 'Opens', amount: opens.totalCount }],
              type: 'Opens',
              userBreakdown: this.sortAndFilter(opens.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'The number of times a user has interacted or used the items.',
              headers: [{ name: 'Engagements', amount: engagements.totalCount }],
              type: 'Engagements',
              userBreakdown: this.sortAndFilter(engagements.userBreakdown, 10),
            });
            this.sortAnalytics();
            this.setCardWidth();
          });
        break;

      case '5f4add80-54d7-413a-815a-f6d12a09f3f0':
        //Most Popular Content Section
        forkJoin({
          administrators: this.dataService.getAnalyticsForMostPopular(this.id ?? '', 'organization', 'administrator', 'instance'),
          instructors: this.dataService.getAnalyticsForMostPopular(this.id ?? '', 'organization', 'instructor', 'instance'),
          learners: this.dataService.getAnalyticsForMostPopular(this.id ?? '', 'organization', 'learner', 'instance'),
        })
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(({ administrators, instructors, learners }) => {
            this.analytics.push({
              name: 'Administrators',
              description: 'The most popular content with administrators.',
              userBreakdown: this.sortAndFilter(administrators.content, 5),
            });

            this.analytics.push({
              name: 'Instructors',
              description: 'The most popular content with instructors.',
              userBreakdown: this.sortAndFilter(instructors.content, 5),
            });

            this.analytics.push({
              name: 'Learners',
              description: 'The most popular content with learners.',
              userBreakdown: this.sortAndFilter(learners.content, 5),
            });
            this.sortAnalytics();
            this.setCardWidth();
          });
        break;

      case 'ecd9ab23-4785-42fa-b530-a998a7c27ea8':
        //Most Popular Features Section
        forkJoin({
          administrators: this.dataService.getAnalyticsForMostPopular(this.id ?? '', 'organization', 'administrator', 'feature'),
          instructors: this.dataService.getAnalyticsForMostPopular(this.id ?? '', 'organization', 'instructor', 'feature'),
          learners: this.dataService.getAnalyticsForMostPopular(this.id ?? '', 'organization', 'learner', 'feature'),
        })
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(({ administrators, instructors, learners }) => {
            this.analytics.push({
              name: 'Administrators',
              description: 'The most popular content with administrators.',
              userBreakdown: this.sortAndFilter(administrators.content, 5),
            });

            this.analytics.push({
              name: 'Instructors',
              description: 'The most popular content with instructors.',
              userBreakdown: this.sortAndFilter(instructors.content, 5),
            });

            this.analytics.push({
              name: 'Learners',
              description: 'The most popular content with learners.',
              userBreakdown: this.sortAndFilter(learners.content, 5),
            });
            this.sortAnalytics();
            this.setCardWidth();
          });
        break;

      case 'eb68519a-4649-498b-b7d7-75b21aaaad3e':
        //Most Popular Industries Section
        forkJoin({
          administrators: this.dataService.getAnalyticsForMostPopular(this.id ?? '', 'organization', 'administrator', 'industry'),
          instructors: this.dataService.getAnalyticsForMostPopular(this.id ?? '', 'organization', 'instructor', 'industry'),
          learners: this.dataService.getAnalyticsForMostPopular(this.id ?? '', 'organization', 'learner', 'industry'),
        })
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(({ administrators, instructors, learners }) => {
            this.analytics.push({
              name: 'Administrators',
              description: 'The most popular content with administrators.',
              userBreakdown: this.sortAndFilter(administrators.content, 5),
            });

            this.analytics.push({
              name: 'Instructors',
              description: 'The most popular content with instructors.',
              userBreakdown: this.sortAndFilter(instructors.content, 5),
            });

            this.analytics.push({
              name: 'Learners',
              description: 'The most popular content with learners.',
              userBreakdown: this.sortAndFilter(learners.content, 5),
            });
            this.sortAnalytics();
            this.setCardWidth();
          });
        break;

      case '75a3ea85-d787-4f6e-a0ec-cedbe85fed97':
        //Training & Help Articles Section
        forkJoin({
          opens: this.dataService.getAnalyticsForIEPCbytype(
            this.id ?? '',
            'organization',
            'opens',
            '2F935D85-A01B-403C-A77E-C5C7CF3AB008,F13DFC68-4E53-48F3-B0F1-F32FA5813591,E21EB3DF-2BBF-41F6-8F8C-EDDF6D3CB480'
          ),
          engagements: this.dataService.getAnalyticsForIEPCbytype(
            this.id ?? '',
            'organization',
            'engagements',
            '2F935D85-A01B-403C-A77E-C5C7CF3AB008,F13DFC68-4E53-48F3-B0F1-F32FA5813591,E21EB3DF-2BBF-41F6-8F8C-EDDF6D3CB480'
          ),
          completions: this.dataService.getAnalyticsForIEPCbytype(
            this.id ?? '',
            'organization',
            'completions',
            '2F935D85-A01B-403C-A77E-C5C7CF3AB008,F13DFC68-4E53-48F3-B0F1-F32FA5813591,E21EB3DF-2BBF-41F6-8F8C-EDDF6D3CB480'
          ),
        })
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(({ opens, engagements, completions }) => {
            this.analytics.push({
              description: 'Times that people opened and saw training content.',
              headers: [{ name: 'Opens', amount: opens.totalCount }],
              type: 'Opens',
              userBreakdown: this.sortAndFilter(opens.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Times people have interacted with training content.',
              headers: [{ name: 'Engagements', amount: engagements.totalCount }],
              type: 'Engagements',
              userBreakdown: this.sortAndFilter(engagements.userBreakdown, 10),
            });

            this.analytics.push({
              description: 'Times that people have completed training content.',
              headers: [{ name: 'Completions', amount: completions.totalCount }],
              type: 'Completions',
              userBreakdown: this.sortAndFilter(completions.userBreakdown, 10),
            });
            this.sortAnalytics();
            this.setCardWidth();
          });
        break;
    }
  }
  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
