@if (feature$ | async; as feature) {
  <div class="repository">
    <div class="header-container" style="--background-image: url('assets/images/admin-header.jpg')">
      <h6>You are {{ tabDescription }} the template for</h6>
      <h1>{{ feature.title }}</h1>
    </div>
    <div class="content">
      <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" (selectedTabChange)="tabSelected($event)">
        <mat-tab id="material-tab" label="REPOSITORY">
          <ng-template matTabContent>
            <app-feature-repository-dashboard [feature]="feature"></app-feature-repository-dashboard>
          </ng-template>
        </mat-tab>
        @if (isEfManager === true) {
          <mat-tab class="material-tab" label="TEMPLATE">
            <ng-template matTabContent>
              <app-feature-repository-template [feature]="feature" (featureUpdated)="updateFeature($event)"></app-feature-repository-template>
            </ng-template>
          </mat-tab>
        }
        @if (isEfManager === true) {
          <mat-tab class="material-tab" label="COMMUNICATIONS">
            <ng-template matTabContent>
              <app-feature-repository-communications [feature]="feature"></app-feature-repository-communications>
            </ng-template>
          </mat-tab>
        }
        @if (isEfManager === true) {
          <mat-tab class="material-tab" label="ANALYTICS">
            <ng-template matTabContent>
              <app-feature-repository-analytics [feature]="feature"></app-feature-repository-analytics>
            </ng-template>
          </mat-tab>
        }
      </mat-tab-group>
    </div>
  </div>
}
