:host {
  .parent-container {
    height: 100%;

    .top-actions-row {
      margin-top: 5px;
      .back-button-col {
        display: flex;
        padding-left: 14px;
        ion-button {
          display: flex;
          justify-content: center;
          align-items: center;

          ion-icon {
            margin-right: 2px;
          }
        }

        ion-button::part(native) {
          line-height: 0;
          padding-inline-start: 2px;
        }
      }

      .save-button-col {
        display: flex;
        justify-content: flex-end;
        padding-right: 14px;

        ion-button::part(native) {
          line-height: 0;
        }
      }
    }
  }
}
