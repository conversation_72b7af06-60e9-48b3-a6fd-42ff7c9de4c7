.organization-parent-container {
  .component-header {
    font-style: italic;
    font-size: 20px;
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 10px;
    color: white;
  }

  mat-expansion-panel {
    background-color: rgba(41, 41, 41);
    box-shadow: none;

    .expansion-panel-header {
      height: 100%;
      border-radius: 8px;
      padding: 10px;

      .inner-panel {
        .heading {
          font-weight: bold;
          font-size: 20px;
          color: white;
        }

        .sub-heading {
          margin-bottom: 5px;
          font-style: italic;
          color: rgba(170, 170, 170);
        }
      }

      .inner-panel-role {
        display: flex;
        margin-right: 25px;
        .role-container {
          margin-right: 20px;
          .heading {
            font-weight: bold;
            font-size: 20px;
            color: white;
          }

          .sub-heading {
            margin-bottom: 5px;
            font-style: italic;
            color: rgba(170, 170, 170);
          }
        }

        .button-container {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      ::ng-deep .mat-content {
        justify-content: space-between;
      }
    }

    .panel-body-container {
      .org-permissions-data-container {
        margin-top: 15px;
        h1 {
          font-size: 20px;
          color: white;
          margin: 0px;
        }

        h2 {
          margin: 0px;
          font-style: italic;
          font-size: 16px;
          color: white;
        }

        .privacy-types-list {
          padding-left: 15px;
          list-style-type: disc !important;
          li {
            font-size: 16px;
            color: rgba(170, 170, 170);
            margin-left: 10px;
          }
        }
      }

      .user-data-parent-container {
        .user-permissions-data-container {
          margin-top: 15px;
          h1 {
            font-size: 20px;
            color: white;
            margin: 0px;
          }

          h2 {
            margin: 0px;
            font-style: italic;
            font-size: 16px;
            color: white;
          }

          .privacy-types-list {
            padding-left: 15px;
            list-style-type: disc !important;
            li {
              font-size: 16px;
              color: rgba(170, 170, 170);
              margin-left: 10px;
            }
          }
        }

        .party-data-container {
          margin-top: 15px;
          h1 {
            font-size: 20px;
            color: white;
            margin: 0px;
          }

          p {
            margin: 0px;
            color: rgba(170, 170, 170);
          }

          h2 {
            margin: 0px;
            margin-top: 10px;
            font-style: italic;
            font-size: 16px;
            color: white;
          }

          .account-info-list {
            padding-left: 15px;
            list-style-type: disc !important;
            li {
              font-style: italic;
              font-size: 16px;
              color: rgba(170, 170, 170);
              margin-left: 10px;

              .li-text {
                font-style: normal;
                font-size: 16px;
                color: white;
              }
            }
          }
        }
      }
    }

    .expansion-panel-header:hover {
      background-color: rgba(41, 41, 41) !important;
    }

    //TN: This overrides the >> li style in the global.
    li::before {
      content: normal;
    }
    li::marker {
      content: normal;
      font-size: 18px;
      color: #ee9907;
    }

    ::ng-deep .mat-expansion-indicator::after,
    .mat-expansion-panel-header-description {
      border-color: white;
    }

    ::ng-deep .mat-mdc-tab {
      min-width: 25px !important;
      height: 25px;
      padding: 0px;
      background-color: transparent;
      margin-right: 15px;
      font-size: 16px;
      color: white;
    }

    ::ng-deep .mat-mdc-tab-header {
      margin-bottom: 15px !important;
      color: white;
    }

    ::ng-deep .mat-ink-bar {
      background-color: rgba(250, 167, 0) !important;
    }

    ::ng-deep .mat-expansion-panel-body {
      padding-left: 15px !important;
      border-top: 1px solid gray;
    }
  }

  .load-more {
    font-size: 16px;
    color: white;
    margin-top: 10px;
    text-align: center;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }
}
