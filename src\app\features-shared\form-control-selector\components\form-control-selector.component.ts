import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { IComponent, IGuestContext, IInstance, IInstanceSectionComponent, IQuestion, IRouteParams, ISystemProperty } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { ComponentType } from '@app/core/enums/component-type.enum';
import { DataService } from '@app/core/services/data-service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { Observable, Subject, map, takeUntil } from 'rxjs';

@Component({
    selector: 'app-form-control-selector',
    templateUrl: './form-control-selector.component.html',
    styleUrls: ['./form-control-selector.component.scss'],
    standalone: false
})
export class FormControlSelectorComponent implements OnInit, OnD<PERSON>roy {
  @Input() component!: IComponent;
  @Input() instanceSectionComponent: IInstanceSectionComponent | undefined;
  @Input() instance!: IInstance;
  @Input() formGroup!: UntypedFormGroup;
  @Input() formGroupName!: any;
  @Input() id: string;
  @Input() viewType = 2;
  @Input() featureId: string;
  @Input() routeParams: IRouteParams;
  @Input() isSidePanelBuilder = false;
  @Input() showSelected = false;
  @Input() showPlaceHolder = false;
  @Input() rowBuilderView = false;
  @Output() questionUpdated = new EventEmitter<IQuestion>();
  componentType = ComponentType;
  controlBackground = 'none';
  selectOptions$: Observable<KeyValue[]>;
  selectedUserOptions$: Observable<KeyValue[]>;
  mediaId: string | null;
  campaignId: string | null;
  instanceId: string;
  selectOptions: KeyValue[];
  selectedUserOptions: KeyValue[];
  componentDestroyed$: Subject<boolean> = new Subject();
  userId: string | undefined;
  guestUserContext: IGuestContext | null;

  constructor(
    private dataService: DataService,
    private systemPropertyService: SystemPropertiesService
  ) {
    this.systemPropertyService.propertyChanged$.pipe(takeUntil(this.componentDestroyed$)).subscribe((systemProperty: ISystemProperty) => {
      if (this.component?.templateField?.isParentSystemPropertyLinkField && this.component.templateField.parentIdSystemPropertyLink?.property === systemProperty.property) {
        // this.initData();
      }
    });
  }

  ngOnInit(): void {
    if (this.component?.templateField?.isParentSystemPropertyLinkField || this.component?.templateField?.dropDownLinkType) {
      this.initData();
    }

    if (this.instance) {
      this.instanceId = this.instance.id;
    } else if (this.id) {
      this.instanceId = this.id;
    }
    if (this.instance?.feature?.featureType?.name === 'Media Manager') {
      this.mediaId = this.id;
    }
    if (this.instance?.feature?.featureType?.name === 'Campaign Manager') {
      this.campaignId = this.id;
    }
  }

  getFormControlValue() {
    return this.formGroup.get([this.formGroupName, this.component.id])?.value;
  }

  getFormGroupStatus() {
    return this.formGroup.status === 'DISABLED';
  }

  setFormControlValue(value: string | null) {
    this.formGroup.get([this.formGroupName, this.component.id])?.markAsDirty();
    return this.formGroup.get([this.formGroupName, this.component.id])?.setValue(value);
  }

  initData() {
    this.setSelectionOptions();
    this.setUserContextData();
  }

  setUserContextData() {
    const userTagsDropDown = this.component?.templateField?.dropDownLinkType?.title === 'User Tags';
    if (userTagsDropDown || this.component?.componentType?.name === 'Persona Selector') {
      if (this.instance?.feature?.featureSlug !== 'my-journey') {
        this.userId = this.id;
      }
      if (this.instance?.feature?.featureSlug === 'my-journey') {
        this.guestUserContext = JSON.parse(localStorage.getItem('guest_context') as string);
        this.userId = this.guestUserContext?.impersonate ? this.guestUserContext?.id : undefined;
      }
      if (userTagsDropDown) {
        this.selectedUserOptions$ = this.dataService.getUserTags(this.userId).pipe(
          map(tags => {
            return tags
              .filter(x => x.type !== 'Persona')
              .map((userTag: { tagId: any }) => {
                return { id: userTag.tagId } as KeyValue;
              });
          })
        );
      }
    }

    if (!this.selectedUserOptions$) {
      return;
    }

    this.selectedUserOptions$.pipe(takeUntil(this.componentDestroyed$)).subscribe(data => {
      if (data) {
        this.selectedUserOptions = data;
      }
    });
  }

  setSelectionOptions() {
    if (
      this.component?.templateField?.dropDownLinkType?.title === 'Tags' ||
      this.component?.templateField?.dropDownLinkType?.title === 'User Tags' ||
      this.component?.templateField?.dropDownLinkType?.title === 'Campaign User Tags' ||
      this.component?.templateField?.isParentSystemPropertyLinkField
    ) {
      const parentId: string = this.component?.templateField?.isParentSystemPropertyLinkField
        ? (this.systemPropertyService.getSystemPropertyValue(this.component?.templateField?.parentIdSystemPropertyLink as ISystemProperty) as string)
        : this.component.templateField.tagTreeId;

      this.selectOptions$ = this.dataService.getTagChildren(parentId ?? null, 0, false, this.component?.templateField?.dropDownLinkType?.title).pipe(
        map(tags => {
          return tags.map(t => {
            return { id: t.id, value: t.name, parentId: t.parentId, level: t.treeLevel, hasChildren: t.inverseParent ?? false } as KeyValue;
          });
        })
      );
    } else if (this.component?.templateField?.dropDownLinkType?.title === 'Users') {
      if (!this.instance?.organizationId) {
        return;
      }
      this.selectOptions$ = this.dataService.getOrganizationTableUsersByOrgId(this.instance?.organizationId ?? '').pipe(
        map(tags => {
          return tags.map(t => {
            return { id: t.id, value: t.name } as KeyValue;
          });
        })
      );
    } else if (this.component?.templateField?.dropDownLinkType?.title === 'Educators') {
      if (!this.instance?.organizationId) {
        return;
      }
      this.selectOptions$ = this.dataService.getOrganizationEducators(this.instance?.organizationId ?? '').pipe(
        map(tags => {
          return tags?.map(t => {
            return { id: t.id, value: t.name } as KeyValue;
          });
        })
      );
    } else if (this.component?.templateField?.dropDownLinkType?.title === 'Instances') {
      if (!this.instance?.organizationId) {
        return;
      }
      this.selectOptions$ = this.dataService.getInstances(null, this.instance?.organizationId).pipe(
        map(tags => {
          return tags.map(t => {
            return { id: t.id, value: t.title } as KeyValue;
          });
        })
      );
    } else if (this.component?.templateField?.dropDownLinkType?.title === 'Products') {
      if (this.component?.templateField?.systemProperty?.name === 'Campaign Product') {
        this.selectOptions$ = this.dataService.getAllProducts().pipe(
          map(tags => {
            return tags.map(t => {
              return { id: t.id, value: t.name } as KeyValue;
            });
          })
        );
      } else {
        this.selectOptions$ = this.dataService.getMyProducts().pipe(
          map(tags => {
            return tags.map(t => {
              return { id: t.id, value: t.name } as KeyValue;
            });
          })
        );
      }
    } else if (this.component?.templateField?.dropDownLinkType?.title === 'User Roles') {
      if (this.instance?.organizationId) {
        this.selectOptions$ = this.dataService.getUserOrganizationRoles(this.instance.organizationId).pipe(
          map(roles => {
            return roles.map(r => {
              return { id: r.id, value: r.name };
            });
          })
        );
      }
    } else if (this.component?.templateField?.dropDownLinkType?.title === 'Feature Types') {
      this.selectOptions$ = this.dataService.getFeatureTypes().pipe(
        map(tags => {
          return tags.map(t => {
            return { id: t.id, value: t.name } as KeyValue;
          });
        })
      );
    } else if (this.component?.templateField?.dropDownLinkType?.title === 'Notification Trigger') {
      this.selectOptions$ = this.dataService.getWorkFlows().pipe(
        map(tags => {
          return tags.map(t => {
            return { id: t.id, value: t.name } as KeyValue;
          });
        })
      );
    } else if (this.component?.templateField?.dropDownLinkType?.title === 'Organization Status') {
      this.selectOptions$ = this.dataService.getOrganizationStatusTypes().pipe(
        map(tags => {
          return tags.map(t => {
            return { id: t.id, value: t.name } as KeyValue;
          });
        })
      );
    }
    if (!this.selectOptions$) {
      return;
    }

    this.selectOptions$.pipe(takeUntil(this.componentDestroyed$)).subscribe(data => {
      if (data) {
        this.selectOptions = data;
      }
    });
  }

  updateQuestionValue(question: IQuestion) {
    this.questionUpdated.next(question);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
