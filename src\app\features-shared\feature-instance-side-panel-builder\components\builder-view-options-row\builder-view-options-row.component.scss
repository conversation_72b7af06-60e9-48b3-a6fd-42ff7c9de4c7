.parent-container {
  .view-options-row {
    height: 45px;
    position: relative;

    .preview-col {
        display: flex;
        align-items: center;
      .preview-header {
          color: #ffffff;
          font-family: 'Roboto';
          font-size: 25px;
          font-weight: bold;
        }
      }
    @media (min-width: 960px) {
    .preview-col {
        width: 100% !important;
        justify-content: center;
      }
    }

    @media (max-width: 960px) {
    .preview-col {
        justify-content: flex-start;
      }
    }

    .publish-col {
      position: absolute;
      right: 0px;
      .publish-button {
        fill: #f99e00;
        cursor: pointer;
      }
    }
  }
}
