import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IEarningCriteriaContentIn, IEarningCriteriaContentSearch, IEarningCriteriaIn } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { SelectOptionControlComponent } from '../../select-option-control/select-option-control.component';

@Component({
    selector: 'app-create-atleast',
    templateUrl: './create-atleast.component.html',
    styleUrls: ['./create-atleast.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, SelectOptionControlComponent]
})
export class CreateAtleastComponent implements OnInit, OnDestroy {
  @Input() type: string;
  @Input() earningCriteria: IEarningCriteriaIn;
  @Output() criteriaUpdated: EventEmitter<IEarningCriteriaContentIn[]> = new EventEmitter();

  formValueChanges$: Subscription;
  createAtleastForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    this.createAtleastForm = this.formBuilder.group({
      minValue: [this.earningCriteria?.minValue],
      refId: [this.earningCriteria.earningCriteriaContent?.[0]?.refId],
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.createAtleastForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setObjectValues();
    });
  }

  setObjectValues() {
    if (this.createAtleastForm.valid) {
      if (this.createAtleastForm.controls['minValue'].value) {
        this.earningCriteria.minValue = this.createAtleastForm.controls.minValue.value;
        this.criteriaUpdated.emit(this.earningCriteria.earningCriteriaContent);
      }
    }
  }

  getFormControlValue() {
    return this.createAtleastForm.get('refId')?.value;
  }

  setSelectedContent(data: IEarningCriteriaContentSearch) {
    const criteriaContentIn = {
      id: this.earningCriteria.earningCriteriaContent ? this.earningCriteria.earningCriteriaContent[0]?.id : null,
      earningCriteriaId: this.earningCriteria.id,
      refId: data.id,
      type: data.type,
      name: data.name,
    } as IEarningCriteriaContentIn;

    const index = this.earningCriteria.earningCriteriaContent?.findIndex(x => x.id === criteriaContentIn.id);

    if (!index || index === -1) {
      if (this.earningCriteria.earningCriteriaContent) {
        this.earningCriteria.earningCriteriaContent.push(criteriaContentIn);
      } else {
        this.earningCriteria.earningCriteriaContent = [criteriaContentIn];
      }
    } else {
      this.earningCriteria.earningCriteriaContent[0] = criteriaContentIn;
    }

    this.criteriaUpdated.emit(this.earningCriteria.earningCriteriaContent);
  }

  checkInputLength(event: any) {
    const numberVal = Number(event.detail.value);
    if (numberVal < 0) {
      this.createAtleastForm.controls.minValue.setValue(0);
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
