import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ContentChange, QuillEditorComponent } from 'ngx-quill';
import Quill from 'quill';
import { Subject, takeUntil } from 'rxjs';
import { PersonalizeQuilComponent } from '@app/standalone/components/content-quill-editor/personalize-quil-dialog/personalize-quil-dialog.component';

@Component({
    selector: 'app-content-quill-editor',
    templateUrl: './content-quill-editor.component.html',
    styleUrls: ['./content-quill-editor.component.scss'],
    imports: [QuillEditorComponent, FormsModule]
})
export class ContentQuillEditorComponent implements OnDestroy {
  @Input() public placeHolder: string;
  @Input() disabled = false;
  @Input() hideQuillPersonalize = false;
  @Input() value: string | null;
  @Output() public dataChanged: EventEmitter<string> = new EventEmitter();
  quill: Quill;
  componentDestroyed$: Subject<boolean> = new Subject();
  personalizeDialog: MatDialogRef<PersonalizeQuilComponent>;
  htmlData: string;
  charCount = 0;

  constructor(private dialog: MatDialog) {}

  getEditorInstance(quill: Quill) {
    quill.focus();
    this.quill = quill;
  }

  onKeyUp(): void {
    this.charCount = this.quill.getText().trim().length;
  }

  onContentChanged(event: ContentChange) {
    this.htmlData = event?.html ?? '';
    this.dataChanged.emit(this.htmlData);
  }

  personalizeContent() {
    this.personalizeDialog = this.dialog.open(PersonalizeQuilComponent, {
      width: '390px',
      height: '400px',
      disableClose: true,
    });

    this.personalizeDialog
      .afterClosed()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((response: boolean) => {});
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}

//Intercept hyperlink and adds https:// if it does not have a protocol.
const Link = Quill.import('formats/link') as any;

class CustomLinkSanitizer extends Link {
  static PROTOCOL_WHITELIST = ['http', 'https'];

  static sanitize(url: string) {
    // Use the original sanitize method
    const sanitizedUrl = super.sanitize(url);

    // Return early if the URL is blank or empty
    if (!sanitizedUrl || sanitizedUrl === 'about:blank') return sanitizedUrl;

    // Check if the URL starts with any of the whitelisted protocols
    const hasWhitelistedProtocol = this.PROTOCOL_WHITELIST?.some((protocol: string) => sanitizedUrl.startsWith(protocol));

    // If the URL has a whitelisted protocol, return it; otherwise, prepend "//"
    return hasWhitelistedProtocol ? sanitizedUrl : `//${sanitizedUrl}`;
  }
}

// Register the custom Link format
Quill.register('formats/link', CustomLinkSanitizer, true);
