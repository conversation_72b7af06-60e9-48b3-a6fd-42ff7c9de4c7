import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ICommunicationBlock, ITag } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { ContentQuillEditorComponent } from '../../content-quill-editor/content-quill-editor.component';
import { SelectOptionControlComponent } from '../../select-option-control/select-option-control.component';

@Component({
    selector: 'app-bell-alert-block',
    templateUrl: './bell-alert-block.component.html',
    styleUrls: ['./bell-alert-block.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, TextInputControlComponent, ContentQuillEditorComponent, SelectOptionControlComponent]
})
export class BellAlertBlockComponent implements OnInit, OnDestroy {
  @Input() communicationId: string;
  @Input() bellBlock: ICommunicationBlock | null;
  @Output() communicationBlockUpdated: EventEmitter<ICommunicationBlock> = new EventEmitter();
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  bellAlertBlockForm: UntypedFormGroup;
  formValueChanges$: Subscription;
  backgroundColor = '#181818';
  hideQuillPersonalize = true;
  behaviours: KeyValue[];
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit() {
    this.getBehaviours();
  }

  getBehaviours() {
    this.dataService
      .getTagChildrenByParentName('Behaviours')
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        if (data) {
          this.behaviours = (data as ITag[]).map(x => ({ id: x.id, value: x.name }) as KeyValue);
        }

        this.createForm();
      });
  }

  createForm() {
    this.bellAlertBlockForm = this.formBuilder.group({
      title: [this.bellBlock?.title],
      message: [this.bellBlock?.message],
      behaviour: [this.bellBlock?.actionTypeId],
    });

    this.subscribeToFormChanges();
  }

  setMessageValue(quillData: any) {
    this.bellAlertBlockForm.controls.message.setValue(quillData);
  }

  setObjectValues() {
    if (this.bellAlertBlockForm.valid) {
      let communicationBlock = {
        id: this.bellBlock?.id,
        communicationId: this.communicationId,
        title: this.bellAlertBlockForm.controls.title.value,
        message: this.bellAlertBlockForm.controls.message.value,
        actionTypeId: this.bellAlertBlockForm.controls.behaviour.value,
        blockType: 'Bell',
      } as ICommunicationBlock;

      //Merge
      if (this.bellBlock) {
        communicationBlock = { ...this.bellBlock, ...communicationBlock };
      }

      this.communicationBlockUpdated.emit(communicationBlock);
    }
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.bellAlertBlockForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.bellAlertBlockForm.valid);
      this.setObjectValues();
    });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
