form{
    height: fit-content;
}
ion-icon {
    font-size: 20px;
    height: 25px;
}

.heading {
    font-weight: bold;
    font-size: 20px;
    color: white;
    text-align: center;
}

ion-row {
    margin: 15px 20px 0px 20px;
}

.view-options-row {
    color: white;
    display: flex;
    margin-bottom: 10px;
    justify-content: space-between;

    -ms-flex-align: center;
    -webkit-align-items: center;
    -webkit-box-align: center;
    align-items: center;
}

.view-checkbox {
    display: flex;
    align-items: center;
}

ion-label {
    margin-top: 30px;
}

.sub-heading {
    color: white;
    size: 12px;
}

hr {
    margin-bottom: 0;
    width: 100%;
    border-top: 3px solid #1e1e1e;
    color: #1e1e1e;
}

ion-input,
.dropdown {
    border: 1px solid #000000;
    border-radius: 5px;
    color: white !important;
    font-size: 16px;
    padding-left: 10px !important;
    padding-right: 10px !important;
    caret-color: #7f550c;
    background-color: #1e1e1e;
    margin-top: 5px;
}

.dropdown {
    width: 100%;
    height: 45px;
    option{
        padding-left: 10px !important;
    }
}

.reqAsterisk {
    color: #7f550c;
    font-size: 14px;
}