<div class="product-search-container">
  <form [formGroup]="searchForm">
    <ion-grid>
      <ion-row>
        <ion-col size="11" style="text-align: center">
          <h5>Add a product feature:</h5>
        </ion-col>
        <ion-col size="1">
          <ion-button fill="clear" (click)="cancel(false)">
            <ion-icon name="close-circle-outline"></ion-icon>
          </ion-button>
        </ion-col>
      </ion-row>
      <ion-row class="search-bar-row">
        <ion-col>
          <ion-searchbar color="dark" formControlName="featureSearch" (ionChange)="searchFeatures()" type="search" placeholder="Search features" showCancelButton="focus" debounce="600">
          </ion-searchbar>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
  <ion-content>
    <ion-grid>
      @for (feature of features; track feature) {
        <ion-row>
          <ion-col size="12" (click)="addProductFeature(feature.id)" class="ion-no-padding">
            <div class="heading-container">
              <span class="heading">{{ feature.title }}</span>
              @if (feature.description) {
                <div class="sub-heading">
                  <span>{{ feature.description }}</span>
                </div>
              }
            </div>
          </ion-col>
        </ion-row>
      }
    </ion-grid>
  </ion-content>
</div>
