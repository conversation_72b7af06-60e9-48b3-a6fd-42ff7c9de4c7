.mat-expansion-panel {
  background: #181818;
  color: #8b8b8b;
}

.mat-expansion-panel-header-title {
  color: #8b8b8b !important;
}

.mat-expansion-panel-header-description {
  color: #8b8b8b !important;
}

::ng-deep .mat-expansion-panel-header > .mat-expansion-indicator:after {
  color: white;
}

.button-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  justify-content: flex-end;
}

::ng-deep .mat-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  justify-content: space-between;
}

.panel-right {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;

  ion-icon {
    color: white;
    font-size: 20px;
    padding-left: 10px;
  }
}

.properties-container {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.property {
  height: 40px;
  width: 100%;
  border-radius: 5px;
  background-color: #1e1e1e;
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  justify-content: space-between;
  margin-bottom: 5px;
  border: 1px solid #4e4e4e;
  padding: 0px 10px 0px 10px;
  cursor: pointer;
}

.property-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
}

.radio {
  margin: 4px;
}

.propertyName {
  color: white;
}

.org-links {
  font-size: small;
}

.org-title {
  color: #4e4e4e;
  margin-left: 20px;
}

.type {
  margin-right: 5px;
}

.underline-text {
  text-decoration: underline;
}

.row {
  display: flex;
  flex-direction: row;
}

ion-radio::part(container){
  min-width: 20px !important;
}
