import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-display-list-field-editor',
    templateUrl: './display-list-field-editor.component.html',
    styleUrls: ['./display-list-field-editor.component.scss'],
    standalone: false
})
export class DisplayListFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  displayListForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.displayListForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }

      this.setFormValues();
    }
  }
  setTypeBwValues(typeBw: number | undefined) {
    if (typeBw !== undefined) {
      return this.builderService.acceptedImageFormats.filter(x => Number(x.id) & typeBw).map(x => x.id);
    }
    return null;
  }

  createForm() {
    this.displayListForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      description: [this.component?.templateField?.helpDescription],
      caption: [this.component?.templateField?.helpTitle],
      defaultImageUrl: [this.component?.templateField?.defaultImageUrl],
      fileTypeBw: [this.setTypeBwValues(this.component?.templateField?.fileTypeBw), Validators.required],
      maxFileSize: [this.component?.templateField?.maxFileSize],
      rowNumber: [this.component?.builderRowNumber ?? 0],
      hoverSortOrder: [this.component?.hoverSortOrder ?? 0],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      label1: [this.component?.templateField?.label1, Validators.required],
      placeHolder1: [this.component?.templateField?.placeHolder1],
      default1: [this.component?.templateField?.default1],
      caption1: [this.component?.templateField?.caption1],
      description1: [this.component?.templateField?.description1],
      label2: [this.component?.templateField?.label2, Validators.required],
      placeHolder2: [this.component?.templateField?.placeHolder2],
      default2: [this.component?.templateField?.default2],
      caption2: [this.component?.templateField?.caption2],
      description2: [this.component?.templateField?.description2],
      label3: [this.component?.templateField?.label3, Validators.required],
      placeHolder3: [this.component?.templateField?.placeHolder3],
      caption3: [this.component?.templateField?.caption3],
      description3: [this.component?.templateField?.description3],
      centerContent: [this.component?.templateField?.showGradient],
      moveOverBanner: [this.component?.templateField?.moveToBack],
      isSystemProperty: [this.component?.templateField?.isInherit ?? true],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.displayListForm) {
      return;
    }

    this.displayListForm.controls.label.setValue(this.component.templateField.label);
    this.displayListForm.controls.description.setValue(this.component.templateField.helpDescription);
    this.displayListForm.controls.caption.setValue(this.component.templateField.helpTitle);
    this.displayListForm.controls.defaultImageUrl.setValue(this.component.templateField.defaultImageUrl);
    this.displayListForm.controls.fileTypeBw.setValue(this.component.templateField.fileTypeBw);
    this.displayListForm.controls.maxFileSize.setValue(this.component.templateField.maxFileSize);
    this.displayListForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.displayListForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.displayListForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.displayListForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.displayListForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.displayListForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.displayListForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.displayListForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.displayListForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.displayListForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);

    this.displayListForm.controls.label1.setValue(this.component?.templateField?.label1);
    this.displayListForm.controls.placeHolder1.setValue(this.component?.templateField?.placeHolder1);
    this.displayListForm.controls.default1.setValue(this.component?.templateField?.default1);
    this.displayListForm.controls.caption1.setValue(this.component?.templateField?.caption1);
    this.displayListForm.controls.description1.setValue(this.component?.templateField?.description1);

    this.displayListForm.controls.labe2.setValue(this.component?.templateField?.label2);
    this.displayListForm.controls.placeHolder2.setValue(this.component?.templateField?.placeHolder2);
    this.displayListForm.controls.default2.setValue(this.component?.templateField?.default2);
    this.displayListForm.controls.caption2.setValue(this.component?.templateField?.caption2);
    this.displayListForm.controls.description2.setValue(this.component?.templateField?.description2);

    this.displayListForm.controls.label3.setValue(this.component?.templateField?.label3);
    this.displayListForm.controls.placeHolder3.setValue(this.component?.templateField?.placeHolder3);
    this.displayListForm.controls.caption3.setValue(this.component?.templateField?.caption3);
    this.displayListForm.controls.description3.setValue(this.component?.templateField?.description3);

    this.displayListForm.controls.centerContent.setValue(this.component?.templateField?.showGradient);
    this.displayListForm.controls.moveOverBanner.setValue(this.component?.templateField?.moveToBack);
    this.displayListForm.controls.isSystemProperty.setValue(this.component?.templateField?.isInherit ?? true);
    this.displayListForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth ?? false);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.displayListForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.displayListForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.displayListForm.valid) {
      this.component.templateField.label = this.displayListForm.controls.label.value;
      this.component.templateField.helpDescription = this.displayListForm.controls.description.value;
      this.component.templateField.helpTitle = this.displayListForm.controls.caption.value;
      this.component.templateField.defaultImageUrl = this.displayListForm.controls.defaultImageUrl.value;
      this.component.templateField.fileTypeBw = this.displayListForm.controls.fileTypeBw.value.reduce((a: number, b: number) => a + b, 0);
      this.component.templateField.maxFileSize = this.displayListForm.controls.maxFileSize.value;
      this.component.builderRowNumber = this.displayListForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.displayListForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.displayListForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.displayListForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.displayListForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.displayListForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.displayListForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.displayListForm.controls.isViewField.value;
      this.component.templateField.colspan = this.displayListForm.controls.colspan.value;
      this.component.templateField.colNumber = this.displayListForm.controls.colNumber.value;
      this.component.templateField.label1 = this.displayListForm.controls.label1.value;
      this.component.templateField.placeHolder1 = this.displayListForm.controls.placeHolder1.value;
      this.component.templateField.default1 = this.displayListForm.controls.default1.value;
      this.component.templateField.caption1 = this.displayListForm.controls.caption1.value;
      this.component.templateField.description1 = this.displayListForm.controls.description1.value;
      this.component.templateField.label2 = this.displayListForm.controls.label2.value;
      this.component.templateField.placeHolder2 = this.displayListForm.controls.placeHolder2.value;
      this.component.templateField.default2 = this.displayListForm.controls.default2.value;
      this.component.templateField.caption2 = this.displayListForm.controls.caption2.value;
      this.component.templateField.description2 = this.displayListForm.controls.description2.value;
      this.component.templateField.label3 = this.displayListForm.controls.label3.value;
      this.component.templateField.placeHolder3 = this.displayListForm.controls.placeHolder3.value;
      this.component.templateField.caption3 = this.displayListForm.controls.caption3.value;
      this.component.templateField.description3 = this.displayListForm.controls.description3.value;
      this.component.templateField.showGradient = this.displayListForm.controls.centerContent.value;
      this.component.templateField.moveToBack = this.displayListForm.controls.moveOverBanner.value;
      this.component.templateField.isInherit = this.displayListForm.controls.isSystemProperty.value;
      this.component.templateField.useMaxWidth = this.displayListForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
