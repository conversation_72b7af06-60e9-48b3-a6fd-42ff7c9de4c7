.parent-container {
  .title {
    color: #ffffff;
    font-family: '<PERSON><PERSON>';
    font-weight: bold;
    font-size: 20px;
    font-style: italic;
    line-height: 1.1;
    text-align: left;
    margin: 16px 0 8px 0;
  }

  .subtitle {
    margin: 8px 0;
    color: #aaaaaa;
    font-family: '<PERSON><PERSON>';
    font-weight: inherit;
    font-size: 18px;
    line-height: 1.3;
    text-align: left;
  }

  ion-col {
    display: flex;
    align-items: center;
    margin: 8px 0;
  }

  .icon {
    margin-right: 16px;
    color: #ffffff;
  }

  .info {
    color: #aaaaaa;
    border-radius: 10px 11px 11px 11px;
    font-family: '<PERSON>o';
    font-weight: 400;
    font-size: 18px;
    line-height: 1.4;
    letter-spacing: 0.2px;
    text-align: left;
    flex-wrap: wrap;
  }

  .row {
    display: block;
    color: #aaaaaa;
    border-radius: 10px 11px 11px 11px;
    font-family: '<PERSON><PERSON>';
    font-weight: 400;
    font-size: 18px;
    line-height: 1.4;
    letter-spacing: 0.2px;
    text-align: left;
    flex-wrap: wrap;
    width: 100%;
  }
}
