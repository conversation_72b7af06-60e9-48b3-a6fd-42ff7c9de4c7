import { <PERSON><PERSON>p<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '@env/environment';
import { Observable } from 'rxjs';

/**
 * Prefixes all requests with `environment.serverUrl`.
 */
@Injectable()
export class ApiPrefixInterceptor implements HttpInterceptor {
  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (request.headers.get('cdn') === 'true') {
      request = request.clone({ url: environment.contentUrl + request.url });
      return next.handle(request);
    }

    if (!/^(http|https):/i.test(request.url) && !/^\/assets/i.test(request.url) && !/^\/silent-callback/i.test(request.url)) {
      request = request.clone({ url: environment.apiUrl + request.url });
    }

    return next.handle(request);
  }
}
