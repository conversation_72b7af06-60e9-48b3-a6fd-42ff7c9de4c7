.parent-container {
  height: 100%;
  padding: 25px 0px;

  .button-container {
    align-items: center;
    display: flex;

    &.Left {
      justify-content: flex-start;
    }

    &.Center {
      justify-content: center;
    }

    &.Right {
      justify-content: flex-end;
    }

    ion-button {
      margin: 0px;
      --padding-top: 15px;
      --padding-bottom: 15px;
      color: black;
      text-transform: none;
      border-radius: 3px;
      font-weight: 600;
      font-size: 1.125em;
      background-color: #f99e00;
      letter-spacing: 0.5px;
      line-height: 1.1;
      min-height: 25px;
      min-width: 120px;
      font-family: 'Roboto';
      --box-shadow: none !important;
    }

    @media screen and (max-width: 960px) {
      ion-button {
        font-size: 1em;
        --padding-top: 0px;
        --padding-bottom: 0px;
      }

      ion-button::part(native) {
        min-height: 36px;
      }
    }

    ion-button:hover {
      --background: #8d5e1f !important;
    }
  }
}