@if (asset$ | async; as asset) {
  @if (asset?.mediaUploadType === 'Upload' || asset?.mediaUploadType === 'Url' || asset?.mediaUploadType === '' || !asset?.mediaUploadType) {
    @if (isImage) {
      <app-image-viewer-value [inheritedPropertyValue]="inheritedPropertyValue" [instanceSectionComponent]="instanceSectionComponent"></app-image-viewer-value>
    }
    @if (isVideo) {
      @defer (on immediate) {
        <app-video-player [assetId]="asset.id" [instanceSectionComponent]="instanceSectionComponent" [component]="component" [instanceId]="instanceId"></app-video-player>
      }
    }
    @if (isFile) {
      <app-file-download-control [assetId]="asset?.id" [instanceSectionComponent]="instanceSectionComponent" [instanceId]="instanceId"></app-file-download-control>
    }
  }
  @if (asset?.mediaUploadType === 'Embed') {
    @if (asset?.embedCode) {
      <div class="iframe-container" [innerHTML]="safeHtml"></div>
    }
  }
}
