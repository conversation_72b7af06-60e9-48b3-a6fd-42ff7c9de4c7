import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { IProductHistory, IRole } from '@app/core/contracts/contract';
import { UserRole } from '@app/core/enums/userRoles.enum';
import { DataService } from '@app/core/services/data-service';
import { ProductHistoryService } from '@app/core/services/product-history.service';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { IonicModule } from '@ionic/angular';
import { UpperCasePipe, DatePipe } from '@angular/common';

@Component({
    selector: 'app-product-history',
    templateUrl: './product-history.component.html',
    styleUrls: ['./product-history.component.scss'],
    imports: [MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, IonicModule, UpperCasePipe, DatePipe]
})
export class ProductHistoryComponent implements OnInit, OnChanges {
  @Input() id: string | null | undefined;
  @Input() featureType: string;
  @Input() searchFilter: string;
  userRoleName: string;
  userRoles: UserRole;
  productOrganizationsHistory: IProductHistory[] = [];
  currentAmount = 0;
  getAmount = 25;
  moreResults = false;
  constructor(
    private dataService: DataService,
    private productHistoryService: ProductHistoryService
  ) {}

  ngOnInit() {
    //UniqueForUserManager:
    if (this.id === undefined && this.featureType === 'User Manager') {
      this.id = '00000000-0000-0000-0000-000000000000';
    }

    if (this.id) {
      this.getProductHistoryById(this.id, false);
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['searchFilter']) {
      this.productOrganizationsHistory = [];
      this.currentAmount = 0;

      if (this.id) {
        this.getProductHistoryById(this.id, false, this.searchFilter);
      }
    }
  }

  subscriptionStatus(date: number): string {
    return this.productHistoryService.subscriptionStatus(date);
  }

  getProductOrgUserRoleById(organization: IProductHistory) {
    if (organization != null) {
      this.dataService.getProductOrgUserRoleById(organization.id).subscribe((orgUserRole: IRole) => {
        if (orgUserRole != null) {
          organization.orgUserRoleName = orgUserRole.name;
        }
      });
    }
  }

  getProductHistoryById(orgId: string, loadMore: boolean, searchFilter?: string) {
    if (orgId !== undefined) {
      this.dataService.getProductHistoryById(orgId, this.featureType, this.currentAmount, this.getAmount, searchFilter).subscribe((productOrganizationsHistory: IProductHistory[]) => {
        if (productOrganizationsHistory.length > 0) {
          //OnLoadMoreData
          if (!loadMore) {
            this.productOrganizationsHistory = productOrganizationsHistory;
            this.currentAmount += productOrganizationsHistory.length;
          } else {
            productOrganizationsHistory.forEach(org => {
              this.productOrganizationsHistory = [...this.productOrganizationsHistory, org];
            });
            this.currentAmount += productOrganizationsHistory.length;
          }

          if (productOrganizationsHistory.length < this.getAmount) {
            this.moreResults = false;
          } else {
            this.moreResults = true;
          }

          this.productOrganizationsHistory.forEach(organization => {
            this.getProductOrgUserRoleById(organization);
          });
        }
      });
    }
  }
}
