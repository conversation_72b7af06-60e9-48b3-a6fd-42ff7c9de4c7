<div class="chip-list-container">
  <mat-form-field>
    <mat-label>{{ component?.templateField?.label }}</mat-label>
    <mat-chip-grid #chipList aria-label="Tag selection">
      @for (tag of existingTags$ | async; track tag) {
        <mat-chip-row (removed)="removeDirect(tag.id)">
          <div class="name">{{ tag.name }}</div>
          <button matChipRemove>
            <mat-icon>cancel</mat-icon>
          </button>
        </mat-chip-row>
      }
      @if (!hasChildrenTags) {
        <input
          readonly
          id="chipListInput"
          [matChipInputFor]="chipList"
          [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
          [matChipInputAddOnBlur]="addOnBlur"
          (matChipInputTokenEnd)="add($event)"
          (click)="openTagModal()" />
      }
    </mat-chip-grid>
  </mat-form-field>
</div>
