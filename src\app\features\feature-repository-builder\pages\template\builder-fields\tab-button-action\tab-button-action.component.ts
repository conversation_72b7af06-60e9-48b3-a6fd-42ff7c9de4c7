import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { IAction, IFeature, IFeatureTabButton, IFeatureTabButtonAction, IFeatureTabButtonLinkType } from '@app/core/contracts/contract';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

@Component({
    selector: 'app-tab-button-action',
    templateUrl: './tab-button-action.component.html',
    styleUrls: ['./tab-button-action.component.scss'],
    standalone: false
})
export class TabButtonActionComponent implements OnInit {
  @Input() featureTabButton: IFeatureTabButton;
  @Input() buttonLinkTypes$: Observable<IFeatureTabButtonLinkType[]>;
  @Input() actions$: Observable<IAction[]>;
  @Input() features$: Observable<IFeature[]>;
  @Output() deleteClicked = new EventEmitter<void>();
  backgroundColor = '#181818';

  selectedTabButtonActions: IAction[];

  constructor() {}

  ngOnInit() {
    this.filterFeatures();
    if (this.featureTabButton.featureTabButtonActions) {
      this.selectedTabButtonActions = this.featureTabButton.featureTabButtonActions.map(x => ({ id: x.actionId, name: '', actionBw: 0 }) as IAction);
    }
  }

  //Filter Only Codes For Now.
  //Alot Of Empties - Looks Strange.
  filterFeatures() {
    this.features$
      .pipe(
        map(val => {
          return (val = val.filter(x => x.code != null));
        })
      )
      .subscribe(featuresFiltered => {
        {
          this.features$ = of(featuresFiltered);
        }
      });
  }

  delete() {
    this.deleteClicked.emit();
  }

  compareWith(a1: IAction, a2: IAction | IAction[]) {
    if (!a1 || !a2) {
      return a1 === a2;
    }

    if (Array.isArray(a2)) {
      return a2?.some((a: IAction) => a.id === a1.id);
    }

    return a1.id === a2.id;
  }

  linkCompareWith(a1: IFeatureTabButtonLinkType, a2: IFeatureTabButtonLinkType) {
    return a1.id === a2.id;
  }

  setButtonActions(event: any) {
    this.selectedTabButtonActions = event.detail.value;
    this.featureTabButton.featureTabButtonActions = event.detail.value.map((x: IAction) => ({ id: '', actionId: x.id }) as IFeatureTabButtonAction);
  }
}
