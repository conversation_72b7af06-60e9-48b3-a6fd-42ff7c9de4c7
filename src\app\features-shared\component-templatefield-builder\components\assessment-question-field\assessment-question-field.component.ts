import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { IQuestion } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { debounceTime, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-assessment-question-field',
    templateUrl: './assessment-question-field.component.html',
    styleUrls: ['./assessment-question-field.component.scss'],
    standalone: false
})
export class AssessmentQuestionFieldComponent implements OnInit, OnDestroy {
  @Input() selectedQuestionIds: string[] = [];
  @Output() selectionChanged = new EventEmitter();
  query: string;
  questions: IQuestion[];
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.search();
  }

  checkboxChanged($event: any, questionId: string) {
    if ($event?.srcElement?.checked) {
      this.selectedQuestionIds.push(questionId);
    } else {
      const index = this.selectedQuestionIds.indexOf(questionId);
      this.selectedQuestionIds.splice(index, 1);
    }
    this.selectionChanged.emit();
  }

  isSelected(questionId: string) {
    return this.selectedQuestionIds ? this.selectedQuestionIds.findIndex(x => x === questionId) !== -1 : false;
  }

  search() {
    this.dataService
      .searchQuestions(this.query)
      .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
      .subscribe(data => {
        this.questions = data;
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
