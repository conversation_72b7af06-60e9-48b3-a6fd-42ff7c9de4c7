.content {
  background-color: #111;
  background-image: var(--background-image);
  background-size: cover;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  min-height: 0;
  padding: 8px;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Exo 2';
    text-shadow: 2px 2px #000;
    margin: 0;
    position: absolute;
    color: #cccccc;
  }

  h1 {
    color: white;
    font-size: 22px;
    bottom: 18px;
    font-weight: 600;
  }
  h2 {
    font-size: 2.5em;
  }
  h3 {
    font-size: 2em;
  }
  h4 {
    font-size: 1.8em;
  }
  h5 {
    bottom: 40px;
    font-style: italic;
    font-size: 12px;
  }
  h6 {
    bottom: 4px;
    font-style: italic;
    font-size: 11px;
  }
  p {
    font-family: 'Roboto';
    text-shadow: 2px 2px #000;
  }
}

.hover-item {
  // background-color: #111;
  background-size: cover;
  width: 100%;
  height: 100%;
  min-height: 0;
  background: linear-gradient(to bottom, rgba(58, 58, 58, 0.4) 0%, rgba(58, 58, 58, 0.5) 80px, rgba(58, 58, 55, 11) 140px), var(--background-image) no-repeat;
  border-radius: 7px;
  padding-left: 1.25vw;
  padding-right: 0.5vw;
  padding-top: 0.25vw;
  border: 1.5px solid #333;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Exo 2';
    text-shadow: 2px 2px #000;
    margin: 0;
    // position: absolute;
    color: #cccccc;
  }

  h1 {
    color: white;
    font-size: 22px;
    // bottom: 18px;
    font-weight: 600;
  }
  h2 {
    font-size: 2.5em;
  }
  h3 {
    font-size: 2em;
  }
  h4 {
    font-size: 1.8em;
  }
  h5 {
    bottom: 40px;
    font-style: italic;
    font-size: 12px;
  }
  h6 {
    // bottom: 4px;
    font-style: italic;
    font-size: 11px;
  }
  p {
    font-family: 'Roboto';
    text-shadow: 2px 2px #000;
  }

  .btn-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    align-content: center;

    .row {
      display: flex;
      flex-direction: row;
    }
  }

  .play-row {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    align-content: center;
    height: 50px;
    width: 100%;
  }

  .description {
    margin-top: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* number of lines to show */
    -webkit-box-orient: vertical;
  }

  .outcome {
    margin-top: 20px;
  }

  li {
    font-size: 12px;
    margin-bottom: 10px;
  }
}

.left-col {
  border-right: 1px solid #181818;
}

.row-icon {
  height: 60px;
  width: 60px;
  border-radius: 5px;
  margin: 0px 10px;
}

.mat-expansion-panel {
  background: #333333;
  color: #8b8b8b;
  margin-bottom: 10px;
  padding: 0px;
}

.mat-expansion-panel-header {
  height: 200px;
  background-color: #111;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 80%, rgba(0, 0, 0, 0.9) 100%), var(--background-image) no-repeat;
  background-size: cover;
}

.mat-expansion-panel-header-title {
  color: #8b8b8b !important;
}

.mat-expansion-panel-header-description {
  color: #8b8b8b !important;
}

::ng-deep .mat-expansion-panel-header > .mat-expansion-indicator:after {
  color: white;
}

.button-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  justify-content: flex-end;
}

.panel-left {
  display: flex;
  align-items: center;
  height: 100%;

  ion-icon {
    color: white;
    font-size: 20px;
  }
}

.panel-right {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-end;
}

.right-col {
  padding-left: 20px;

  .top-header {
    margin-top: 0px !important;
    margin-bottom: 20px;
  }
}

.include-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  justify-content: space-between;
  background-color: #181818;
  border-radius: 5px;
  width: 100%;
  padding: 10px;
}

.content-container {
  height: fit-content;
}

.mat-expansion-panel-header {
  padding: 8px;
}

.top-row {
  width: 100%;
}

.bottom-row {
  width: 100%;
}

ion-grid {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  min-height: 100%;
}

::ng-deep .mat-content {
  height: 100%;
}
