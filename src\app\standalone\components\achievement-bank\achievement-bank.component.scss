:host {
  width: 100%;

  .reqAsterisk {
    color: #7f550c;
    font-size: 28px;
  }

  .achievement {
    border-color: grey;
  }

  ion-item{
    color: white;
  }

  ion-toggle {
    --background: #797a7f;
    --background-checked: #a87122;

    --handle-background: #acaeb5;
    --handle-background-checked: #f99e00;
  }

  .container {
    width: 100%;
  }

  .checkboxes {
    align-items: center;
  }

  .title-container {
    padding-top: 5px;
  }

  .title {
    color: white;
    font-size: 20px;
    float: left;
  }

  .achievementDescription {
    color: grey;
  }

  .weight-container {
    width: 150px;
  }

  .weight {
    background-color: #272727;
    text-align: center;
    border-radius: 5px;
  }
}
