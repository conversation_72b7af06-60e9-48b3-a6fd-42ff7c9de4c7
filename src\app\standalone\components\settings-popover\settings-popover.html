<ion-list style="background-color: transparent">
  <ion-item color="dark" style="cursor: pointer" (click)="itemSelected('info')">
    <ion-label color="light"> <ion-icon color="primary" style="font-size: 18px" name="person-outline"></ion-icon> My Info </ion-label>
  </ion-item>
  <ng-container>
    @for (org of managedOrganization; track org) {
      <ion-item color="dark" style="cursor: pointer" (click)="itemSelected('organization', org.id)">
        <ion-label color="light"> <ion-icon color="primary" style="font-size: 18px" name="briefcase-outline"></ion-icon> {{ org.name }} </ion-label>
      </ion-item>
    }
  </ng-container>
  <ion-item color="dark" lines="none" style="cursor: pointer" (click)="itemSelected('logout')">
    <ion-label color="light"> <ion-icon color="primary" style="font-size: 18px" name="log-out-outline"></ion-icon> Logout </ion-label>
  </ion-item>
</ion-list>
