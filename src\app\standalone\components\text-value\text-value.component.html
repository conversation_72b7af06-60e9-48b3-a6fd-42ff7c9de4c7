@if (inheritedPropertyValue || instanceComponent?.value || textValue) {
  <div [ngClass]="noMargin === true ? 'parent-container instance-label' : 'parent-container parent-margin instance-label'">
    <ng-container>
      @if (displayShowMore) {
        <div [ngClass]="textType === 'Text' ? 'text-container' : 'text-area-container'">
          @if (instanceComponent?.value) {
            <div>
              @if (!displayMoreComponent) {
                <div>
                  <span class="component-style text-value">
                    {{ trimString(instanceComponent?.value, 100) }}
                    <a (click)="displayMoreComponent = !displayMoreComponent" class="show-more-less"> <span [innerHtml]="displayMoreComponent ? 'less' : 'more'"> </span> </a>
                  </span>
                </div>
              }
              @if (displayMoreComponent) {
                <div>
                  <span class="component-style text-value">
                    {{ instanceComponent?.value }}
                    <a (click)="displayMoreComponent = !displayMoreComponent" class="show-more-less"> <span [innerHtml]="displayMoreComponent ? 'less' : 'more'"> </span> </a>
                  </span>
                </div>
              }
            </div>
          }
          @if (inheritedPropertyValue) {
            <div>
              @if (!displayMoreProperty) {
                <div>
                  <span class="property-style text-value">
                    {{ trimString(inheritedPropertyValue, 100) }}
                    <a (click)="displayMoreProperty = !displayMoreProperty" class="show-more-less"> <span [innerHtml]="displayMoreProperty ? 'less' : 'more'"> </span> </a>
                  </span>
                </div>
              }
              @if (displayMoreProperty) {
                <div>
                  <span class="property-style text-value">
                    {{ inheritedPropertyValue }}
                    <a (click)="displayMoreProperty = !displayMoreProperty" class="show-more-less"> <span [innerHtml]="displayMoreProperty ? 'less' : 'more'"> </span> </a>
                  </span>
                </div>
              }
            </div>
          }
        </div>
      }
      @if (!displayShowMore && !onlyHover) {
        <div [ngClass]="textType === 'Text' ? 'text-container' : 'text-area-container'">
          <span class="text-value">
            {{ textValue ?? '' | parsePipe: instanceId | async }}
          </span>
        </div>
      }
    </ng-container>
  </div>
}

@if (onlyHover) {
  <div [ngClass]="{ 'hover-Container' : hoverDetails.title !== undefined && hoverDetails?.description !== undefined}">
      <p class="title">
          {{ hoverDetails.title }}
      </p>
      <p class="description">
          {{ hoverDetails?.description }}
      </p>
  </div>
}
