@if (displayListForm) {
  <form class="parent-form-container" [formGroup]="displayListForm">
    <ion-grid>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="header">Edit {{ component.componentType.name }}</div>
        </ion-col>
      </ion-row>
      <div class="middle-line">
        <hr />
      </div>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Add field label...'" formControlName="label"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Description'"
            [placeHolder]="'Add field description...'"
            formControlName="description"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Add field caption...'" formControlName="caption"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line">
        <hr />
      </div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Personalize Instance Title</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Add field label...'" formControlName="label1"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Placeholder'" [placeHolder]="'Type here...'" formControlName="placeHolder1"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Default'" [placeHolder]="'Type here...'" formControlName="default1"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Type here...'" formControlName="caption1"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Description'" [placeHolder]="'Type here...'" formControlName="description1"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line">
        <hr />
      </div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Personalize Description</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Type here...'" formControlName="label2"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Placeholder'" [placeHolder]="'Type here...'" formControlName="placeHolder2"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Default'" [placeHolder]="'Type here...'" formControlName="default2"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Type here...'" formControlName="caption2"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Description'" [placeHolder]="'Type here...'" formControlName="description2"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line">
        <hr />
      </div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Personalize Thumbnail</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Type here...'" formControlName="label3"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Placeholder'" [placeHolder]="'Type here...'" formControlName="placeHolder3"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Type here...'" formControlName="caption3"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Description'" [placeHolder]="'Type here...'" formControlName="description3"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-file-upload-control
            [componentType]="component?.componentType?.name"
            [fileTypeBw]="component?.templateField?.fileTypeBw"
            formControlName="defaultImageUrl"
            [label]="'Default photo'"
            [component]="component"
            [toolTip]="'Default photo'">
          </app-file-upload-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-select-option-control
            [multiple]="true"
            [label]="'Accepted Formats'"
            [options]="builderService.acceptedImageFormats"
            [backgroundColor]="'#333333'"
            [disabled]="false"
            formControlName="fileTypeBw"></app-select-option-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [isCustom]="true" [label]="'File Limit'" [backgroundColor]="'#292929'" [placeHolder]="'Mb'" formControlName="maxFileSize" type="number"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line">
        <hr />
      </div>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Hover Sort Order'"
            [placeHolder]="'Add field hover sort order...'"
            formControlName="hoverSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Instance Sort Order'"
            [placeHolder]="'Add field instance sort order...'"
            formControlName="instanceSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="row-number-col">
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Row Number'"
            [placeHolder]="'Add field row number...'"
            formControlName="rowNumber"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Column Number (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add column number (Out of 12)...'"
            formControlName="colNumber"
            [type]="'number'">
          </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Colspan (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add field colspan (Out of 12)...'"
            formControlName="colspan"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line">
        <hr />
      </div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Settings</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <mat-slide-toggle style="width: 100%" color="primary" formControlName="centerContent">Center Content</mat-slide-toggle>
          <mat-slide-toggle style="width: 100%" color="primary" formControlName="moveOverBanner">Move over Banner</mat-slide-toggle>
          <mat-slide-toggle style="width: 100%" color="primary" formControlName="isSystemProperty">Link to System Properties</mat-slide-toggle>
          <app-field-checkboxes-base [baseForm]="displayListForm"></app-field-checkboxes-base>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
