import { Component, EventEmitter, Input, OnChang<PERSON>, OnDestroy, OnInit, Output, SimpleChanges } from "@angular/core";
import { IComponent, IDropDownLinkType, IQlikComponent, IQlikSheet } from "@app/core/contracts/contract";
import { Subject, Subscription, takeUntil } from "rxjs";
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { qlikComponentTypes } from "@app/core/constants/data";
import { KeyValue } from "@app/core/dtos/KeyValue";
import { DataService } from "@app/core/services/data-service";

@Component({
    selector: 'app-qlik-analytics-editor',
    templateUrl: './qlik-analytics-editor.html',
    styleUrls: ['./qlik-analytics-editor.scss'],
    standalone: false
})

export class QlikAnalyticsEditor implements OnInit, OnDestroy, OnChanges {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  componentDestroyed$: Subject<boolean> = new Subject();
  qlikAnalyticsFieldForm: UntypedFormGroup;
  formValueChanges$: Subscription;

  qlikTypes: KeyValue[] = qlikComponentTypes;
  qlikSheets: KeyValue[] = [];
  qlikComponents: KeyValue[] = [];

  constructor(private formBuilder: UntypedFormBuilder, private dataService: DataService) {
  }

  ngOnInit() {
    this.dataService.getQlikSheets()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((data: IQlikSheet[]) => {
      if (data && data.length > 0) {
        this.qlikSheets = data.map(x => ({id: x.id, value: x.title}) as KeyValue);
      }
    });
    if (this.component?.templateField?.placeHolder4) {
      this.dataService.getQlikComponents(this.component?.templateField?.placeHolder4)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((data: IQlikComponent[]) => {
        if (data && data.length > 0) {
          this.qlikComponents = data.map(x => ({id: x.id, value: x.title}) as KeyValue);
        }
      });
    }
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.qlikAnalyticsFieldForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      description1: [this.component?.templateField?.description1],
      label4: [this.component?.templateField?.label4],
      description4: [this.component?.templateField?.description4],
      placeHolder4: [this.component?.templateField?.placeHolder4],
      rowNumber: [this.component?.builderRowNumber],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.qlikAnalyticsFieldForm) {
      return;
    }

    this.qlikAnalyticsFieldForm.controls.label.setValue(this.component.templateField.label);
    this.qlikAnalyticsFieldForm.controls.description1.setValue(this.component.templateField.description1);
    this.qlikAnalyticsFieldForm.controls.label4.setValue(this.component.templateField.label4);
    this.qlikAnalyticsFieldForm.controls.description4.setValue(this.component.templateField.description4);
    this.qlikAnalyticsFieldForm.controls.placeHolder4.setValue(this.component.templateField.placeHolder4);
    this.qlikAnalyticsFieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.qlikAnalyticsFieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.qlikAnalyticsFieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.qlikAnalyticsFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.qlikAnalyticsFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.qlikAnalyticsFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.qlikAnalyticsFieldForm.valid);
      this.setValues();
    });
  }


  setValues() {
    if (this.qlikAnalyticsFieldForm.valid) {
      this.component.templateField.label = this.qlikAnalyticsFieldForm.controls.label.value;
      this.component.templateField.description1 = this.qlikAnalyticsFieldForm.controls.description1.value;
      this.component.templateField.label4 = this.getObjectTypeDropDownValue().id ?? '';
      this.component.templateField.placeHolder4 = this.getComponentSheetValue().id ?? '';
      this.component.templateField.description4 = this.getObjectDropDownValue().id ?? '';
      this.component.builderRowNumber = this.qlikAnalyticsFieldForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.qlikAnalyticsFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.qlikAnalyticsFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.colspan = this.qlikAnalyticsFieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.qlikAnalyticsFieldForm.controls.colNumber.value;
    }
  }

  getObjectTypeDropDownValue() {
    const link = this.qlikTypes.find(x => x.id === this.qlikAnalyticsFieldForm?.controls?.label4?.value);
    return { id: link?.id, title: link?.value } as IDropDownLinkType;
  }

  getObjectDropDownValue() {
    if (this.getObjectTypeDropDownValue().title === 'Sheets' && this.qlikAnalyticsFieldForm?.controls?.description4?.value) {
      const link = this.qlikSheets.find(x => x.id === this.qlikAnalyticsFieldForm?.controls?.description4?.value);
      return { id: link?.id, title: link?.value } as IDropDownLinkType;
    } else if (this.getObjectTypeDropDownValue().title === 'Components' && this.qlikAnalyticsFieldForm?.controls?.description4?.value) {
      const link = this.qlikComponents.find(x => x.id === this.qlikAnalyticsFieldForm?.controls?.description4?.value);
      return { id: link?.id, title: link?.value } as IDropDownLinkType;
    } else {
      return { } as IDropDownLinkType;
    }
  }

  getComponentSheetValue() {
    if (this.getObjectTypeDropDownValue().title === 'Components' && this.qlikAnalyticsFieldForm?.controls?.placeHolder4?.value) {
      const link = this.qlikSheets.find(x => x.id === this.qlikAnalyticsFieldForm?.controls?.placeHolder4?.value);
      return { id: link?.id, title: link?.value } as IDropDownLinkType;
    } else {
      return { } as IDropDownLinkType;
    }
  }

  getComponents(id: string) {
    this.qlikComponents = [];
    if (this.qlikAnalyticsFieldForm.controls.placeHolder4.value) {
      this.dataService.getQlikComponents(id).pipe(takeUntil(this.componentDestroyed$)).subscribe((data: IQlikComponent[]) => {
        if (data && data.length > 0) {
          this.qlikComponents = data.map(x => ({id: x.id, value: x.title}) as KeyValue);
        }
      });
    }
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
