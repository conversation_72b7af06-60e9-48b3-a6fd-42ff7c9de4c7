<div class="parent-container">
  @if (instanceForm) {
    <form [formGroup]="instanceForm" class="parent-form-container">
      <ion-grid>
        <ion-row>
          <ion-col>
            <app-builder-side-panel-heading [text]="component?.templateField?.caption1 ?? ''"></app-builder-side-panel-heading>
            @if (component?.templateField?.description1) {
              <app-builder-side-panel-description [text]="component.templateField.description1" [noPadding]="true"></app-builder-side-panel-description>
            }
            <app-text-input-control [noPadding]="true" [options]="options" [selectedOption]="instanceForm.get('headingStyle')?.value || ''" [backgroundColor]="'#292929'" formControlName="title" [label]="component?.templateField?.label1 ?? typeName + ' Title'" (optionsValueChanged)="onHeadingStyleChanged($event)"></app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col class="header-spacing">
            @if (component?.templateField?.caption2) {
              <app-builder-side-panel-heading [text]="component?.templateField?.caption2 ?? ''"></app-builder-side-panel-heading>
            }
            @if (component?.templateField?.description2) {
              <app-builder-side-panel-description [noPadding]="true" [text]="component.templateField.description2"></app-builder-side-panel-description>
            }
            <app-text-area-input-control
              [noPadding]="true"
              [backgroundColor]="'#292929'"
              formControlName="description"
              [label]="component?.templateField?.label2 ?? typeName + ' Description'"></app-text-area-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col class="header-spacing">
            @if (component?.templateField?.caption3) {
              <app-builder-side-panel-heading [text]="component?.templateField?.caption3 ?? ''"></app-builder-side-panel-heading>
            }
            @if (component?.templateField?.description3) {
              <app-builder-side-panel-description [text]="component.templateField.description3" [noPadding]="true"></app-builder-side-panel-description>
            }
            <app-file-upload-control
              formControlName="iconAssetId"
              [label]="component?.templateField?.label3 ?? typeName + ' Thumbnail'"
              [toolTip]="'The thumbnail image for the ' + typeName"></app-file-upload-control>
          </ion-col>
        </ion-row>
      </ion-grid>
    </form>
  }
</div>
