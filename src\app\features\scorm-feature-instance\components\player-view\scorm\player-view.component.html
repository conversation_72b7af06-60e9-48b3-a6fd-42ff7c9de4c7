@if (layoutService.currentScreenSize === 'lg') {
  <div class="parent-container">
    <div class="player-row-flex">
      <div class="player-width">
        <div class="player-inner-container">
          <div class="content-wrapper player-view-info-container">
            <div id="scrollContent" [class.player-outer-container]="routeParams?.viewType === viewTypes.Player">
              <div [class.player-container]="routeParams?.viewType === viewTypes.Player">
                @if (routeParams.instanceSlug) {
                  <app-scorm-player-view-information [routeParams]="routeParams"></app-scorm-player-view-information>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
      @if (instance?.isDefault === true || instance?.feature?.featureType?.name === 'Accredited Learning Container Pages') {
        <div class="player-side-panel-container">
          <app-player-view-side-panel-scorm [template]="template" [routeParams]="routeParams" [instance]="instance" [searchFilter]="searchFilter"></app-player-view-side-panel-scorm>
        </div>
      }
    </div>
  </div>
}
@if (layoutService.currentScreenSize === 'xs') {
  <div class="parent-container-mobile">
    <div class="main-btn-container">
      <ion-button class="menu-button" (click)="openMobileMenu = !openMobileMenu">
        <ion-icon size="large" color="light" name="menu"></ion-icon>
      </ion-button>
    </div>
    <mat-sidenav-container class="player-row-flex">
      <mat-sidenav #sidenav position="end" mode="side" [(opened)]="openMobileMenu">
        <div class="player-side-panel-container">
          <app-player-view-side-panel-scorm [template]="template" [routeParams]="routeParams" [instance]="instance" [searchFilter]="searchFilter"> </app-player-view-side-panel-scorm>
        </div>
      </mat-sidenav>
      <mat-sidenav-content class="player-width" (click)="openMobileMenu ? sidenav.toggle() : null">
        <div class="player-inner-container">
          @if (!openMobileMenu) {
            <div class="content-wrapper player-view-info-container">
              <div [class.player-outer-container]="routeParams.viewType === viewTypes.Player">
                <div [class.player-container]="routeParams.viewType === viewTypes.Player">
                  @if (routeParams.instanceSlug) {
                    <app-scorm-player-view-information [routeParams]="routeParams"></app-scorm-player-view-information>
                  }
                </div>
              </div>
            </div>
          }
        </div>
      </mat-sidenav-content>
    </mat-sidenav-container>
  </div>
}
