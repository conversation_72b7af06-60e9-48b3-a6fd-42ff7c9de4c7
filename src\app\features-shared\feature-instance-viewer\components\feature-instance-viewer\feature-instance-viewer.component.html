<!-- NOT PLAYER -->
@if (routeParams?.viewType !== viewTypes.Player && instance.feature.featureType.name !== 'V7 Landing Page' && instance.feature.isFullWidth !== true) {
  <div>
    <app-view-options-row
      [template]="template"
      [canAdd]="instance?.title === 'Admin'"
      [instance]="instance"
      (buttonChanged)="buttonClicked($event)"
      [featureTab]="featureTab"
      (optionSelected)="setSelectedViewType($event)"
      (rowFiltered)="rowFiltered($event)"
      (searchBarAvailable)="setSearchBarAvailable($event)"
      [selectedItem]="routeParams?.viewType ?? 0">
    </app-view-options-row>
  </div>
}
@if (routeParams?.viewType !== viewTypes.Player) {
  <ion-content
    [style]="'height:' + height + 'vh'"
    [ngClass]="{
      'content-no-header': instance.feature.featureType.name === 'V7 Landing Page' || (instance.feature.isFullWidth === true && routeParams.viewType !== viewTypes.Player),
    }"
    (ionScroll)="checkScroll($event)"
    [scrollEvents]="true">
    <div class="full-container">
      @if (routeParams?.viewType !== viewTypes.Player) {
        <!-- Section -->
        @for (instanceSection of template.instanceSections; track instanceSection; let i = $index) {
          @if (instanceSection.section) {
            <div
              [style]="getBackgroundStyle(instanceSection)"
              [ngClass]="instanceSection.section?.hideBackground ? 'section-no-background' : 'section'"
              #parentDiv>
              @if (instanceSection.section?.title && instanceSection.section?.showTitleOnPlayer === true) {
                <h1 class="instance-title-default">
                  {{ instanceSection.section?.title }}
                </h1>
              }
              @if (
                instanceSection.section?.description &&
                instanceSection.section?.showOnInstanceViewer === true &&
                instanceSection.section?.showDescOnPlayer === true
              ) {
                <div class="description">
                  {{ instanceSection.section?.description }}
                </div>
              }
              <!-- COMPONENT -->
              <ion-grid>
                @for (groupedComponents of instanceSection.instanceSectionComponents | groupBy: 'component.builderRowNumber' | values; track groupedComponents) {
                  <ion-row 
                    [ngClass]="instance.feature.featureType.name === 'Organization Manager' ? 'vertical-row' : ''"
                    [ngStyle]="getSectionMaxWidthStyle(instanceSection.section, instanceSection.id, groupedComponents[0]?.component?.builderRowNumber)">
                    @for (
                      instanceComponent of groupedComponents | pageViewComponentFilter: instance?.feature?.isFullWidth === true | orderBy: 'component.templateField.colNumber';
                      track instanceComponent;
                      let compIndex = $index
                    ) {
                      <ion-col
                        col-12
                        col-md-6
                        col-lg-4
                        col-xl-3
                        [size]="
                          layoutService.currentScreenSize === 'xs' && instance.feature.featureType.name === 'Organization Manager'
                            ? 'auto'
                            : instanceComponent.component?.templateField?.colspan !== 0
                              ? instanceComponent.component?.templateField?.colspan
                              : null
                        ">
                        @if (instanceComponent.component.componentType.name === 'Listing Details' && i === 0 && compIndex === 0 && instance.feature.isFullWidth === true) {
                          <div [style]="'--background-image:url(' + assetUrl + ');'"></div>
                        }
                        @if (!instanceComponent.component?.templateField?.isFilter) {
                          <app-component-row-selector
                            [instance]="instance"
                            [instanceSectionComponent]="instanceComponent"
                            [routeParams]="routeParams"
                            [searchFilter]="searchFilter"
                            [instanceSection]="instanceSection"
                            [continuousFeedback]="instanceSection?.section?.isContinuousFeedback ?? false"
                            (updateInstanceComponentValue)="setNewValue($event, instanceComponent.id)">
                          </app-component-row-selector>
                        }
                      </ion-col>
                    }
                  </ion-row>
                }
              </ion-grid>
            </div>
          }
        }
      }
    </div>
    @if (instance.feature.featureType.name !== 'V7 Landing Page' && !isScorm) {
      <div>
        <app-link-footer></app-link-footer>
      </div>
    }
  </ion-content>
}
<!-- NOT PLAYER -->

<!-- PLAYER -->
@if (routeParams?.viewType === viewTypes.Player) {
  <app-player-view
    [instanceTemplate]="template"
    [instance]="instance"
    [featureTab]="featureTab"
    [routeParams]="routeParams"
    [isScorm]="isScorm"
    [searchFilter]="searchFilter"
    [selectedUserId]="selectedUserId"
    (optionSelected)="setSelectedViewType($event)"
    (buttonChanged)="buttonClicked($event)">
  </app-player-view>
}
<!-- PLAYER -->
