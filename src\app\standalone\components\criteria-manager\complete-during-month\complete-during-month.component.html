@if (completeDuringMonthForm) {
  <form [formGroup]="completeDuringMonthForm">
    <div class="parent-container">
      <ion-row>
        <ion-col size="auto" class="heading-col">Complete the assigned resources during the month of:</ion-col>
        <ion-col size="auto">
          <mat-form-field color="primary" appearance="fill">
            <mat-label>Enter a date range</mat-label>
            <mat-date-range-input [rangePicker]="picker">
              <input formControlName="minValue" matStartDate placeholder="Start date" />
              <input formControlName="maxValue" matEndDate placeholder="End date" />
            </mat-date-range-input>
            <mat-datepicker-toggle color="primary" matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-date-range-picker color="primary" #picker></mat-date-range-picker>
          </mat-form-field>
        </ion-col>
      </ion-row>
    </div>
  </form>
}
