@* @using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model EdgeFactor.ID.STS.Identity.ViewModels.Account.ResetPasswordViewModel
@{
    ViewData["Title"] = Localizer["Title"];
}

<h1>@ViewData["Title"]</h1>

@await Html.PartialAsync("_ValidationSummary")

<form asp-controller="Account" asp-action="ResetPassword" method="post" class="form-horizontal">
    <h4>@Localizer["SubTitle"]</h4>
    <hr />
    <input asp-for="Code" type="hidden" />
    <div class="form-group">
        <label asp-for="Email" class="col-md-2 control-label">@Localizer["Email"]</label>
        <div class="col-md-10">
            <input asp-for="Email" class="form-control" />
            <span asp-validation-for="Email" class="text-danger"></span>
        </div>
    </div>
    <div class="form-group">
        <label asp-for="Password" class="col-md-2 control-label">@Localizer["Password"]</label>
        <div class="col-md-10">
            <input asp-for="Password" class="form-control" />
            <span asp-validation-for="Password" class="text-danger"></span>
        </div>
    </div>
    <div class="form-group">
        <label asp-for="ConfirmPassword" class="col-md-2 control-label">@Localizer["ConfirmPassword"]</label>
        <div class="col-md-10">
            <input asp-for="ConfirmPassword" class="form-control" />
            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
        </div>
    </div>
    <div class="form-group">
        <div class="col-md-offset-2 col-md-10">
            <button type="submit" class="btn btn-primary">@Localizer["Reset"]</button>
        </div>
    </div>
</form> *@


@inject IViewLocalizer Localizer
@using Microsoft.AspNetCore.Mvc.Localization
@model EdgeFactor.ID.STS.Identity.ViewModels.Account.ResetPasswordViewModel

<div class="login-page">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@600;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Exo+2:wght@500;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Exo:wght@500;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
    <style>
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        textarea:-webkit-autofill,
        textarea:-webkit-autofill:hover,
        textarea:-webkit-autofill:focus,
        select:-webkit-autofill,
        select:-webkit-autofill:hover,
        select:-webkit-autofill:focus {
            border: 1px solid transparent;
            -webkit-text-fill-color: #F1F1F1;
            -webkit-box-shadow: unset;
            background: transparent;
            transition: background-color 5000s ease-in-out 0s;
            width: calc(100% - 20px);
            margin-right: 20px;
            box-sizing: border-box;
            font-family: 'Montserrat', Arial, sans-serif;
        }
        .logo {
            width: 258px;
            height: 57px;
            position: fixed;
            left: 56px;
            top: 42px;
        }
        .outer-container {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .main-container {
            width: 475px;
            height: 601px;
            border-radius: 4px;
            position: relative;
            background-color: #1e1e1e;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        .main-container h2 {
            margin-bottom: 10px;
        }
        .change-desc {
            color: #cccccc;
            font-family: 'Roboto', Arial, sans-serif;
            font-size: 16px;
            font-weight: 400;
            margin-left: 36px;
            margin-right: 36px;
            margin-top: 54px;
        }
        .InputBox {
            height: 56px;
            background: #1A191B;
            width: calc(100% - 72px);
            margin-top: 8px;
            margin-left: 36px;
            margin-right: 36px;
            border-bottom: 1px solid #96959D;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
        }
        .InputBox:first-of-type {
            margin-top: 17.48px;
        }
        .InputLabel {
            margin-left: 8px;
            line-height: 22px;
            font-weight: 400;
            color: #96959D;
            font-family: 'Montserrat';
            font-size: 10px;
            margin-bottom: 2px;
        }
        .ChangeInput {
            all: unset;
            width: 100%;
            background: #1A191B;
            margin-left: 8px;
            font-family: 'Montserrat', Arial, sans-serif;
            font-size: 14px;
            box-sizing: border-box;
            max-width: 100%;
        }
        .material-icons-outlined {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            color: #fff;
            cursor: pointer;
            width: 24px;
            height: 24px;
            font-size: 24px;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-btn {
            margin-top: 48px;
            width: calc(100% - 72px);
            margin-left: 36px;
            margin-right: 36px;
            height: 40px;
            border: none;
            border-radius: 4px;
            font-family: 'Montserrat', Arial, sans-serif;
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            letter-spacing: 0;
            color: #1A191B;
            background: linear-gradient(90deg, #F99E00 0%, #FDE734 100%);
            cursor: pointer;
            transition: box-shadow 0.2s;
            box-shadow: none;
            text-align: center;
        }
        .login-btn:active,
        .login-btn:focus {
            outline: none;
            box-shadow: 0 0 0 2px #FDE73455;
        }
        .main-container {
            display: flex;
            flex-direction: column;
        }
        .main-container .spacer {
            flex: 1 1 auto;
        }
        h2 {
            height: 39px;
            color: #ffffff;
            font-family: 'Exo', Arial, sans-serif;
            font-weight: 800;
            font-size: 30px;
            line-height: 1.3;
            text-align: left;
            margin: 0;
            padding: 0;
            margin-left: 36px;
            margin-top: 26px;
        }
        .password-requirements {
            font-family: 'Inter', Arial, sans-serif;
            font-weight: 400;
            font-size: 12px;
            color: #FFFFFF;
            margin-left: 52px;
            margin-right: 52px;
            margin-top: 8px;
            margin-bottom: 25px;
        }
        .password-requirements .requirements-title {
            display: block;
            margin-bottom: 2px;
        }
        .password-requirements ul {
            margin: 0 0 0 18px;
            padding: 0;
        }
        .password-requirements li {
            margin-bottom: 0;
            list-style-type: disc;
        }
        .input-hint {
            color: #96959D;
            font-size: 11px;
            margin-left: 8px;
            margin-top: 2px;
            font-family: 'Montserrat', Arial, sans-serif;
        }
        .input-indicator {
            font-size: 12px;
            margin-left: 36px;
            margin-top: 2px;
            display: block;
            color: #F64C4C;
            font-family: 'Montserrat', Arial, sans-serif;
        }
    </style>

    @await Html.PartialAsync("_ValidationSummary")
    <img class="logo" src="~/images/EFLogo_White.png" />
    <div class="outer-container">
        <div class="main-container">
            <h2>Create New Password</h2>
            <div class="change-desc">Please enter your updated password</div>
            <form method="post">
                <div class="InputBox">
                    <span class="InputLabel">@Localizer["NewPassword"]</span>
                    <input asp-for="Password" class="ChangeInput" type="password" id="newPassword" />
                    <span class="material-icons-outlined" id="toggleNewPassword">visibility_off</span>
                </div>
                <span asp-validation-for="Password" class="input-indicator"></span>
                <div class="password-requirements">
                    <span class="requirements-title">Password requirements</span>
                    <ul>
                        <li>Minimum of 8 characters</li>
                        <li>At least one uppercase letter</li>
                        <li>At least one number</li>
                        <li>At least one special character</li>
                    </ul>
                </div>
                <div class="InputBox">
                    <span class="InputLabel">@Localizer["ConfirmPassword"]</span>
                    <input asp-for="ConfirmPassword" class="ChangeInput" type="password" id="confirmPassword" />
                    <span class="material-icons-outlined" id="toggleConfirmPassword">visibility_off</span>
                </div>
                <span asp-validation-for="ConfirmPassword" class="input-indicator"></span>
                <button type="submit" class="login-btn">@Localizer["Update"]</button>
            </form>
            <script>
                function setupTogglePassword(inputId, toggleId) {
                    const input = document.getElementById(inputId);
                    const toggle = document.getElementById(toggleId);
                    toggle.addEventListener('click', function () {
                        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
                        input.setAttribute('type', type);
                        this.textContent = type === 'text' ? 'visibility' : 'visibility_off';
                    });
                }

                setupTogglePassword('newPassword', 'toggleNewPassword');
                setupTogglePassword('confirmPassword', 'toggleConfirmPassword');

                @* function passwordValidation(password) {
                    const minLength = password.length >= 8;
                    const hasUpper = /[A-Z]/.test(password);
                    const hasNumber = /\d/.test(password);
                    // Regex for validating the input fields
                    const hasSpecial = /[!$%\^&*(),.?"{}|<>\[\]\\/'`~:;_+=\-#]/.test(password);
                    return minLength && hasUpper && hasNumber && hasSpecial;
                }

                const newPasswordInput = document.getElementById('newPassword');
                const newPasswordIndicator = document.getElementById('newPasswordIndicator');
                const confirmPasswordInput = document.getElementById('confirmPassword');
                const confirmPasswordIndicator = document.getElementById('confirmPasswordIndicator');
                const submitBtn = document.querySelector('.login-btn');

                function updateButtonState() {
                    const validPassword = passwordValidation(newPasswordInput.value);
                    const passwordsMatch = newPasswordInput.value.length > 0 && confirmPasswordInput.value === newPasswordInput.value;
                    if (validPassword && passwordsMatch) {
                        submitBtn.disabled = false;
                        submitBtn.style.opacity = '1';
                        submitBtn.style.cursor = 'pointer';
                    } else {
                        submitBtn.disabled = true;
                        submitBtn.style.opacity = '0.6';
                        submitBtn.style.cursor = 'not-allowed';
                    }
                }

                // Initial state
                updateButtonState();

                newPasswordInput.addEventListener('input', function () {
                    if (this.value.length === 0) {
                        newPasswordIndicator.textContent = '';
                    } else if (passwordValidation(this.value)) {
                        newPasswordIndicator.textContent = '✔ Meets requirements';
                        newPasswordIndicator.style.color = '#4caf50';
                    } else {
                        newPasswordIndicator.textContent = '✖ Does not meet requirements';
                        newPasswordIndicator.style.color = '#f44336';
                    }

                    if (confirmPasswordInput.value.length > 0) {
                        if (confirmPasswordInput.value === newPasswordInput.value) {
                            confirmPasswordIndicator.textContent = '✔ Passwords match';
                            confirmPasswordIndicator.style.color = '#4caf50';
                        } else {
                            confirmPasswordIndicator.textContent = '✖ Passwords do not match';
                            confirmPasswordIndicator.style.color = '#f44336';
                        }
                    } else if (confirmPasswordInput.value.length === 0) {
                        confirmPasswordIndicator.textContent = '';
                    }

                    updateButtonState();
                });

                // Repeat Password Validation
                confirmPasswordInput.addEventListener('input', function () {
                    if (this.value.length === 0) {
                        confirmPasswordIndicator.textContent = '';
                    } else if (this.value === newPasswordInput.value) {
                        confirmPasswordIndicator.textContent = '✔ Passwords match';
                        confirmPasswordIndicator.style.color = '#4caf50';
                    } else {
                        confirmPasswordIndicator.textContent = '✖ Passwords do not match';
                        confirmPasswordIndicator.style.color = '#f44336';
                    }

                    updateButtonState();
                }); *@
            </script>
        </div>
    </div>
</div>