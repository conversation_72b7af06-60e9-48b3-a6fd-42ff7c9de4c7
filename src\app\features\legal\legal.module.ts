import { NgModule } from '@angular/core';

import { RouterModule } from '@angular/router';
import { ROUTES } from './legal.routes';
import { featureComponents } from './legal.declarations';
import { SharedModule } from '@app/shared/shared.module';

@NgModule({
  declarations: [...featureComponents],
  imports: [SharedModule, RouterModule.forChild(ROUTES)],
  exports: [...featureComponents],
})
export class LegalModule {}
