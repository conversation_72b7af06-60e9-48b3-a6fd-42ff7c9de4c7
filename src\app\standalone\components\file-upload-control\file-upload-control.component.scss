:host {
  .parent-container {
    .image-container {
      width: 100%;
      border-radius: 7px;
      border: 2px dashed black;
      position: relative;
      margin-top: 7px;
    }

    img {
      border-radius: 7px;
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      z-index: 1000;
    }

    .inner-container {
      border-radius: 3px;
      background: transparent;

      .inner-item {
        --display: flex;
        --flex-direction: column;
        --border-color: transparent;
        --color: white;
        --padding-start: 0px !important;
        --inner-padding-end: 0px;
        font-size: 20px;
        font-family: 'Exo 2';
        width: 100%;
        --background: var(--background);

        ion-icon {
          cursor: help !important;
        }

        .image {
          background-color: #171717;
          aspect-ratio: 3/2;
        }

        .main-container {
          text-align: center;
          width: 100%;
          min-height: 150px;
          max-height: 500px;
          position: relative;
          background-size: cover;
          background-repeat: no-repeat;
          display: flex;
          align-items: center;
          justify-content: center;

          .delete-block {
            width: 100%;
          }

          input {
            opacity: 0;
            position: absolute;
            z-index: 2;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            cursor: pointer;
          }

          .delete {
            top: 0;
            right: 0;
            cursor: pointer;
            color: #aaa;
            font-size: 1.5em;
            position: absolute;
            z-index: 1000;

            ion-icon {
              pointer-events: none;
            }
          }

          .delete:hover {
            color: #ee9907;
          }

          .text-area {
            width: 100%;
            text-align: center;
            top: 10%;
          }

          .text-area p {
            color: #aaa;
            font-style: italic;
            font-weight: 300;
            font-size: 14px;
          }

          .text-area ion-button {
            --background: lightgray;
            color: black;
            border-radius: 1em;
            text-transform: none;
          }
        }

        ion-label {
          cursor: help !important;
          padding-bottom: 0.04em;
          font-weight: 500;
          letter-spacing: 0.03em;

          .reqAsterisk {
            color: #7f550c;
            font-size: 28px;

            ion-icon {
              cursor: help !important;
            }

            .identifier {
              background-color: #7f550c;
              border-radius: 0.2em;
              color: black;
              font-weight: bold;
              padding: 0.3em;
              margin-left: 0.4em;
            }
          }
        }

        .disabledNoOfCasesDiv {
          pointer-events: none;
          opacity: 2;
        }

        .download-container {
          justify-content: center;
        }

        .video-container {
          min-width: 150px;
        }

        .border-color-red {
          border-color: red;
        }
      }

      .no-padding {
        --inner-padding-end: 0;
        --padding-start: 0;

        input {
          display: block !important;
        }
      }
    }

    .size-warning {
      padding: 8px;
      border-radius: 4px;
      z-index: 999999;
      position: relative;
      font-size: 14px;
      background-color: #171717;

      &.warning {
        color: #ffc107;
        border: 1px solid #ffc107;
      }

      &.danger {
        color: #dc3545;
        border: 1px solid #dc3545;
      }
    }

    .update-button-container {
      position: absolute;
      top: 0px;
      right: 30px;
      z-index: 1001;
      display: flex;
      justify-content: flex-end;

      ion-button {
        --background: #f99e00;
      }
    }
  }

  .no-container {
    margin: 0px !important;

    ion-card {
      margin: 0px !important;
      padding: 0px !important;
    }
  }

  .side-panel-input-padding {
    margin: 0px !important;

    ion-card {
      margin: 0px !important;
    }
  }

  ion-card {
    box-shadow: none !important;
  }
}
