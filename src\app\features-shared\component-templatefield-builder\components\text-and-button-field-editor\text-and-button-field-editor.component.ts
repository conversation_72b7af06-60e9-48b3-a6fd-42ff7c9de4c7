import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-text-and-button-field-editor',
    templateUrl: './text-and-button-field-editor.component.html',
    styleUrls: ['./text-and-button-field-editor.component.scss'],
    standalone: false
})
export class TextAndButtonFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  textAndButtonForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.textAndButtonForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }

      this.setFormValues();
    }
  }

  createForm() {
    this.textAndButtonForm = this.formBuilder.group({
      rowNumber: [this.component?.builderRowNumber ?? 0],
      hoverSortOrder: [this.component?.hoverSortOrder ?? 0],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      label1: [this.component?.templateField?.label1, Validators.required],
      placeHolder1: [this.component?.templateField?.placeHolder1],
      default1: [this.component?.templateField?.default1],
      caption1: [this.component?.templateField?.caption1],
      description1: [this.component?.templateField?.description1],
      label2: [this.component?.templateField?.label2, Validators.required],
      placeHolder2: [this.component?.templateField?.placeHolder2],
      default2: [this.component?.templateField?.default2],
      caption2: [this.component?.templateField?.caption2],
      description2: [this.component?.templateField?.description2],
      label3: [this.component?.templateField?.label3, Validators.required],
      placeHolder3: [this.component?.templateField?.placeHolder3],
      default3: [this.component?.templateField?.default3],
      caption3: [this.component?.templateField?.caption3],
      description3: [this.component?.templateField?.description3],
      label4: [this.component?.templateField?.label4, Validators.required],
      placeHolder4: [this.component?.templateField?.placeHolder4],
      default4: [this.component?.templateField?.default4],
      caption4: [this.component?.templateField?.caption4],
      description4: [this.component?.templateField?.description4],
      isButtonBelowText: [this.component?.templateField?.isButtonBelowText],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.textAndButtonForm) {
      return;
    }

    this.textAndButtonForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.textAndButtonForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.textAndButtonForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.textAndButtonForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.textAndButtonForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.textAndButtonForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.textAndButtonForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.textAndButtonForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.textAndButtonForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.textAndButtonForm.controls.isButtonBelowText.setValue(this.component?.templateField?.isButtonBelowText);

    this.textAndButtonForm.controls.label1.setValue(this.component?.templateField?.label1);
    this.textAndButtonForm.controls.placeHolder1.setValue(this.component?.templateField?.placeHolder1);
    this.textAndButtonForm.controls.default1.setValue(this.component?.templateField?.default1);
    this.textAndButtonForm.controls.caption1.setValue(this.component?.templateField?.caption1);
    this.textAndButtonForm.controls.description1.setValue(this.component?.templateField?.description1);

    this.textAndButtonForm.controls.labe2.setValue(this.component?.templateField?.label2);
    this.textAndButtonForm.controls.placeHolder2.setValue(this.component?.templateField?.placeHolder2);
    this.textAndButtonForm.controls.default2.setValue(this.component?.templateField?.default2);
    this.textAndButtonForm.controls.caption2.setValue(this.component?.templateField?.caption2);
    this.textAndButtonForm.controls.description2.setValue(this.component?.templateField?.description2);

    this.textAndButtonForm.controls.label3.setValue(this.component?.templateField?.label3);
    this.textAndButtonForm.controls.placeHolder3.setValue(this.component?.templateField?.placeHolder3);
    this.textAndButtonForm.controls.default3.setValue(this.component?.templateField?.default3);
    this.textAndButtonForm.controls.caption3.setValue(this.component?.templateField?.caption3);
    this.textAndButtonForm.controls.description3.setValue(this.component?.templateField?.description3);

    this.textAndButtonForm.controls.label4.setValue(this.component?.templateField?.label4);
    this.textAndButtonForm.controls.placeholder4.setValue(this.component?.templateField?.placeHolder4);
    this.textAndButtonForm.controls.default4.setValue(this.component?.templateField?.default4);
    this.textAndButtonForm.controls.caption4.setValue(this.component?.templateField?.caption4);
    this.textAndButtonForm.controls.description4.setValue(this.component?.templateField?.description4);

    this.textAndButtonForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth ?? false);
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.textAndButtonForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.textAndButtonForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.textAndButtonForm.valid) {
      this.component.builderRowNumber = this.textAndButtonForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.textAndButtonForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.textAndButtonForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.textAndButtonForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.textAndButtonForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.textAndButtonForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.textAndButtonForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.textAndButtonForm.controls.isViewField.value;
      this.component.templateField.colspan = this.textAndButtonForm.controls.colspan.value;
      this.component.templateField.colNumber = this.textAndButtonForm.controls.colNumber.value;
      this.component.templateField.label1 = this.textAndButtonForm.controls.label1.value;
      this.component.templateField.placeHolder1 = this.textAndButtonForm.controls.placeHolder1.value;
      this.component.templateField.default1 = this.textAndButtonForm.controls.default1.value;
      this.component.templateField.caption1 = this.textAndButtonForm.controls.caption1.value;
      this.component.templateField.description1 = this.textAndButtonForm.controls.description1.value;
      this.component.templateField.label2 = this.textAndButtonForm.controls.label2.value;
      this.component.templateField.placeHolder2 = this.textAndButtonForm.controls.placeHolder2.value;
      this.component.templateField.default2 = this.textAndButtonForm.controls.default2.value;
      this.component.templateField.caption2 = this.textAndButtonForm.controls.caption2.value;
      this.component.templateField.description2 = this.textAndButtonForm.controls.description2.value;
      this.component.templateField.label3 = this.textAndButtonForm.controls.label3.value;
      this.component.templateField.placeHolder3 = this.textAndButtonForm.controls.placeHolder3.value;
      this.component.templateField.default3 = this.textAndButtonForm.controls.default3.value;
      this.component.templateField.caption3 = this.textAndButtonForm.controls.caption3.value;
      this.component.templateField.description3 = this.textAndButtonForm.controls.description3.value;
      this.component.templateField.label4 = this.textAndButtonForm.controls.label4.value;
      this.component.templateField.placeHolder4 = this.textAndButtonForm.controls.placeHolder4.value;
      this.component.templateField.default4 = this.textAndButtonForm.controls.default4.value;
      this.component.templateField.caption4 = this.textAndButtonForm.controls.caption4.value;
      this.component.templateField.description4 = this.textAndButtonForm.controls.description4.value;
      this.component.templateField.isButtonBelowText = this.textAndButtonForm.controls.isButtonBelowText.value;
      this.component.templateField.useMaxWidth = this.textAndButtonForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
