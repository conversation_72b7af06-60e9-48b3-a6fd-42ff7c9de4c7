/* eslint @typescript-eslint/naming-convention: 0 */
/*eslint no-unused-vars:0 */
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, of, throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { AuthService } from '../services/auth-service';

@Injectable()
export class HttpAuthInterceptor implements HttpInterceptor {
  inflightAuthRequest: any;
  contentType = 'application/json';
  blacklist: any = [/accounts\/login/, /token\/refresh/, /assets/];
  constructor(
    private injector: Injector,
    private router: Router
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (req.headers.has('noheader')) {
      req = req.clone({
        headers: req.headers.delete('Content-Type', 'application/json'),
      });
    } else if (req.headers.has('Content-Type')) {
      this.contentType = req.headers.get('Content-Type') ?? 'application/json';
    } else {
      req = req.clone({
        setHeaders: {
          'Content-Type': this.contentType,
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
      });
    }

    if (this.blacklistCheckup(req.url) || req.headers.get('authExempt') === 'true') {
      const headerReq = req.clone({
        setHeaders: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
      });
      return next.handle(headerReq);
    }

    const authService = this.injector.get(AuthService);
    this.inflightAuthRequest = authService.getToken();
    // if (!this.inflightAuthRequest) {
    //   this.inflightAuthRequest = authService.getToken();
    // }

    return this.inflightAuthRequest.pipe(
      switchMap((newToken: string) => {
        if (authService.userContext) {
          req = req.clone({
            setHeaders: {
              'Tracking-Id': authService.userContext.userTrackingId,
              'Cache-Control': 'no-cache',
              Pragma: 'no-cache',
            },
          });
        }

        if (newToken === 'guest' || authService.guestUserContext) {
          const guest = req.clone({
            setHeaders: {
              'Guest-Id': authService.guestUserContext?.id ?? '',
              'Cache-Control': 'no-cache',
              Pragma: 'no-cache',
            },
          });
          return next.handle(guest);
        }

        // unset request inflight
        this.inflightAuthRequest = null;
        // use the newly returned token
        const authReq = req.clone({
          setHeaders: {
            Authorization: `Bearer ${newToken}`,
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache',
          },
        });
        return next.handle(authReq);
      }),
      catchError(error => {
        // checks if a url is to an admin api or not
        if (error.status === 0) {
          throwError(error);
        }

        if (error.status === 401) {
          // If unauthorized from token then user auth required again.
          if (!this.inflightAuthRequest) {
            this.inflightAuthRequest = authService.silentRefresh();
          }

          return this.inflightAuthRequest.pipe(
            switchMap((newToken: string) => {
              // unset inflight request
              this.inflightAuthRequest = null;

              // clone the original request
              const authReqRepeat = req.clone({
                setHeaders: {
                  Authorization: `Bearer ${newToken}`,
                  'Content-Type': this.contentType,
                  'Cache-Control': 'no-cache',
                  Pragma: 'no-cache',
                },
              });

              // resend the request
              return next.handle(authReqRepeat);
            })
          );
        } else {
          return throwError(error);
        }
      })
    );
  }

  blacklistCheckup($url: string): boolean {
    let returnValue = false;
    Object.keys(this.blacklist).forEach(i => {
      if (this.blacklist[i].exec($url) != null) {
        returnValue = true;
        return;
      }
    });
    return returnValue;
  }
}
