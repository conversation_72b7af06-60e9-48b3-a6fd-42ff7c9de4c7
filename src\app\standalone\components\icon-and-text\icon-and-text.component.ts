import { Component, Input, OnDestroy, OnInit, forwardRef } from '@angular/core';
import { UntypedFormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, UntypedFormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IComponent, IIconAndText } from '@app/core/contracts/contract';
import { Subject, takeUntil } from 'rxjs';
import { NgClass } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';

@Component({
    selector: 'app-icon-and-text',
    templateUrl: './icon-and-text.component.html',
    styleUrls: ['./icon-and-text.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => IconAndTextComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => IconAndTextComponent),
        },
    ],
    imports: [NgClass, IonicModule, FormsModule, ReactiveFormsModule, TextInputControlComponent]
})
export class IconAndTextComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override label: string;
  @Input() component!: IComponent;
  @Input() sidePanelPadding = false;
  textForm: UntypedFormGroup;
  iconAndText: IIconAndText;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor() {
    super();
  }

  ngOnInit() {
    this.fieldValueChanged.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.createForm();
      this.formChanges();
    });
  }

  createForm() {
    this.textForm = new UntypedFormGroup({
      iconText: new UntypedFormControl(this.textValue),
    });
  }

  formChanges() {
    this.textForm.valueChanges.subscribe(() => {
      this.updateComponent(this.textForm.controls['iconText'].value);
    });
  }

  updateComponent(text: string) {
    this.setValue(text);
  }

  override setValue(value: string) {
    this.writeValue(value);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
