import { animate, state, style, transition, trigger } from '@angular/animations';
import { SelectionModel } from '@angular/cdk/collections';
import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatSort } from '@angular/material/sort';
import { MatCell, MatCellDef, MatColumnDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatRow, MatRowDef, MatTable, MatTableDataSource } from '@angular/material/table';
import { IHeading, IInstance, IInstanceResult, IPeople } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { DataService } from '@app/core/services/data-service';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { RolesService } from '@app/core/services/roles.service';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { HeadingValueComponent } from '../heading-value/heading-value.component';
import { GradingTableUsersComponent } from '../grading-table-users/grading-table-users.component';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { NgClass } from '@angular/common';
import { LayoutService } from '@app/core/services/layout-service';
import { environment } from '@env/environment';

@Component({
  selector: 'app-grading-table',
  templateUrl: './grading-table.component.html',
  animations: [
    trigger('detailExpand', [
      state('collapsed,void', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
  styleUrls: ['./grading-table.component.scss'],
  imports: [
    IonicModule,
    MatTable,
    MatSort,
    MatColumnDef,
    MatHeaderCellDef,
    MatHeaderCell,
    MatCellDef,
    MatCell,
    MatHeaderRowDef,
    MatHeaderRow,
    MatRowDef,
    MatRow,
    HeadingValueComponent,
    GradingTableUsersComponent,
  ],
  standalone: true,
})
export class GradingTableComponent implements OnInit, OnDestroy {
  @ViewChild('sort', { static: true }) sort: MatSort;
  @Input() id: string | null | undefined;
  @Input() instance: IInstance;
  @Input() name: string | undefined | null;
  @Input() type: string | undefined;
  @Input() orgId: string | undefined;
  // Assignments table (outer table)
  assignmentsDataSource = new MatTableDataSource<any>();
  displayedColumns: string[] = ['name', 'grade'];

  // People table (inner table)
  peopleDataSource = new MatTableDataSource<any>();
  innerDisplayedColumns: string[] = ['select', 'name', 'role', 'progress', 'grade'];
  peopleSelection = new SelectionModel<any>(true, []);

  currentAmount = 0;
  moreResults = false;
  isLoading = false;
  componentDestroyed$: Subject<boolean> = new Subject();
  hasGrade = false;
  expandedElement: any | null = null;
  heading = { text: 'Assignments', stylingDirection: 'left', darkText: false, description: 'Manage assignments and student progress' } as IHeading;
  height = 300;
  getAmount = 100;

  // Store people data for each assignment
  assignmentPeopleMap = new Map<string, any[]>();
  instanceUsers: IPeople[] = [];

  constructor(
    private dataService: DataService,
    private toast: GlobalToastService,
    private rolesService: RolesService,
    public layoutService: LayoutService,
    private breadcrumbService: BreadcrumbService
  ) {}

  ngOnInit() {
    if (this.id) {
      this.getAssignmentsById(this.id, false);
      this.getPeopleTableById(this.id, false);
    }
  }

  getCoverUrl(coverMediaAssetId: string) {
    return `${environment.contentUrl}asset/${coverMediaAssetId}/content${this.height ? '?height=' + this.height : ''}`;
  }

  setExpandedElement(element: IInstanceResult | null) {
    this.expandedElement = this.expandedElement === element ? null : element;
  }

  getAssignmentsById(id: string, loadMore: boolean) {
    if (id !== undefined) {
      this.isLoading = true;
      const parentBreadcrumb = this.breadcrumbService.getByfeatureType('Modifiable Learning Container Pages');

      this.dataService
        .getPeopleGradeTableAssignmentsByInstanceId(id)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((results: IInstanceResult[]) => {
          this.isLoading = false;

          if (results.length > 0) {
            // Transform the data to create assignments
            const assignments = results;
            this.hasGrade = true;

            // Load more data if needed
            if (!loadMore) {
              this.setDataSource(assignments);
              this.currentAmount += assignments.length;
            }

            if (this.type === 'Modifiable Learning Container Pages') {
              this.heading.description = 'Manage assignments and student progress.';
              this.heading.chipText = `${assignments.length} assignments`;
            }
          }
        });
    }
  }

  getPeopleTableById(id: string, loadMore: boolean) {
    const parentBreadcrumb = this.breadcrumbService.getByfeatureType('Modifiable Learning Container Pages');
    this.dataService
      .getPeopleTableByInstanceId(id, this.currentAmount, this.getAmount, this.id !== parentBreadcrumb?.id ? parentBreadcrumb?.id : null)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((instanceUsers: IPeople[]) => {
        this.isLoading = false;
        if (instanceUsers.length > 0) {
          this.currentAmount += instanceUsers.length;

          if (instanceUsers.length < this.getAmount) {
            this.moreResults = false;
          } else {
            this.moreResults = true;
          }

          this.instanceUsers = instanceUsers;
        }
      });
  }

  // Methods for people table (inner table)
  isAllPeopleSelected() {
    const numSelected = this.peopleSelection.selected.length;
    const numRows = this.peopleDataSource.data.length;
    return numSelected === numRows;
  }

  masterTogglePeople() {
    if (this.isAllPeopleSelected() === false) {
      this.peopleDataSource.data.forEach(row => this.peopleSelection.select(row));
    } else {
      this.peopleSelection.clear();
    }
  }

  setDataSource(assignments: any[]) {
    this.assignmentsDataSource = new MatTableDataSource(assignments);
    this.assignmentsDataSource.sort = this.sort;
  }

  // Clear all selections
  clearSelections() {
    this.peopleSelection.clear();
  }

  // Update selected people
  updateSelected(status: string) {
    if (this.peopleSelection.selected.length > 0) {
      const userIds = this.peopleSelection.selected.map(x => x.userId);
      this.toast.presentToast('Users updated successfully');
      this.peopleSelection.clear();
      this.getAssignmentsById(this.id ?? '', false);
    }
  }

  hasAdminAccess() {
    return this.rolesService.hasFeatureRoleAccess([ActionTypes.Manage, ActionTypes.Publish]);
  }

  timeAgo(date: Date | string): string {
    try {
      const dateObj = date instanceof Date ? date : new Date(date);

      if (isNaN(dateObj.getTime())) {
        return '-';
      }

      const now = new Date();
      const diffInMs = now.getTime() - dateObj.getTime();
      const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) return 'Today';
      if (diffInDays === 1) return 'Yesterday';
      return `${diffInDays} Days Ago`;
    } catch (error) {
      return '-';
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
