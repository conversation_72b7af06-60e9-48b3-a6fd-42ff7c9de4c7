import { Component, CUSTOM_ELEMENTS_SCHEMA, Input, NO_ERRORS_SCHEMA, On<PERSON>estroy, OnInit } from '@angular/core';
import { ITemplateField } from '@app/core/contracts/contract';
import { QlikService } from '@app/core/services/qlik-service';
import { HeadingValueComponent } from '@app/standalone/components/heading-value/heading-value.component';
import { QlikSelectionComponent } from '@app/standalone/components/qlik-selection/qlik-selection.component';
import { IonicModule } from '@ionic/angular';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-qlik-analytics',
  standalone: true,
  styleUrls: ['./qlik-analytics.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
  imports: [HeadingValueComponent, IonicModule, QlikSelectionComponent],
  templateUrl: './qlik-analytics.component.html',
})
export class QlikAnalyticsComponent implements OnInit, OnD<PERSON>roy {
  @Input() id: string | null | undefined;
  @Input() featureSlug!: string | null | undefined;
  @Input() templateField: ITemplateField | undefined;
  componentDestroyed$: Subject<boolean> = new Subject();
  date: string;
  componentType: string | undefined;
  componentId: string | undefined;

  constructor(public qlikService: QlikService) {}

  ngOnInit(): void {
    const startDate = new Date();
    var year = startDate.getFullYear();
    const month = startDate.getMonth();
    if (month <= 7) {
      year = year - 1;
    }

    this.componentType = this.templateField?.label4;
    this.componentId = this.templateField?.description4;
    this.date = `=[Event Date]>=08/01/${year}`;
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
