@if (instance) {
  <app-page-header
    [onlyContent]="onlyContent()"
    [scrollPosition]="scrollPosition"
    [viewType]="routeParams.viewType"
    [instance]="instance"
    [content]="content"
    [routeParams]="routeParams"
    [isCurrentFirstLevelView]="isCurrentFirstLevelView"
    [featureTab]="getTabs()[selectedIndex()]"
    [featureTabs]="getTabs()"
    [selectedIndex]="selectedIndex"
    [liked]="likedState"
    [share]="share.bind(this)"
    (like)="like($event)">
  </app-page-header>
  <ng-template #content>
    @if (getTabs() && getTabs()?.length > 1) {
      <app-tabs
        [featureTabs]="instance.feature.featureTabs"
        [instance]="instance"
        [routeParams]="routeParams"
        [selectedIndex]="selectedIndex"
        (reloadSystemProperties)="reloadSystemProperties()"
        (instanceChanged)="setData()">
      </app-tabs>
    }
    @if (getTabs() && getTabs()?.length === 1) {
      <app-template
        class="no-tabs"
        [templateId]="getTabs()[0].tab.templateId"
        [instance]="instance"
        [featureTab]="getTabs()[0]"
        [routeParams]="routeParams"
        [onlyContent]="onlyContent()"
        (instanceChanged)="setData()"></app-template>
    }
  </ng-template>
}
@if (scormLoading) {
  <ion-grid class="scormloader">
    <ion-row class="ion-align-items-center">
      <ion-col size="12">
        <img class="img" src="assets/images/EdgeFactor-EF_rings-2018-white_small_png.png" />
        <br />
        <ion-spinner color="primary" name="dots"></ion-spinner>
      </ion-col>
    </ion-row>
  </ion-grid>
}
