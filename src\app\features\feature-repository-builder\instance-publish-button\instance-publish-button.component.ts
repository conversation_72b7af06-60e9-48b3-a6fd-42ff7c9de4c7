import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { IFeature, IKeyValue } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { OverlayEventDetail } from '@app/features-shared/row-instance/components/content/grid-view/thumbnail-styles/styles/image-background/image-background.component';
import { ConfirmationDialogComponent } from '@app/standalone/modals/confirmation-dialog/confirmation-dialog.component';
import { OptionSelectorDialogComponent } from '@app/standalone/modals/option-selector-dialog/option-selector-dialog.component';
import { ModalController, PopoverController } from '@ionic/angular';

@Component({
  selector: 'app-instance-publish-button',
  standalone: false,
  templateUrl: './instance-publish-button.component.html',
  styleUrl: './instance-publish-button.component.scss',
})
export class InstancePublishButtonComponent implements OnChanges {
  @Input() columnValue: string;
  @Input() feature: IFeature;
  @Output() statusChanged = new EventEmitter<string>();

  statusTypes: KeyValue[] = [
    { id: 'AwaitingReview', value: 'AwaitingReview' },
    { id: 'Approved', value: 'Approved' },
    { id: 'EdgeFactorRejected', value: 'EdgeFactorRejected' },
    { id: 'CustomerRejected', value: 'CustomerRejected' },  
    { id: 'Deleted', value: 'Deleted' },  
  ];
  isPublishEnabled = false;
  isUnpublishEnabled = false;
  controlBackground = '#181818';

  constructor(
    private popOver: PopoverController,
    private modalController: ModalController
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['columnValue']) {
      this.setPublishEnabled(this.columnValue);
      this.setUnpublishEnabled(this.columnValue);
    }
  }

  setPublishEnabled(status: string) {
    this.isPublishEnabled = status === 'Approved' || status === 'private';
  }

  setUnpublishEnabled(status: string) {
    this.isUnpublishEnabled = status === 'organization' || status === 'public' || status === 'network';
  }

  updateStatus(status: string) {
    if (status === this.statusTypes.find(x => x.value === 'Deleted')?.id) {
      this.delete();
    }
    if (status === 'Publish') {
      this.openPublishOptions(null, status);
      return;
    }
    this.columnValue = status;
    this.statusChanged.next(status ?? '');
    this.setPublishEnabled(this.columnValue);
    this.setUnpublishEnabled(this.columnValue);
  }

  changeStatus(status: string) {    
    if (status === 'Publish') {
      this.openPublishOptions(null, status);
      return;
    }
    this.columnValue = status;
    this.statusChanged.next(status ?? '');
    this.setPublishEnabled(this.columnValue);
    this.setUnpublishEnabled(this.columnValue);
  }

  async delete()
  {
    const modal = await this.modalController.create({
    component: ConfirmationDialogComponent,
    cssClass: 'confirm-dialog',
    componentProps: {
      headerText: 'Delete your page',
      bodyText: `You're about to delete this instance. Once you delete your work it will no longer be visible to enrolled people and other users at your Organization.`,
      buttonText: 'Delete',
    },
  });

  modal.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
    if (overlayEventDetail.role === 'confirm') {
      this.changeStatus('deleted');
    }
  });
  await modal.present();
  }

  async openPublishOptions(event: any, status: string | undefined) {
    if (status && status === 'Publish') {
      if (this.feature.featureType.name === 'Modifiable Learning Container Pages') {
        await this.openPublishDialog('organization');
        return;
      }
      const options: IKeyValue<string, string>[] = [
        { key: 'organization', value: 'To my organization' },
        { key: 'public', value: 'Publicly' },
        { key: 'network', value: 'To my network' },
      ];

      const popover = await this.popOver.create({
        component: OptionSelectorDialogComponent,
        cssClass: 'question-type-popover',
        componentProps: {
          options: options,
        },
        event: event,
        side: 'bottom',
      });

      popover.onDidDismiss().then(async (overlayEventDetail: OverlayEventDetail) => {
        if (overlayEventDetail.data) {
          await this.openPublishDialog(overlayEventDetail.data.key);
        }
      });

      await popover.present();
    } else {
      this.changeStatus('private');
    }
  }

  async openPublishDialog(status: string) {
    const modal = await this.modalController.create({
      component: ConfirmationDialogComponent,
      cssClass: 'confirm-dialog',
      componentProps: {
        headerText: 'Update your page',
        bodyText: `You're about to update your ${this.feature?.title}. Once you publish your work it will be visible to enrolled people and other users at your Organization.`,
        buttonText: 'Publish',
      },
    });

    modal.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
      if (overlayEventDetail.role === 'confirm') {
        this.changeStatus(status);
      }
    });

    await modal.present();
  }
}
