.parent-container {
    margin-bottom: 40px;

    .header-container {
        font-family: '<PERSON><PERSON>';
        margin-top: auto;
        padding-bottom: 10px;
        letter-spacing: 0.3px;
        text-align: left;

        .heading {
            color: #f7f6f6;
            font-weight: 900;
            font-size: 22px;
            line-height: 1.1;
        }
    }

    .card {
        padding: 15px 16px 9px 25px;
        background: #333333;
        color: #ffffff;
        border-color: #656565;
        border-width: 1.5px;
        border-style: solid;
        border-radius: 7px 7px 7px 7px;
        font-family: '<PERSON><PERSON>';
        font-weight: 900;
        line-height: 1.3;
        text-align: left;

        .card-content {
            height: 100%;
            border-radius: 8px;
            padding: 0;

            .inner-panel {
                .description {
                    color: #aaaaaa;
                    font-family: 'Roboto';
                    font-weight: 300;
                    font-style: italic;
                    line-height: 1.3;
                    letter-spacing: 0.5px;
                    text-align: left;
                    padding: 10px 0px;
                }
            }
        }

        .black-underline {
            margin-bottom: 15px;
            width: 100%;
            height: 2px;
            background: #232323;
        }
    }

    @media (min-width: 959px) {

        .header-container {
            font-family: '<PERSON>o';
            margin-top: auto;
            padding-bottom: 10px;
            letter-spacing: 0.3px;
            text-align: left;

            .heading {
                color: #f7f6f6;
                font-weight: 900;
                font-size: 22px;
                line-height: 1.1;
            }
        }

        .card {
            font-size: 33px;
        }

        .card-content {
            .inner-panel {
                .description {
                    font-size: 14px;
                }
            }
        }
    }

    @media (max-width: 960px) {

        .header-container {
            margin-top: auto;
            padding-bottom: 10px;
            font-family: 'Roboto';
            letter-spacing: 0.3px;
            text-align: left;

            .heading {
                color: #f7f6f6;
                font-weight: 900;
                font-size: 20px;
                line-height: 1.1;
                text-align: left;
            }
        }

        .card {
            font-size: 23px;
        }

        .card-content {
            .inner-panel {
                .description {
                    font-size: 12px;
                }
            }
        }
    }
}
