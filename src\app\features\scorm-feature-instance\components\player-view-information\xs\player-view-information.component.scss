.info-container {
  position: relative;
  height: auto;
  display: flex;
  margin: 0px 0px 30px 0px;
  height: 100%;

  .header {
    position: absolute;
    padding: 8px;
    width: 100%;
    background-image: linear-gradient(to bottom, transparent 60%, #232323), var(--background-image);
    background-size: cover;
    z-index: 999;
    border-top-left-radius: 13px;
    border-top-right-radius: 13px;
    height: 140px;
  }

  .content {
    align-self: flex-end;
    z-index: 1000;
    width: 100%;
    border-radius: 5px;
    height: 100%;

    .visual-row {
      margin-top: 90px;
      display: flex;
      height: auto;
      align-items: flex-start;
      width: 100%;
      height: 100%;

      .component-container {
        margin: 4px 10px 0 10px;
        height: 100%;
      }
    }
  }
}
