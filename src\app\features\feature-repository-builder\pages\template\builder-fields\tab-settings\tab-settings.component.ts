import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IAction, IComponent, IFeatureTab, IFeatureTabRowType, IRowType, ITagIn, ITemplateField } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { ModalController } from '@ionic/angular';
import { map, Observable } from 'rxjs';

@Component({
    selector: 'app-tab-settings',
    templateUrl: './tab-settings.component.html',
    styleUrls: ['./tab-settings.component.scss'],
    standalone: false
})
export class TabSettingsComponent implements OnInit {
  @Input() featureTab: IFeatureTab;
  settingsForm: UntypedFormGroup;
  defaultSelectOptions$: Observable<KeyValue[]>;
  actions$: Observable<KeyValue[]>;
  rowTypes$: Observable<IRowType[]>;
  featureTabTypes$: Observable<KeyValue[]>;
  personaSelectOptions$: Observable<KeyValue[]>;
  selectedRowTypeIds: string[] | null;
  controlBackground = '#181818';

  constructor(
    private formBuilder: UntypedFormBuilder,
    private dataService: DataService,
    private modalController: ModalController
  ) {}

  ngOnInit() {
    this.createForm();
    this.initData();

    this.selectedRowTypeIds = this.featureTab.featureTabRowTypes ? this.featureTab.featureTabRowTypes.map(x => x.rowType.id) : null;
  }

  createForm() {
    this.settingsForm = this.formBuilder.group({
      function: [this.featureTab?.type?.id, Validators.required],
      access: [this.featureTab?.featureTabActions.map(x => x.id), Validators.required],
      personaAccess: [this.featureTab.featureTabPersonas?.map(x => x.id)],
      editAccess: [this.featureTab?.featureTabEditActions?.map(x => x.id)],
      showForEfAdmin: [this.featureTab?.showForEfAdmin ?? false, Validators.required],
      showForGuest: [this.featureTab?.showForGuest ?? false, Validators.required],
      buttonText: [this.featureTab?.buttonText],
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.settingsForm.valueChanges.subscribe(() => {
      this.featureTab.typeId = this.settingsForm.controls.function.value;
      this.featureTab.featureTabActions = this.settingsForm.controls.access.value?.map((x: string) => ({ id: x, name: '' }) as IAction);
      this.featureTab.featureTabPersonas = this.settingsForm.controls.personaAccess.value?.map((x: string) => ({ id: x, name: '' }) as ITagIn);
      this.featureTab.showForEfAdmin = this.settingsForm.controls.showForEfAdmin.value;
      this.featureTab.showForGuest = this.settingsForm.controls.showForGuest.value;
      this.featureTab.featureTabEditActions = this.settingsForm.controls.editAccess.value ? this.settingsForm.controls.editAccess.value?.map((x: string) => ({ id: x, name: '' }) as IAction) : null;

      this.featureTab.buttonText = this.settingsForm.controls.buttonText.value;
    });
  }

  initData() {
    this.personaSelectOptions$ = this.dataService.getTagChildren('E72D1551-DFB6-4F1A-A783-28C8B930AD6C').pipe(
      map(tags => {
        return tags.map(t => {
          return { id: t.id, value: t.name } as KeyValue;
        });
      })
    );

    this.actions$ = this.dataService.getActions().pipe(
      map(action => {
        return action.map(t => {
          return { id: t.id, value: t.name } as KeyValue;
        });
      })
    );

    this.featureTabTypes$ = this.dataService.getFeatureTabTypes().pipe(
      map(action => {
        return action.map(t => {
          return { id: t.id, value: t.name } as KeyValue;
        });
      })
    );

    this.rowTypes$ = this.dataService.getRowTypes();
  }

  checkboxChanged($event: any, rowType: IRowType) {
    if (!this.featureTab.featureTabRowTypes) {
      this.featureTab.featureTabRowTypes = [];
    }

    if ($event?.srcElement?.checked) {
      this.selectedRowTypeIds?.push(rowType.id);
      this.featureTab.featureTabRowTypes.push({ id: '', rowType: rowType } as IFeatureTabRowType);
    } else {
      const index = this.selectedRowTypeIds?.indexOf(rowType.id) ?? -1;
      this.selectedRowTypeIds?.splice(index, 1);

      const index1 = this.featureTab.featureTabRowTypes.findIndex(x => x.rowType.id === rowType.id);
      this.featureTab.featureTabRowTypes.splice(index1, 1);
    }
  }

  isSelected(rowTypeId: string) {
    return this.selectedRowTypeIds ? this.selectedRowTypeIds.findIndex(x => x === rowTypeId) !== -1 : false;
  }

  getRowTypeLabel(rowTypeName: string) {
    switch (rowTypeName) {
      case 'Manual':
        return "Manually (If checked, users will be able to add content using the 'add' button)";
      case 'Dynamic':
        return 'Dynamically (If checked, users will see the tagging tab)';
      default:
        return `${rowTypeName} Items`;
    }
  }

  close() {
    this.modalController.dismiss(this.featureTab);
  }
}
