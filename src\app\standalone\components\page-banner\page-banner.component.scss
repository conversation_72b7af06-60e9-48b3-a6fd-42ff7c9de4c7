.page-image-header-gradient {
  width: 100%;
  background-image: linear-gradient(to bottom, #23232300 50%, #23232373, #232323e6 90%, #232323), var(--background-image);
  background-size: cover;
  top: -0px;
  left: -0px;
}

.page-image-header {
  width: 100%;
  background-image: var(--background-image);
  background-size: cover;
  top: -0px;
  left: -0px;
  background-position: center;
  margin-bottom: 30px;
}

.absolute {
  position: absolute;
}

.bg-video {
  width: 100%;
  top: 0px;
  left: 0px;
  object-fit: cover;
  min-height: 180px;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #23232300 50%, #23232373, #232323e6 90%, #232323);
}