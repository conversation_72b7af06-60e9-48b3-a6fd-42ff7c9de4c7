import { Component, EventEmitter, Input, OnChang<PERSON>, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { IComponent, IInstance, IInstanceSection, IInstanceSectionIn, IInstanceTemplate, IRouteParams, ISection, ISidePanelBuilder } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { SidePanelBuilderType } from '@app/core/enums/builder-selection-types';
import { SectionTypes } from '@app/core/enums/section-types.enum';
import { AlertService } from '@app/core/services/alert-service';
import { InstanceService } from '@app/core/services/instance-service';
import { LayoutService } from '@app/core/services/layout-service';
import { RolesService } from '@app/core/services/roles.service';
import { environment } from '@env/environment';
import { Subject } from 'rxjs';

@Component({
    selector: 'app-builder-instance-preview',
    templateUrl: './builder-instance-preview.component.html',
    styleUrls: ['./builder-instance-preview.component.scss'],
    standalone: false
})
export class BuilderInstancePreviewComponent implements OnInit, OnChanges, OnDestroy {
  @Input() template: IInstanceTemplate;
  @Input() instance: IInstance;
  @Input() routeParams: IRouteParams;
  @Input() editPanelBuilderIn: ISidePanelBuilder;
  @Input() editInstanceSections: IInstanceSection[] = [];
  @Input() disabled: boolean;
  @Input() id: string;
  @Input() panelState: boolean;
  @Output() editAddClicked = new EventEmitter<any>();
  @Output() emitEditInstanceSections = new EventEmitter<IInstanceSection[]>();
  @Output() setupStepsOnEditClick = new EventEmitter<boolean>();
  componentDestroyed$: Subject<boolean> = new Subject();
  builderType = SidePanelBuilderType;
  searchFilter: string;
  iconUrl: string;
  assetUrl: string;
  instanceSectionsIn: IInstanceSectionIn[] = [];
  sectionTypes = SectionTypes;
  maxWidthRowsMap: Map<string, number[]> = new Map();

  constructor(
    private alertService: AlertService,
    public instanceService: InstanceService,
    private rolesService: RolesService,
    private layoutService: LayoutService
  ) { }

  ngOnInit() {
    this.setGradientImageOverlay();
    this.initializeMaxWidthRows();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['editPanelBuilderIn']) {
      this.scrollToElement();
    }
    if (changes['template'] && !changes['template'].firstChange) {
      this.initializeMaxWidthRows();
    }
  }

  scrollToElement() {
    const element = document.getElementById(this.editPanelBuilderIn.id);
    if (element) {
      element.style.scrollMarginTop = '50px';
      element.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
    }
  }

  setGradientImageOverlay() {
    if (this.instance?.coverMediaAssetId) {
      this.assetUrl = `${environment.contentUrl}asset/${this.instance.coverMediaAssetId}/content`;
    } else {
      this.assetUrl = 'assets/images/defaultbackgroundgradient.png';
    }
  }

  getMaxWidthStyle(section: any) {
    if (!section || (!section.webMaxWidth && !section.mobileMaxWidth)) {
      return { 'max-width': '100%' };
    }

    const maxWidth = this.layoutService.currentScreenSize === 'xs'
      ? section.mobileMaxWidth
      : section.webMaxWidth;

    if (!maxWidth || maxWidth === 'auto') {
      return { 'max-width': '100%' };
    }

    // Format the maxWidth value
    let formattedMaxWidth = maxWidth;
    if (!maxWidth.includes('px') && !maxWidth.includes('vw')) {
      formattedMaxWidth = `${maxWidth}px`;
    }

    return {
      'max-width': formattedMaxWidth,
      'margin-left': 'auto',
      'margin-right': 'auto'
    };
  }

  getSectionMaxWidthStyle(section: any, sectionId: string, rowNumber: number) {
    if (!section) {
      return {};
    }

    // Get the maxWidthRowNumbers for this section
    const maxWidthRowNumbers = this.maxWidthRowsMap.get(sectionId) || [];

    // Only apply max width if this row number is in the maxWidthRowNumbers array
    if (maxWidthRowNumbers.includes(rowNumber)) {
      return section.webMaxWidth || section.mobileMaxWidth ? this.getMaxWidthStyle(section) : {};
    }

    return {};
  }

  initializeMaxWidthRows() {
    if (!this.template?.instanceSections) {
      return;
    }

    // Process each section
    this.template.instanceSections.forEach(section => {
      if (!section.instanceSectionComponents) {
        return;
      }

      // Group components by builderRowNumber
      interface RowGroup {
        [key: number]: any[];
      }

      const rowGroups: RowGroup = section.instanceSectionComponents.reduce((acc, comp) => {
        const rowNumber = comp.component?.builderRowNumber;
        if (rowNumber !== undefined && rowNumber !== null) {
          if (!acc[rowNumber]) {
            acc[rowNumber] = [];
          }
          acc[rowNumber].push(comp);
        }
        return acc;
      }, {} as RowGroup);

      // Filter for rows where all components have useMaxWidth=true
      const maxWidthRowNumbers = Object.entries(rowGroups)
        .filter(([_, components]) => components.every(comp => comp.component?.templateField?.useMaxWidth === true))
        .map(([rowNumber]) => Number(rowNumber));

      // Store the result in the map
      this.maxWidthRowsMap.set(section.id, maxWidthRowNumbers);
    });
  }

  trackByJsonObject(index: any, item: any) {
    return JSON.stringify(item);
  }

  editComponent(selectedComponent: IComponent, event: any) {
    event?.stopPropagation();

    if (this.disabled === true) {
      this.alertService.presentAlert('PUBLISHED INSTANCE', 'Instance must be unpublished before edit!');
      return;
    }

    this.editPanelBuilderIn.id = selectedComponent.id;
    this.editPanelBuilderIn.builderType = this.builderType.EditComponent;
    this.editPanelBuilderIn.heading = 'Edit Component';

    this.editAddClicked.next(null);
  }

  editSection(selectedSection: ISection, event: any) {
    event?.stopPropagation();

    if (selectedSection.templateId) {
      return;
    }

    this.editPanelBuilderIn.id = selectedSection.id;
    this.editPanelBuilderIn.builderType = this.builderType.EditSection;
    this.editPanelBuilderIn.heading = 'Edit Section';

    this.editAddClicked.next(null);
  }

  addSection(previousIndex: number) {
    this.editPanelBuilderIn.id = '';
    this.editPanelBuilderIn.previousIndex = previousIndex;
    this.editPanelBuilderIn.builderType = this.builderType.AddSection;
    this.editPanelBuilderIn.heading = 'Add Section';

    this.editAddClicked.emit(null);
  }

  addComponent(selectedInstanceSection: IInstanceSection, event: any) {
    event?.stopPropagation();
    this.editPanelBuilderIn.id = selectedInstanceSection.id;
    this.editPanelBuilderIn.builderType = this.builderType.AddComponent;
    this.editPanelBuilderIn.heading = 'Add Component';

    this.editAddClicked.emit(null);
  }

  hasEditAccess() {
    return this.rolesService.hasFeatureRoleAccess([ActionTypes.Manage, ActionTypes.Publish]);
  }

  setupStepsOnEdit() {
    if (this.disabled === true) {
      this.alertService.presentAlert('PUBLISHED INSTANCE', 'Instance must be unpublished before edit!');
      return;
    }
    this.setupStepsOnEditClick.emit(true);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
