import { Component } from '@angular/core';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { IonicModule } from '@ionic/angular';
import { TextValueComponent } from '../text-value/text-value.component';

@Component({
    selector: 'app-phone-number-value',
    templateUrl: './phone-number-value.component.html',
    styleUrls: ['./phone-number-value.component.scss'],
    imports: [IonicModule, TextValueComponent]
})
export class PhoneNumberValueComponent {
  constructor(private systemPropertyService: SystemPropertiesService) {}

  getTemplateFieldValue() {
    if (!this.systemPropertyService.userProperties) {
      return null;
    }

    const fullName = this.systemPropertyService.userProperties.find(x => x.key.includes('phone_number'));
    return fullName?.value;
  }
}
