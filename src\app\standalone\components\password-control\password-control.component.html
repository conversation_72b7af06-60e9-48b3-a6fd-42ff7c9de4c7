<div class="parent-container">
  <mat-expansion-panel #panel hideToggle="true" [expanded]="true">
    <mat-expansion-panel-header>
      <div>Password</div>
      <div class="panel-right">
        @if (!panel.expanded) {
          <ion-icon name="arrow-forward"></ion-icon>
        }
        @if (panel.expanded) {
          <ion-icon name="arrow-down"></ion-icon>
        }
      </div>
    </mat-expansion-panel-header>
    @if (passwordForm) {
      <form class="passwordForm" [formGroup]="passwordForm">
        <app-text-input-control
          [toolTip]="'New password'"
          [placeHolder]="'Enter new password'"
          [label]="'New password'"
          [formControlName]="'newPassword'"
          [backgroundColor]="getBackgroundColor()"
          [disabled]="false"
          [noPadding]="true"
          [borderColor]="getColor(true)"
          [type]="'password'"></app-text-input-control>
        @if (passwordStrength >= 0) {
          <div class="progress-segment">
            <div class="item" [ngClass]="{ 'red-common': passwordStrength <= 1, 'gold-common': passwordStrength === 2, 'green-common': passwordStrength >= 3 }"></div>
            <div class="item" [ngClass]="{ 'gold-common': passwordStrength === 2, 'green-common': passwordStrength >= 3 }"></div>
            <div class="item" [ngClass]="{ 'green-common': passwordStrength >= 3 }"></div>
            <div class="item" [ngClass]="{ 'green-common': passwordStrength === 4 }"></div>
          </div>
        }
        @if (passwordStrength >= 0) {
          <div class="info-container" [style]="'color:' + getColor()">
            <mat-icon (click)="clickedShowPasswordInformation()" class="info-icon" svgIcon="information-transparent"></mat-icon>
            @if (passwordStrength <= 1) {
              <span>Very weak</span>
            }
            @if (passwordStrength === 2) {
              <span>Weak</span>
            }
            @if (passwordStrength >= 3) {
              <span>Strong</span>
            }
          </div>
        }
        @if (showPasswordInformation) {
          <div class="information-box-container">
            <div class="information-box">
              <div class="text-container">
                <span>To make your password stronger:</span>
              </div>
              <div class="text-container">
                @if (!passwordLengthGreaterThanSeven) {
                  <mat-icon class="icon error" svgIcon="close"></mat-icon>
                }
                @if (passwordLengthGreaterThanSeven) {
                  <mat-icon class="icon correct" svgIcon="correct"></mat-icon>
                }
                <span class="info-text">Use 8-20 characters</span>
              </div>
              <div class="text-container">
                @if (!passwordIncludesNumber) {
                  <mat-icon class="icon error" svgIcon="close"></mat-icon>
                }
                @if (passwordIncludesNumber) {
                  <mat-icon class="icon correct" svgIcon="correct"></mat-icon>
                }
                <span class="info-text">Include a number</span>
              </div>
              <div class="text-container">
                @if (!passwordIncludesUppercase) {
                  <mat-icon class="icon error" svgIcon="close"></mat-icon>
                }
                @if (passwordIncludesUppercase) {
                  <mat-icon class="icon correct" svgIcon="correct"></mat-icon>
                }
                <span class="info-text">Include an uppercase letter</span>
              </div>
              <div class="text-container">
                @if (!passwordIncludesSpecialChar) {
                  <mat-icon style="color: #aaaaaa" class="icon" svgIcon="dot"></mat-icon>
                }
                @if (passwordIncludesSpecialChar) {
                  <mat-icon class="icon correct" svgIcon="correct"></mat-icon>
                }
                <span class="info-text" style="color: #aaaaaa">Include a special character</span>
              </div>
            </div>
          </div>
        }
        <app-text-input-control
          [toolTip]="'Verify new password'"
          [placeHolder]="'Re-Enter new password'"
          [label]="'Verify new password'"
          [formControlName]="'confirmPassword'"
          [backgroundColor]="getBackgroundColor()"
          [disabled]="false"
          [noPadding]="true"
          [borderColor]="getColor(true)"
          [type]="'password'"></app-text-input-control>
        @if (passwordMatchError) {
          <div class="info-container" style="color: #fd3629">
            <mat-icon class="info-icon" svgIcon="information-transparent"></mat-icon>
            <span>Passwords don't match</span>
          </div>
        }
      </form>
    }
    <div class="button-row">
      <ion-button color="warning" fill="solid" (click)="savePassword()">Save</ion-button>
      <ion-button color="warning" fill="clear" (click)="panel.toggle()">Cancel</ion-button>
    </div>
  </mat-expansion-panel>
</div>
