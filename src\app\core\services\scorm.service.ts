import { Injectable } from '@angular/core';
import { AddSearchModalComponent } from '@app/standalone/modals/add-search-modal/add-search-modal.component';
import { environment } from '@env/environment';
import { Alert<PERSON>ontroller, PopoverController } from '@ionic/angular';
import { saveAs } from 'file-saver';
import { map } from 'rxjs';
import { IInstance, IOrganizationLite, IOrganizationSsoAuth, IScormAuthIn, IScormFile, IScormRosterIn } from '../contracts/contract';
import { AuthService } from './auth-service';
import { DataService } from './data-service';
@Injectable({
  providedIn: 'root',
})
export class ScormService {
  userId: string;
  constructor(
    private dataService: DataService,
    private alertController: AlertController,
    private popoverController: PopoverController,
    private authService: AuthService
  ) {}

  public validAuthToken(authToken: string, origin: string) {
    const data = {
      origin: origin,
      authToken: authToken,
    } as IScormAuthIn;
    return this.dataService.validateScormAuth(data);
  }

  public rosterScormUser(origin: string, scormUserId: string, externalAuthSecret?: string) {
    const data = {
      origin: origin,
      userId: scormUserId,
      externalAuthSecret: externalAuthSecret,
    } as IScormRosterIn;

    return this.dataService.rosterScormUser(data).pipe(
      map(user => {
        this.userId = user.userId;
      })
    );
  }

  public generateScormFile(instance: IInstance, event: any) {
    this.dataService.getMyOrganizations().subscribe(async data => {
      if (data?.length < 1) {
        await this.presentAlert();
        return;
      }
      if (data?.length > 1 || this.authService.userContext?.canManage === true) {
        this.openSelectOrganization(instance, event);
      } else {
        this.generateScormForOrg(data[0], instance);
      }
    });
  }

  private generateScormForOrg(org: IOrganizationLite, instance: IInstance) {
    this.dataService.getOrgSsoAuthById(org.id).subscribe(data => {
      if (data) {
        this.exportScormFile(instance, org, data);
      } else {
        const orgSsoAuth = {
          organizationId: org.id,
          externalSystem: 'SCORM',
          externalId: org.id,
          externalUrl: '',
        } as IOrganizationSsoAuth;
        this.dataService.updateOrgSsoAuth(orgSsoAuth).subscribe(() => {
          this.generateScormForOrg(org, instance);
        });
      }
    });
  }

  private exportScormFile(instance: IInstance, org: IOrganizationLite, auth: IOrganizationSsoAuth) {
    this.dataService
      .generateScormFile({
        url: `${environment.appUrl}/scorm/${instance.id}`,
        name: this.cleanFileName(`${org.name} - ${instance.title}`),
        authToken: auth.externalAuthSecret,
        packageTitle: this.cleanFileName(instance.title),
        itemTitle: this.cleanFileName(instance.title),
      } as IScormFile)
      .subscribe(blob => {
        saveAs(blob, `${org.name} - ${instance.title}.zip`);
      });
  }

  private cleanFileName(name: string) {
    return name.replace(/[/\\?%*:|"<>]/g, '-');
  }

  private async openSelectOrganization(instance: IInstance, event: any) {
    const popover = await this.popoverController.create({
      component: AddSearchModalComponent,
      cssClass: 'add-search-modal',
      componentProps: { linkTypeName: 'Organizations', criteriaType: null, options: null },
      event: event,
      side: 'bottom',
    });

    popover.onDidDismiss().then((result: any) => {
      if (result.data) {
        if (result.data) {
          this.generateScormForOrg(result.data, instance);
        }
      }
    });

    await popover.present();
  }

  private async presentAlert() {
    const alert = await this.alertController.create({
      cssClass: '',
      header: 'Please Note:',
      message: 'User not linked to an organization.',
      buttons: ['OK'],
    });

    await alert.present();

    await alert.onDidDismiss();
  }
}
