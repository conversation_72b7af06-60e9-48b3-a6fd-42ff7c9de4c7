@if (formGroup) {
  <ng-container [formGroup]="formGroup">
    @if (mediaUploadType === 'Upload') {
      <app-file-upload-control
        [label]="component?.templateField?.label ?? ''"
        [itemBackgroundColor]="controlBackground"
        [fileFormat]="'/*/'"
        [fileTypeBw]="component?.templateField?.fileTypeBw"
        [minFileSize]="component?.templateField?.minFileSize"
        [maxFileSize]="component?.templateField?.maxFileSize"
        [componentType]="component?.componentType?.name"
        [formControlName]="'fileUpload'"
        [defaultImageUrl]="component?.templateField?.defaultImageUrl ?? ''"
        [buttonText]="component?.templateField?.buttonText ?? ''"
        [placeHolderText]="component?.templateField?.placeHolderText ?? ''"
        [mediaId]="assetId ?? ''"
        [component]="component"
        (valueUpdated)="assetUpdated($event)"
        [disabled]="disabled"></app-file-upload-control>
    }
    @if (mediaUploadType === 'Embed') {
      <app-text-input-control
        [toolTip]="'Copy and Paste embedded code i.e. iFrame'"
        [placeHolder]="'Paste your embedded code here...'"
        [label]="'Embedded Code'"
        [formControlName]="'embed'"
        [itemBackgroundColor]="controlBackground"></app-text-input-control>
      @if (embedCode) {
        <app-media-block [assetId]="assetId" [inheritedPropertyValue]="getSystemPropertyValue(component?.templateField?.systemProperty)"></app-media-block>
      }
    }
    @if (mediaUploadType === 'Url') {
      <app-text-input-control
        [toolTip]="'Enter a Url to stream from different server'"
        [placeHolder]="'Paste your url here...'"
        [label]="'Upload Url'"
        [formControlName]="'url'"
        [itemBackgroundColor]="controlBackground"></app-text-input-control>
      @if (urlUpload) {
        <app-media-block [assetId]="assetId" [inheritedPropertyValue]="getSystemPropertyValue(component?.templateField?.systemProperty)"></app-media-block>
      }
    }
  </ng-container>
}
