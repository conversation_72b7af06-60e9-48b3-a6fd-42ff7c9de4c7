import { Injectable, signal, computed, effect } from '@angular/core';
import { IInstance } from '@app/core/contracts/contract';
import { Subject } from 'rxjs';

export enum BannerType {
  COMPLETED_ALL = 1,
  COMPLETED_SOME = 2,
  MINIMUM_REQUIRED = 3,
  NONE = 4,
}

export interface BannerState {
  visible: boolean;
  type: BannerType;
  completed?: number;
  totalCount?: number;
  minimumItems?: number;
  instance?: IInstance;
}

@Injectable({
  providedIn: 'root',
})
export class BannerService {
  // Banner state signal
  private bannerStateSignal = signal<BannerState>({
    visible: false,
    type: BannerType.NONE,
  });
  bannerVisible$ = new Subject<{ visible: boolean }>();

  // Expose the banner state as a readable signal
  public readonly bannerState = this.bannerStateSignal.asReadonly();

  constructor() {}

  /**
   * Show a banner indicating all required items are completed
   * @param instance The instance that has all required items completed
   */
  showCompletedAllBanner(instance: IInstance): void {
    this.bannerStateSignal.set({
      visible: true,
      type: BannerType.COMPLETED_ALL,
      instance,
    });

    this.bannerVisible$.next({ visible: true });
  }

  /**
   * Show a banner indicating some required items are completed
   * @param completed Number of completed items
   * @param totalCount Total number of required items
   * @param instance The instance that has some required items completed
   */
  showCompletedSomeBanner(completed: number, totalCount: number, instance: IInstance): void {
    this.bannerStateSignal.set({
      visible: true,
      type: BannerType.COMPLETED_SOME,
      completed,
      totalCount,
      instance,
    });

    this.bannerVisible$.next({ visible: true });
  }

  /**
   * Show a banner indicating minimum number of items required
   * @param minimumItems Minimum number of items that need to be completed
   */
  showMinimumRequiredBanner(minimumItems: number): void {
    this.bannerStateSignal.set({
      visible: true,
      type: BannerType.MINIMUM_REQUIRED,
      minimumItems,
    });

    this.bannerVisible$.next({ visible: true });
  }

  /**
   * Hide the banner
   */
  hideBanner(): void {
    this.bannerStateSignal.set({
      visible: false,
      type: BannerType.NONE,
    });

    this.bannerVisible$.next({ visible: false });
  }
}
