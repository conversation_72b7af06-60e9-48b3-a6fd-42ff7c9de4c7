.chip-list-container {
  mat-form-field {
    width: 100%;
    padding: 32px 16px 16px 16px;
  }
  mat-label {
    color: orange;
  }

  mat-chip-row {
    background-color: #181818 !important;
    color: white;
    height: auto;
    width: auto;
    border-radius: 10px;
    border: 1px solid black;
    padding: 5px 11px;
    white-space: initial;

    .name {
      color: white;
    }

    button {
      color: white;
    }
  }

  input {
    caret-color: transparent;
    cursor: pointer;
  }
  ::placeholder {
    color: white;
    font-size: 12px;
    padding: 10px;
  }
}

.mat-mdc-text-field-wrapper:not(.mdc-text-field--outlined) .mat-mdc-form-field-infix {
  padding-bottom: 5px;
  min-height: fit-content;
}
