.parent-container {
  position: relative;

  .view-options-row {
    margin-bottom: 10px;
    justify-content: space-between;
    align-items: center;

    .top-header {
      display: flex;
      justify-content: center;
      align-items: center;

      ion-item {
        border-radius: 4px;
        display: flex;
        height: 36px;
      }
    }
  }
}

.custom-table {
  width: 100%;
  border-collapse: separate !important;
  border-spacing: 0 10px;
  background-color: transparent;
  background: transparent;
  /* Space between rows */

  .example-element-row {
    border-radius: 5px;
    background-color: #444444;
    /* Green row background */
    color: white;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
    margin-top: 5px;
    // border-top: 5px solid #222222;
  }

  .mat-cell {
    color: white;
    text-overflow: ellipsis;
    white-space: normal;
    overflow: visible;
    padding: 8px 4px;
  }

  .mat-cell,
  .mat-header-cell {
    border-bottom: none;
    overflow: hidden;
    color: white;
    /* Remove default Material border */
  }

  .mat-header-cell {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .mat-mdc-header-row {
    color: white;
    font-weight: bold;
    border-bottom: 1px solid #424242;
    height: 35px;
    min-height: 30px;
    background: #333;
    border-radius: 5px;
    overflow: hidden;
  }

  .example-detail-expanded-row {
    background: #2e2e2e;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    overflow: hidden;
  }

  .example-element-detail {
    overflow: hidden;
    display: flex;
  }

  .example-expanded-row {
    border-bottom-right-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
    border-bottom: 1px solid #5a5653 !important;
  }

  .mat-column-select {
    padding-left: 0px;
    max-width: 40px;
    padding-right: 0px;
  }

  .mat-column-grade {
    max-width: 280px;
  }

  .mat-column-lastlogin {
    max-width: 180px;
  }

  .mat-column-progress {
    max-width: 200px;
  }

  .mat-column-role {
    max-width: 180px;
  }

  .mat-column-name {
    padding-left: 0px;
  }

  .mat-column-expandedDetail {
    padding: 0px;
  }

  .name-column {
    display: flex;
    flex-direction: column;
    word-break: break-word; // Added to ensure long names wrap properly
  }

  .yellow-name {
    color: var(--ion-color-primary);
    font-weight: bold;
  }

  .subheading {
    font-style: italic;
    color: var(--ion-color-medium);
  }

  .row-space-between {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    align-items: center;

    .notrequired {
      --background: #946b2e;
      color: white;
    }

    .required {
      --background: #c75050;
      color: white;
    }
  }

  app-people-table-user-assignments {
    width: 100%;
  }
}

.progress-indicator {
  display: flex;
  align-items: center;

  .progress-circle {
    position: relative;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .circular-chart {
    display: block;
    width: 100%;
    height: 100%;
  }

  .circle-bg {
    fill: none;
    stroke: transparent;
    stroke-width: 2.8;
  }

  .circle {
    fill: none;
    stroke: #4caf50;
    stroke-width: 2.8;
    stroke-linecap: round;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
  }

  .progress-text {
    margin-left: 10px;
    display: flex;
    align-items: center;
  }

  .items-count {
    font-size: 12px;
    white-space: nowrap;
  }

  .completed-count {
    font-weight: bold;
    color: white;
  }

  .remaining-text {
    font-weight: normal;
    color: #aaaaaa;
  }
}

.mat-mdc-header-cell {
  padding: 0px !important;
}

.mdc-data-table__cell {
  padding: 0px !important;
}

.load-more {
  font-size: 16px;
  color: white;
  margin-top: 20px;
  text-align: center;
  display: flex;
  justify-content: center;
  cursor: pointer;
}

::ng-deep .mdc-checkbox__background {
  border-color: var(--ion-color-medium) !important;
}

.selection-bar {
  position: fixed;
  bottom: 10px;
  background: #333333;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.3);
  left: 50%;
  transform: translateX(-50%);
  width: fit-content;
  height: 40px;
  border-radius: 20px;
  z-index: 999;

  .selected-container {
    border-right: 1px solid #181818;
    font-size: 15px;
    margin-right: 10px;
    height: 100%;
    line-height: 40px;
    padding-right: 10px;
    padding-left: 10px;

    ion-icon {
      margin-right: 5px;
      font-size: 20px;
      height: 20px;
      margin-bottom: -5px;
    }
  }

  .buttons-container {
    border-right: 1px solid #181818;
    height: 100%;
    padding-right: 10px;
    padding-left: 10px;
    align-content: center;

    ion-label {
      cursor: pointer;
      display: inline-flex;
      margin-right: 10px;
      align-items: center;
      color: rgba(250, 167, 0) !important;
      font-size: 15px;

      ion-icon {
        margin-right: 5px;
        font-size: 15px;
      }
    }
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;

  ion-spinner {
    --color: var(--ion-color-warning);
    transform: scale(1.5);
  }
}

.hide-column {
  width: 0;
  padding: 0;
  border: none;
  overflow: hidden;
  opacity: 0;
  max-width: 0 !important;
}

@media (max-width: 768px) {
  .grade-text {
    padding-right: 10px;
  }

  .grade-button::part(native) {
    --padding-start: 5px;
    --padding-end: 5px;
    padding-right: 5px;
  }

  .grade-icon {
    margin-left: 2px;
  }

  .mat-column-progress {
    max-width: 100px !important;
  }

  .progress-circle {
    rotate: 180deg  ;
  }

  .mat-cell {
    padding: 0px !important;
  }

  .responsive-hidden-header {
    display: none !important;
  }

  .custom-table {
    .mat-mdc-header-row {
      height: auto;
      min-height: 30px;
    }

    .mat-column-select {
      display: flex;
    }

    .mat-cell {
      padding: 10px 4px;
      height: auto;
    }

    .example-element-row {
      height: auto;
      padding-top: 7px;
      padding-bottom: 5px;
    }

    .yellow-name {
      white-space: normal;
      word-break: break-word;
    }
  }
}

@media (max-width: 480px) {
  .custom-table {
    border-spacing: 0 6px;

    .mat-cell {
      font-size: 0.9em;
    }
  }
}

.status-badge-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.status-badge {
  display: flex;
  align-items: center;
  padding: 0px 5px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.status-badge ion-icon {
  margin-right: 4px;
  font-size: 14px;
}

.completed {
  background-color: #dffce9;
  color: black;
  border-color: #4e9961;
}

.grade-text {
  padding-right: 10px;
}

.grading-required {
  background-color: #ffe8e8;
  color: black;
  border-color: #c75050;
}
