import { Component } from '@angular/core';
import { InstanceService } from '@app/core/services/instance-service';
import { PlayerViewInformationBaseComponent } from '../base/player-view-information.component';

@Component({
    selector: 'app-scorm-player-view-information-xs',
    templateUrl: './player-view-information.component.html',
    styleUrls: ['./player-view-information.component.scss'],
    standalone: false
})
export class PlayerViewInformationXsComponent extends PlayerViewInformationBaseComponent {
  constructor(instanceService: InstanceService) {
    super(instanceService);
  }
}
