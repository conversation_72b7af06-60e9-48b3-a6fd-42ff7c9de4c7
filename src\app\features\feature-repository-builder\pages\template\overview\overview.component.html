<ion-grid>
  <ion-row class="overview-row">
    <ion-col class="ion-padding" style="background-color: #333333" size="3">
      <p>Use these fields to update the basic information about this feature. This is what they would see in Workspace.</p>
    </ion-col>
    <ion-col size="9">
      <ion-content>
        <div class="feature-overview ion-padding">
          @if (featureForm) {
            <form [formGroup]="featureForm">
              <ion-grid>
                <ion-row>
                  <ion-col class="ion-padding-start">
                    <h2>Feature Overview</h2>
                    <p>This is the information that users will see when they come across the feature.</p>
                  </ion-col>
                  <ion-col size="2" class="save-button">
                    @if (showSave) {
                      <ion-button fill="clear" color="primary" (click)="saveFeature()">Save</ion-button>
                    }
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="4">
                    <app-text-input-control [label]="'Feature Code'" [placeHolder]="'Enter feature code'" [toolTip]="'The code for the feature'" formControlName="code"></app-text-input-control>
                  </ion-col>
                  <ion-col>
                    <app-text-input-control [label]="'Feature Name'" [placeHolder]="'Enter feature Name'" [toolTip]="'The title for the feature'" formControlName="title"></app-text-input-control>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col>
                    <app-text-input-control
                      [label]="'Feature Description'"
                      [placeHolder]="'Enter feature description'"
                      [toolTip]="'The description for the feature'"
                      formControlName="description"></app-text-input-control>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col>
                    <app-dynamic-text-input-control
                      [label]="'Feature Descriptors'"
                      [placeHolder]="'Enter feature descriptors'"
                      [toolTip]="'The descriptors for the feature'"
                      formControlName="featureDescriptors"></app-dynamic-text-input-control>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col>
                    <app-dynamic-text-input-control
                      [label]="'Instance Descriptors'"
                      [placeHolder]="'Enter instance descriptors'"
                      [toolTip]="'The descriptors for the instance'"
                      formControlName="instanceDescriptors"></app-dynamic-text-input-control>
                  </ion-col>
                </ion-row>
                <ion-row class="align-to-form">
                  <ion-col class="checkbox-row">
                    <ion-label title="Append object count to descriptors">Display Object Count </ion-label>
                    <ion-checkbox [formControlName]="'displayObjectCount'"></ion-checkbox>
                  </ion-col>
                </ion-row>
                <ion-row class="ion-align-items-end">
                  <ion-col size="11">
                    <app-text-input-control
                      [label]="'Feature Slug'"
                      [placeHolder]="'Add the feature`s slug here...'"
                      [toolTip]="'The feature`s slug'"
                      formControlName="featureSlug"></app-text-input-control>
                  </ion-col>
                  <ion-col>
                    <ion-button fill="clear" (click)="checkRoute()">Check</ion-button>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="4">
                    <app-file-upload-control formControlName="iconAssetId" [label]="'Icon'" [toolTip]="'The icon image for the feature'"></app-file-upload-control>
                  </ion-col>
                  <ion-col size="8">
                    <app-file-upload-control formControlName="coverMediaAssetId" [label]="'Feature Cover Media'" [toolTip]="'The cover media for the feature'"></app-file-upload-control>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="6">
                    <app-file-upload-control formControlName="textureAssetId" [label]="'Feature Texture'" [toolTip]="'The texture image for the feature'"></app-file-upload-control>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="6">
                    @if (journeyStage$ | async; as journeyStage) {
                      <app-select-option-control
                        [options]="journeyStage"
                        [label]="'Journey Stage'"
                        [placeHolder]="'--select--'"
                        [backgroundColor]="'#181818'"
                        [allowClear]="true"
                        formControlName="journeyStageId"
                        [toolTip]="'Choose the journey stage'"></app-select-option-control>
                    }
                  </ion-col>
                  <ion-col size="6">
                    @if (roleObjectives$ | async; as roleObjectives) {
                      <app-select-option-control
                        [options]="roleObjectives"
                        [label]="'Role Objective'"
                        [placeHolder]="'--select--'"
                        formControlName="roleObjectiveId"
                        [backgroundColor]="'#181818'"
                        [allowClear]="true"
                        [toolTip]="'Choose the role objective'"></app-select-option-control>
                    }
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="6">
                    @if (targetAudience$ | async; as targetAudience) {
                      <app-tag-select-option-control
                        [options]="targetAudience"
                        [label]="'Grade'"
                        [placeHolder]="'--select--'"
                        formControlName="targetAudienceId"
                        [backgroundColor]="'#181818'"
                        [allowClear]="true"
                        [limitTo]="2"
                        [toolTip]="'Choose the target audience'"></app-tag-select-option-control>
                    }
                  </ion-col>
                  <ion-col size="6">
                    @if (featureTypes$ | async; as featureTypes) {
                      <app-select-option-control
                        [options]="featureTypes"
                        [label]="'Feature Type'"
                        [placeHolder]="'--select--'"
                        formControlName="featureTypeId"
                        [backgroundColor]="'#181818'"
                        [allowClear]="true"
                        [toolTip]="'Choose the feature type'"></app-select-option-control>
                    }
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="6">
                    @if (featureCategory$ | async; as featureCategory) {
                      <app-select-option-control
                        [options]="featureCategory"
                        [label]="'Feature Category'"
                        [placeHolder]="'--select--'"
                        formControlName="featureCategoryId"
                        [backgroundColor]="'#181818'"
                        [allowClear]="true"
                        [toolTip]="'Choose the Continuum Timeline'"></app-select-option-control>
                    }
                  </ion-col>
                  <ion-col size="6">
                    @if (continuumTimeline$ | async; as continuumTimeline) {
                      <app-select-option-control
                        [options]="continuumTimeline"
                        [label]="'Continuum Timeline'"
                        [placeHolder]="'--select--'"
                        formControlName="continuumTimelineId"
                        [backgroundColor]="'#181818'"
                        [allowClear]="true"
                        [toolTip]="'Choose the Continuum Timeline'"></app-select-option-control>
                    }
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="6" class="checkbox-row">
                    <ion-label title="Is Full Width Page">Is Full Width Page </ion-label>
                    <ion-checkbox [formControlName]="'isFullWidth'"></ion-checkbox>
                  </ion-col>
                </ion-row>
              </ion-grid>
            </form>
          }
        </div>
      </ion-content>
    </ion-col>
  </ion-row>
</ion-grid>
