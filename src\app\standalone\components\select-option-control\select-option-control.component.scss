:host {
  .parent-container {
    border-radius: 9px;
    padding-left: 16px;
    /* line-height: 14px; */
    .reqAsterisk {
      color: #7f550c;
      font-size: 28px;
    }

    ion-button{
      position: absolute;
      right: 0px;
    }

    ion-text {
      color: red;
      font-style: italic;
      font-size: 14px;
    }

    ion-label {
      cursor: help !important;
    }

    ion-icon {
      cursor: help !important;
    }

    .identifier {
      background-color: #7f550c;
      border-radius: 0.2em;
      color: black;
      font-weight: bold;
      padding: 0.3em;
      margin-left: 0.4em;
    }

    .earning-criteria-ref-type-container {
      ion-input {
        border: 1px solid #4e4e4e;
        border-radius: 5px;
        color: white;
        font-size: 16px;
        --background: #242323;
        text-overflow: ellipsis !important;
      }
    }

    .normal {
      .inner-container {
        --background-hover: var(--background);
        --border-color: transparent;
        --color: white;
        font-size: 20px;
        font-family: 'Exo 2';
        --background: var(--background);
        width: 100%;
      }

      ion-select {
        border: 1px solid #4e4e4e;
        border-radius: 5px;
        margin-top: 0.5em;
        color: white;
        font-size: 16px;
        --padding-start: 8px !important;
        width: 100%;
      }

      ion-input {
        border: 1px solid #4e4e4e;
        border-radius: 5px;
        margin-top: 0.5em;
        color: white;
        font-size: 16px;
        --padding-start: 8px !important;
      }
    }

    .selected {
      .inner-container {
        --background-hover: var(--background);
        --border-color: transparent;
        --color: white;
        font-size: 20px;
        font-family: 'Roboto';
        --background: var(--background);
        width: 100%;
        --inner-padding-end: 0;
        --padding-start: 0;

        .label-header {
          width: fit-content;
          height: 19px;
          padding: 2px 11px 16px 11px;
          background: #f99e00;
          color: #000000;
          border-color: #f99e00;
          border-width: 1px;
          border-style: solid;
          border-radius: 3px 3px 0px 0px;
          font-family: 'Roboto';
          font-weight: bold;
          font-size: 16px;
          line-height: 1.1;
          letter-spacing: 0.3px;
          text-align: left;
        }

        ion-select {
          border: 1px solid #f99e00;
          border-radius: 0px 3px 3px 3px;
          font-size: 16px;
          --padding-start: 8px !important;
          width: 100%;
          background: #222;
          color: #aaaaaa;
          font-family: 'Roboto';
          font-weight: 400;
          font-size: 16px;
          line-height: 1.3;
          letter-spacing: 0.5px;
          text-align: left;
        }

        ion-input {
          border: 1px solid #4e4e4e;
          border-radius: 5px;
          color: white;
          font-size: 16px;
          --padding-start: 8px !important;
          box-shadow: 1px 1px 14px rgba(249, 158, 0, 0.15);
          background: #222;
          color: #aaaaaa;
          font-family: 'Roboto';
          font-weight: 400;
          font-size: 16px;
          line-height: 1.3;
          letter-spacing: 0.5px;
          text-align: left;
        }
      }
    }

    .no-padding {
      --inner-padding-end: 0;
      --padding-start: 0;
    }

    .no-border {
      ion-input {
        height: 100%;
        width: 100%;
        border: none !important;
      }
    }

    .side-panel-input-padding {
      --inner-padding-end: 10px;
      --padding-start: 10px;

      ion-select {
        height: 100%;
        width: 100%;
        border: none !important;
      }
    }

    .no-margin {
      ion-input {
        margin-top: 0px !important;
      }
    }
  }

  .floating {
    height: 50px;
    padding-top: 20px;

    ion-select {
      border-radius: 10px !important;
      padding-top: 10px !important;
      color: white !important;
      width: 350px;
    }

    .label-header {
      margin-bottom: -20px !important;
      z-index: 3 !important;
      margin-left: 10px !important;
      font-style: italic !important;
      font-size: 14px !important;
      color: #a0a0a0 !important;
    }

    ion-item {
      margin-top: -35px !important;
    }

    .view-options-col {
      height: fit-content !important;
    }
  }
}
