import { SelectOptionControlComponent } from '@app/standalone/components/select-option-control/select-option-control.component';
import { FeatureInstanceViewerComponent } from './components/feature-instance-viewer/feature-instance-viewer.component';
import { LinkFooterComponent } from './components/link-footer/link-footer.component';
import { PlayerViewInformationComponent } from './components/player-view-information/player-view-information.component';
import { PlayerViewSidePanelComponent } from './components/player-view-side-panel/player-view-side-panel.component';
import { PlayerViewLgComponent } from './components/player-view/lg/player-view.component';
import { PlayerViewComponent } from './components/player-view/player-view.component';
import { PlayerViewScormComponent } from './components/player-view/scorm/player-view.component';
import { PlayerViewXsComponent } from './components/player-view/xs/player-view.component';
import { ViewOptionsRowLgComponent } from './components/view-options-row/lg/view-options-row.component';
import { ViewOptionsRowComponent } from './components/view-options-row/view-options-row.component';
import { ViewOptionsRowXsComponent } from './components/view-options-row/xs/view-options-row.component';
import { HeadingValueComponent } from '@app/standalone/components/heading-value/heading-value.component';

export const featureComponents: any[] = [
  FeatureInstanceViewerComponent,
  ViewOptionsRowComponent,
  ViewOptionsRowLgComponent,
  ViewOptionsRowXsComponent,
  PlayerViewInformationComponent,
  PlayerViewSidePanelComponent,
  PlayerViewComponent,
  PlayerViewXsComponent,
  PlayerViewLgComponent,
  PlayerViewScormComponent,
  LinkFooterComponent,
];

export const standaloneComponents: any[] = [SelectOptionControlComponent, HeadingValueComponent];
