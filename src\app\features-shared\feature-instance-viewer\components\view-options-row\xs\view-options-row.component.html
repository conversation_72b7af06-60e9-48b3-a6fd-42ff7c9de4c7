@if (!hidden) {
  <div class="container" [class.page-margins-player]="selectedItem === 4" [class.page-margins]="selectedItem !== 4">
    <ion-grid class="grid-container">
      <ion-row class="view-options-row ion-nowrap">
        @if (!mobileMenuClosed) {
          <div class="view-options-col">
            @if (templateFields && templateFields.length > 0) {
              @for (field of templateFields; track field) {
                <ion-button (click)="openTagModal(field.tagTreeId, field.limitTo)">{{ field.label }}</ion-button>
              }
            }
            <ion-searchbar [(ngModel)]="searchValue" debounce="600" type="search" (ionChange)="filterRows($event)" placeholder="Search"></ion-searchbar>
          </div>
        }
        <div>
          <div class="main-btn-container">
            @if (selectedItem === 4) {
              <ion-button class="menu-button" (click)="openMobileMenu()">
                <ion-icon size="large" color="light" name="menu"></ion-icon>
              </ion-button>
            }
            @if (featureTab?.buttonText && featureTab.buttonText !== '' && featureTab.featureTabButtons.length > 0) {
              <div class="btn-container">
                <ion-button size="small" fill="solid" color="primary" (click)="buttonClicked($event)">{{ featureTab?.buttonText }}</ion-button>
              </div>
            }
          </div>
        </div>
      </ion-row>
    </ion-grid>
  </div>
}
