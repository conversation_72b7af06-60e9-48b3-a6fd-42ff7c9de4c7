import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IEarningCriteriaContentIn, IEarningCriteriaContentSearch, IEarningCriteriaIn } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { SelectOptionControlComponent } from '../../select-option-control/select-option-control.component';

@Component({
    selector: 'app-complete-atleast',
    templateUrl: './complete-atleast.component.html',
    styleUrls: ['./complete-atleast.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, SelectOptionControlComponent]
})
export class CompleteAtleastComponent implements OnInit, OnDestroy {
  @Input() type: string;
  @Input() earningCriteria: IEarningCriteriaIn;
  @Output() criteriaUpdated: EventEmitter<IEarningCriteriaContentIn[]> = new EventEmitter();

  formValueChanges$: Subscription;
  completeAtleastForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    this.completeAtleastForm = this.formBuilder.group({
      minValue: [this.earningCriteria?.minValue],
      refId: [this.earningCriteria.earningCriteriaContent?.[0]?.refId],
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.completeAtleastForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setObjectValues();
    });
  }

  setObjectValues() {
    if (this.completeAtleastForm.valid) {
      if (this.completeAtleastForm.controls['minValue'].value) {
        this.earningCriteria.minValue = this.completeAtleastForm.controls.minValue.value;
        this.criteriaUpdated.emit(this.earningCriteria.earningCriteriaContent);
      }
    }
  }

  getFormControlValue() {
    return this.completeAtleastForm.get('refId')?.value;
  }

  setSelectedContent(data: IEarningCriteriaContentSearch) {
    const criteriaContentIn = {
      id: this.earningCriteria.earningCriteriaContent?.[0]?.id,
      earningCriteriaId: this.earningCriteria.id,
      refId: data.id,
      type: data.type,
      name: data.name,
    } as IEarningCriteriaContentIn;

    const index = this.earningCriteria.earningCriteriaContent?.findIndex(x => x.id === criteriaContentIn.id);

    if (!index || index === -1) {
      if (this.earningCriteria.earningCriteriaContent) {
        this.earningCriteria.earningCriteriaContent.push(criteriaContentIn);
      } else {
        this.earningCriteria.earningCriteriaContent = [criteriaContentIn];
      }
    } else {
      this.earningCriteria.earningCriteriaContent[0] = criteriaContentIn;
    }

    this.criteriaUpdated.emit(this.earningCriteria.earningCriteriaContent);
  }

  checkInputLength(event: any) {
    const numberVal = Number(event.detail.value);
    if (numberVal < 0) {
      this.completeAtleastForm.controls.minValue.setValue(0);
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
