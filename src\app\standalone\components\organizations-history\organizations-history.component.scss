p {
    font-family: '<PERSON><PERSON>';
    text-shadow: 2px 2px #000;
}

.btn-container {
    --background: rgb(100, 100, 100);
    color: white;
    font-size: 12px;

    mat-icon {
        size: 12px;
    }
}
mat-expansion-panel {
    background-color: transparent;
    box-shadow: none;
    margin-top: 5px;

    .expansion-panel-header {
        background-color: rgba(68, 68, 68);
        height: 100%;
        border-radius: 8px;
        padding: 15px;

        .inner-panel {

            .sub-heading {
                font-style: italic;
                color: rgba(170, 170, 170);

                .expiry-date {
                    margin-left: 5px;
                    color: white;
                }

                .tag{
                    background: rgb(170, 170, 170);
                    color: #000;
                    margin-left: 10px;
                    padding: 4px 10px;
                    font-size: 12px;
                    border-radius: 5px;
                }
            }

            .sub-heading-margin{
                margin-top: 10px;
                margin-bottom: 5px;
            }
        }
    }
}

.load-more {
    font-size: 16px;
    color: white;
    text-align: center;
    display: flex;
    margin-top: 20px;
    justify-content: center;
    cursor: pointer;
}
