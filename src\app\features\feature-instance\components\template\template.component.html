@if (template$ | async; as template) {
  <div style="height: 100%" #container>
    @if ((featureTab.type?.name === 'Instance Builder' || featureTab.type?.name === 'Repository Builder') && routeParams?.viewType === viewTypes.Builder) {
      @if (instance && template) {
        <app-feature-instance-side-panel-builder [template]="template" [instance]="instance" [featureTab]="featureTab" [routeParams]="routeParams" [onlyContent]="onlyContent">
        </app-feature-instance-side-panel-builder>
      }
    } @else {
      @if (instance && template) {
        <app-feature-instance-viewer
          [containerTop]="container.getBoundingClientRect().top"
          [template]="template"
          [instance]="instance"
          [featureTab]="featureTab"
          [routeParams]="routeParams"
          [selectedUserId]="selectedUserId">
        </app-feature-instance-viewer>
      }
    }
  </div>
}
