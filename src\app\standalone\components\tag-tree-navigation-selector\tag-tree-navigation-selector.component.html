<ion-content>
  @if (currentLevel > 0 || hasSelectionChanges) {
    <ion-item [ngClass]="{ 'no-padding': noPadding, 'no-border': noBorder, 'side-panel-input-padding': sidePanelPadding }">
      @if (currentLevel > 0) {
        <ion-col class="ion-no-padding">
          <ion-button fill="clear" class="back-nav" (click)="getTagChildrenOnBackClick()">back</ion-button>
        </ion-col>
      }
      @if (hasSelectionChanges) {
        <ion-col class="ion-no-padding" style="display: flex; justify-content: flex-end">
          <ion-button fill="clear" color="primary" (click)="saveTagList()">Save</ion-button>
        </ion-col>
      }
    </ion-item>
  }
  @if (options.length > 0) {
    @for (option of options; track option) {
      <ion-item-group>
        <ion-item class="options-select" [ngClass]="{ 'no-padding': noPadding, 'no-border': noBorder, 'side-panel-input-padding': sidePanelPadding }">
          @if (multiple) {
            <ion-col size="2" class="checkbox-col">
              <ion-checkbox
                [indeterminate]="option.disabled"
                [checked]="option?.selected === true"
                [(ngModel)]="option.selected"
                (ionChange)="selectMultipleSelectedOption($event, option)"></ion-checkbox>
            </ion-col>
          }
          <ion-col [size]="!option.selected && option.hasChildren && (option.level ?? limitTo) <= limitTo ? 8 : 10">
            @if (!multiple) {
              <div id="option-value" (click)="singleSelectTag(option)">{{ option.value }}</div>
            } @else {
              {{ option.value }}
            }
          </ion-col>
          @if (!option.selected && option.hasChildren && (option.level ?? limitTo) <= limitTo) {
            <ion-col [size]="!option.selected && option.hasChildren && (option.level ?? limitTo) <= limitTo ? 4 : 2" (click)="getTagChildrenOnClick(option)">
              <ion-button fill="clear" class="ion-no-padding">
                <ion-icon name="add-circle-outline"></ion-icon>
              </ion-button>
            </ion-col>
          }
        </ion-item>
      </ion-item-group>
    }
  }
</ion-content>
