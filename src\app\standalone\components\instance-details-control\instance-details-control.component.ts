import { Component, forwardRef, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, NG_VALIDATORS, NG_VALUE_ACCESSOR, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { IComponent, IInstance, IInstanceDetailsValues, ITag } from '@app/core/contracts/contract';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { IonicModule } from '@ionic/angular';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { BuilderSidePanelDescriptionComponent } from '../builder-side-panel-description/builder-side-panel-description.component';
import { BuilderSidePanelHeadingComponent } from '../builder-side-panel-heading/builder-side-panel-heading.component';
import { FileUploadControlComponent } from '@app/standalone/components/file-upload-control/file-upload-control.component';
import { TextAreaInputControlComponent } from '../text-area-input-control/text-area-input-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { BaseControlComponent } from '../base-control/base-control.component';
import { Events } from '@app/core/services/events-service';
import { DataService } from '@app/core/services/data-service';
@Component({
    selector: 'app-instance-details-control',
    templateUrl: './instance-details-control.component.html',
    styleUrls: ['./instance-details-control.component.scss'],
    imports: [
        FormsModule,
        ReactiveFormsModule,
        IonicModule,
        BuilderSidePanelHeadingComponent,
        BuilderSidePanelDescriptionComponent,
        TextInputControlComponent,
        TextAreaInputControlComponent,
        FileUploadControlComponent,
    ],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => InstanceDetailsControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => InstanceDetailsControlComponent),
        },
    ]
})
export class InstanceDetailsControlComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() instance: IInstance;
  @Input() component: IComponent;
  @Input() override textValue: string | null;
  instanceForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;
  typeName = 'Instance';
  options = [
    { value: 'h1', label: 'Heading 1' },
    { value: 'h2', label: 'Heading 2' },
    { value: 'h3', label: 'Heading 3' },
    { value: 'h4', label: 'Heading 4' },
    { value: 'h5', label: 'Heading 5' },
    { value: 'h6', label: 'Heading 6' },
    { value: '', label: 'Default Style' },
  ];

  constructor(
    private systemPropertiesService: SystemPropertiesService,
    private eventService: Events,
    private dataService: DataService
  ) {
    super();
  }

  ngOnInit() {
    this.createSectionForm();
  }

  onHeadingStyleChanged(headingStyle: any) {
    this.instanceForm.patchValue({ headingStyle: headingStyle });
  }

  createSectionForm() {
    if (this.component.templateField.isInherit === false) {
      const instanceDetailsValue: IInstanceDetailsValues = this.textValue ? JSON.parse(this.textValue ?? '') : null;
      this.instanceForm = new UntypedFormGroup({
        iconAssetId: new UntypedFormControl(instanceDetailsValue?.iconAssetId ?? ''),
        title: new UntypedFormControl(instanceDetailsValue?.title ?? ''),
        description: new UntypedFormControl(instanceDetailsValue?.description ?? ''),
        headingStyle: new UntypedFormControl(this.component.templateField.headingStyle),
      });
    }
    // Do not like the org-profile check it is a hack......
    else if (this.instance.feature.featureType.name === 'Organization Manager' || this.instance.feature.featureSlug === 'org-profile') {
      this.typeName = 'Organization';
      this.instanceForm = new UntypedFormGroup({
        iconAssetId: new UntypedFormControl(this.systemPropertiesService.organizationProperties.find(x => x.key.indexOf('LogoAssetId') !== -1)?.value),
        title: new UntypedFormControl(this.systemPropertiesService.organizationProperties.find(x => x.key.indexOf('Name') !== -1)?.value),
        description: new UntypedFormControl(this.systemPropertiesService.organizationProperties.find(x => x.key.indexOf('OrganizationBio') !== -1)?.value),
      });
    } else if (this.instance.feature.featureType.name === 'User Manager') {
      this.typeName = 'User';
      this.instanceForm = new UntypedFormGroup({
        iconAssetId: new UntypedFormControl(this.systemPropertiesService.userProperties.find(x => x.key.indexOf('picture') !== -1)?.value),
        title: new UntypedFormControl(this.systemPropertiesService.userProperties.find(x => x.key.indexOf('name') !== -1)?.value),
        description: new UntypedFormControl(this.systemPropertiesService.userProperties.find(x => x.key.indexOf('family_name') !== -1)?.value),
      });
    } else if (this.instance.feature.featureType.name === 'Product Manager') {
      this.typeName = 'Product';
      this.instanceForm = new UntypedFormGroup({
        iconAssetId: new UntypedFormControl(this.systemPropertiesService.instanceProperties.find(x => x.key.indexOf('IconAssetId') !== -1)?.value),
        title: new UntypedFormControl(this.systemPropertiesService.productProperties.find(x => x.key.indexOf('Name') !== -1)?.value),
        description: new UntypedFormControl(this.systemPropertiesService.productProperties.find(x => x.key.indexOf('Description') !== -1)?.value),
      });
    } else {
      this.typeName = 'Instance';
      this.instanceForm = new UntypedFormGroup({
        iconAssetId: new UntypedFormControl(this.systemPropertiesService.instanceProperties.find(x => x.key.indexOf('IconAssetId') !== -1)?.value),
        title: new UntypedFormControl(this.systemPropertiesService.instanceProperties.find(x => x.key.indexOf('Title') !== -1)?.value),
        description: new UntypedFormControl(this.systemPropertiesService.instanceProperties.find(x => x.key.indexOf('Description') !== -1)?.value),
        headingStyle: new UntypedFormControl(this.component.templateField.headingStyle),
      });
    }

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.instanceForm) {
      return;
    }

    if (this.component.templateField.isInherit === false) {
      const instanceDetailsValue: IInstanceDetailsValues = this.textValue ? JSON.parse(this.textValue ?? '') : null;
      this.instanceForm.controls.iconAssetId.setValue(instanceDetailsValue?.iconAssetId ?? '');
      this.instanceForm.controls.title.setValue(instanceDetailsValue?.title ?? '');
      this.instanceForm.controls.description.setValue(instanceDetailsValue?.description ?? '');
      this.instanceForm.controls.headingStyle.setValue(this.component.templateField.headingStyle);
    } else if (this.instance.feature.featureType.name === 'Organization Manager' || this.instance.feature.featureSlug === 'org-profile') {
      const iconAssetIdIndex = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('LogoAssetId'));
      const titleIndex = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('Name'));
      const descriptionIndex = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('OrganizationBio'));

      this.instanceForm.controls.title.setValue(this.systemPropertiesService.organizationProperties[iconAssetIdIndex]?.value);
      this.instanceForm.controls.description.setValue(this.systemPropertiesService.organizationProperties[titleIndex]?.value);
      this.instanceForm.controls.hideBackground.setValue(this.systemPropertiesService.organizationProperties[descriptionIndex]?.value);
    } else if (this.instance.feature.featureType.name === 'User Manager') {
      const iconAssetIdIndex = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('picture'));
      const titleIndex = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('name'));
      const descriptionIndex = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('family_name'));

      this.instanceForm.controls.title.setValue(this.systemPropertiesService.userProperties[iconAssetIdIndex]?.value);
      this.instanceForm.controls.description.setValue(this.systemPropertiesService.userProperties[titleIndex]?.value);
      this.instanceForm.controls.hideBackground.setValue(this.systemPropertiesService.userProperties[descriptionIndex]?.value);
    } else if (this.instance.feature.featureType.name === 'Product Manager') {
      const iconAssetIdIndex = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('IconAssetId'));
      const titleIndex = this.systemPropertiesService.productProperties.findIndex(x => x.key.includes('Name'));
      const descriptionIndex = this.systemPropertiesService.productProperties.findIndex(x => x.key.includes('Description'));

      this.instanceForm.controls.title.setValue(this.systemPropertiesService.instanceProperties[iconAssetIdIndex]?.value);
      this.instanceForm.controls.description.setValue(this.systemPropertiesService.productProperties[titleIndex]?.value);
      this.instanceForm.controls.hideBackground.setValue(this.systemPropertiesService.productProperties[descriptionIndex]?.value);
    } else {
      const iconAssetIdIndex = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('IconAssetId'));
      const titleIndex = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Title'));
      const descriptionIndex = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Description'));
      this.instanceForm.controls.title.setValue(this.systemPropertiesService.instanceProperties[iconAssetIdIndex]?.value);
      this.instanceForm.controls.description.setValue(this.systemPropertiesService.instanceProperties[titleIndex]?.value);
      this.instanceForm.controls.hideBackground.setValue(this.systemPropertiesService.instanceProperties[descriptionIndex]?.value);
      this.instanceForm.controls.headingStyle.setValue(this.component.templateField.headingStyle);
    }
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.instanceForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.instanceForm.valid) {
        if (this.component.templateField.isInherit === false) {
          const instanceDetailsValue = {
            iconAssetId: this.instanceForm.controls.iconAssetId.value,
            title: this.instanceForm.controls.title.value,
            description: this.instanceForm.controls.description.value,
            headingStyle: this.instanceForm.controls.headingStyle.value,
          } as IInstanceDetailsValues;
          this.writeValue(JSON.stringify(instanceDetailsValue));
          this.eventService.publish('componentValueChanged', { componentId: this.component.id, value: JSON.stringify(instanceDetailsValue) });
        } else if (this.instance.feature.featureType.name === 'Organization Manager' || this.instance.feature.featureSlug === 'org-profile') {
          const iconAssetIdIndex = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('LogoAssetId'));
          const titleIndex = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('Name'));
          const descriptionIndex = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('OrganizationBio'));

          this.systemPropertiesService.organizationProperties[iconAssetIdIndex].value = this.instanceForm.controls.iconAssetId.value;
          this.systemPropertiesService.organizationProperties[titleIndex].value = this.instanceForm.controls.title.value;
          this.systemPropertiesService.organizationProperties[descriptionIndex].value = this.instanceForm.controls.description.value;
        } else if (this.instance.feature.featureType.name === 'User Manager') {
          const iconAssetIdIndex = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('picture'));
          const titleIndex = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('name'));
          const descriptionIndex = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('family_name'));

          this.systemPropertiesService.userProperties[iconAssetIdIndex].value = this.instanceForm.controls.iconAssetId.value;
          this.systemPropertiesService.userProperties[titleIndex].value = this.instanceForm.controls.title.value;
          this.systemPropertiesService.userProperties[descriptionIndex].value = this.instanceForm.controls.description.value;
        } else if (this.instance.feature.featureType.name === 'Product Manager') {
          const iconAssetIdIndex = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('IconAssetId'));
          const titleIndex = this.systemPropertiesService.productProperties.findIndex(x => x.key.includes('Name'));
          const descriptionIndex = this.systemPropertiesService.productProperties.findIndex(x => x.key.includes('Description'));

          this.systemPropertiesService.instanceProperties[iconAssetIdIndex].value = this.instanceForm.controls.iconAssetId.value;
          this.systemPropertiesService.productProperties[titleIndex].value = this.instanceForm.controls.title.value;
          this.systemPropertiesService.productProperties[descriptionIndex].value = this.instanceForm.controls.description.value;
        } else {
          const iconAssetIdIndex = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('IconAssetId'));
          this.systemPropertiesService.instanceProperties[iconAssetIdIndex].value = this.instanceForm.controls.iconAssetId.value;
          this.instance.iconAssetId = this.instanceForm.controls.iconAssetId.value;
          const titleIndex = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Title'));
          this.systemPropertiesService.instanceProperties[titleIndex].value = this.instanceForm.controls.title.value;
          this.instance.title = this.instanceForm.controls.title.value;
          const descriptionIndex = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Description'));
          this.systemPropertiesService.instanceProperties[descriptionIndex].value = this.instanceForm.controls.description.value;
          this.instance.description = this.instanceForm.controls.description.value;
          const updatedTemplateField = {
            ...this.component.templateField,
            headingStyle: this.instanceForm.controls.headingStyle.value,
            dropDownValues: this.component.templateField.dropDownValues?.map((tag: ITag) => tag.id) ?? [],
            parentIdSystemPropertyLink: this.component.templateField.parentIdSystemPropertyLink?.id,
          };
          this.dataService.updateTemplateField(updatedTemplateField).subscribe(() => {
            this.component.templateField = { ...this.component.templateField, headingStyle: this.instanceForm.controls.headingStyle.value };
            this.systemPropertiesService.reload$.next(null);
          });
        }
        this.instance.description = this.instanceForm.controls.description.value;
      }
      this.systemPropertiesService.reload$.next(null);
    });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
