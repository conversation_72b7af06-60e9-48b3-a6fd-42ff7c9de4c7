import { Component, Input, OnChang<PERSON>, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { IInstance, IInstanceSectionComponent, ITag, ITemplateField, IUserTag } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { TagService } from '@app/core/services/tag.service';
import { Observable, Subject, debounceTime, takeUntil } from 'rxjs';
import { NgClass } from '@angular/common';
import { TextValueComponent } from '../text-value/text-value.component';

@Component({
    selector: 'app-select-option-value',
    templateUrl: './select-option-value.component.html',
    styleUrls: ['./select-option-value.component.scss'],
    imports: [NgClass, TextValueComponent]
})
export class SelectOptionValueComponent implements OnInit, OnChanges, OnDestroy {
  @Input() instance: IInstance;
  @Input() instanceComponent: IInstanceSectionComponent | undefined;
  @Input() inheritedPropertyValue: string | null;
  @Input() templateField: ITemplateField | undefined;
  @Input() noPadding = false;

  tag$: Observable<ITag>;
  tagValue: ITag;
  linkTypeName: string;
  textValue: string;
  displayValue: string;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private dataService: DataService,
    private tagService: TagService
  ) {}

  ngOnInit() {
    this.setDisplayValue();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['inheritedPropertyValue'] || changes['instanceComponent']) {
      this.setDisplayValue();
    }
  }

  setDisplayValue() {
    this.textValue = this.inheritedPropertyValue ? this.inheritedPropertyValue : (this.instanceComponent?.value ?? '');
    this.linkTypeName = this.instanceComponent?.component?.templateField?.dropDownLinkType?.title ?? '';
    if (this.textValue) {
      switch (this.linkTypeName) {
        case 'Organizations':
          this.dataService
            .getOrganizationById(this.textValue)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe(organization => {
              this.displayValue = organization.name;
            });
          break;
        case 'Features':
          this.dataService
            .getFeatureById(this.textValue)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe(feature => {
              this.displayValue = feature.title;
            });
          break;
        case 'Products':
          this.dataService
            .getProductById(this.textValue)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe(product => {
              this.displayValue = product.name;
            });
          break;
        case 'Educators':
          this.dataService
            .getOrganizationEducators(this.instance?.organizationId ?? '')
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe(orgUsers => {
              this.displayValue = orgUsers.find(x => x.id === this.textValue)?.name ?? '';
            });
          break;
        case 'Instances':
          this.dataService
            .getInstance(this.textValue)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe(instance => {
              this.displayValue = instance.title;
            });
          break;
        default:
          if (this.tagService.isValidGUID(this.textValue)) {
            this.dataService
              .getTag(this.textValue)
              .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
              .subscribe(tag => {
                this.displayValue = tag?.name;
              });
          }
          break;
      }
    } else if (this.linkTypeName === 'User Tags') {
      this.dataService
        .getUserTags()
        .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
        .subscribe((userTags: IUserTag[]) => {
          this.displayValue = userTags.filter(x => x.type !== 'Persona')[0].tag?.name ?? '';
        });
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
