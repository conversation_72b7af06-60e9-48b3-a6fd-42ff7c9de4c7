import { Component, forwardRef, Input, OnDestroy, OnInit } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR, UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { NgClass } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { AuthService } from '@app/core/services/auth-service';

@Component({
    selector: 'app-full-name',
    templateUrl: './full-name.component.html',
    styleUrls: ['./full-name.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => FullNameComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => FullNameComponent),
        },
    ],
    imports: [NgClass, IonicModule, FormsModule, ReactiveFormsModule, TextInputControlComponent]
})
export class FullNameComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() description: string;
  @Input() override label: string;
  @Input() sidePanelPadding = false;
  componentDestroyed$: Subject<boolean> = new Subject();
  fullNameForm: UntypedFormGroup;
  formValueChanges$: Subscription;

  constructor(private systemPropertiesService: SystemPropertiesService, private authService: AuthService) {
    super();
  }

  ngOnInit() {
    this.createFormControls();
  }

  createFormControls() {
    if (!this.systemPropertiesService.userProperties) {
      this.fullNameForm = new UntypedFormGroup({
        fullName: new UntypedFormControl(''),
        firstName: new UntypedFormControl(''),
        lastName: new UntypedFormControl(''),
      });
      return;
    }

    const firstName = this.systemPropertiesService.userProperties.find(x => x.key.includes('given_name'));
    const fullName = this.systemPropertiesService.userProperties.find(x => x.key.includes('name'));
    const lastName = this.systemPropertiesService.userProperties.find(x => x.key.includes('family_name'));

    this.fullNameForm = new UntypedFormGroup({
      fullName: new UntypedFormControl(fullName?.value),
      firstName: new UntypedFormControl(firstName?.value),
      lastName: new UntypedFormControl(lastName?.value),
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.fullNameForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      const firstName = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('given_name'));
      this.systemPropertiesService.userProperties[firstName].value = this.fullNameForm.controls.firstName.value;
      const fullName = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('name'));
      this.systemPropertiesService.userProperties[fullName].value = this.fullNameForm.controls.fullName.value;
      const lastName = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('family_name'));
      this.systemPropertiesService.userProperties[lastName].value = this.fullNameForm.controls.lastName.value;

      // Refresh the user context on the client
      if (this.authService.userContext) {
        this.authService.userContext.fullName = this.fullNameForm.controls.firstName.value + ' ' + this.fullNameForm.controls.lastName.value;;
        localStorage.setItem('user_context', JSON.stringify(this.authService.userContext));
      }
    });
  }

  setFormControlValues() {
    const firstName = this.systemPropertiesService.userProperties.find(x => x.key.includes('given_name'));
    const fullName = this.systemPropertiesService.userProperties.find(x => x.key.includes('name'));
    const lastName = this.systemPropertiesService.userProperties.find(x => x.key.includes('family_name'));

    this.fullNameForm.controls.addressLine1.setValue(fullName?.value);
    this.fullNameForm.controls.country.setValue(firstName?.value);
    this.fullNameForm.controls.state.setValue(lastName?.value);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
