.parent-container {
  .inner-container {
    margin-top: 0.2em;
    font-size: 20px;
    font-family: 'Exo 2';
    --border-color: transparent;
    --color: white;
    --background: transparent;

    ion-textarea {
      border: 1px solid #4e4e4e;
      border-radius: 5px;
      font-size: 16px;
      margin-top: 2px;
      --background: #373737;
      --color: #fff;
      --padding-end: 10px;
      --padding-start: 10px;
      --placeholder-color: #ddd;
      --placeholder-opacity: 0.8;
      background-color: var(--background-color);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    ion-text {
      color: red;
      font-style: italic;
      font-size: 14px;
    }

    ion-label {
      margin-bottom: 0.2em;
      cursor: help !important;
      padding-bottom: 0.04em;
      font-weight: 500;
      letter-spacing: 0.03em;
    }

    .reqAsterisk {
      color: #7f550c;
      font-size: 28px;
    }

    .identifier {
      background-color: #7f550c;
      border-radius: 0.2em;
      color: black;
      font-weight: bold;
      padding: 0.3em;
      margin-left: 0.4em;
    }
  }

  .no-padding {
    --inner-padding-end: 0;
    --padding-start: 0;
  }

  .no-border {
    ion-textarea {
      height: 100%;
      width: 100%;
      border: none !important;
    }
  }

  .side-panel-input-padding {
    --inner-padding-end: 0;
    --padding-start: 0;

    ion-textarea {
      height: 100%;
      width: 100%;
      border: none !important;
    }
  }
}