.top-container {
  display: flex;
  justify-content: flex-end;
  flex-direction: row;

  ion-col {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    height: 60px;
  }

  .start-col-search {
    justify-content: flex-start;

    form {
      width: 100%;
    }
  }

  .select-option {
    margin-top: -5px;
  }

  .end-col-buttons {
    justify-content: flex-end;

    .buttons{
      display:flex;
      gap: 10px;
    }
  }
}

.scroll-cap {
  margin-top: 300px;
}

.feature-repository-dashboard-container {
  position: relative;
  height: calc(100vh - 230px);
  overflow: hidden;
}

@media (max-width: 959px) {
  .feature-repository-dashboard-container {
    height: calc(100vh - 280px) !important;
  }
}
