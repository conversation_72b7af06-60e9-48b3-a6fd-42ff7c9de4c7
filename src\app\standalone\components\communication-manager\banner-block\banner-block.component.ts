import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ICommunicationBlock, ITag } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { forkJoin, Subject, Subscription, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { ContentQuillEditorComponent } from '../../content-quill-editor/content-quill-editor.component';
import { SelectOptionControlComponent } from '../../select-option-control/select-option-control.component';

@Component({
    selector: 'app-banner-block',
    templateUrl: './banner-block.component.html',
    styleUrls: ['./banner-block.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, TextInputControlComponent, ContentQuillEditorComponent, SelectOptionControlComponent]
})
export class BannerBlockComponent implements OnInit, OnDestroy {
  @Input() communicationId: string;
  @Input() bannerBlock: ICommunicationBlock | null;
  @Output() communicationBlockUpdated: EventEmitter<ICommunicationBlock> = new EventEmitter();
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  bannerBlockForm: UntypedFormGroup;
  formValueChanges$: Subscription;
  backgroundColor = '#181818';
  hideQuillPersonalize = true;
  notificationTypes: KeyValue[];
  behaviours: KeyValue[];
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit() {
    this.getData();
  }

  getData() {
    const requestList = [this.dataService.getTagChildrenByParentName('Notification Types'), this.dataService.getTagChildrenByParentName('Behaviours')];
    forkJoin(requestList).subscribe(data => {
      if (data) {
        if (data[0]) {
          this.notificationTypes = (data[0] as ITag[]).map(x => ({ id: x.id, value: x.name }) as KeyValue);
        }

        if (data[1]) {
          this.behaviours = (data[1] as ITag[]).map(x => ({ id: x.id, value: x.name }) as KeyValue);
        }
      }

      this.createForm();
    });
  }

  createForm() {
    this.bannerBlockForm = this.formBuilder.group({
      title: [this.bannerBlock?.title],
      message: [this.bannerBlock?.message],
      behaviour: [this.bannerBlock?.actionTypeId],
      notificationType: [this.bannerBlock?.notificationTypeId],
    });

    this.subscribeToFormChanges();
  }

  setMessageValue(quillData: any) {
    this.bannerBlockForm.controls.message.setValue(quillData);
  }

  setObjectValues() {
    if (this.bannerBlockForm.valid) {
      let communicationBlock = {
        id: this.bannerBlock?.id,
        communicationId: this.communicationId,
        title: this.bannerBlockForm.controls.title.value,
        message: this.bannerBlockForm.controls.message.value,
        notificationTypeId: this.bannerBlockForm.controls.notificationType.value,
        actionTypeId: this.bannerBlockForm.controls.behaviour.value,
        blockType: 'Banner',
      } as ICommunicationBlock;

      //Merge
      if (this.bannerBlockForm) {
        communicationBlock = { ...this.bannerBlock, ...communicationBlock };
      }

      this.communicationBlockUpdated.emit(communicationBlock);
    }
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.bannerBlockForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.bannerBlockForm.valid);
      this.setObjectValues();
    });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
