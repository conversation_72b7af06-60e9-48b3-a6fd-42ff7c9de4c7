<div class="parent-container">
  <div [ngClass]="['text-container', stylingDirection]">
    @if (instanceComponent?.value || headingModal?.text) {
      @if (headingStyle === 'h1') {
        <h1 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ headingModal.text }}
        </h1>
      }
      @if (headingStyle === 'h2') {
        <h2 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ headingModal.text }}
        </h2>
      }
      @if (headingStyle === 'h3') {
        <h3 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ headingModal.text }}
        </h3>
      }
      @if (headingStyle === 'h4') {
        <h4 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ headingModal.text }}
        </h4>
      }
      @if (headingStyle === 'h5') {
        <h5 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ headingModal.text }}
        </h5>
      }
      @if (headingStyle === 'h6') {
        <h6 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ headingModal.text }}
        </h6>
      }
      @if (headingStyle === undefined || headingStyle === '') {
        <span class="heading" [style.font-size.px]="fontSize" [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ headingModal.text }}
        </span>
      }
    }
    @if (inheritedPropertyValue) {
      @if (headingStyle === 'h1') {
        <h1 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ inheritedPropertyValue }}
        </h1>
      }
      @if (headingStyle === 'h2') {
        <h2 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ inheritedPropertyValue }}
        </h2>
      }
      @if (headingStyle === 'h3') {
        <h3 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ inheritedPropertyValue }}
        </h3>
      }
      @if (headingStyle === 'h4') {
        <h4 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ inheritedPropertyValue }}
        </h4>
      }
      @if (headingStyle === 'h5') {
        <h5 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ inheritedPropertyValue }}
        </h5>
      }
      @if (headingStyle === 'h6') {
        <h6 [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ inheritedPropertyValue }}
        </h6>
      }
      @if (headingStyle === undefined || headingStyle === '') {
        <span class="heading" [style.font-size.px]="fontSize" [ngClass]="{ darkText: darkText }" [style.color]="color">
          {{ inheritedPropertyValue }}
        </span>
      }
    }
    @if (!inheritedPropertyValue && !instanceComponent?.value && defaultText) {
      <span class="heading" [style.font-size.px]="fontSize" [ngClass]="{ darkText: darkText }" [style.color]="color"> {{ defaultText }} </span>
    }
    @if (!inheritedPropertyValue && !instanceComponent?.value && !defaultText && !headingModal.text) {
      <span class="heading" [style.font-size.px]="fontSize" [ngClass]="{ darkText: darkText }" [style.color]="color"> Heading </span>
    }
    @if (headingModal.chipText) {
      <ion-chip>{{ headingModal.chipText }}</ion-chip>
    }
  </div>
  @if (headingModal.description) {
    <div [ngClass]="['text-container', stylingDirection]">
      <span class="description" [ngClass]="{ darkText: darkText }">{{ headingModal.description }}</span>
    </div>
  }
</div>
