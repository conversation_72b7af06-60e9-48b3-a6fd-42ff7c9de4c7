import { Component, Input, OnInit } from '@angular/core';
import { IInstance, IRouteParams } from '@app/core/contracts/contract';
import { InstanceService } from '@app/core/services/instance-service';
import { RowContentHoverValues } from '@app/shared/directives/row-content-hover.directive';
import { environment } from '@env/environment';
import { map, Observable } from 'rxjs';

@Component({
    selector: 'app-instance-details-expander',
    templateUrl: './instance-details-expander.component.html',
    styleUrls: ['./instance-details-expander.component.scss'],
    standalone: false
})
export class InstanceDetailsExpanderComponent implements OnInit {
  @Input() routeParams: IRouteParams;
  imageUrl: string;
  iconUrl: string;
  instance$: Observable<IInstance>;
  rowContentHoverValues: RowContentHoverValues;

  constructor(private instanceService: InstanceService) {}

  ngOnInit() {
    this.instance$ = this.instanceService.getInstance(this.routeParams.instanceSlug).pipe(
      map((instance: IInstance) => {
        if (instance.coverAssetId) {
          this.imageUrl = `${environment.contentUrl}asset/${instance.coverAssetId}/content`;
        } else {
          this.imageUrl = 'assets/images/no-image.png';
        }

        if (instance.feature.iconAssetId) {
          this.iconUrl = `${environment.contentUrl}asset/${instance.feature.iconAssetId}/content`;
        } else {
          this.iconUrl = 'assets/images/no-image.png';
        }

        return instance;
      })
    );
  }

  updateRowContentHoverValues(value: any) {
    this.rowContentHoverValues = value;
  }
}
