import { Injectable } from '@angular/core';
import { AlertController } from '@ionic/angular';

@Injectable({
  providedIn: 'root',
})
export class AlertService {
  constructor(private alertController: AlertController) {}

  presentAlert(header: string, message: string): Promise<void> {
    return new Promise(resolve => {
      this.alertController
        .create({
          header,
          message,
          cssClass: 'html-alert',
          buttons: [
            {
              text: 'Cancel',
              role: 'cancel',
            },
            {
              text: 'OK',
              handler: () => resolve(),
            },
          ],
        })
        .then(res => {
          res.present();
        });
    });
  }

  infoAlert(header: string, message: string): Promise<void> {
    return new Promise(resolve => {
      this.alertController
        .create({
          header,
          message,
          buttons: [
            {
              text: 'OK',
              handler: () => resolve(),
            },
          ],
        })
        .then(res => {
          res.present();
        });
    });
  }

  unsavedChangesAlert(header: string, message: string): Promise<void> {
    return new Promise(resolve => {
      this.alertController
        .create({
          header,
          message,
          buttons: [
            {
              text: 'Keep Editing',
              role: 'cancel',
            },
            {
              text: 'Discard',
              handler: () => resolve(),
            },
          ],
        })
        .then(res => {
          res.present();
        });
    });
  }

  leaveBuilderAlert(header: string, message: string): Promise<void> {
    return new Promise(resolve => {
      this.alertController
        .create({
          header,
          message,
          buttons: [
            {
              text: 'Keep Editing',
              role: 'cancel',
            },
            {
              text: 'Close Builder',
              handler: () => resolve(),
            },
          ],
        })
        .then(res => {
          res.present();
        });
    });
  }

  presentAlertOptions(header: string, message: string): Promise<boolean> {
    return new Promise(resolve => {
      this.alertController
        .create({
          header,
          message,
          buttons: [
            {
              text: 'Cancel',
              handler: () => resolve(false),
            },
            {
              text: 'OK',
              handler: () => resolve(true),
            },
          ],
        })
        .then(res => {
          res.present();
        });
    });
  }
}
