@if (sectionForm) {
  <form [formGroup]="sectionForm" class="parent-form-container" [ngClass]="{ 'no-margin': panelBuilder }">
    <ion-grid>
      <ion-row>
        @if (!panelBuilder) {
          <ion-col class="back-col">
            <ion-button fill="clear" size="small" color="warning" (click)="back()"> <ion-icon slot="start" name="chevron-back-outline"></ion-icon>Back</ion-button>
          </ion-col>
        }
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" formControlName="title" [label]="'Section title'"></app-text-input-control>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
