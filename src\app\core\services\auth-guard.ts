import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { map, Observable } from 'rxjs';
import { AuthService } from './auth-service';

@Injectable()
export class AuthGuard {
  constructor(private authService: AuthService) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    if (!sessionStorage.getItem('returnUrl') || state.url !== '/') {
      sessionStorage.setItem('returnUrl', state.url);
    }
    const hasPersonaTags = this.authService.userContext?.hasPersonaTags;
    return this.authService.canActivateRoute().pipe(
      map(res => {
        if (!res && route.data?.viewType !== 3) {
          this.authService.openGuestModel$.next(state.url);
        } else if (!hasPersonaTags && route.data?.viewType !== 3) {
          this.authService.openPersonaSelector$.next(state.url);
        }
        return res;
      })
    );
  }
}
