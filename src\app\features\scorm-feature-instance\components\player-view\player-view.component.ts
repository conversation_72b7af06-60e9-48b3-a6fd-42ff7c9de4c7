import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IFeatureTab, IInstance, IInstanceTemplate, IRouteParams } from '@app/core/contracts/contract';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-player-view',
    templateUrl: './player-view.component.html',
    styleUrls: ['./player-view.component.scss'],
    standalone: false
})
export class PlayerViewComponent {
  @Input() instanceTemplate: IInstanceTemplate;
  @Input() instance: IInstance;
  @Input() featureTab: IFeatureTab;
  @Input() routeParams: IRouteParams;
  @Input() isScorm = false;
  @Input() searchFilter: string;
  @Output() buttonChanged = new EventEmitter<number>();
  @Output() optionSelected = new EventEmitter<number>();
  constructor(public layoutService: LayoutService) {}

  buttonClicked(option: any) {
    this.buttonChanged.emit(option);
  }

  setSelectedRowType(option: any) {
    this.optionSelected.emit(option);
  }
}
