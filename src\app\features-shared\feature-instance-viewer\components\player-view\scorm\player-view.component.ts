import { Component, ElementRef, Renderer2 } from '@angular/core';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RolesService } from '@app/core/services/roles.service';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { PlayerViewBaseComponent } from '../base/player-view.component';
import { LayoutService } from '@app/core/services/layout-service';
import { DataService } from '@app/core/services/data-service';
import { AuthService } from '@app/core/services/auth-service';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { BannerService } from '@app/core/services/banner.service';

@Component({
  selector: 'app-player-view-scorm',
  templateUrl: './player-view.component.html',
  styleUrls: ['./player-view.component.scss'],
  standalone: false,
})
export class PlayerViewScormComponent extends PlayerViewBaseComponent {
  constructor(
    instanceService: InstanceService,
    eventService: Events,
    globalToastService: GlobalToastService,
    renderer: Renderer2,
    el: ElementRef,
    layoutService: LayoutService,
    dataService: DataService,
    authService: AuthService,
    breadcrumbService: BreadcrumbService,
    rolesService: RolesService,
    bannerService: BannerService
  ) {
    super(instanceService, eventService, globalToastService, renderer, el, layoutService, dataService, authService, breadcrumbService, rolesService, bannerService);
  }
}
