import { Injectable } from '@angular/core';
import { AddToDialogComponent } from '@app/standalone/modals/add-to-dialog/add-to-dialog.component';
import { ModalController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { GlobalToastService } from './global-toast.service';
import { IAchievementCompletion, IEngagementIn } from '../contracts/contract';
import { EngagementTypes } from '../enums/engagment-types.enum';
import { DataService } from './data-service';

@Injectable({
  providedIn: 'root',
})
export class RowService {
  reload$: Subject<string> = new Subject();
  firstUngradedRowLoad$: Subject<string> = new Subject();
  constructor(
    private dataService: DataService,
    private modalController: ModalController,
    private toast: GlobalToastService
  ) {}

  public async openAddInstanceToRow(instanceId: string, achievementCompletion?: IAchievementCompletion, featureType: string = 'Learning Objects') {
    const modal = await this.modalController.create({
      component: AddToDialogComponent,
      componentProps: { instanceId: instanceId, achievementCompletion: achievementCompletion, featureTypeName: featureType },
      cssClass: 'my-instances-dialog',
    });

    modal.onDidDismiss().then(value => {
      if (value?.data) {
        // this.addRowContent(value.data.rowId, instanceId, value.data.selectedInstance.id, value.data.selectedInstance.featureType);
      }
    });

    await modal.present();
  }

  public addRowContent(rowId: string, instanceId: string, selectedInstanceId: string, featureType: string) {
    this.dataService.addRowContent(rowId, instanceId).subscribe(data => {
      if (data) {
        this.dataService.addInstanceEngagement({ instanceId: instanceId, engagementType: EngagementTypes.Add, nominalValue: 1, percentageValue: 100 } as IEngagementIn).subscribe(() => {
          this.toast.addToNotificationToast(selectedInstanceId, featureType);
        });
        this.reload$.next(rowId);
      } else {
        this.toast.presentToast(`Already added to ${featureType}!`);
      }
      this.reload$.next(rowId);
    });
  }

  public getMaxColumnCount(thumbNailType: string): number {
    const aspectRatio = AspectRatios.find(aspectRatio => aspectRatio.type === thumbNailType);
    return aspectRatio?.columnCount ?? 6;
  }

  public setupVariables(thumbNailType: string, width: number) {
    const data = AspectRatios.find(x => {
      if (x.type === thumbNailType) {
        if (thumbNailType === 'Portrait') {
          if (width >= 1501) x.columnCount = 6;
          else if (width <= 1500 && width >= 993) x.columnCount = 5;
          else if (width <= 992 && width >= 480) x.columnCount = 3;
          else x.columnCount = 2;

          return x;
        }

        if (thumbNailType === 'Landscape' || thumbNailType === 'Photo blocks' || thumbNailType === 'Photo' || thumbNailType === 'Photo + Text') {
          if (width >= 1501) x.columnCount = 5;
          else if (width <= 1500 && width >= 993) x.columnCount = 4;
          else if (width <= 992 && width >= 769) x.columnCount = 3;
          else x.columnCount = 2;

          return x;
        }

        if (thumbNailType === 'Text container' || thumbNailType === 'Narrow container') {
          x.columnCount = 1;

          return x;
        }

        if (thumbNailType === 'Icon') {
          if (width >= 993) x.columnCount = 4;
          else if (width <= 992 && width >= 480) x.columnCount = 3;
          else x.columnCount = 2;

          return x;
        }

        if (thumbNailType === 'Badge') {
          if (width >= 993) x.columnCount = 4;
          else if (width <= 992 && width >= 769) x.columnCount = 3;
          else if (width <= 768 && width >= 480) x.columnCount = 2;
          else x.columnCount = 1;

          return x;
        }
        if (thumbNailType === 'Big content') {
          if (width >= 480) x.columnCount = 2;
          else x.columnCount = 1;

          return x;
        }
        return x;
      }
      return undefined;
    });
    return data;
  }
}
export interface IAspectRatio {
  type: string;
  ratio: string;
  columnCount: number;
}

export const AspectRatios: IAspectRatio[] = [
  {
    type: 'Landscape',
    ratio: '3 / 2',
    columnCount: 5,
  },
  {
    type: 'Portrait',
    ratio: '5 / 4',
    columnCount: 6,
  },
  {
    type: 'Big content',
    ratio: '3 / 2',
    columnCount: 2,
  },
  {
    type: 'Icon',
    ratio: '6 / 1',
    columnCount: 4,
  },
  {
    type: 'Photo',
    ratio: '21 / 9',
    columnCount: 5,
  },
  {
    type: 'Photo container',
    ratio: '9 / 2',
    columnCount: 1,
  },
  {
    type: 'Text container',
    ratio: '9 / 2',
    columnCount: 1,
  },
  {
    type: 'Narrow container',
    ratio: '15 / 9',
    columnCount: 1,
  },
  {
    type: 'Photo + Text',
    ratio: '21 / 9',
    columnCount: 5,
  },
  {
    type: 'Photo blocks',
    ratio: '21 / 9',
    columnCount: 5,
  },
  {
    type: 'Badge',
    ratio: '9 / 2',
    columnCount: 4,
  },
];
