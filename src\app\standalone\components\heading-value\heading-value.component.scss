.parent-container {
  padding: 25px 0px;

  h1 {
    text-align: left;
    color: #fff;
    font-family: 'Exo 2';
    font-weight: 800;
    font-size: calc(40px + 0.22vw);
    letter-spacing: 0.03em;
    line-height: 1.1;
  }

  h2 {
    text-align: left;
    color: #fff;
    font-family: 'Exo 2';
    font-weight: 800;
    font-size: calc(35px + 0.22vw);
    letter-spacing: 0.03em;
    line-height: 1.1;
  }

  h3 {
    text-align: left;
    color: #fff;
    font-family: 'Exo 2';
    font-weight: 800;
    font-size: calc(29px + 0.22vw);
    letter-spacing: 0.03em;
    line-height: 1.1;
  }

  h4 {
    text-align: left;
    color: #fff;
    font-family: 'Exo 2';
    font-weight: 800;
    font-size: calc(24px + 0.22vw);
    letter-spacing: 0.03em;
    line-height: 1.1;
  }

  h5 {
    text-align: left;
    color: #fff;
    font-family: 'Exo 2';
    font-weight: 800;
    font-size: calc(20px + 0.22vw);
    letter-spacing: 0.03em;
    line-height: 1.1;
  }

  h6 {
    text-align: left;
    color: #fff;
    font-family: 'Exo 2';
    font-weight: 800;
    font-size: calc(18px + 0.22vw);
    letter-spacing: 0.03em;
    line-height: 1.1;
  }

  .text-container {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .Center {
    text-align: center;
    justify-content: center;
  }

  .Left {
    text-align: start;
    justify-content: flex-start;
  }

  .Right {
    text-align: end;
    justify-content: flex-end;
  }

  ion-chip {
    padding: 0px 8px;
    /* Adjust padding as needed */

    font-size: 12px;
    /* Adjust font size as needed */

    min-height: 20px;
    /* Adjust height as needed */
  }

  .heading {
    color: #f7f6f6;
    font-family: 'Roboto';
    font-weight: 700;
    line-height: 1.4;
    letter-spacing: 0.3px;
  }

  .darkText {
    color: black !important;
  }

  .description {
    color: #aaaaaa;
    letter-spacing: 0.2px;
  }
}