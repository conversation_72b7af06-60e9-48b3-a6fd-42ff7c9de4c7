import { Component, EventEmitter, Input, OnChang<PERSON>, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-google-maps-autocomplete-field-editor',
    templateUrl: './google-maps-autocomplete-field-editor.component.html',
    styleUrls: ['./google-maps-autocomplete-field-editor.component.scss'],
    standalone: false
})
export class GoogleMapsAutocompleteFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  form: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.form.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }

      this.setFormValues();
    }
  }

  createForm() {
    this.form = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      description: [this.component?.templateField?.helpDescription],
      placeHolderText: [this.component?.templateField?.placeHolderText],
      caption: [this.component?.templateField?.helpTitle],
      rowNumber: [this.component?.builderRowNumber ?? 0],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      systemProperty: [this.component?.templateField?.systemProperty?.id],
      isInherit: [this.component?.templateField?.isInherit ?? false],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      showMap: [this.component?.templateField?.showMap ?? false],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.form) {
      return;
    }

    this.form.controls.label.setValue(this.component.templateField.label);
    this.form.controls.description.setValue(this.component.templateField.helpDescription);
    this.form.controls.placeHolderText.setValue(this.component.templateField.placeHolderText);
    this.form.controls.caption.setValue(this.component.templateField.helpTitle);
    this.form.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.form.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.form.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.form.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.form.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.form.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.form.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.form.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.form.controls.systemProperty.setValue(this.component.templateField.systemProperty);
    this.form.controls.isInherit.setValue(this.component.templateField.isInherit);
    this.form.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.form.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.form.controls.isInherit.setValue(this.component?.templateField?.isInherit);
    this.form.controls.isVisibleRepository.setValue(this.component?.templateField?.isVisibleRepository);
    this.form.controls.showMap.setValue(this.component?.templateField?.showMap);
    this.form.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.form.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.form.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.form.valid) {
      this.component.templateField.label = this.form.controls.label.value;
      this.component.templateField.helpDescription = this.form.controls.description.value;
      this.component.templateField.placeHolderText = this.form.controls.placeHolderText.value;
      this.component.templateField.helpTitle = this.form.controls.caption.value;
      this.component.builderRowNumber = this.form.controls.rowNumber.value;
      this.component.instanceSortOrder = this.form.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.form.controls.isRequiredField.value;
      this.component.templateField.isVisibleRepository = this.form.controls.isVisibleRepository.value;
      this.component.templateField.isBuilderEnabled = this.form.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.form.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.form.controls.isHoverField.value;
      this.component.templateField.isViewField = this.form.controls.isViewField.value;
      this.component.templateField.isInherit = this.form.controls.isInherit.value;
      this.component.templateField.colspan = this.form.controls.colspan.value;
      this.component.templateField.colNumber = this.form.controls.colNumber.value;
      this.component.templateField.isInherit = this.form.controls.isInherit.value;
      this.component.templateField.showMap = this.form.controls.showMap.value;
      this.component.templateField.useMaxWidth = this.form.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
