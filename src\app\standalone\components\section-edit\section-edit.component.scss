.parent-form-container {
  margin: 10px 16px 10px 16px;

  .back-col {
    display: flex;
    justify-content: flex-start;

    ion-button.button::part(native) {
      line-height: 0px;
    }
  }

  .top-header-col {
    margin-top: 10px;

    .header {
      color: white;
      font-family: '<PERSON><PERSON>';
      font-weight: bold;
      font-size: 25px;
    }
  }

  .middle-line {
    margin-top: 20px;

    hr {
      border-top: 3px solid rgb(40, 40, 40);
    }
  }

  .toggle-row {
    padding-top: 10px;

    .toggle-col {
      display: flex;

      align-items: center;

      .mat-mdc-slide-toggle {
        vertical-align: middle;
        margin-right: 15px;
      }

      ion-label {
        font-size: 18px;
        color: white;
        font-family: '<PERSON><PERSON>';
        font-weight: bold;
      }
    }
  }

  .label-header {
    margin-bottom: 0.2em;
  }

  .normal {
    cursor: pointer;

    ion-item {
      --border-color: transparent; // default underline color
      --color: white;
      font-size: 20px;
      font-family: 'Exo 2';
      --background: var(--background);
    }

    .inner-container {
      --border-color: transparent;
      --color: white;
      font-size: 20px;
      font-family: 'Roboto';
      --background: var(--background);
      width: 100%;
      --inner-padding-end: 0;
      --padding-start: 0;

      .label-header {
        margin-bottom: 0.2em;
      }
    }

    ion-textarea {
      border: 1px solid #4e4e4e;
      border-radius: 5px;
      font-size: 16px;
      margin-top: 2px;
      --color: #fff;
      --padding-end: 10px;
      --padding-start: 10px;
      --placeholder-color: #ddd;
      --placeholder-opacity: 0.8;
      caret-color: #7f550c;
      background-color: var(--background-color);
    }

    ion-input {
      border: 1px solid #4e4e4e;
      border-radius: 5px;
      margin-top: 0.2em;
      color: white;
      font-size: 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      --padding-start: 8px !important;
      caret-color: #7f550c;
      background-color: var(--background-color);
    }

    ion-button {
      --background: #f99e00;
      text-transform: none;
      line-height: 1.1em;
      border-radius: 3px;
      letter-spacing: 0.01em;
      width: 200px;
    }
  }
}

.no-margin {
  margin: 0px !important;
}