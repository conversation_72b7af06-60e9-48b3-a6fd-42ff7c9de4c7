.header-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  align-content: center;
  border-bottom: 1px solid #080808;
  position: relative;

  h6 {
    color: white;
    margin: 0px;
  }

  span {
    //font-size: 10px;
  }

  .header-left {
    padding-left: 10px;
    padding-top: 10px;
  }

  .header-right {
    border-left: 1px solid #080808;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 6px;
  }
}

.body {
  padding: 10px;

  .checkbox-row {
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;

    ion-checkbox {
      min-width: 18px;
      margin-right: 18px;
    }

    .light-grey-text {
      color: lightgray;
      height: fit-content;
      margin: 0px;
      padding: 0px;
    }
  }
}

ion-select {
  border: 1px solid #292929;
  border-radius: 5px;
  margin: 10px;
  padding: 3px;
  font-size: 12px;
  color: white;
  width: 300px;
  padding: 10px;
}
