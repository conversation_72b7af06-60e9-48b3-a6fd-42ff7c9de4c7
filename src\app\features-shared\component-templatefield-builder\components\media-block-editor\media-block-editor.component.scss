.mediablock-form-container {
  .file-size-parent-container {
    .reqAsterisk {
      color: #7f550c;
      font-size: 28px;
    }

    ion-item {
      font-size: 20px;
      --background: rgb(51, 51, 51);
      --border-color: transparent;
      --color: white;
    }
  }

  .slide-fields-container-card {
    .percentage-container-row {
      margin-top: 10px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      .counter-col {
        ion-input {
          height: 25px;
          --background: #292929;
          --padding-start: 8px !important;
          border: 1px solid #4e4e4e;
          border-radius: 5px;
          color: white;
          font-size: 16px;
        }
      }

      .sub-text-col {
        padding-left: 10px;
      }
    }
  }
}
