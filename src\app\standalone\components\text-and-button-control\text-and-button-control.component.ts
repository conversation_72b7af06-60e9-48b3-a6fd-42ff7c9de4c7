import { NgClass } from '@angular/common';
import { Component, Input, On<PERSON><PERSON>roy, OnInit, forwardRef } from '@angular/core';
import { FormsModule, NG_VALIDATORS, NG_VALUE_ACCESSOR, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { IComponent, ITextAndButton } from '@app/core/contracts/contract';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { DynamicTextInputControlComponent } from '../dynamic-text-input-control/dynamic-text-input-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';

@Component({
    selector: 'app-text-and-button-control',
    templateUrl: './text-and-button-control.component.html',
    styleUrls: ['./text-and-button-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => TextAndButtonControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => TextAndButtonControlComponent),
        },
    ],
    imports: [NgClass, IonicModule, FormsModule, ReactiveFormsModule, TextInputControlComponent, DynamicTextInputControlComponent, MatSlideToggle]
})
export class TextAndButtonControlComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override label: string;
  @Input() description: string;
  @Input() component!: IComponent;
  @Input() sidePanelPadding = false;
  textForm: UntypedFormGroup;
  textAndButton: ITextAndButton;
  componentDestroyed$: Subject<boolean> = new Subject();
  stylingDirection: string;

  constructor(private signalService: ComponentUpdateSignalService) {
    super();
  }

  ngOnInit() {
    this.fieldValueChanged.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.textValue) {
        this.textAndButton = JSON.parse(this.textValue) as ITextAndButton;
        if (this.textAndButton?.stylingDirection) {
          this.stylingDirection = this.textAndButton?.stylingDirection;
        } else {
          this.stylingDirection = 'Bottom';
        }
      }

      this.createForm();
      this.formChanges();
    });
  }

  createForm() {
    this.textForm = new UntypedFormGroup({
      heading: new UntypedFormControl(this.textAndButton?.heading, [Validators.required]),
      description: new UntypedFormControl(this.textAndButton?.description, [Validators.required]),
      buttonName: new UntypedFormControl(this.textAndButton?.buttonName, [Validators.required]),
      url: new UntypedFormControl(this.textAndButton?.url, [Validators.required]),
      sameUrlNavigation: new UntypedFormControl(this.textAndButton?.sameUrlNavigation ?? false),
      stylingDirection: new UntypedFormControl(this.textAndButton?.stylingDirection, Validators.required),
      darkText: new UntypedFormControl(this.textAndButton?.darkText ?? false),
    });
  }

  formChanges() {
    this.textForm.valueChanges.subscribe(() => {
      this.updateComponent(this.textForm.value as ITextAndButton);
    });
  }

  setBottomButton() {
    return 'assets/images/Text-&-Button-Styling-Button-Bottom.png';
  }

  setRightButton() {
    return 'assets/images/Text-&-Button-Styling-Button-Right.png';
  }

  isSelected(styling: string) {
    return this.stylingDirection ? this.stylingDirection === styling : false;
  }

  checkboxChanged(styling: string) {
    this.stylingDirection = styling;
    this.textForm.patchValue({ stylingDirection: this.stylingDirection });
  }

  updateComponent(textAndButtonIn: ITextAndButton) {
    this.setValue(JSON.stringify(textAndButtonIn));
  }

  override setValue(value: string) {
    this.writeValue(value);
    if (this.component.id) {
      this.signalService.triggerSignal({ componentId: this.component.id, updateValue: value });
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
