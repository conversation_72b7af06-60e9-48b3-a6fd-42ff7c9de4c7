import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-achievement-editor',
    templateUrl: './achievement-editor.component.html',
    styleUrls: ['./achievement-editor.component.scss'],
    standalone: false
})
export class AchievementEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  headerFieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(private formBuilder: UntypedFormBuilder) {}

  get isInheritControl(): AbstractControl {
    return this.headerFieldForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.headerFieldForm = this.formBuilder.group({
      showOnHover: [this.component?.templateField?.isHoverField ?? false],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      isVisible: [this.component?.templateField?.isPreviewField ?? true],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.headerFieldForm) {
      return;
    }

    this.headerFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.headerFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.headerFieldForm.controls.showOnHover.setValue(this.component.templateField.isHoverField);
    this.headerFieldForm.controls.isVisible.setValue(this.component.templateField.isPreviewField);
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.headerFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.headerFieldForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.headerFieldForm.valid) {
      this.component.hoverSortOrder = this.headerFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.headerFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.isHoverField = this.headerFieldForm.controls.showOnHover.value;
      this.component.templateField.isPreviewField = this.headerFieldForm.controls.isVisible.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
