@if (layoutService.currentScreenSize === 'lg') {
  <app-view-options-row-lg
    [canAdd]="canAdd"
    [addRoute]="addRoute"
    [template]="template"
    [featureTab]="featureTab"
    [selectedItem]="selectedItem"
    [instance]="instance"
    [isEducator]="isEducator"
    [selectedUserId]="selectedUserId"
    (optionSelected)="optionSelectedChange($event)"
    (rowFiltered)="rowFilteredChange($event)"
    (buttonChanged)="buttonClicked($event)"
    (searchBarAvailable)="searchBarAvailable($event)"
    (userSelected)="selectUser($event)">
  </app-view-options-row-lg>
}

@if (layoutService.currentScreenSize === 'xs') {
  <app-view-options-row-xs
    [canAdd]="canAdd"
    [addRoute]="addRoute"
    [template]="template"
    [featureTab]="featureTab"
    [selectedItem]="selectedItem"
    [instance]="instance"
    [mobileMenuClosed]="mobileMenuClosed"
    (optionSelected)="optionSelectedChange($event)"
    (rowFiltered)="rowFilteredChange($event)"
    (buttonChanged)="buttonClicked($event)"
    (searchBarAvailable)="searchBarAvailable($event)"
    (mobileMenuClicked)="openMobileMenu()">
  </app-view-options-row-xs>
}
