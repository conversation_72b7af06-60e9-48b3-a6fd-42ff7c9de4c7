.container{
  position: relative;
  padding: 16px;
  background-color: #333333;

  h1{
    color: white;
  }
}

.text-and-checkbox{
  display: flex;
  align-items: center;
  
  ion-checkbox{
    margin: 5px 10px;
  }
}

.italic{
  font-style: italic;
}

.gray-text{
  color: gray;
  font-size: 14px;
}

.white-text{
  color: white;
}

.disabled{
  pointer-events: none;
}

.input-row{
  padding-bottom: 20px;
  border-bottom: 1px solid gray;
}

.text-row{
  margin-top: 10px;
}

.close{
  position: absolute;
  right: 0px;
  top: 10px;
  padding: 0px;

  ion-icon{
    font-size: 25px;
    height: 25px;
  }
}

    .radio-group-container {
      // background-color: #292929;
      max-height: 200px;
      padding: 10px;
      border-radius: 5px;
      overflow-y: auto;

      .properties-container {
        width: 100%;
        display: flex;
        flex-direction: column;

        .property {
          height: 40px;
          width: 100%;
          border-radius: 5px;
          background-color: #111111;
          display: flex;
          flex-direction: row;
          align-items: center;
          align-content: center;
          justify-content: space-between;
          margin-bottom: 5px;
          border: 1px solid #111111;
          padding: 0px 10px 0px 10px;
          cursor: pointer;

          .property-left {
            display: flex;
            flex-direction: row;
            align-items: center;
            align-content: center;

            ion-checkbox {
              margin-right: 10px;
            }

            .heading {
              font-weight: bold;
              font-size: 14px;
              color: white;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis !important;
            }

            .sub-heading {
              font-style: italic;
              font-size: small;
              color: rgb(170, 170, 170);

              span {
                margin-left: 5px;
                margin-right: 5px
              }
            }
          }
        }
      }
    }
