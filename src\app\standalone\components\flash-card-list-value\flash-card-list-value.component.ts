import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { IAccordionItem, IEngagementIn, IInstanceSectionComponent } from '@app/core/contracts/contract';
import { EngagementTypes } from '@app/core/enums/engagment-types.enum';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { environment } from '@env/environment';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { NgClass } from '@angular/common';
import { BaseValueComponent } from '@app/standalone/components/base-control/base-value.component';

@Component({
    selector: 'app-flash-card-list-value',
    templateUrl: './flash-card-list-value.component.html',
    styleUrls: ['./flash-card-list-value.component.scss'],
    imports: [IonicModule, NgClass]
})
export class FlashCardListValueComponent extends BaseValueComponent implements OnDestroy, OnInit {
  @Input() type: string;
  @Input() instanceId: string;
  @Input() instanceSectionComponent: IInstanceSectionComponent | undefined;
  flashCardItemList: IAccordionItem[] = [];
  isSelected = false;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private dataService: DataService,
    private eventsService: Events
  ) {
    super();
  }

  ngOnInit(): void {
    this.setData();
  }

  override setData(): void {
    if (this.instanceSectionComponent?.value) {
      this.flashCardItemList = JSON.parse(this.instanceSectionComponent?.value) as IAccordionItem[];
    } else {
      this.flashCardItemList = [{ heading: 'First Item', title: 'First Item', description: '', sortOrder: 0 } as IAccordionItem];
    }
  }

  setImage(assetId: any) {
    if (assetId === '') {
      return 'assets/images/no-image.png';
    }
    return `${environment.contentUrl}asset/${assetId}/content`;
  }

  setSelectedCard(selectedCard: IAccordionItem) {
    const index = this.flashCardItemList.findIndex(x => x.sortOrder === selectedCard.sortOrder);
    if (index !== -1) {
      if (selectedCard.selected) {
        this.flashCardItemList[index].selected = false;
      } else {
        this.flashCardItemList[index].selected = true;
      }
    }

    if (this.instanceSectionComponent?.component?.templateField?.isRequiredField === true) {
      const selectedCount = this.flashCardItemList.filter(x => x.selected === true).length;
      const totalCount = this.flashCardItemList.length;
      const progress = (selectedCount / totalCount) * 100;

      if (progress === 100) {
        this.completeInstanceComponent(progress);
      }

      this.addInstanceSectionComponentEngagement(progress);
    }
  }

  completeInstanceComponent(progress: number) {
    this.dataService
      .addInstanceSectionComponentCompletion(this.instanceSectionComponent?.id ?? '', this.instanceId ?? '', Math.round(progress))
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.eventsService.publish('instanceSectionComponentCompleted', this.instanceSectionComponent?.id);
        if (this.instanceSectionComponent) {
          this.instanceSectionComponent.completed = true;
        }
      });
  }

  addInstanceSectionComponentEngagement(progress: number) {
    this.dataService
      .addInstanceSectionComponentEngagement({
        instanceId: this.instanceId,
        instanceSectionComponentId: this.instanceSectionComponent?.id,
        engagementType: EngagementTypes.Click,
        percentageValue: progress !== undefined ? Math.round(progress) : 0,
        nominalValue: 1,
      } as IEngagementIn)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {});
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
