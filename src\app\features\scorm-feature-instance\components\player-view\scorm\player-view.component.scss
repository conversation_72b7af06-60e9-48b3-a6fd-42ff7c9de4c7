.parent-container{
  height: 100%;

  .player-row-flex {
    display: flex;
    flex-direction: row;
    height: 100%;

    .player-width {
      width: 100%;
      position: relative;

      .player-inner-container {
        width: 100%;
        float: left;
        height: 100%;

        .top-border {
          border-color: #171717;
          border-width: 1px;
          border-style: solid;
          width: 100%;
        }

        .content-wrapper {
          height: 100%;
        }

        .player-view-info-container {
          width: 100%;
          height: 100%;
          overflow-y: scroll;
          margin-top: 5px;
        }

        .player-outer-container {
          margin-right: var(--page-margin-right-player);
          margin-left: var(--page-margin-left-player);
          height: calc(100% - 70px);

          .player-container {
            border: 2px solid rgba(249, 158, 0, 0.4);
            background-color: #232323;
            border-radius: 13px 13px 15px 15px;
            margin-bottom: 20px;
            overflow: hidden;
            height: 100%;
          }
        }
      }
    }

    .player-side-panel-container {
      overflow-y: auto;
      height: calc(100vh - 110px);
      width: 20%;
      background-color: #292929;
      margin-top: 5px;
      margin-right: 5px;
      border-radius: 10px;
    }
  }
}

.parent-container-mobile {
  height: 100%;

  .main-btn-container {
      justify-content: flex-end;
      width: 100%;
      display: flex;
      align-items: center;
  
      ion-button {
        --background: #333333 !important;
        border-radius: 5px;
        color: #fff;
        height: 42px;
      }
    }

  .player-row-flex {
    display: flex;
    flex-direction: row;
    background-color: transparent;
    height: 100%;

    mat-sidenav {
      width: 80%;
      background-color: #292929;
      overflow-y: auto;
      height: calc(100% - 130px);

      .player-side-panel-container {
        background-color: #292929;
      }
    }

    .player-width {
      width: 100%;
      overflow: hidden;

      .player-inner-container {
        width: 100%;
        float: left;
        height: 100%;

        .content-wrapper {
          height: 100%;
          overflow: auto;
        }

        .player-view-info-container {
          width: 100%;
          height: 100%;
          margin-top: 5px;
        }

        .player-outer-container {
          margin-right: var(--page-margin-right-player);
          margin-left: var(--page-margin-left-player);
          height: calc(100% - 120px);

          .player-container {
            border: 2px solid rgba(249, 158, 0, 0.4);
            background-color: #232323;
            border-radius: 13px 13px 15px 15px;
            margin-bottom: 20px;
            overflow: hidden;
            height: 100%;
          }
        }
      }
    }
  }
}
