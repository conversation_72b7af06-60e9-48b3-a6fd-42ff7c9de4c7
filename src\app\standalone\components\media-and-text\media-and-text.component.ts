import { NgClass } from '@angular/common';
import { Component, forwardRef, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormsModule, NG_VALIDATORS, NG_VALUE_ACCESSOR, ReactiveFormsModule, UntypedFormGroup, Validators } from '@angular/forms';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { IComponent, IMediaAndText } from '@app/core/contracts/contract';
import { IonicModule, ItemReorderEventDetail } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { DynamicTextInputControlComponent } from '../dynamic-text-input-control/dynamic-text-input-control.component';
import { FileUploadControlComponent } from '@app/standalone/components/file-upload-control/file-upload-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';

@Component({
    selector: 'app-media-and-text',
    templateUrl: './media-and-text.component.html',
    styleUrls: ['./media-and-text.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => MediaAndTextComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => MediaAndTextComponent),
        },
    ],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, FileUploadControlComponent, TextInputControlComponent, DynamicTextInputControlComponent, MatSlideToggle, NgClass]
})
export class MediaAndTextComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override label: string;
  @Input() description: string;
  @Input() component!: IComponent;
  imageTextList: IMediaAndText[] = [];
  imageTextForm: UntypedFormGroup;
  imageTextFormArray = new FormArray<any>([]);
  componentDestroyed$: Subject<boolean> = new Subject();
  stylingDirection: string;
  darkText: boolean;

  constructor(
    private formBuilder: FormBuilder,
    private signalService: ComponentUpdateSignalService
  ) {
    super();
  }

  ngOnInit() {
    this.fieldValueChanged.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.textValue) {
        this.imageTextList = JSON.parse(this.textValue) as IMediaAndText[];   
          this.stylingDirection = this.component.templateField.stylingDirection ?? 'left';
          this.darkText = this.component.templateField.darkText ?? false;
        
      }

      if (!this.imageTextList.length) {
        this.imageTextList.push({ sortOrder: 0, heading: '', paragraph: '', stylingDirection: 'Left', darkText: false } as IMediaAndText);
      }

      this.createForm();
      this.formChanges();
    });
  }

  createForm() {
    this.imageTextForm = this.formBuilder.group({
      items: this.imageTextFormArray,
      stylingDirection: [this.stylingDirection ?? 'left', Validators.required] ,
      darkText: [this.darkText ?? false, Validators.required],
    });

    for (const item of this.imageTextList) {
      this.addControlToForm(item);
    }
  }
  setTopImage() {
    return 'assets/images/Image-&-Text-Photo-Styling-Top.png';
  }
  setBottomImage() {
    return 'assets/images/Image-&-Text-Photo-Styling-Bottom.png';
  }

  setLeftImage() {
    return 'assets/images/Image-&-Text-Photo-Styling-Left.png';
  }

  setRightImage() {
    return 'assets/images/Image-&-Text-Photo-Styling-Right.png';
  }

  isSelected(styling: string) {
    return this.stylingDirection ? this.stylingDirection === styling : false;
  }

  checkboxChanged(styling: string) {    
    this.stylingDirection = styling;
    const item = this.imageTextForm;
    item.patchValue({ stylingDirection: this.stylingDirection });
  }

  reorder() {
    for (let i = 0; i < this.imageTextFormArray.length; i++) {
      const item = this.imageTextFormArray.at(i);
      if (i === 0) {
        item.patchValue({ stylingDirection: this.stylingDirection });
      }
      item.patchValue({ sortOrder: i }, { emitEvent: false });
    }
  }

  addControlToForm(item: IMediaAndText) {
    this.imageTextFormArray.push(
      this.formBuilder.group({
        sortOrder: [item.sortOrder, Validators.required],
        heading: [item.heading],
        paragraph: [item.paragraph],
        asset: [item.asset, Validators.required],
        assetType: [item.assetType, Validators.required],
        buttonText: [item.buttonText],
        buttonUrl: [item.buttonUrl],      
        sameUrlNavigation: [this.component.templateField.stylingDirection ?? false],   
      })
    );
  }

  add() {
    const nextIndex = this.imageTextList.length;
    const newItem = { sortOrder: nextIndex, heading: '', paragraph: '' } as IMediaAndText;
    this.addControlToForm(newItem);
  }

  remove(index: number) {
    this.imageTextFormArray.removeAt(index);
    this.reorder();
  }

  handleReorder(event: CustomEvent<ItemReorderEventDetail>) {
    const currentItem = this.imageTextFormArray.at(event.detail.from);
    if (event.detail.to === 0) {
      const currentFirstItem = this.imageTextFormArray.at(0);     
    }
    this.imageTextFormArray.removeAt(event.detail.from);
    this.imageTextFormArray.insert(event.detail.to, currentItem);
    this.reorder();
    event.detail.complete();
  }

  formChanges() {   
    this.imageTextForm.valueChanges.subscribe(() => {
      const reorderedList = this.imageTextFormArray.getRawValue();
      this.updateImageTextList(reorderedList as IMediaAndText[]);
    });
  }

  setAssetType(assetType: string, index: number) {
    const item = this.imageTextFormArray.at(index);
    item.patchValue({ assetType: assetType });
  }

  updateImageTextList(listIn: IMediaAndText[]) {
    listIn = listIn.map((item, index) => ({ ...item, sortOrder: index + 1 }));
    const valueAsString = JSON.stringify(listIn);
    this.setValue(valueAsString);
    
  }

  override setValue(value: string) {      
    try { 
      const formValue = this.imageTextForm.value;
      if (formValue && formValue.items?.length) {
          // Update the component's templateField properties
          const { stylingDirection, darkText } = formValue;
          Object.assign(this.component.templateField, { stylingDirection, darkText });
      }
        const parsedValue = JSON.parse(value);   
        if (parsedValue && Array.isArray(parsedValue)) {
            const jsonValue = JSON.stringify(parsedValue);         
            this.forceWriteValue(jsonValue);
            // Trigger update signal
            this.signalService.triggerSignal({ componentId: this.component.id, updateValue: jsonValue });
        }
    } catch (error) {
        console.error('Invalid JSON passed to setValue:', error, value);
    }
}

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
