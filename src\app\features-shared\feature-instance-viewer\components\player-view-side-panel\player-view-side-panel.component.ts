import { AfterViewInit, EventEmitter, Component, Input, Output, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { IInstance, IInstanceSection, IInstanceTemplate, IRouteParams } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { SectionTypes } from '@app/core/enums/section-types.enum';
import { ViewType } from '@app/core/enums/view-type';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';

@Component({
    selector: 'app-player-view-side-panel',
    templateUrl: './player-view-side-panel.component.html',
    styleUrls: ['./player-view-side-panel.component.scss'],
    standalone: false
})
export class PlayerViewSidePanelComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  @Input() template: IInstanceTemplate;
  @Input() routeParams: IRouteParams;
  @Input() instance: IInstance;
  @Input() searchFilter: string;
  @Input() selectedUserId: string;
  @Output() selectedChanged = new EventEmitter<{ event: string; actionBw: ActionTypes | undefined }>();
  instanceSections: IInstanceSection[];
  isLastContentItem = false;

  constructor(
    private eventsService: Events,
    private instanceService: InstanceService,
    private breadCrumbService: BreadcrumbService
  ) {}

  ngOnInit(): void {
    this.eventsService.subscribe('isLastRowContentItem', isLastContentItem => {
      this.isLastContentItem = isLastContentItem;
    });
  }

  ngAfterViewInit(): void {
    this.eventsService.subscribe('checkSidePanelContent', () => {
      if (this.instanceSections.length === 0) {
        this.breadCrumbService.goToPrev(ViewType.Grid, 'students& grades');
      }

      return;
    });

    this.eventsService.subscribe('playerRowCompleted', rowId => {
      const nextRowInCurrentSection = this.GetNextRowInCurrentSection(rowId);
      if (nextRowInCurrentSection) {
        this.eventsService.publish('playerRowSelected', nextRowInCurrentSection);
        return;
      }

      const currentSection = this.template.instanceSections.find(x => x.instanceSectionComponents.some(y => y.component.rowId === rowId));

      const reverseComponents = currentSection?.instanceSectionComponents.slice().reverse();
      const lastComponentWithRowId = reverseComponents?.find(x => x.component.rowId !== null && x.component.rowId !== undefined);
      const isLastRow = lastComponentWithRowId?.component.rowId === rowId;
      let nextSection: IInstanceSection | null = null;

      if (isLastRow) {
        nextSection = this.GetNextSection(rowId);
      } else {
        const currentRowIndex: number = currentSection?.instanceSectionComponents.findIndex(x => x.component.rowId === rowId) ?? -1;
        const slicedComponents = currentSection?.instanceSectionComponents.slice(currentRowIndex + 1);
        const nextRowId: string | undefined = slicedComponents?.find(x => x.component.rowId !== null && x.component.rowId !== undefined)?.component.rowId;
        // If there is a next row but for some reason its not found then we exit player view.
        if (nextRowId) {
          this.eventsService.publish('playerRowSelected', nextRowId);
          return;
        }
      }

      if (nextSection) {
        const firstCompIndex = nextSection.instanceSectionComponents.findIndex(x => (x.component.rowId !== null && x.component.rowId !== undefined) || x.instanceSectionComponentRows.length > 0);

        if (firstCompIndex !== -1) {
          let selectedRowId = nextSection.instanceSectionComponents[firstCompIndex].component.rowId;

          if (!selectedRowId) {
            selectedRowId = nextSection.instanceSectionComponents[firstCompIndex].instanceSectionComponentRows[0].rowId;
          }

          this.eventsService.publish('playerRowSelected', selectedRowId);
        }
        else {
          this.breadCrumbService.goToPrev(ViewType.Grid, 'students& grades');
        }
      } else if (!this.selectedUserId) {
        const prevInstanceSlug = this.instanceService.getPrevInstanceSlug();

        if (prevInstanceSlug && prevInstanceSlug.length > 0) {
          this.routeParams.instanceSlug = prevInstanceSlug?.length > 0 ? prevInstanceSlug : this.routeParams.instanceSlug;
          this.instanceService.openInstance(this.routeParams.featureSlug, prevInstanceSlug?.length > 0 ? prevInstanceSlug : this.routeParams.instanceSlug, this.routeParams.tabName, 'grid');
          return;
        }

        this.eventsService.publish('changeViewType', 1);
      } else {
        this.breadCrumbService.goToPrev(ViewType.Grid, 'students& grades');
      }
    });
  }

  selectedRowContent(rowId: string, actionBw?: ActionTypes) {
    this.selectedChanged.emit({ event: rowId, actionBw: actionBw });
    const nextSection = this.GetNextSection(rowId);
    const nextRowInCurrentSection = this.GetNextRowInCurrentSection(rowId);

    // Only set isLastSection if we're in the last section AND there are no more rows in current section
    if ((!nextSection || nextSection?.instanceSectionComponents?.length === 0) && 
        !nextRowInCurrentSection && 
        this.isLastContentItem) {
      this.eventsService.publish('isLastSection');
    }
  }

  GetNextRowInCurrentSection(rowId: string): string | null {
    // Find the section containing our row
    const currentSection = this.template.instanceSections.find(x => 
      x.instanceSectionComponents?.some(y => 
        y.component.rowId === rowId || y.instanceSectionComponentRows?.some(z => z.rowId === rowId)
      )
    );
    if (!currentSection) return null;

    // Find the component containing our row
    const currentComponent = currentSection.instanceSectionComponents.find(x => 
      x.component.rowId === rowId || x.instanceSectionComponentRows?.some(y => y.rowId === rowId)
    );
    if (!currentComponent) return null;

    // If it's a direct component row, look for next component with a row
    if (currentComponent.component.rowId === rowId) {
      const nextComponent = currentSection.instanceSectionComponents
        .slice(currentSection.instanceSectionComponents.indexOf(currentComponent) + 1)
        .find(x => x.component.rowId);
      return nextComponent?.component.rowId ?? null;
    }

    // If it's in component rows, find next row by sort order
    if (currentComponent.instanceSectionComponentRows?.length) {
      const sortedRows = [...currentComponent.instanceSectionComponentRows]
        .sort((a, b) => (a.sortOrder ?? 0) - (b.sortOrder ?? 0));
      const currentRowIndex = sortedRows.findIndex(x => x.rowId === rowId);
      return currentRowIndex !== -1 && currentRowIndex < sortedRows.length - 1 
        ? sortedRows[currentRowIndex + 1].rowId 
        : null;
    }

    return null;
  }

  GetNextSection(rowId: string) {
    this.template.instanceSections = this.template.instanceSections.filter(x => x.section.showOnPlayerSidepanel === true);
    let index = this.template.instanceSections.findIndex(x => x.instanceSectionComponents?.some(y => y.component.rowId === rowId || y.instanceSectionComponentRows?.some(z => z.rowId === rowId)));

    if (index === -1) {
      index = this.template.instanceSections.findIndex(x => x.instanceSectionComponents?.some(y => y.instanceSectionComponentRows?.some(z => z.rowId === rowId)));
    }

    return this.template.instanceSections[index + 1];
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['template']) {
      this.instanceSections = this.template.instanceSections.filter(x => x.section.showOnPlayerSidepanel !== false && !(x.section.typeBw === SectionTypes.Dynamic && x.section.templateId));
    }
  }

  ngOnDestroy(): void {
    this.eventsService.unsubscribe('playerRowCompleted');
    this.eventsService.unsubscribe('isLastRowContentItem');
  }
}
