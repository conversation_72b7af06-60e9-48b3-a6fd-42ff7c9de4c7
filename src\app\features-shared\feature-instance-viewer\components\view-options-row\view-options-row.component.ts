import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IFeatureTab, IInstance, IInstanceTemplate } from '@app/core/contracts/contract';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-view-options-row',
    templateUrl: './view-options-row.component.html',
    styleUrls: ['./view-options-row.component.scss'],
    standalone: false
})
export class ViewOptionsRowComponent {
  @Input() canAdd: boolean;
  @Input() addRoute: string;
  @Input() template: IInstanceTemplate;
  @Input() featureTab: IFeatureTab;
  @Input() selectedItem = 1;
  @Input() mobileMenuClosed = false;
  @Input() instance: IInstance;
  @Input() isEducator: boolean;
  @Input() hasAdminAccess: boolean;
  @Input() hideAddButton: boolean;
  @Input() selectedUserId: string;
  @Output() optionSelected = new EventEmitter<any>();
  @Output() rowFiltered = new EventEmitter<string>();
  @Output() buttonChanged = new EventEmitter<any>();
  @Output() searchBarAvailableChanged = new EventEmitter<boolean>();
  @Output() mobileMenuClicked = new EventEmitter<any>();
  @Output() userSelected = new EventEmitter<string>();

  constructor(public layoutService: LayoutService) {}

  optionSelectedChange(option: number) {
    this.optionSelected.emit(option);
  }

  rowFilteredChange(option: any) {
    this.rowFiltered.emit(option);
  }

  buttonClicked(option: any) {
    this.buttonChanged.emit(option);
  }

  searchBarAvailable(event: any) {
    this.searchBarAvailableChanged.emit(event);
  }

  openMobileMenu() {
    this.mobileMenuClicked.emit(true);
  }

  selectUser(event: any) {
    this.userSelected.emit(event);
  }
}
