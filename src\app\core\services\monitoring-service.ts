import { Injectable } from '@angular/core';
import { ApplicationInsights } from '@microsoft/applicationinsights-web';
import { ClickAnalyticsPlugin } from '@microsoft/applicationinsights-clickanalytics-js';
import { environment } from '../../../environments/environment';

@Injectable()
export class MonitoringService {
  appInsights: ApplicationInsights;
  private reloadAttempts = 0;
  private readonly maxReloadAttempts = 3; // Set a maximum number of reload attempts
  constructor() {
    const clickPluginInstance = new ClickAnalyticsPlugin();
    // Click Analytics configuration
    const clickPluginConfig = {
      autoCapture: true,
      dataTags: {
        useDefaultContentNameOrId: true,
      },
    };
    this.appInsights = new ApplicationInsights({
      config: {
        connectionString: environment.appInsights.connectionString,
        extensions: [clickPluginInstance],
        extensionConfig: {
          [clickPluginInstance.identifier]: clickPluginConfig,
        },
        enableAutoRouteTracking: true, // option to log all route changes
        [clickPluginInstance.identifier]: clickPluginConfig,
      },
    });

    this.appInsights.loadAppInsights();
    this.loadCustomTelemetryProperties();
  }

  logPageView(name?: string, url?: string) {
    // option to call manually
    this.appInsights.trackPageView({
      name: name,
      uri: url,
    });
  }

  setUserId(userId: string) {
    this.appInsights.setAuthenticatedUserContext(userId);
  }
  clearUserId() {
    this.appInsights.clearAuthenticatedUserContext();
  }

  logEvent(name: string, properties?: { [key: string]: any }) {
    this.appInsights.trackEvent({ name: name }, properties);
  }

  logMetric(name: string, average: number, properties?: { [key: string]: any }) {
    this.appInsights.trackMetric({ name: name, average: average }, properties);
  }

  logException(exception: Error, severityLevel?: number) {
    this.appInsights.trackException({ exception: exception, severityLevel: severityLevel });
    const chunkFailedMessage = `Loading chunk `;

    if (this.reloadAttempts < this.maxReloadAttempts && exception.message?.toLocaleLowerCase().includes(chunkFailedMessage?.toLocaleLowerCase())) {
      this.reloadAttempts++;
      window.location.reload();
    }
  }

  logTrace(message: string, properties?: { [key: string]: any }) {
    this.appInsights.trackTrace({ message: message }, properties);
  }

  private loadCustomTelemetryProperties() {
    this.appInsights.addTelemetryInitializer(envelope => {
      const item = envelope.baseData;
      if (item) {
        item.properties = item.properties || {};
        item.properties['ApplicationPlatform'] = environment.platform;
        item.properties['ApplicationName'] = 'EdgeFactor';
      }
    });
  }
}
