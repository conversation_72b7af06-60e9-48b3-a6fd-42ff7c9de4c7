@if (!hidden && ((featureTab?.buttonText && featureTab.buttonText !== '' && featureTab.featureTabButtons.length > 0) || (showGradingView === true && isEducator === true))) {
  <div class="container" [class.page-margins-player]="selectedItem === 4" [class.page-margins]="selectedItem !== 4">
    <ion-grid class="grid-container">
      <ion-row class="view-options-row ion-nowrap">
        <ion-col class="view-options-col" size="7">
          @if (isEducator === true && parentClassExists === true && showGradingView === true) {
            @if (users$ | async; as users) {
              <div class="user-search-custom">
                <ion-label>Grading View</ion-label>
                &nbsp;
                <ion-label>|</ion-label>
                &nbsp;
                <ion-label>Change Student:</ion-label>
                <app-select-option-control
                  [placeHolder]="'Switch Students'"
                  [options]="usersSelectOptions"
                  [backgroundColor]="'#333333'"
                  [showLabel]="true"
                  [textValue]="selectedUserId"
                  [linkTypeName]="'users'"
                  (valueChanged)="selectUser($event)"></app-select-option-control>
              </div>
            }
          }
          @if (isEducator !== true && selectedItem !== viewType.Player) {
            @if (templateFields && templateFields.length > 0) {
              @for (field of templateFields; track field) {
                <ion-button (click)="openTagModal(field.tagTreeId, field.limitTo)">{{ field.label }}</ion-button>
              }
            }
            <ion-searchbar class="player-view-searchbar" [(ngModel)]="searchValue" debounce="600" type="search" (ionChange)="filterRows($event)" placeholder="Search this page"></ion-searchbar>
          }
        </ion-col>
        <ion-col class="view-options-col view-options-col-right" size="5">
          @if (featureTab?.buttonText && featureTab.buttonText !== '' && featureTab.featureTabButtons.length > 0) {
            <div class="button-container">
              <ion-button [disabled]="(isLoading$ | async)" size="small" fill="solid" color="primary" (click)="buttonClicked($event)">{{ featureTab?.buttonText }}</ion-button>
            </div>
          }
          <!-- @if (instance.hasScormAccess === true) {
            <div class="button-container">
              <ion-button size="small" fill="solid" color="dark" (click)="generateScormFile($event)">Generate SCORM</ion-button>
            </div>
          } -->
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>
}
