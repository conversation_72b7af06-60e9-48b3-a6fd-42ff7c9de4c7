@if (layoutService.currentScreenSize === 'lg' && !isScorm && !hidden) {
  <app-player-view-lg
    [template]="instanceTemplate"
    [instance]="instance"
    [featureTab]="featureTab"
    [routeParams]="routeParams"
    [searchFilter]="searchFilter"
    [selectedUserId]="selectedUserId"
    (rowFiltered)="rowFiltered($event)"
    (buttonChanged)="buttonClicked($event)"
    (optionSelected)="setSelectedViewType($event)">
  </app-player-view-lg>
}
@if (layoutService.currentScreenSize === 'xs' && !isScorm && !hidden) {
  <app-player-view-xs
    [template]="instanceTemplate"
    [instance]="instance"
    [featureTab]="featureTab"
    [routeParams]="routeParams"
    [searchFilter]="searchFilter"
    (rowFiltered)="rowFiltered($event)"
    (buttonChanged)="buttonClicked($event)"
    (optionSelected)="setSelectedViewType($event)">
  </app-player-view-xs>
}
