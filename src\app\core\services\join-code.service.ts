import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { JoinDialogComponent } from '@app/standalone/modals/join-dialog/join-dialog.component';
import { Alert<PERSON>ontroller, ModalController } from '@ionic/angular';
import { OverlayEventDetail } from '@ionic/core';
import { GlobalToastService } from './global-toast.service';
import { IUserDomain } from '../contracts/contract';
import { DataService } from './data-service';

@Injectable({
  providedIn: 'root',
})
export class JoinCodeService {
  hasOpened = false;
  popupPreference: boolean;

  constructor(
    private modalController: ModalController,
    private dataService: DataService,
    private router: Router,
    private toast: GlobalToastService,
    private alertController: AlertController
  ) {}

  async openJoin(onStart: boolean) {
    this.dataService.getUserOrgsByDomain().subscribe(async res => {
      this.popupPreference = res.shouldReceiveDomainPopup;
      if (onStart && res.organizations?.length === 1 && !res.hasDomainOrg) {
        const alert = await this.alertController.create({
          cssClass: '',
          header: 'Please note',
          message: ` You've been given the role of ${res.organizations[0].domainRoleName} at ${res.organizations[0].name}.`,
          buttons: ['OK'],
        });
        await alert.present();
        await alert.onDidDismiss();
        this.hasOpened = onStart;
        const url = this.router.url;
        await this.router.navigateByUrl(url);
        return;
      }
      if (onStart && (!res.shouldReceiveDomainPopup || res.hasDomainOrg || !res.organizations?.length)) {
        this.hasOpened = onStart;
        const url = this.router.url;
        await this.router.navigateByUrl(url);
        return;
      }
      this.openJoinModal(res as IUserDomain);
    });
  }

  async openJoinModal(userDomain: IUserDomain) {
    const modal = await this.modalController.create({
      component: JoinDialogComponent,
      componentProps: { userDomain: userDomain },
      cssClass: userDomain?.organizations?.length ? 'join-dialog' : 'join-dialog-no-orgs',
    });
    this.hasOpened = true;

    modal.onDidDismiss().then(async (overlayEventDetail: OverlayEventDetail) => {
      if (userDomain?.shouldReceiveDomainPopup !== undefined && this.popupPreference !== userDomain?.shouldReceiveDomainPopup) {
        this.dataService.saveUserDomainPopupPreference(userDomain?.shouldReceiveDomainPopup).subscribe();
      }

      if (overlayEventDetail?.data) {
        const code = overlayEventDetail?.data?.code;
        const selectedOrgId = overlayEventDetail?.data?.organization?.selectedOrgId;
        const orgName = overlayEventDetail?.data?.organization?.orgName;
        const domain = overlayEventDetail?.data?.domain;
        const domainRoleId = overlayEventDetail?.data.organization.domainRoleId;

        if (code && code !== '') {
          this.joinWithCode(code);
        }
        if (selectedOrgId) {
          this.dataService.saveDomainSettings(selectedOrgId, domain, domainRoleId).subscribe(async () => {
            const alert = await this.alertController.create({
              cssClass: '',
              header: 'Please note',
              message: `You have joined ${orgName}'s membership as ${userDomain.organizations.find(x => x.id === selectedOrgId)?.domainRoleName}. You can close this window and get started.`,
              buttons: ['OK'],
            });

            await alert.present();

            const url = this.router.url;
            await alert.onDidDismiss().then(() => this.router.navigateByUrl(url));
          });
        }
      }
    });

    await modal.present();
  }

  joinWithCode(code: string) {
    this.dataService.joinUser(code).subscribe(data => {
      if (data && code[0] === 'I') {
        this.router.navigateByUrl(`/${data.instanceSlug ?? 'instance'}/${data.instanceId}/default/grid`);
      }
      this.toast.presentToast('Join Code Successful');
    });
  }
}
