
import { OnInit, Component, On<PERSON><PERSON>roy, signal } from '@angular/core';
import { Meta } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { IEngagementIn, IInstance, IProductFeatureRole, IRouteParams, IUserContext } from '@app/core/contracts/contract';
import { SystemPropertyType } from '@app/core/enums/system-property-type.enum';
import { ViewType } from '@app/core/enums/view-type';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';
import { JoinCodeService } from '@app/core/services/join-code.service';
import { ParseService } from '@app/core/services/parse-service';
import { RolesService } from '@app/core/services/roles.service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { ParseContentPipe } from '@app/shared/pipes/parse-content';
import { TabFilterPipe } from '@app/shared/pipes/tab-filter';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { ModalController, ViewDidEnter, ViewWillLeave } from '@ionic/angular';
import { BehaviorSubject, Subject, first, forkJoin, takeUntil } from 'rxjs';
import { ShareModalComponent } from '@app/standalone/Components/share-modal/share-modal.component';
import { DataService } from '@app/core/services/data-service';

@Component({
    selector: 'app-feature-instance',
    templateUrl: './feature-instance.component.html',
    styleUrls: ['./feature-instance.component.scss'],
    providers: [ParseContentPipe],
    standalone: false
})
export class FeatureInstanceComponent implements OnInit, OnDestroy, ViewDidEnter, ViewWillLeave {
  instanceId: string;
  componentDestroyed$: Subject<boolean> = new Subject();
  selectedIndex = signal(0);
  productFeatureRoles: IProductFeatureRole[];
  scrollPosition = 0;
  isScorm = false;
  scormLoading = false;
  progress = new BehaviorSubject(0);
  instance: IInstance | null;
  routeParams: IRouteParams;
  orgId: string | null;
  userContext: IUserContext | null;
  isCurrentFirstLevelView: boolean;
  likedState = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private systemPropertiesService: SystemPropertiesService,
    private geolocation: Geolocation,
    private rolesServie: RolesService,
    private eventsService: Events,
    private router: Router,
    private tabFilterPipe: TabFilterPipe,
    private joinCodeService: JoinCodeService,
    private instanceService: InstanceService,
    private breadcrumbService: BreadcrumbService,
    private parseContentPipe: ParseContentPipe,
    private modalController: ModalController,
    private parseService: ParseService,
    private dataService: DataService,
  ) {}

  ngOnInit() {
    this.setData();
    this.getLikedStatus();

    if (this.joinCodeService.hasOpened === false) {
      this.joinCodeService.openJoin(true);
    }

    this.instanceService.hardReload$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.breadcrumbService.hardRefresh = false;
      this.setData();
    });

    this.instanceService.breadcrumbReload$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setData();
    });

    this.isCurrentFirstLevelView = this.breadcrumbService.firstLevelViewSlugs.includes(this.routeParams.featureSlug ?? '');
  }

  ionViewDidEnter(): void {
    if (this.instance) {
      this.setData();
      return;
    }
  }

  setBreadCrumb() {
    if (this.instance) {
      if (this.isCurrentFirstLevelView) {
        this.breadcrumbService.resetBreadCrumbs();
      }

      this.parseContentPipe
        .transform(this.instance.title, this.instance.id, null, true)
        .pipe(first())
        .subscribe((result: string) => {
          this.breadcrumbService.addBreadCrumb(
            this.instance?.id ?? '',
            this.router.url,
            result,
            this.instance?.feature.featureSlug === 'my-organization' ? this.orgId : null,
            this.instance?.feature.featureType.name ?? ''
          );
        });
    }
  }

  setRouteParams() {
    const featureSlug = this.getRouteParamValue('featureSlug');
    const instanceSlug = (this.getRouteParamValue('instanceslug') && this.getRouteParamValue('instanceslug')) !== 'default' ? this.getRouteParamValue('instanceslug') : null;
    const tabName = (this.getRouteParamValue('tabName') && this.getRouteParamValue('tabName')) !== 'default' ? this.getRouteParamValue('tabName') : null;
    const view = (this.getRouteParamValue('view') && this.getRouteParamValue('view')) !== 'default' ? this.getRouteParamValue('view') : 'grid';

    this.routeParams = { featureSlug: featureSlug, instanceSlug: instanceSlug, tabName: tabName, viewType: this.getViewType(view) } as IRouteParams;
  }

  getViewType(type: string) {
    if (type?.toLowerCase() === 'builder') {
      return ViewType.Builder;
    } else if (type?.toLowerCase() === 'player') {
      return ViewType.Player;
    } else if (type?.toLowerCase() === 'list') {
      return ViewType.List;
    } else {
      return ViewType.Grid;
    }
  }

  getTabs() {
    return this.instance?.feature?.featureTabs ? this.tabFilterPipe.transform(this.instance?.feature?.featureTabs, this.instance?.isDefault ?? false) : [];
  }

  getRouteParamValue(paramName: string): string {
    return this.activatedRoute.snapshot.data[paramName] ?? this.activatedRoute.snapshot.params[paramName];
  }

  setData() {
    this.setRouteParams();

    this.instanceService.setPrevFeatureSlug(this.routeParams.featureSlug ?? '');

    if (this.routeParams?.featureSlug) {
      this.userContext = JSON.parse(localStorage.getItem('user_context') as string);
      this.geolocation.getCurrentPosition().then(x => {
        if (this.userContext) {
          this.userContext.latitude = x.coords.latitude;
          this.userContext.longitude = x.coords.longitude;
          localStorage.setItem('user_context', JSON.stringify(this.userContext));
        }
      });
    }

    this.setInstance();
  }

  ionViewWillLeave(): void {
    this.eventsService.publish('viewLeft', null);
  }

  setInstance() {
    let slug = this.routeParams.featureSlug;
    let orgId = '';

    if (this.routeParams.featureSlug?.toLocaleLowerCase() === 'instance') {
      slug = this.routeParams.instanceSlug;
    } else if (
      this.routeParams.featureSlug?.toLocaleLowerCase() === 'my-organization' &&
      this.routeParams.viewType !== ViewType.Player &&
      this.instanceService.isValidGUID(this.routeParams.instanceSlug ?? '')
    ) {
      this.selectedIndex.set(0);
      // STOPS RELOAD WHEN GOING BACK TO FIRST TAB
      if (this.orgId === this.routeParams.instanceSlug && this.instance) {
        this.setInstanceData(this.instance);
        return;
      } else if (this.routeParams.viewType !== ViewType.Player) {
        this.orgId = this.routeParams.instanceSlug ?? '';
      }
    }
    // DO NOT SET ORG ID FROM PLAYER INSTANCE ID SLUGS
    if (this.instanceService.isValidGUID(this.routeParams.instanceSlug ?? '') && this.routeParams.viewType !== ViewType.Player) {
      orgId = this.orgId ?? this.routeParams.instanceSlug ?? '';
    }

    if (!slug || slug === 'undefined') {
      return;
    }

    this.instance = null;

    this.instanceService
      .getInstance(slug, orgId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((instance: IInstance) => {
        this.setInstanceData(instance);
      });
  }

  setInstanceData(instance: IInstance) {
    this.rolesServie.getProductFeatureRoles(instance?.feature?.id).pipe(takeUntil(this.componentDestroyed$)).subscribe();
    this.getSystemProperties(instance);
  }

  getSystemProperties(instance: IInstance) {
    // Clear old data
    this.systemPropertiesService.clearAllSystemProperties();

    if (instance) {
      const requestList = [];
      requestList.push(this.systemPropertiesService.getSystemPropertyValues(SystemPropertyType.Instance, instance.id) as never);

      if (instance?.feature?.id) {
        requestList.push(this.systemPropertiesService.getSystemPropertyValues(SystemPropertyType.Feature, instance.feature.id) as never);
      }

      if (instance.organizationId && instance.feature?.featureType?.systemPropertyType?.typeBw !== SystemPropertyType.Organization) {
        requestList.push(this.systemPropertiesService.getSystemPropertyValues(SystemPropertyType.Organization, instance.organizationId) as never);
      }
      //TODO:Need to find a more strict check to get the Credential Engine CTID
      if (instance.feature?.title == 'Badge Manager') {
        requestList.push(this.systemPropertiesService.getSystemPropertyValues(SystemPropertyType.CredentialEngineBadge, instance.id) as never);
      }

      if (instance.feature?.featureType?.systemPropertyType?.typeBw) {
        if (this.routeParams?.instanceSlug && this.instanceService.isValidGUID(this.routeParams?.instanceSlug)) {
          const systemPropertyType = instance.feature.featureType.systemPropertyType.typeBw;
          if (systemPropertyType) {
            requestList.push(this.systemPropertiesService.getSystemPropertyValues(systemPropertyType, this.routeParams?.instanceSlug) as never);
          }
        } else if (instance.feature?.featureType?.systemPropertyType?.typeBw === SystemPropertyType.User) {
          //MyJourneyDefaultSlug
          requestList.push(this.systemPropertiesService.getSystemPropertyValues(SystemPropertyType.User, instance.id) as never);
        }
      }

      forkJoin(requestList)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.instance = { ...instance } as IInstance;
          if (instance?.feature?.featureType?.systemPropertyType?.typeBw === SystemPropertyType.Organization) {
            this.parseService.populateOrganizationAssets(this.instance, this.systemPropertiesService.organizationProperties);
          }
          this.setBreadCrumb();
        });
    }
  }

  reloadSystemProperties() {
    if (this.instance) {
      this.getSystemProperties(this.instance);
    }
  }

  onlyContent() {
    const tabLenght = this.getTabs()?.length ?? 0;
    return (
      (tabLenght === 1 && this.routeParams?.viewType === ViewType.Builder && this.instance?.feature?.featureType?.name.toLocaleLowerCase().indexOf('manager') === -1) ||
      (this.instance?.feature.isFullWidth === true && this.routeParams?.viewType !== ViewType.Player)
    );
  }

  async share() {
    if (!this.instance) return;

    let shareUrl = window.location.href;
    const baseUrl = location.origin.replace(/\/$/, '');

    if (this.instance.id) {
      if (this.routeParams?.viewType === ViewType.Player) {
        shareUrl = `${baseUrl}/${this.instance.id}/${this.instance.id}/default/player`;
      } else if (this.instance.actionUrl && this.instance.actionUrl !== 'player-direct') {
        shareUrl = `${baseUrl}/${this.instance.actionUrl}`;
      }
    }

    const modal = await this.modalController.create({
      component: ShareModalComponent,
      componentProps: {
        shareUrl: shareUrl,
      },
      cssClass: 'share-modal-container',
    });

    return await modal.present();
  }

  like(liked: boolean) {
    this.likedState = liked;

    if (this.orgId) {
      this.dataService.addOrganizationInterest(this.orgId, liked ? 3 : -1).subscribe();
    }
  }

  getLikedStatus() {
    if (this.orgId) {
      this.dataService.getOrganizationInterests().subscribe(
        response => {
          if (Array.isArray(response) && response.length > 0) {
            const index = response.findIndex(item => item.organizationId === this.orgId);

            if (index !== -1) {
              this.likedState = response[index].nominalVal === 3;
            }
          }
        },
      );
    }
  }
  

  ngOnDestroy() {
    if (this.instance?.feature?.featureType?.systemPropertyType?.typeBw === SystemPropertyType.Organization) {
      this.parseService.clearOrganizationAssets();
    }
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
