import { Location } from '@angular/common';
import { Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Observable, Subject } from 'rxjs';
import { IInstance, IInstanceSectionComponent, IInstanceSectionsAndComponentsResult, IRole } from '../contracts/contract';
import { DataService } from './data-service';
import { NavController } from '@ionic/angular';
import { BreadcrumbService } from './breadcrumb-service';

export interface questionOrder {
  instanceSortOrder: number;
  builderRowNumber: number;
  sectionIndex: number;
  questionId: string;
}

@Injectable()
export class InstanceService {
  reload$ = new Subject<any>();
  breadcrumbReload$ = new Subject<any>();
  hardReload$ = new Subject<any>();
  questionOrderChanged$ = new Subject<any>();
  isScorm: boolean;
  selectedUserId: string;
  private questionOrderList: questionOrder[] = [];
  private prevFeatureSlug: string;
  private prevInstanceSlug: string;
  private openInstanceHappened = false;
  constructor(
    private dataService: DataService,
    private router: Router,
    public location: Location,
    private navCtrl: NavController,
    private breadcrumbService: BreadcrumbService
  ) {
    router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        let index = event.url.replace('/', '').indexOf('/');
        this.prevFeatureSlug = index === -1 ? event.url.replace('/', '') : event.url.replace('/', '').substring(0, index);

        if (this.prevFeatureSlug === 'my-organization') {
          index = event.url.replace('/my-organization/', '').indexOf('/');
          this.prevInstanceSlug = index === -1 ? event.url.replace('/', '') : event.url.replace('/my-organization/', '').substring(0, index);
        }
      }
    });
  }

  public navigationHappened(): boolean {
    return this.openInstanceHappened;
  }

  public getPrevInstanceSlug(): string {
    return this.prevInstanceSlug;
  }

  public getInstance(slug?: string | null, orgId?: string | null, isScorm = false, showInterest = false): Observable<IInstance> {
    this.isScorm = this.router.url.indexOf('scorm') !== -1;
    this.resetQuestionOrder();
    if (slug && this.isValidGUID(slug)) {
      return this.getInstanceById(slug, isScorm, showInterest);
    } else {
      return this.getInstanceFromSlug(slug ?? '', orgId && this.isValidGUID(orgId) ? orgId : null, isScorm, showInterest);
    }
  }

  public getInstanceUserRoles(slug: string): Observable<IRole[]> {
    if (slug && this.isValidGUID(slug)) {
      return this.getInstanceUserRolesById(slug);
    } else {
      return this.getInstanceUserRolesBySlug(slug);
    }
  }

  public getInstanceSectionsAndComponents(
    slug: string,
    userId: string | null,
    isViewField?: boolean,
    isHoverField?: boolean,
    isPreviewField?: boolean,
    isRequiredField?: boolean
  ): Observable<IInstanceSectionsAndComponentsResult> {
    this.resetQuestionOrder();
    if (slug && this.isValidGUID(slug)) {
      return this.getInstanceSectionsAndComponentsById(slug, userId, isViewField ?? false, isHoverField ?? false, isPreviewField ?? false, isRequiredField ?? false, this.isScorm);
    } else {
      return this.getInstanceSectionsAndComponentsBySlug(slug, userId, isViewField ?? false, isHoverField ?? false, isPreviewField ?? false, isRequiredField ?? false, this.isScorm);
    }
  }

  public openInstance(
    featureSlug?: string | null,
    instanceSlug?: string | null,
    tabName?: string | null,
    view?: string | null,
    queryParams?: any,
    hardReload = false,
    isBreadcrumb = false,
    sessionStorageValue?: { key: string; value: string }
  ) {
    if (sessionStorageValue) {
      sessionStorage.setItem(sessionStorageValue.key, sessionStorageValue.value);
    }

    this.openInstanceHappened = true;
    const isScorm = this.router.url.indexOf('scorm') !== -1;
    // Open a new page....
    if ((featureSlug !== this.prevFeatureSlug && !isScorm) || hardReload === true) {
      this.navCtrl.setDirection('root');
      if (featureSlug && !instanceSlug && !tabName && !view) {
        this.setPrevFeatureSlug(featureSlug);
        this.navigate(`/${featureSlug}`, queryParams);
      } else if (featureSlug && featureSlug.indexOf('repository') !== -1) {
        this.navigate(`/${featureSlug}/${instanceSlug}`);
      } else if (featureSlug) {
        this.setPrevFeatureSlug(featureSlug);
        this.navigate(`/${featureSlug}/${instanceSlug ?? 'default'}/${tabName?.toLocaleLowerCase().replace(' ', '') ?? 'default'}/${view?.toLocaleLowerCase() ?? 'grid'}`, queryParams);
      } else {
        this.navigate('/', queryParams);
      }
    } else {
      // Just change the route
      if (isScorm === true) {
        this.location.go(`scorm/${featureSlug}/${instanceSlug ?? 'default'}/${tabName?.toLocaleLowerCase().replace(' ', '') ?? 'default'}/${view?.toLocaleLowerCase() ?? 'grid'}`);
      } else {
        const url = `${featureSlug}/${instanceSlug ?? 'default'}/${tabName?.toLocaleLowerCase().replace(' ', '') ?? 'default'}/${view?.toLocaleLowerCase() ?? 'grid'}`;
        this.location.go(url);
        this.breadcrumbService.updateCurrentBreadcrumbUrl(url);
      }
    }

    if (hardReload === true) {
      this.hardReload$.next(null);
    } else if (isBreadcrumb !== true) {
      this.reload$.next(tabName);
    } else {
      this.breadcrumbReload$.next(null);
    }
  }

  public setPrevFeatureSlug(slug: string) {
    this.prevFeatureSlug = slug;
  }

  public isValidGUID(value: string): boolean {
    if (value?.length > 0) {
      return /^(\{){0,1}[0-9a-fA-F]{8}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{12}(\}){0,1}$/.test(value);
    }
    return false;
  }

  public checkInstanceSectionIsGraded(instanceId: string, instanceSectionId: string, userId: string) {
    return this.dataService.checkInstanceSectionIsGraded(instanceId, instanceSectionId, userId);
  }

  public getInstanceStatus(slug: string): Observable<boolean> {
    if (this.isValidGUID(slug)) {
      return this.dataService.getInstanceStatusById(slug);
    } else {
      return this.dataService.getInstanceStatusBySlug(slug);
    }
  }

  private getInstanceFromSlug(slug: string, orgId: string | null, isScorm = false, showInterest = false): Observable<IInstance> {
    return this.dataService.getInstanceBySlug(slug, orgId, isScorm, showInterest);
  }

  private getInstanceById(instanceId: string, isScorm = false, showInterest = false): Observable<IInstance> {
    return this.dataService.getInstance(instanceId, isScorm, showInterest);
  }

  private getInstanceUserRolesBySlug(slug: string): Observable<IRole[]> {
    return this.dataService.getInstanceUserRolesBySlug(slug);
  }

  private getInstanceUserRolesById(instanceId: string): Observable<IRole[]> {
    return this.dataService.getInstanceUserRoles(instanceId);
  }

  private getInstanceSectionsAndComponentsById(
    instanceId: string,
    userId: string | null = null,
    isViewField: boolean,
    isHoverField: boolean,
    isPreviewField: boolean,
    isRequiredField: boolean,
    isScorm: boolean
  ): Observable<IInstanceSectionsAndComponentsResult> {
    return this.dataService.getInstanceSectionsAndComponents(instanceId, userId, isViewField, isHoverField, isPreviewField, isRequiredField, isScorm);
  }

  private getInstanceSectionsAndComponentsBySlug(
    slug: string,
    userId: string | null = null,
    isViewField: boolean,
    isHoverField: boolean,
    isPreviewField: boolean,
    isRequiredField: boolean,
    isScorm: boolean
  ): Observable<IInstanceSectionsAndComponentsResult> {
    return this.dataService.getInstanceSectionsAndComponentsBySlug(slug, userId, isViewField, isHoverField, isPreviewField, isRequiredField, isScorm);
  }

  private navigate(url: string, queryParams?: any) {
    if (queryParams) {
      this.router.navigate([`${url}`], { skipLocationChange: false, queryParams: queryParams });
    } else {
      this.router.navigateByUrl(url, { skipLocationChange: false });
    }
  }

  resetQuestionOrder() {
    this.questionOrderList = [];
  }

  getQuestionOrderIndex(instanceSectionOrder: number, instanceComponent: IInstanceSectionComponent | undefined) {
    if (this.questionOrderList.length === 0 || !this.questionOrderList?.some(x => x.questionId === instanceComponent?.component?.question?.id)) {
      this.questionOrderList.push({
        instanceSortOrder: instanceComponent?.component.instanceSortOrder ?? 0,
        builderRowNumber: instanceComponent?.component?.builderRowNumber ?? 0,
        sectionIndex: instanceSectionOrder,
        questionId: instanceComponent?.component?.question?.id,
      } as questionOrder);
      this.questionOrderList = this.questionOrderList
        .sort((n1, n2) => n1.instanceSortOrder - n2.instanceSortOrder)
        .sort((n1, n2) => n1.builderRowNumber - n2.builderRowNumber)
        .sort((n1, n2) => n1.sectionIndex - n2.sectionIndex);
      this.questionOrderChanged$.next(null);
    }
    return this.questionOrderList.findIndex(x => x.questionId === instanceComponent?.component?.question?.id) + 1;
  }
}
