.parent-container {
  ion-row {
    ion-col {
      display: flex;
      align-items: center;
    }

    .heading-col {
      padding: 10px;
    }

    .inner-col {
      width: 100%;
      border-radius: 8px;
      border: 2px solid rgb(54, 54, 54);
      background-color: rgb(35, 35, 35);
      padding: 10px;

      .search-icon-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: auto;
        border-left: 2px solid rgb(54, 54, 54);
        cursor: pointer;

        ion-icon {
          margin-left: 10px;
          font-size: 25px;
          color: white !important;
        }
      }
    }
  }
}
