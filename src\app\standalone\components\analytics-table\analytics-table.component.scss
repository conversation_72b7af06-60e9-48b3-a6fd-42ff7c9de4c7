.inner-container {
    display: flex;
    width: 100%;
    justify-content: space-between;

    .label {
        line-height: 1.1;
        font-family: "Roboto";
        display: flex;
        min-height: 20px;

        .amount-container {
            display: inline-table;


            .amount {
                color: #ffffff;
                font-weight: 900;
                justify-content: flex-end;
                width: 100%;
                display: flex;
            }
        }

        .title {
            color: #d6d6d6;
            font-weight: 300;
        }
    }
}
 @media (min-width: 959px) {
     .inner-container {
        padding-right: 10px;

        .label {

            .title {
                font-size: 14px;
            }

            .amount-container {
                min-width: 40px;

                .amount {
                    font-size: 14px;
                    padding-right: 10px;
                }
            }
        }
     }
 }
@media (max-width: 960px) {
    .inner-container {
        padding-right: 5px;

        .label {

            .title {
                font-size: 12px;
            }

            .amount-container {
                min-width: 20px;

                .amount {
                    padding-right: 5px;
                }
            }
        }
    }
}

mat-expansion-panel {
    background-color: rgba(41, 41, 41);
    box-shadow: none;
    margin-bottom: 15px !important;

    .expansion-panel-header {
        min-height: 34px;
        height: 100%;
        padding: 8px 8px 8px 8px;
        background: #1e1e1e;
        border-color: #000000;
        border-width: 1px;
        border-style: solid;
        border-radius: 4px 4px 0px 0px;
    }

    .expansion-panel-header:hover {
        background-color: rgba(41, 41, 41) !important;
    }

    ::ng-deep .mat-expansion-indicator::after,
    .mat-expansion-panel-header-description {
        border-color: white;
    }

    ::ng-deep .mat-expansion-panel-body {
        padding: 0 !important
    }

    ::ng-deep .mat-mdc-tab {
        min-width: 25px !important;
        padding: 0px;
        background-color: transparent;
        margin-right: 15px;
        font-size: 16px;
        color: white;
    }

    ::ng-deep .mat-mdc-tab-header {
        margin-bottom: 15px !important;
        color: white;
    }
}

.users-table {

    .table-container {
        display: flex;
        flex-direction: column;
        align-content: stretch;
        align-items: stretch;

        .table-grid {
            flex: 1;
            width: 100%;
            background-color: #444444;
        }

        .name-cell{
            color: #fff !important;
            font-style: bold;
        }

        @media (min-width: 959px) {
            .name-header{
                padding-left: 15px;
            }

            .align-header-right {
                padding: 0px;
                max-width: 100px;
                min-width: 100px;
                margin-left: 10px;
            }

            .name-cell {
                padding-left: 15px !important;
            }

            .mat-mdc-cell {
                margin-right: 15px;
                padding: 10px 0px;
                font-size: 14px;
            }

            .cell-width-small {
                min-width: 100px;
            }
        }
        @media (max-width: 960px) {
            .name-header {
                padding-left: 10px;
            }

            .align-header-right {
                padding: 0px;
                max-width: 70px;
                min-width: 70px;
                margin-left: 10px;
            }

            .name-cell {
                padding-left: 10px !important;
            }

            .mat-mdc-cell {
                margin-right: 10px;
                padding: 8px 0px;
                font-size: 12px;
            }

            .cell-width-small {
                min-width: 70px;
                margin-left: 10px;
            }
        }

        .mat-mdc-header-cell {
            font-size: 12px;
            color: #aaaaaa;
        }

        .mat-mdc-header-row{
            padding-top: 10px;
            min-height: 20px !important;
            border-bottom: none;
        }

        .mat-mdc-row{
            min-height: 40px;
        }

        .mat-content{
            display: none!important;
        }

        .mat-mdc-cell {
            color: #aaaaaa;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .cell-width-small{
            max-width: fit-content;
            margin-right: 0px !important;
            margin-left: 10px;
        }
    }
}
