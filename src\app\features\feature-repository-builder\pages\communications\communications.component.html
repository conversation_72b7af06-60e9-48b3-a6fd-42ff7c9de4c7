<form [formGroup]="communicationsForm">
  <ion-grid>
    <ion-row>
      <ion-col size="11" class="ion-padding-start">
        <p>Manage the transactional communications that users receive about his feature.</p>
      </ion-col>
      <ion-col>
        @if (showSave) {
          <ion-button fill="clear" color="primary" (click)="saveCommunication()">Save</ion-button>
        }
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-card>
          <ion-card-header>
            <ion-card-title>Author Communications</ion-card-title>
            <ion-card-subtitle>Manage the notifications an author or publisher will receive</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="authorInstanceCompletion">Reminder to finish completing an instance.</mat-slide-toggle>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="authorInstanceFeedback">Notify when someone provides feedback on an instance.</mat-slide-toggle>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="authorInstanceLive">Notify when the instance is live on the platform.</mat-slide-toggle>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-card>
          <ion-card-header>
            <ion-card-title>User Communications</ion-card-title>
            <ion-card-subtitle>Manage the notifications an user will receive</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="userInstanceCompletion">Remind the user to finish completing this instance</mat-slide-toggle>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="userAchievement">Notify the user when they earn an achievement from this instance</mat-slide-toggle>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
</form>
