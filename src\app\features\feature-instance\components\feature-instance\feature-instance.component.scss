:host {
  height: 100%;
  background-color: #181818;
  color: white;
  position: relative;

  ion-split-pane {
    --side-width: 400px;
    --side-max-width: 400px;

    ion-menu {
      background-color: rgb(45, 46, 50);

      .side-pane-content-parent {
        --padding-bottom: 20px;
        --padding-end: 20px;
        --padding-start: 20px;
        --padding-top: 20px;

        .cancel-col {
          display: flex;
          justify-content: flex-end;

          .close-btn {
            margin: 8px;
            width: 20px;
            height: 20px;
            ion-icon {
              font-size: 20px;
            }
          }
        }

        .edit-col {
          .edit-component {
            margin-top: 10px;
          }
        }

        .nav-col {
        }
      }

      .main-view-content-parent {
        .inner-content {
        }
      }
    }
  }

  ion-content {
    height: calc(100% - 250px);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Exo 2';
    text-shadow: 2px 2px #000;
    margin: 0;
    font-weight: 800;
    align-self: flex-end;
  }

  h1 {
    font-size: 40px;
  }
  h2 {
    font-size: 2.5em;
  }
  h3 {
    font-size: 2em;
  }
  h4 {
    font-size: 1.8em;
  }
  h5 {
    font-size: 1.5em;
  }
  h6 {
    font-size: 1.2em;
  }
  p {
    font-family: 'Roboto';
    text-shadow: 2px 2px #000;
  }

  .scormloader {
    background-color: #111;
    height: 100%;

    ion-row {
      height: 100%;

      ion-col {
        text-align: center;
        color: #f99e00;
      }
    }
  }

  .full-height {
    height: 100%;
  }
}
