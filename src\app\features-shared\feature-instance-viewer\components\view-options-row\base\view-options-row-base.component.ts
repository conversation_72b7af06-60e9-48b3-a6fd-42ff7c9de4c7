import { AfterViewInit, Directive, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { IFeatureTab, IInstance, IInstanceTemplate, IPeople, ITemplateField } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { ViewType } from '@app/core/enums/view-type';
import { PendingRequestsInterceptor } from '@app/core/interceptors/pending-requests-interceptor';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { DataService } from '@app/core/services/data-service';
import { LayoutService } from '@app/core/services/layout-service';
import { RolesService } from '@app/core/services/roles.service';
import { ScormService } from '@app/core/services/scorm.service';
import { StorageService } from '@app/core/services/storage-service';
import { TagSelectorDialogComponent } from '@app/standalone/modals/tag-selector-dialog/tag-selector-dialog.component';
import { Observable, Subject, map, takeUntil } from 'rxjs';
import { delay } from 'rxjs/operators';

@Directive()
export class ViewOptionsRowBaseComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() canAdd: boolean;
  @Input() addRoute: string;
  @Input() template: IInstanceTemplate;
  @Input() featureTab: IFeatureTab;
  @Input() selectedItem = 1;
  @Input() instance: IInstance;
  @Input() selectedUserId: string;
  @Input() isEducator: boolean;
  @Input() hasAdminAccess: boolean;
  @Input() hideAddButton: boolean;
  @Output() optionSelected = new EventEmitter<any>();
  @Output() rowFiltered = new EventEmitter<string>();
  @Output() buttonChanged = new EventEmitter<any>();
  @Output() mobileMenuClicked = new EventEmitter<any>();
  @Output() userSelected = new EventEmitter<string>();
  @Output() searchBarAvailable = new EventEmitter<boolean>();
  viewType = ViewType;
  tagDialog: MatDialogRef<TagSelectorDialogComponent>;
  templateFields: ITemplateField[];
  componentDestroyed$: Subject<boolean> = new Subject();
  searchValue: string;
  currentSlug: string;
  users: IPeople[] = [];
  usersSelectOptions: KeyValue[] = [];
  users$: Observable<IPeople[]>;
  hidden = false;
  parentClassExists = false;
  showGradingView = false;
  isLoading$: Observable<boolean>;

  constructor(
    private dataService: DataService,
    private storageService: StorageService,
    private dialog: MatDialog,
    private activatedRoute: ActivatedRoute,
    public layoutService: LayoutService,
    private scormService: ScormService,
    private breadcrumbService: BreadcrumbService,
    private pendingRequestsInterceptor: PendingRequestsInterceptor,
  ) {}

  ngOnInit() {    
    this.isLoading$ = this.pendingRequestsInterceptor.PendingRequestsStatus$.pipe(delay(0));
    this.showGradingView = localStorage.getItem('showGradingView') === 'true';
    this.parentClassExists = this.breadcrumbService.featureTypeExists('Accredited Learning Container Pages');
    this.checkHidden();
    this.getFilterComponents();
    this.getUsers();
    this.currentSlug = this.activatedRoute.snapshot.data['featureSlug'] ?? this.activatedRoute.snapshot.params['featureSlug'];
    if (this.currentSlug === 'global-search') {
      this.searchValue = this.storageService.initialSearchVal ?? '';
      this.filterRows();
      this.storageService.globalSearchValue$.pipe(takeUntil(this.componentDestroyed$)).subscribe(res => {
        this.searchValue = res;
        this.filterRows();
      });
    }
  }

  getFilterComponents() {
    this.dataService
      .getFilters(this.template.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(res => {
        if (res) {
          if (res.length > 2) {
            res.length = 2; //take the first two filters
          }
          this.templateFields = res;
        }
      });
  }

  getUsers() {
    const parentBreadcrumb = this.breadcrumbService.getByfeatureType('Modifiable Learning Container Pages');
    this.users$ = this.dataService.getPeopleTableByInstanceId(this.instance.id, 0, 100, this.instance.id !== parentBreadcrumb?.id ? parentBreadcrumb?.id : null).pipe(
      takeUntil(this.componentDestroyed$),
      map(data => {
        if (data) {
          this.users = data;
          this.usersSelectOptions = data.map(x => ({ id: x.userId, value: x.name && x.name.length > 0 ? x.name : x.userId }) as KeyValue);
        }
        return data;
      })
    );
  }

  selectUser(userId: string) {
    this.userSelected.emit(userId);
    sessionStorage.setItem('peopleTableSelectedUserId', userId);
    sessionStorage.setItem('peopleTableSelectedUserFirstName', this.usersSelectOptions.find(x => x.id === userId)?.value ?? 'User');
  }

  selectOption(option: any) {
    if (this.layoutService.currentScreenSize === 'xs') {
      this.optionSelectedChange(Number(option.detail.value));
      this.selectedItem = Number(option.detail.value);
    } else {
      this.optionSelectedChange(option);
    }
  }

  generateScormFile(event: any) {
    this.scormService.generateScormFile(this.instance, event);
  }

  optionSelectedChange(option: number) {
    this.optionSelected.emit(option);
    this.searchValue = '';
  }

  filterRows(filter?: any) {
    if (filter?.detail?.value && filter?.detail?.value !== '') {
      this.rowFiltered.emit(filter.detail.value);
    } else if (this.searchValue && this.searchValue !== '') {
      this.rowFiltered.emit(this.searchValue);
    }
  }

  buttonClicked(event: any) {
    this.buttonChanged.emit(event);
  }

  openTagModal(id: string, level?: number) {
    this.tagDialog = this.dialog.open(TagSelectorDialogComponent, {
      disableClose: true,
      data: {
        tagId: id ?? '4c9f2d31-8b20-4461-a5f3-eeb09fcad575',
        level: level ?? 3,
        view: 'isFilter',
      },
    });
    this.tagDialog
      .afterClosed()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(response => {
        if (response) {
          //save selected tag
        }
      });
  }

  openMobileMenu() {
    this.mobileMenuClicked.emit(true);
  }

  checkHidden() {
    //Hide For PeopleTable And UserTable.
    this.hidden = this.template.instanceSections.every(x =>
      x.instanceSectionComponents?.every(y => y.component.componentType.name === 'People Table' || y.component.componentType.name === 'Users Table')
    );

    if (!this.hidden) {
      // Losing all the admin functionality we need if we do not do this check!
      this.hidden = this.selectedItem === ViewType.Player && !this.hasAdminAccess && !this.isEducator;
    }
  }

  ngAfterViewInit(): void {
    const searchBar = document.querySelector('.player-view-searchbar');
    const menuButton = document.querySelector('.menu-button');
    if (searchBar || menuButton) {
      this.searchBarAvailable.next(true);
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
