.parent-container {
  mat-expansion-panel {
    background: #2d2e32;
    color: #8b8b8b;
    line-height: 1.1;
    width: 100%;
    height: 100%;
    mat-expansion-panel-header {
      padding: 0 8px;
    }

    .mat-expansion-panel-header-title {
      color: #8b8b8b !important;
    }

    .mat-expansion-panel-header-description {
      color: #8b8b8b !important;
    }

    ::ng-deep .mat-expansion-panel-header > .mat-expansion-indicator:after {
      color: white;
    }

    ::ng-deep .mat-expansion-panel-body {
      padding: 0px 0px 8px 8px !important;
    }

    .passwordForm {
      @media screen and (max-device-width: 960px) {
        width: 100%;
      }
      @media screen and (min-device-width: 960px) {
        width: 50%;
      }

      $red-common: #fd3629;
      $gold-common: #ff902b;
      $green-common: #469009;

      .progress-segment {
        margin-top: 5px;
        display: flex;

        .item {
          width: 100%;
          background-color: #7a7a7a;
          margin-right: 2px;
          height: 5px;

          &:first-child {
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
          }

          &:last-child {
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
          }

          &.red-common {
            background: $red-common !important;
          }

          &.gold-common {
            background: $gold-common !important;
          }

          &.green-common {
            background: $green-common !important;
          }
        }
      }

      .information-box-container{
        position: relative;

        .information-box{
          margin-top: 10px;
          min-width: 200px;
          width: 100%;
          height: 100%;
          padding: 10px 20px 10px 20px;
          box-shadow: 0px 11px 25px rgba(0, 0, 0, 0.75);
          background: #181818;
          color: #ffffff;
          border-color: #232323;
          border-width: 2px;
          border-style: solid;
          border-radius: 5px 5px 5px 5px;
          font-family: "Roboto";
          font-weight: 400;
          font-size: 16px;
          text-align: left;
          line-height: 1.8;

          .text-container{
            display: flex;
            margin-bottom: 5px;
          }
          .icon{
            height: 17px;
            width: 14px;
            min-height: 17px;
            min-width: 14px;
            margin-right: 5px;
          }

          .error{
            color: $red-common;
          }

          .correct{
            color: #489012;
          }

          .info-text{
            font-size: 14px;
          }
        }

        .information-box:before {
          content: "";
          position: absolute;
          height: 0px;
          width: 0px;
          top: 0px;
          transform: rotate(89deg);
          left: 10px;
          top: -18px;
          border-width: 10px;
          border-color: transparent #161616 transparent transparent;
          border-style: solid;
        }
      }
    }

    .button-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      align-content: center;
      justify-content: flex-end;

      ion-button {
        margin-right: 10px;
      }
      @media screen and (max-device-width: 960px) {
        padding-top: 10px;
      }
    }

    ::ng-deep .mat-content {
      display: flex;
      flex-direction: row;
      align-items: center;
      align-content: center;
      justify-content: space-between;
    }

    .panel-right {
      display: flex;
      flex-direction: row;
      align-items: center;
      align-content: center;

      ion-icon {
        color: white;
        font-size: 20px;
        padding-left: 10px;
      }
    }
  }

  .info-container{
    margin-top: 5px;
    width: 100%;
    height: 100%;
    font-family: "Roboto";
    font-weight: 400;
    font-size: 14px;
    line-height: 1.5;
    text-align: left;
    display: flex;
  }

  .info-icon {
    width: 18px;
    height: 18px;
    margin-right: 5px;
    cursor: pointer;
  }
}