@if (qlikAnalyticsTypeFieldForm){
  <form [formGroup]="qlikAnalyticsTypeFieldForm">
    <ion-grid>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Label'"
                                  [placeHolder]="'Add field title...'" title="Add Title" formControlName="label"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Description'"
                                  [placeHolder]="'Add field description...'" title="Add Description"
                                  formControlName="description"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-select-option-control [label]="'Qlik Object Type'" [options]="qlikReportingTypes" [backgroundColor]="'#333333'" [disabled]="false" formControlName="label4"></app-select-option-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Row Number'" title="Set Row Number"
                                  [placeHolder]="'Add field row number...'" formControlName="rowNumber"
                                  [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" title="Set Column Number" [label]="'Column Number (Out of 12)'" [limit]="12"
                                  [placeHolder]="'Add column number (Out of 12)...'" formControlName="colNumber"
                                  [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" title="Set Column Span" [label]="'Colspan (Out of 12)'" [limit]="12"
                                  [placeHolder]="'Add field colspan (Out of 12)...'" formControlName="colspan"
                                  [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" title="Hover Sort Order" [label]="'Hover Sort Order'"
                                  [placeHolder]="'Add field hover sort order...'" formControlName="hoverSortOrder"
                                  [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" title="Instance Sort Order" [label]="'Instance Sort Order'"
                                  [placeHolder]="'Add field instance sort order...'" formControlName="instanceSortOrder"
                                  [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
