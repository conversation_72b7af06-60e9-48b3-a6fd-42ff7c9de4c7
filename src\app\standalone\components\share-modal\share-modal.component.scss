.share-modal {
  padding: 20px;
  height: 481px;
  max-width: 562px;
  background-color: #111111;

  h2 {
    font-size: 30px;
    font-weight: 700;
    font: 'Inter';
    color: white;
  }

  h3 {
    margin-top: 20px;
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 0;
    font: 'Inter';
    color: #AAAAAA;
  }

  .direct-link {
    margin-top: 30px;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-button {
  margin: 0;

  ion-icon {
    font-size: 30px;
    color: #bababa;
  }
}

.platform-grid {
  display: flex;
  gap: 25px;
}

.platform-button {
  margin-top: 10px;
  display: flex;
  height: 93px;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
  width: 64px;

  mat-icon {
    font-size: 25px;
    height: 50px;
    width: 50px;
  }

  img {
    width: 64px;
    height: 64px;
    max-width: none;
  }

  span {
    text-align: center;
    color: #aaaaaa;
    font-family: 'Inter';
    font-weight: 400;
    font-size: 12px;
  }

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.direct-link {
  margin-top: 24px;
}

.copy-link-container {
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.link-input {
  flex-grow: 1;
  background-color: #333333;
  color: #aaaaaa;
  border: none;
  height: 33px;
  margin-right: 20px;
  padding: 10px 12px;
  font-size: 14px;
  outline: none;
  border-radius: 5px;
}

.copy-button {
  background-color: #fd9a08;
  color: #111111;
  width: 57px;
  height: 33px;
  border-radius: 5px;
  padding: 10px 20px !important;
  font-weight: bold;
  min-width: auto !important;
  line-height: normal !important;
  border: none;
}

@media (max-width: 480px) {
  .share-modal {
    padding: 16px;
  }

  .platform-grid {
    gap: 20px;
  }
}
