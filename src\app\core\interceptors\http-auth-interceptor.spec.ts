import { HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Routes } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { CoreModule } from '../core.module';
import { AuthService } from '../services/auth-service';
import { HttpAuthInterceptor } from './http-auth-interceptor';

describe(`HttpAuthInterceptor`, () => {
  let http: HttpTestingController;
  let httpClient: HttpClient;

  const fakeRoutes: Routes = [{ path: '', redirectTo: '', pathMatch: 'full' }];

  beforeEach(() => {
    const testBed = TestBed.configureTestingModule({
      imports: [CoreModule, RouterTestingModule.withRoutes(fakeRoutes)],
      providers: [
        AuthService,
        {
          provide: HTTP_INTERCEPTORS,
          useClass: HttpAuthInterceptor,
          multi: true,
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });
    http = testBed.inject(HttpTestingController);
    httpClient = testBed.inject(HttpClient);
  });

  it('should add an Authorization header', () => {
    const testUrl = '';
    httpClient.get(testUrl).subscribe(response => {
      expect(response).toBeTruthy();
    });
    const req = http.expectOne(testUrl);
    expect(req.request.headers.has('Authorization')).toEqual(true);
  });

  it('should add an Content-Type header', () => {
    const testUrl = '';
    httpClient.get(testUrl).subscribe(response => {
      expect(response).toBeTruthy();
    });
    const req = http.expectOne(testUrl);
    expect(req.request.headers.has('Content-Type')).toEqual(true);
  });

  afterEach(() => {
    http.verify();
  });
});
