<mat-accordion>
  @for (productSetting of productSettings; track productSetting) {
    <mat-expansion-panel (opened)="openGroup(true)" (closed)="openGroup(false)">
      <mat-expansion-panel-header class="expansion-panel-header">
        <div class="inner-panel">
          <div class="heading">{{ productSetting?.title }}</div>
          <div class="sub-heading">
            <span>{{ productSetting?.description }} </span>
          </div>
        </div>
      </mat-expansion-panel-header>
      @if (productSetting.id === '1' && isParentPanelClosed) {
        <app-d2l-portal-settings [productSetting]="productSetting" [organizationId]="organizationId" [userRoles]="userRoles" (orgSsoAuthChange)="saveOrgSsoAuth($event)"></app-d2l-portal-settings>
      }
      @if (productSetting.id === '2' && isParentPanelClosed) {
        <app-product-domain-mapping-settings [userRoles]="userRoles" [productOrgId]="productOrgId" [productSetting]="productSetting" (productOrgDomainsChange)="saveProductOrgDomains($event)">
        </app-product-domain-mapping-settings>
      }
      @if (productSetting.id === '3' && isParentPanelClosed) {
        <app-product-join-code-settings
          [productId]="productId"
          [userRoles]="userRoles"
          [productOrganizationId]="productOrgId"
          [organizationId]="organizationId"
          [productJoinCodeSettings]="productJoinCodeSettings"
          (productJoinCodesChange)="saveProductJoinCodes()"></app-product-join-code-settings>
      }
      @if (productSetting.id === '4' && isParentPanelClosed) {
        <app-privacy-manager-setup [privacyTypeId]="privacyTypeId" [organizationId]="organizationId"></app-privacy-manager-setup>
      }
    </mat-expansion-panel>
  }
</mat-accordion>
