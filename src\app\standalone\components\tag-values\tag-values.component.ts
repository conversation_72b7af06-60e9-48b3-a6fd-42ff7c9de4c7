import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ITag } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { environment } from '@env/environment';
import { Observable, Subject, map, takeUntil } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { MatChipListbox, MatChipOption, MatChipAvatar } from '@angular/material/chips';
import { AsyncPipe } from '@angular/common';

@Component({
    selector: 'app-tag-values',
    templateUrl: './tag-values.component.html',
    styleUrls: ['./tag-values.component.scss'],
    imports: [IonicModule, MatChipListbox, MatChipOption, MatChipAvatar, AsyncPipe]
})
export class TagValuesComponent implements OnInit, OnDestroy {
  componentDestroyed$: Subject<boolean> = new Subject();
  @Input() componentId: string;
  @Input() instanceId: string | undefined;
  @Input() dropDownLinkType: string | undefined;
  @Input() campaignId: string | null | undefined;
  @Input() systemPropertyType: string | null | undefined;
  @Input() textOnly = false;
  @Input() isTag: boolean | undefined = false;
  organizationId: string | undefined;

  tags$: Observable<ITag[]>;
  contentUrl = environment.contentUrl;
  constructor(
    private dataService: DataService,
    private activatedRoute: ActivatedRoute,
    private eventsService: Events
  ) {}

  ngOnInit() {
    if (this.dropDownLinkType === 'Organization Tags') {
      this.activatedRoute.params.pipe(takeUntil(this.componentDestroyed$)).subscribe(params => {
        this.organizationId = params['instanceslug'];
      });
    }
    this.loadData();
    this.eventsService.subscribe('loadTags', () => {
      this.loadData();
    });
  }

  loadData() {
    if (this.dropDownLinkType && this.dropDownLinkType === 'User Tags') {
      this.tags$ = this.dataService.getUserTags().pipe(
        map(tags => {
          return tags
            .filter(x => x.type !== 'Persona')
            .map(userTag => {
              return { name: userTag?.tag?.name } as ITag;
            });
        })
      );
    } else if (this.campaignId && this.dropDownLinkType === 'Campaign User Tags') {
      this.tags$ = this.dataService.getCampaignTags(this.campaignId);
    } else if (this.campaignId && this.systemPropertyType === 'Campaign') {
      this.tags$ = this.dataService.getCampaignTag(this.campaignId);
    } else if (this.dropDownLinkType === 'Organizations' && this.instanceId && this.isTag) {
      this.tags$ = this.dataService.getOrganizationsByInstanceId(this.instanceId).pipe(
        map(organizations => {
          return organizations.map(organization => {
            return { name: organization.name } as ITag;
          });
        })
      );
    } else if (this.dropDownLinkType === 'Instances' && this.organizationId && this.isTag) {
      this.tags$ = this.dataService.getInstancesByOrganizationId(this.organizationId).pipe(
        map(instances => {
          return instances.map(instance => {
            return { name: instance.title } as ITag;
          });
        })
      );
    } else if (this.organizationId && this.dropDownLinkType === 'Organization Tags') {
      this.tags$ = this.dataService.getOrganizationTags(this.organizationId, this.componentId);
    } else if (this.instanceId) {
      this.tags$ = this.dataService.getInstanceTags(this.instanceId, this.componentId);
    }
  }

  ngOnDestroy(): void {
    this.eventsService.unsubscribe('loadTags');
  }
}
