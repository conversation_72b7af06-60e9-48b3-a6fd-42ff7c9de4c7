<div class="layout">
  <div>
    <h3>Insert Personalization Token</h3>
    <ion-label position="stacked" class="reqAsterisk">
      Select a property
      <span title="Select a property" class="reqAsterisk">
        <span>* </span>
        <ion-icon name="information-circle-outline"></ion-icon>
      </span>
    </ion-label>
    <select matNativeControl class="dropdown" (change)="optionSelect()">
      @for (item of personalizationOptions; track item) {
        <option [value]="item">
          {{ item }}
        </option>
      }
    </select>
    <div class="searchbar">
      <div class="bar">
        <input type="text" #searchbar [(ngModel)]="searchInput" class="dropdown" (keyup)="fetchSeries($event)" placeholder="Saerch here..." />
      </div>
      <select matNativeControl class="dropdown" (change)="optionSelect()">
        @for (item of searchResult; track item) {
          <option [value]="item">
            {{ item.name }}
          </option>
        }
      </select>
    </div>
    <ion-button (click)="onClose(true)" style="width: 100%">Add</ion-button>
  </div>
  <div mat-dialog-actions [align]="'end'">
    <ion-button (click)="onClose(true)">Save changes</ion-button>
    <ion-button color="medium" (click)="onClose()">Cancel</ion-button>
  </div>
</div>
