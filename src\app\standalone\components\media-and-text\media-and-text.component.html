<div class="parent-container">
  <div class="inner-container">
    <form [formGroup]="imageTextForm">
      <ion-reorder-group formArrayName="items" [disabled]="false" (ionItemReorder)="handleReorder($any($event))">
        @for (item of imageTextFormArray.controls; track item; let i = $index) {
          <div [formGroupName]="i">
            <div class="reorder-container-left">
              <ion-icon (click)="remove(i)" class="trash-icon" name="trash"></ion-icon>
              <div class="reorder-icon-container">
                <ion-reorder>
                  <ion-icon name="apps-outline"></ion-icon>
                </ion-reorder>
              </div>
            </div>
            <ion-card class="card-container">
              <div class="upload-container">
                @if (component?.templateField?.caption1) {
                  <p class="caption">{{ component?.templateField?.caption1 }}</p>
                }
                @if (component?.templateField?.description1) {
                  <p class="description">{{ component?.templateField?.description1 }}</p>
                }
                <app-file-upload-control
                  [formControlName]="'asset'"
                  [label]="component?.templateField?.label1 ?? 'Media'"
                  [fileFormat]="'/*/'"
                  [itemBackgroundColor]="'#1E1E1E'"
                  [fileTypeBw]="component?.templateField?.fileTypeBw"
                  [minFileSize]="component?.templateField?.minFileSize"
                  [maxFileSize]="component?.templateField?.maxFileSize"
                  [componentType]="component?.componentType?.name"
                  [defaultImageUrl]="component?.templateField?.defaultImageUrl"
                  [buttonText]="component?.templateField?.buttonText ?? ''"
                  [component]="component"
                  [placeHolderText]="component?.templateField?.placeHolder1 ?? ''"
                  (assetType)="setAssetType($event, i)"></app-file-upload-control>
              </div>
              <div class="text-input-container">
                @if (component?.templateField?.caption2) {
                  <p class="caption">{{ component?.templateField?.caption2 }}</p>
                }
                @if (component?.templateField?.description2) {
                  <p class="description">{{ component?.templateField?.description2 }}</p>
                }
                <app-text-input-control
                  [component]="component"
                  [backgroundColor]="'#1E1E1E'"
                  [noPadding]="true"
                  [placeHolder]="component?.templateField?.placeHolder2 ?? 'Start typing here'"
                  [label]="component?.templateField?.label2 ?? 'Header'"
                  formControlName="heading"
                  [itemBackgroundColor]="'#292929'"></app-text-input-control>
              </div>
              <div class="text-input-container">
                @if (component?.templateField?.caption3) {
                  <p class="caption">{{ component?.templateField?.caption3 }}</p>
                }
                @if (component?.templateField?.description3) {
                  <p class="description">{{ component?.templateField?.description3 }}</p>
                }
                <app-dynamic-text-input-control
                  [component]="component"
                  [backgroundColor]="'#1E1E1E'"
                  [noPadding]="true"
                  [label]="component?.templateField?.label3 ?? 'Paragraph'"
                  [placeHolder]="component?.templateField?.placeHolder3 ?? 'Start typing here'"
                  formControlName="paragraph"
                  [hideQuillPersonalize]="true"></app-dynamic-text-input-control>
              </div>              
              <div class="text-input-container">
                <div class="small-text">Button</div>
                <div class="image-button-container">
                  <div class="button-input-fields">
                    <app-text-input-control
                      [component]="component"
                      [backgroundColor]="'#1E1E1E'"
                      [noPadding]="true"
                      [placeHolder]="'Learn More'"
                      [label]="'Text'"
                      [toolTip]="'Button text'"
                      formControlName="buttonText"
                      [itemBackgroundColor]="'#292929'"></app-text-input-control>
                  </div>
                  <div class="button-input-fields">
                    <app-text-input-control
                      [component]="component"
                      [backgroundColor]="'#1E1E1E'"
                      [noPadding]="true"
                      [placeHolder]="'www.edgefactor.com'"
                      [label]="'URL'"
                      [toolTip]="'Button URL'"
                      formControlName="buttonUrl"
                      [itemBackgroundColor]="'#292929'"></app-text-input-control>
                  </div>
                </div>
                <mat-slide-toggle style="width: 100%" color="primary" formControlName="sameUrlNavigation">Navigate on current page</mat-slide-toggle>
              </div>
            </ion-card>
          </div>
        }

        <div class="section-add-line">
          <div class="icon-container">
            <ion-icon (click)="add()" name="add-circle-outline"></ion-icon>
          </div>
        </div>
      </ion-reorder-group>
      <div class="styling-container">
        <p class="styling-heading">Styling</p>    
        <mat-slide-toggle style="width: 100%" color="primary" formControlName="darkText">Dark text</mat-slide-toggle>    
        <div class="styles-container">  
          <div class="styling-img-container">
            <div (click)="checkboxChanged('Top')">
              <div class="image-container">
                <img [ngClass]="isSelected('Top') ? 'selected' : null" [src]="setTopImage()" />
              </div>
            </div>
            <p class="image-text">Top Centered</p>
          </div>
          <div class="styling-img-container">
            <div (click)="checkboxChanged('Bottom')">
              <div class="image-container">
                <img [ngClass]="isSelected('Bottom') ? 'selected' : null" [src]="setBottomImage()" />
              </div>
            </div>
            <p class="image-text">Bottom Centered</p>
          </div>
          <div class="styling-img-container">
            <div (click)="checkboxChanged('Right')">
              <div class="image-container">
                <img [ngClass]="isSelected('Right') ? 'selected' : null" [src]="setRightImage()" />
              </div>
            </div>
            <p class="image-text">Photo Right</p>
          </div>
          <div class="styling-img-container">
            <div (click)="checkboxChanged('Left')">
              <div class="image-container">
                <img [ngClass]="isSelected('Left') ? 'selected' : null" [src]="setLeftImage()" />
              </div>
            </div>
            <p class="image-text">Photo Left</p>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
