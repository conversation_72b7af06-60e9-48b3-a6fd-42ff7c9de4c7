import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ITag, ITemplateField } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-tag-tree-field',
    templateUrl: './tag-tree-field.component.html',
    styleUrls: ['./tag-tree-field.component.scss'],
    standalone: false
})
export class TagTreeFieldComponent implements OnInit, OnDestroy {
  @Input() templateField: ITemplateField;
  @Output() valueChanged: EventEmitter<boolean> = new EventEmitter();
  tags: ITag[] = [];
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.getTags();
  }

  getTags() {
    this.dataService
      .getTags()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        if (data) {
          this.tags = data;
        }
      });
  }

  setPropertyTag(tag: ITag) {
    this.templateField.limitTo = 1;
    this.templateField.tagTreeId = tag.id;
    this.valueChanged.emit(true);
  }

  setPropertyLevel(level: any) {
    this.templateField.limitTo = level.detail?.value;
    this.valueChanged.emit(true);
  }

  getLevels(tag: ITag) {
    const tagList = this.getMaxLevelList(tag);
    return tagList.map(x => x.treeLevel - tag.treeLevel).filter((value, index, self) => self.indexOf(value) === index && value !== 0);
  }

  getMaxLevelList(tag: ITag) {
    const tagList: ITag[] = [];
    this.addNestedChildrenToArray(tag, tagList);
    return tagList;
  }

  addNestedChildrenToArray(tag: ITag, resultArray: ITag[]) {
    resultArray.push(tag);
    if (tag.inverseParent) {
      tag.inverseParent.forEach(child => {
        this.addNestedChildrenToArray(child, resultArray);
      });
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
