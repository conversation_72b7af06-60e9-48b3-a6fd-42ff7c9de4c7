import { Component, Inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogActions, MatDialogRef } from '@angular/material/dialog';
import { IonicModule } from '@ionic/angular';

@Component({
    selector: 'app-personalize-quil-dialog',
    templateUrl: './personalize-quil-dialog.component.html',
    styleUrls: ['./personalize-quil-dialog.component.scss'],
    imports: [IonicModule, FormsModule, MatDialogActions]
})
export class PersonalizeQuilComponent {
  selectedPersonalizationOptions: string[] = [];
  personalizationOptions: string[] = ['First Name', 'Last Name', 'Organization Name', 'Job Title', 'Instance Name'];
  search = '';
  public searchInput: string;
  public searchResult: Array<any> = [];
  public seriesList: Array<any> = [];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<PersonalizeQuilComponent>
  ) {}

  fetchSeries(event: any) {
    if (event.target.value === '') {
      this.searchResult = [];
      return '';
    } else {
      this.searchResult = this.seriesList.filter(series => {
        return series.name.toLowerCase().startsWith(event.target.value.toLowerCase());
      });
    }
    return '';
  }

  onSearchChange() {
    const filteredItems = this.seriesList.filter(x => x.name?.indexOf(this.search) > -1);
    this.searchResult = Object.assign([], filteredItems);
  }

  onClose(resend = false) {
    this.dialogRef.close(resend);
  }

  optionSelect() {
    //Append option to array and list the selected options in buttons that can be clicked and removed from the array
  }
}
