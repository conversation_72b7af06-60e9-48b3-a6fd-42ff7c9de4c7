import { AsyncPipe } from '@angular/common';
import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { DomSanitizer, SafeHtml, SafeResourceUrl } from '@angular/platform-browser';
import { IAsset, IComponent, IInstanceSectionComponent } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { map, Observable } from 'rxjs';
import { ImageViewerValueComponent } from '@app/standalone/components/image-viewer-value/image-viewer-value.component';
import { VideoPlayerComponent } from '@app/standalone/components/video-player/video-player.component';
import { FileDownloadControlComponent } from '@app/standalone/components/file-download-control/file-download-control.component';

@Component({
    selector: 'app-media-block',
    templateUrl: './media-block.component.html',
    styleUrls: ['./media-block.component.scss'],
    imports: [ImageViewerValueComponent, VideoPlayerComponent, FileDownloadControlComponent, AsyncPipe]
})
export class MediaBlockComponent implements OnInit, OnChanges {
  @Input() assetId: string | undefined;
  @Input() instanceId: string;
  @Input() component: IComponent;
  @Input() instanceSectionComponent: IInstanceSectionComponent | undefined;
  @Input() inheritedPropertyValue: string | null;
  asset$: Observable<IAsset>;
  asset: IAsset;
  safeHtml: SafeHtml;

  constructor(
    private dataService: DataService,
    public builderService: BuilderService,
    private sanitizer: DomSanitizer
  ) {}

  get safeUrl(): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(`${this.asset?.urlUpload}`);
  }

  get isImage(): boolean {
    if (this.asset) {
      return this.builderService.imageContentTypes?.some(x => x === this.asset?.contentType);
    }
    return false;
  }

  get isVideo(): boolean {
    if (this.asset) {
      return this.builderService.videoContentTypes?.some(x => x === this.asset?.contentType);
    }
    return false;
  }

  get isFile(): boolean {
    if (this.asset) {
      return this.builderService.fileContentTyps?.some(x => x === this.asset?.contentType);
    }
    return false;
  }

  ngOnInit() {
    this.getAsset();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['assetId']) {
      this.getAsset();
    }
  }

  setSafeHtml() {
    this.safeHtml = this.sanitizer.bypassSecurityTrustHtml(`${this.asset?.embedCode}`);
  }

  getAsset() {
    if (!this.assetId || this.assetId.length === 0) {
      return;
    }
    this.asset$ = this.dataService.getAssetDetailsById(this.assetId).pipe(
      map(res => {
        if (res) {
          this.asset = res;

          if (this.asset.mediaUploadType === 'Embed') {
            this.setSafeHtml();
          }
        } else {
          this.asset = {} as IAsset;
        }

        return res;
      })
    );
  }
}
