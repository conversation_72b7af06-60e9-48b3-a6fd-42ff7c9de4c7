import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { IAction, IProductFeature, IProductFeatureInstance, IProductFeatureInstanceIn, IProductFeatureRoleIn, IRole } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { AlertService } from '@app/core/services/alert-service';
import { DataService } from '@app/core/services/data-service';
import { ModalController, PopoverController, IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { MatTabChangeEvent, MatTabGroup, MatTab, MatTabContent } from '@angular/material/tabs';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { OverlayEventDetail } from '@app/features-shared/row-instance/components/content/grid-view/thumbnail-styles/styles/image-background/image-background.component';
import { ConfirmationDialogComponent } from '../../modals/confirmation-dialog/confirmation-dialog.component';
import { AddProductFeatureComponent } from './add-product-feature/add-product-feature.component';
import { MatIcon } from '@angular/material/icon';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { MatCheckbox } from '@angular/material/checkbox';
import { NgArrayPipesModule } from 'ngx-pipes';

@Component({
    selector: 'app-product-action-roles',
    templateUrl: './product-action-roles.component.html',
    styleUrls: ['./product-action-roles.component.scss'],
    imports: [IonicModule, MatIcon, MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, MatTabGroup, MatTab, MatTabContent, MatCheckbox, FormsModule, ReactiveFormsModule, NgArrayPipesModule]
})
export class ProductActionRolesComponent implements OnInit, OnDestroy {
  @Input() id: string | null | undefined;
  @Input() type: string;
  productFeatures: IProductFeature[] = [];
  productFeatureRoleIn: IProductFeatureRoleIn;
  productFeatureInstances: IProductFeatureInstance[] = [];
  actions: IAction[] = [];
  actionTypes = ActionTypes;
  userRoleName: string | undefined;
  hasProductFeatures = false;
  currentAmount = 0;
  moreResults = false;
  getAmount = 25;
  currentInstanceAmount = 0;
  moreInstanceResults = false;
  searchForm: UntypedFormGroup;
  searchControl: UntypedFormControl;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private dataService: DataService,
    private alertService: AlertService,
    private popoverController: PopoverController,
    private modalController: ModalController
  ) {}
  ngOnInit() {
    //UniqueForUserManager:
    if (this.id === undefined && this.type === 'User Manager') {
      this.id = '00000000-0000-0000-0000-000000000000';
    }
    this.getActions();
    this.createFormControls();
    this.createForm();
  }

  createFormControls() {
    this.searchControl = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      searchControl: this.searchControl,
    });
  }

  getActions() {
    if (this.id) {
      this.dataService
        .getActions()
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((actions: IAction[]) => {
          if (actions) {
            this.actions = actions;
            if (this.id) {
              this.getProductActionFeaturesById(this.id, false);
            }
          }
        });
    }
  }

  getProductActionFeaturesById(id: string, loadMore: boolean) {
    if (id) {
      this.dataService
        .getProductActionFeaturesById(id, this.type, this.currentAmount, this.getAmount)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((productFeatures: IProductFeature[]) => {
          if (productFeatures.length > 0) {
            //OnScrollDataLoad
            if (!loadMore) {
              this.productFeatures = productFeatures;
              this.currentAmount += productFeatures.length;
            } else {
              productFeatures.forEach(feat => {
                this.productFeatures = [...this.productFeatures, feat];
              });
              this.currentAmount += productFeatures.length;
            }

            if (productFeatures.length < this.getAmount) {
              this.moreResults = false;
            } else {
              this.moreResults = true;
            }
          }
        });
    }
  }

  getProductFeatureRolesById(productFeatId: string) {
    this.dataService
      .getProductFeatureRolesById(productFeatId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((roles: IRole[]) => {
        //MapProductFeatureRolesToTheSelectedProductFeature.
        const selectedProductFeature = this.productFeatures.find(x => x.id === productFeatId);
        if (selectedProductFeature) {
          selectedProductFeature.productFeatureRoles = roles;
        }
      });
  }

  manageActionChange(event: any, actionTypeBw: number, selectedRole: IRole, featId: string) {
    //UpdateEdit
    let sumTypeBw = actionTypeBw;
    if (selectedRole.productFeatRoles.length > 0) {
      selectedRole.productFeatRoles.forEach(role => {
        if (event.checked) {
          role.actionBw |= actionTypeBw;
        } else {
          role.actionBw ^= actionTypeBw;
        }

        sumTypeBw = role.actionBw;
      });
    } else if (this.productFeatureRoleIn != null) {
      if (this.productFeatureRoleIn.productFeatureId === featId && this.productFeatureRoleIn.id === selectedRole.id) {
        if (event.checked) {
          sumTypeBw = this.productFeatureRoleIn.actionBw |= actionTypeBw;
        } else {
          sumTypeBw = this.productFeatureRoleIn.actionBw ^= actionTypeBw;
        }

        sumTypeBw = this.productFeatureRoleIn.actionBw;
      }
    }

    const productFeatureRoleIn: IProductFeatureRoleIn = {
      id: selectedRole.id,
      actionBw: sumTypeBw,
      productFeatureId: featId,
    };

    this.productFeatureRoleIn = productFeatureRoleIn;
    this.saveProductFeatureRoles();
  }

  isSelected(selectedRole: IRole, actionBw: number) {
    let returnVal = false;
    if (selectedRole.productFeatRoles.length > 0) {
      const sumTypeBw = selectedRole.productFeatRoles.reduce((a, b) => a | b.actionBw, 0);
      if (sumTypeBw & actionBw) {
        returnVal = true;
      } else {
        returnVal = false;
      }
    }
    return returnVal;
  }

  saveProductFeatureRoles() {
    if (this.productFeatureRoleIn) {
      this.dataService.updateProductFeatureRoles(this.productFeatureRoleIn).pipe(takeUntil(this.componentDestroyed$)).subscribe();
    }
  }

  showAlert() {
    this.alertService.presentAlert('', 'Product feature(s) updated');
  }

  async addProductFeature() {
    const popover = await this.popoverController.create({
      component: AddProductFeatureComponent,
      cssClass: 'product-add-popover',
      componentProps: { productId: this.id },
      animated: true,
      side: 'bottom',
    });

    popover.onDidDismiss().then(result => {
      if (result.data) {
        this.productFeatures = [...this.productFeatures, result.data];
        this.currentAmount += this.productFeatures.length;
        this.showAlert();
      }
    });

    await popover.present();
  }

  getProductFeatureInstances(id: string, loadMore: boolean) {
    if (id) {
      if (!loadMore) {
        this.currentInstanceAmount = 0;
      }
      this.dataService
        .getProductFeatureInstances(id, this.currentInstanceAmount, this.getAmount, this.searchControl.value)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((productFeatureInstances: IProductFeatureInstance[]) => {
          if (productFeatureInstances.length > 0) {
            //OnScrollDataLoad
            if (!loadMore) {
              this.productFeatureInstances = productFeatureInstances;
              this.currentInstanceAmount += productFeatureInstances.length;
            } else {
              productFeatureInstances.forEach(feat => {
                this.productFeatureInstances = [...this.productFeatureInstances, feat];
              });
              this.currentInstanceAmount += productFeatureInstances.length;
            }

            if (productFeatureInstances.length < this.getAmount) {
              this.moreInstanceResults = false;
            } else {
              this.moreInstanceResults = true;
            }
          }
        });
    }
  }

  updateProductFeatureInstance(event: any, id: string, instanceId: string) {
    const productFeatureInstanceIn = { id: id, instanceId, isActive: event.checked } as IProductFeatureInstanceIn;
    this.dataService.updateProductFeatureInstance(productFeatureInstanceIn).pipe(takeUntil(this.componentDestroyed$)).subscribe();
  }

  tabSelected($event: MatTabChangeEvent, id: string) {
    this.currentAmount = 0;
    this.moreResults = false;
    switch ($event.index) {
      case 0: {
        this.ngOnDestroy();
        this.getProductFeatureRolesById(id);
        this.productFeatureInstances = [];
        break;
      }
      case 1: {
        this.ngOnDestroy();
        this.getProductFeatureInstances(id, false);
        break;
      }
    }
  }

  async removeFeature(productfeature: IProductFeature) {
    const modal = await this.modalController.create({
      component: ConfirmationDialogComponent,
      cssClass: 'confirm-dialog',
      componentProps: {
        headerText: 'Delete feature',
        bodyText: `Are you sure you would like to delete ${productfeature.title}?`,
        buttonText: 'Delete',
      },
    });

    modal.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
      if (overlayEventDetail.role === 'confirm') {
        if (this.id) {
          this.dataService
            .deleteProductFeature(productfeature.id)
            .pipe(takeUntil(this.componentDestroyed$))
            .subscribe(res => {
              if (res === true) {
                const indexToRemove = this.productFeatures.findIndex(x => x.id === productfeature.id);
                if (indexToRemove !== -1) {
                  this.productFeatures.splice(indexToRemove, 1);
                  this.productFeatures = [...this.productFeatures];
                }
              }
            });
        }
      }
    });

    await modal.present();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
