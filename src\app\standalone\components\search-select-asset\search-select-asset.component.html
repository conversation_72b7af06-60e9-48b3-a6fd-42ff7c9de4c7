<form [formGroup]="searchForm">
  <ion-grid>
    <ion-row class="ion-align-items-center">
      <ion-col offset="1" size="10" style="text-align: center">
        <h5>Add asset to Media Block</h5>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-searchbar color="dark" formControlName="assetSearch" (ionChange)="searchAssets()" type="search" placeholder="Search for asset" showCancelButton="focus" debounce="600"></ion-searchbar>
      </ion-col>
    </ion-row>
  </ion-grid>
</form>
<ion-content class="instance-content">
  <ion-card class="hospital-group">
    <ion-label class="feature-group-header" color="primary"> Assets </ion-label>
    <ion-grid class="instance-grid">
      @for (asset of assets; track asset) {
        <ion-row>
          <ion-col size="12" (click)="addAsset(asset)" class="ion-no-padding">
            {{ asset.name }}
          </ion-col>
        </ion-row>
      }
    </ion-grid>
  </ion-card>
</ion-content>
