<div class="parent-container">
  <ion-grid>
    <ion-row class="view-options-row">
      @if (type !== 'Accredited Package' && type !== 'Modifiable Package') {
        <div class="assignments-heading">
          <app-heading-value [headingModal]="heading" [fontSize]="22"></app-heading-value>
        </div>
      }
    </ion-row>
  </ion-grid>
  @if (isLoading) {
    <div class="loading-overlay">
      <div class="loading-spinner">
        <ion-spinner name="circular"></ion-spinner>
      </div>
    </div>
  }
  <!-- Outer table shows assignments instead of people -->
  <mat-table #table [dataSource]="assignmentsDataSource" matSort #sort="matSort" multiTemplateDataRows class="custom-table">
    <ng-container matColumnDef="name">
      <mat-header-cell *matHeaderCellDef mat-sort-header>ASSIGNMENT</mat-header-cell>
      <mat-cell *matCellDef="let element">
        <div class="row">
          <img class="icon" [src]="getCoverUrl(element?.instance?.coverMediaAssetId)" onerror="this.src='assets/images/no-image.png'" alt="icon" />
          <div class="name-column">
            <span class="yellow-name">{{ element?.instance?.title }}</span>
            <span class="subheading">{{ element?.instance?.feature?.featureType?.name }}</span>
          </div>
        </div>
      </mat-cell>
    </ng-container>
    <ng-container matColumnDef="grade">
      <mat-header-cell *matHeaderCellDef mat-sort-header>AVERAGE GRADE</mat-header-cell>
      <mat-cell *matCellDef="let element">
        <div class="row-space-between">
          <span class="grade-text">{{ !element.containsGrading ? ' - ' : element.grade + '%' }}</span>
          <ion-button class="grade-button" (click)="setExpandedElement(element)">
            Students<ion-icon class="grade-icon" slot="end" [name]="expandedElement === element ? 'chevron-up' : 'chevron-down'"></ion-icon>
          </ion-button>
        </div>
      </mat-cell>
    </ng-container>

    <!-- Expanded Content Column - The detail row is made up of this one column that spans across all columns -->
    <ng-container matColumnDef="expandedDetail">
      <mat-cell *matCellDef="let element" [attr.colspan]="displayedColumns.length">
        @if (element === expandedElement) {
          <!-- Use the grading-table-users component for the inner table -->
          <app-grading-table-users class="inner-table" [instance]="element.instance" [assignment]="element" [people]="instanceUsers"> </app-grading-table-users>
        }
      </mat-cell>
    </ng-container>

    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
    <mat-row *matRowDef="let element; columns: displayedColumns" class="example-element-row" [class.example-expanded-row]="expandedElement === element"></mat-row>
    <mat-row
      *matRowDef="let row; columns: ['expandedDetail']"
      class="example-detail-row"
      [class.example-detail-expanded-row]="expandedElement === row"
      [@detailExpand]="expandedElement === row ? 'expanded' : 'collapsed'"></mat-row>
  </mat-table>
  @if (moreResults) {
    <div (click)="getAssignmentsById(id ?? '', true)" class="load-more">
      <ion-row>
        <ion-col size="12">
          <div>Load More</div>
          <div><ion-icon name="chevron-down-outline"></ion-icon></div>
        </ion-col>
      </ion-row>
    </div>
  }
</div>
@if (hasAdminAccess() && peopleSelection.selected.length > 0) {
  <div class="selection-bar">
    <div class="selected-container">
      <ion-icon (click)="clearSelections()" name="close-outline"></ion-icon>
      {{ peopleSelection.selected.length }} Students Selected
    </div>
    <div class="buttons-container">
      <ion-label (click)="updateSelected('Deleted')"> <ion-icon name="remove-circle"></ion-icon>Remove</ion-label>
    </div>
    <div class="buttons-container">
      <ion-label (click)="updateSelected('Active')"> <ion-icon name="people"></ion-icon>Approve</ion-label>
    </div>
    <div class="buttons-container">
      <ion-label (click)="updateSelected('Suspended')"> <ion-icon name="ban-outline"></ion-icon>Deny</ion-label>
    </div>
  </div>
}
