import { BehaviorSubject, combineLatest, map } from 'rxjs';

export class PlayerViewSidePanelSelectionService {
  private _selectedContentId = new BehaviorSubject<string>('');
  private _selectedContentRowId = new BehaviorSubject<string>('');

  selectedContentId$ = this._selectedContentId.asObservable();
  selectedContentRowId$ = this._selectedContentRowId.asObservable();
  
  selectionState$ = combineLatest([
    this.selectedContentId$,
    this.selectedContentRowId$
  ]).pipe(
    map(([contentId, rowId]) => ({
      selectedContentId: contentId,
      selectedContentRowId: rowId
    }))
  );

  get selectedContentId(): string {
    return this._selectedContentId.value;
  }

  get selectedContentRowId(): string {
    return this._selectedContentRowId.value;
  }

  setSelectedContentId(id: string) {
    this._selectedContentId.next(id);
  }

  setSelectedContentRowId(id: string) {
    this._selectedContentRowId.next(id);
  }
}
