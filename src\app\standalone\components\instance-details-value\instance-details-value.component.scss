@media screen and (min-width: 767px) {
  .moveOverBanner {
    position: static;
    margin-top: -150px;
  }
}

@media screen and (max-width: 766px) {
  .moveOverBanner {
    position: static;
    margin-top: -100px;
  }
}

.parent-container {
  .padding-top {
    padding-top: 50px;
  }

  .center {
    display: flex;
    text-align: center;
  }

  .inner-container {
    display: flex;
    align-items: flex-start;
    width: 100%;
    padding-bottom: 20px;

    .img-col {
      min-width: 60px;
      display: flex;
      justify-content: flex-start;
      margin-top: 10px;
      margin-bottom: 10px;
      margin-right: 1vw;

      .icon-org {
        background-color: #ffffff;
        object-fit: contain !important;
        padding: 10px;
      }

      .icon {
        object-fit: cover;
        border-radius: 5px;
        border: 1px solid #333;
      }

      @media (min-width: 491px) {
        .icon {
          width: 100px;
          height: 100px;
        }
      }

      @media (max-width: 491px) {
        .icon {
          width: 70px;
          height: 70px;
        }
      }
    }

    .center-header {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      align-content: center;
      
      .title-container {
        .title,
        .title h1,
        .title h2,
        .title h3, 
        .title h4,
        .title h5,
        .title h6 {
          text-align: center !important;
        }
      }
    }

    .header-col {
      padding-top: 35px;
      .title-container {
        margin-bottom: 7px;
        .title {
          text-align: left;
          color: #fff;
          font-family: 'Exo 2';
          font-weight: 800;
          font-size: calc(35px + 0.22vw);
          letter-spacing: 0.03em;
          line-height: 1.1;

          h1 {
            text-align: left;
            color: #fff;
            font-family: 'Exo 2';
            font-weight: 800;
            font-size: calc(40px + 0.22vw);
            letter-spacing: 0.03em;
            line-height: 1.1;
          }

          h2 {
            text-align: left;
            color: #fff;
            font-family: 'Exo 2';
            font-weight: 800;
            font-size: calc(35px + 0.22vw);
            letter-spacing: 0.03em;
            line-height: 1.1;
          }

          h3 {
            text-align: left;
            color: #fff;
            font-family: 'Exo 2';
            font-weight: 800;
            font-size: calc(29px + 0.22vw);
            letter-spacing: 0.03em;
            line-height: 1.1;
          }

          h4 {
            text-align: left;
            color: #fff;
            font-family: 'Exo 2';
            font-weight: 800;
            font-size: calc(24px + 0.22vw);
            letter-spacing: 0.03em;
            line-height: 1.1;
          }

          h5 {
            text-align: left;
            color: #fff;
            font-family: 'Exo 2';
            font-weight: 800;
            font-size: calc(20px + 0.22vw);
            letter-spacing: 0.03em;
            line-height: 1.1;
          }

          h6 {
            text-align: left;
            color: #fff;
            font-family: 'Exo 2';
            font-weight: 800;
            font-size: calc(18px + 0.22vw);
            letter-spacing: 0.03em;
            line-height: 1.1;
          }
        }

        @media (min-width: 768px) {
          .title {
            font-size: 33px;
          }
        }
        @media (max-width: 768px) {
          .title {
            font-size: 30px;
          }
        }
      }

      .sub-heading-container {
        .sub-heading {
          margin-top: 5px;
          color: #cccccc;
          font-family: 'Roboto';
          font-weight: 400;
          font-size: 16px;
          font-style: italic;
          text-decoration: underline;
          text-decoration-color: green;
        }
      }

      .description-container {
        .description {
          color: #cccccc;
          font-family: 'Roboto';
          font-weight: 400;
          font-size: 1.25em;
          line-height: 1.4;
        }
      }
    }
  }
}
