.parent-container {
  margin-left: var(--page-margin-left);
  margin-right: var(--page-margin-right);
  ion-grid {
    padding: 0px;
    .view-options-row {
      color: white;
      justify-content: space-between;
      margin-bottom: 10px;

      ion-col {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        height: 50px;
      }

      .end-col-buttons {
        justify-content: flex-end;
        ion-icon {
          margin-right: 5px;
        }
        // ion-button {
        //   margin-right: 10px;
        // }
      }
    }

    mat-expansion-panel {
      margin-bottom: 10px;
      background-color: rgb(17, 17, 17);

      .expansion-panel-header {
        height: 100%;
        border-radius: 8px;
        border-bottom-left-radius: 0px;
        border-bottom-right-radius: 0px;
        background-color: rgba(41, 41, 41);
        padding: 15px;

        dlt-button-col {
          display: flex;
          justify-content: flex-end;
          padding-right: 20px;
        }

        .inner-panel {
          width: 100%;
          ion-row {
            width: 100%;
            .dlt-button-col {
              display: flex;
              justify-content: flex-end;
              padding-right: 20px;
            }
          }
          .heading {
            font-weight: bold;
            font-size: 20px;
            color: white;
          }
          .sub-heading {
            width: 800px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 5px;
            font-style: italic;
            color: rgba(170, 170, 170);
          }
        }
      }

      .expansion-panel-header:hover {
        background-color: rgba(41, 41, 41) !important;
      }

      ::ng-deep .mat-expansion-panel-body {
        border-top: 1px solid rgba(255, 255, 255, 0.5);
        padding-left: 15px !important;
        background-color: rgba(41, 41, 41) !important;
      }

      .content {
        flex: 1;
        display: flex;
        align-items: stretch;
        align-content: stretch;
        flex-direction: column;
        .inner-panel {
          .instance-heading {
            font-weight: bold;
            font-size: 18px;
            color: white;
          }
          .instance-sub-heading {
            width: 800px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 5px;
            font-style: italic;
            color: rgba(170, 170, 170);
          }
        }

        .action-selectors-container {
          display: flex;
          align-items: center;
          background-color: rgba(41, 41, 41);
          color: white;
          margin: 10px 0px;
          .role-name {
            margin-right: 10px;
            font-size: 20px;
          }
          .action-name {
            font-size: 16px;
          }
          .action-checkbox {
            margin: 10px;
            mat-checkbox {
              margin-right: 10px;
            }
          }
          ::ng-deep .mat-mdc-checkbox-checked .mat-mdc-checkbox-background {
            background-color: rgba(250, 167, 0) !important;
          }

          ::ng-deep .mdc-checkbox__checkmark {
            border-color: white;
          }
          ::ng-deep .mat-mdc-checkbox-inner-container {
            width: 18px;
            height: 18px;
          }
        }

        .instance-selectors-container {
          align-items: center;
          background-color: rgba(41, 41, 41);
          color: white;
          margin: 10px 0px;
          padding: 1px 0px;
          ion-col {
            &:first-of-type {
              flex: 0 0 20px;
            }
          }
          .instance-checkbox {
            margin: 10px;

            mat-checkbox {
              margin-right: 10px;
            }
          }

          ::ng-deep .mat-mdc-checkbox-checked .mat-mdc-checkbox-background {
            background-color: rgba(250, 167, 0) !important;
          }

          ::ng-deep .mdc-checkbox__checkmark {
            border-color: white;
          }

          ::ng-deep .mat-mdc-checkbox-inner-container {
            width: 18px;
            height: 18px;
          }
        }

        .search-bar-row {
          margin-bottom: 10px;
          ion-col {
            display: flex;
            justify-content: center;
            align-items: center;

            ion-searchbar {
              border-radius: 5px;
              border: 2px solid rgba(0, 0, 0, 0.3);
              padding: 0px;
            }
          }
        }
      }
    }

    .load-more {
      font-size: 16px;
      color: white;
      margin-top: 20px;
      text-align: center;
      display: flex;
      justify-content: center;
      cursor: pointer;
    }
  }
}
