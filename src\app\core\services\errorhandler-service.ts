import { ErrorHandler, Injectable } from '@angular/core';
import { MonitoringService } from './monitoring-service';

@Injectable()
export class ErrorHandlerService extends ErrorHandler {
  constructor(private monitoringService: MonitoringService) {
    super();
  }

  override handleError(error: Error) {
    // const chunkFailedMessage = /Loading chunk [\d]+ failed/;
    // if (chunkFailedMessage.test(error.message)) {
    //   window.location.reload();
    // }
    console.error(error);
    this.monitoringService.logException(error);
  }
}
