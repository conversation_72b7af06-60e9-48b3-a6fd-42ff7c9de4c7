import { Component, OnDestroy, OnInit } from '@angular/core';
import { AuthService } from '@app/core/services/auth-service';
import { NavController } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-auth-callback-pop',
    styleUrls: ['./auth-callback-pop.scss'],
    templateUrl: './auth-callback-pop.html',
    standalone: false
})
export class AuthCallbackPopComponent implements OnInit, OnDestroy {
  componentDestroyed$: Subject<boolean> = new Subject();
  returnUrl = '/my-journey';
  constructor(
    private authService: AuthService,
    private nav: NavController
  ) {}

  ngOnInit() {
    if (sessionStorage.getItem('returnUrl') && sessionStorage.getItem('returnUrl') !== '/' && sessionStorage.getItem('returnUrl') !== '/splash') {
      this.returnUrl = sessionStorage.getItem('returnUrl') ?? this.returnUrl;
    }

    this.authService.init();
    this.authService
      .completeAuthentication()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.nav.navigateRoot(this.returnUrl).then(() => {
          sessionStorage.setItem('logoutWindow', 'true');
          const refresh = new BroadcastChannel('refresh');
          refresh.postMessage('refresh');
        });
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
