import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { IEarningCriteriaContent, IEarningCriteriaContentIn, IEarningCriteriaIn } from '@app/core/contracts/contract';
import { AlertService } from '@app/core/services/alert-service';
import { PopoverController, IonicModule } from '@ionic/angular';
import { ChipListComponent } from '../../chip-list/chip-list.component';
import { AddSearchModalComponent } from '@app/standalone/modals/add-search-modal/add-search-modal.component';

@Component({
    selector: 'app-complete-content',
    templateUrl: './complete-content.component.html',
    styleUrls: ['./complete-content.component.scss'],
    imports: [IonicModule, ChipListComponent]
})
export class CompleteContentComponent implements OnInit {
  @Input() type: string;
  @Input() earningCriteria: IEarningCriteriaIn;
  @Output() criteriaUpdated: EventEmitter<IEarningCriteriaContentIn[]> = new EventEmitter();

  constructor(
    private popoverController: PopoverController,
    private alertService: AlertService
  ) {}

  ngOnInit() {
    this.setCriteriaContentListDefault();
  }

  async addSearchModalOpen(event: any) {
    const popover = await this.popoverController.create({
      component: AddSearchModalComponent,
      cssClass: 'add-search-modal',
      componentProps: { linkTypeName: 'Content', criteriaType: this.type },
      event: event,
      side: 'bottom',
    });

    popover.onDidDismiss().then(result => {
      if (result.data) {
        const criteriaIn = { earningCriteriaId: this.earningCriteria.id, refId: result.data.id, type: result.data.type, name: result.data.name } as IEarningCriteriaContentIn;
        const currentIndex = this.earningCriteria.earningCriteriaContent?.findIndex(x => x.refId === result.data.id);
        if (!currentIndex || currentIndex === -1) {
          this.earningCriteria.earningCriteriaContent.push(criteriaIn as IEarningCriteriaContent);
          this.emitCompleteContentChanges();
        } else {
          this.showAlert();
        }
      }
    });

    await popover.present();
  }

  setCriteriaContentListDefault() {
    if (!this.earningCriteria.earningCriteriaContent) {
      this.earningCriteria.earningCriteriaContent = [];
    }
  }

  checkToRemoveById(removeCriteriaContent: IEarningCriteriaContent) {
    const indexIn = this.earningCriteria.earningCriteriaContent.indexOf(removeCriteriaContent);
    if (indexIn === -1) {
      const criteriaContentIn = {
        id: removeCriteriaContent.id,
        earningCriteriaId: removeCriteriaContent.earningCriteriaId,
        refId: removeCriteriaContent.refId,
        type: removeCriteriaContent.type,
        name: removeCriteriaContent.name,
        isDeleted: true,
      } as IEarningCriteriaContentIn;

      this.earningCriteria.earningCriteriaContent.push(criteriaContentIn);
    } else {
      this.earningCriteria.earningCriteriaContent[indexIn].isDeleted = true;
    }

    this.emitCompleteContentChanges();
  }

  getChipListItems() {
    return this.earningCriteria.earningCriteriaContent.filter(x => x.isDeleted !== true);
  }

  showAlert() {
    this.alertService.presentAlert('', 'Content already selected!');
  }

  emitCompleteContentChanges() {
    this.criteriaUpdated.emit(this.earningCriteria.earningCriteriaContent);
  }
}
