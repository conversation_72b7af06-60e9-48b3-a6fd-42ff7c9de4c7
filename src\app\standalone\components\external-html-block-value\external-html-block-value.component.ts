import { Component, Input, OnInit } from '@angular/core';
import { Dom<PERSON>anitizer, SafeResourceUrl } from '@angular/platform-browser';
import { NavController, IonicModule } from '@ionic/angular';
import { AuthoringHeaderComponent } from '../authoring-header/authoring-header.component';

@Component({
    selector: 'app-external-html-block-value',
    templateUrl: './external-html-block-value.component.html',
    styleUrls: ['./external-html-block-value.component.scss'],
    imports: [IonicModule, AuthoringHeaderComponent]
})
export class ExternalHtmlBlockValueComponent implements OnInit {
  @Input() resourceUrl: string | undefined;
  @Input() openExternal: boolean | undefined;
  @Input() builderPreviewView = false;
  safeUrl: SafeResourceUrl;

  constructor(
    private sanitizer: DomSanitizer,
    private navCtrl: NavController
  ) {}

  get componentName(): string {
    return 'External Html Block';
  }

  ngOnInit(): void {
    if (this.openExternal) {
      window.open(this.resourceUrl, '_blank');
      this.navCtrl.back();
    } else {
      this.setSafeUrl();
    }
  }

  setSafeUrl() {
    if (this.resourceUrl) {
      this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`${this.resourceUrl}`);
    }
  }
}
