@if (!templateField?.isVariable) {
  <div [ngClass]="{ container: noPadding === false }">
    @if (displayValue) {
      <div>
        <app-text-value defaultValue="{{ templateField?.label }}:  {{ displayValue }}"></app-text-value>
      </div>
    }
    @if (!displayValue) {
      <div>
        <app-text-value [defaultValue]="templateField?.defaultText ?? templateField?.label"></app-text-value>
      </div>
    }
  </div>
}
