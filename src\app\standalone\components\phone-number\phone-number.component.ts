import { Component, forwardRef, Input, OnDestroy, OnInit } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR, UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';

@Component({
    selector: 'app-phone-number',
    templateUrl: './phone-number.component.html',
    styleUrls: ['./phone-number.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => PhoneNumberComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => PhoneNumberComponent),
        },
    ],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, TextInputControlComponent]
})
export class PhoneNumberComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() placeHolder!: string;
  @Input() override label!: string;
  @Input() override disabled = false;
  @Input() backgroundColor = '#181818';
  @Input() noPadding = false;

  componentDestroyed$: Subject<boolean> = new Subject();
  numberForm: UntypedFormGroup;
  formValueChanges$: Subscription;

  constructor(private systemPropertiesService: SystemPropertiesService) {
    super();
  }

  ngOnInit() {
    this.createFormControls();
  }

  createFormControls() {
    if (!this.systemPropertiesService.userProperties) {
      this.numberForm = new UntypedFormGroup({
        phoneNumber: new UntypedFormControl(''),
      });
      return;
    }

    const phoneNumber = this.systemPropertiesService.userProperties.find(x => x.key.includes('phone_number'));
    this.numberForm = new UntypedFormGroup({
      phoneNumber: new UntypedFormControl(phoneNumber?.value),
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.numberForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      const phoneNumber = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('phone_number'));
      this.systemPropertiesService.userProperties[phoneNumber].value = this.numberForm.controls.phoneNumber.value;
    });
  }

  setFormControlValues() {
    const phoneNumber = this.systemPropertiesService.userProperties.find(x => x.key.includes('phone_number'));
    this.numberForm.controls.addressLine1.setValue(phoneNumber?.value);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
