<div class="scroll-box" (scroll)="scrolled($event)">
  <div class="table-container">
    <mat-table class="mat-elevation-z8" [dataSource]="dataSourceIn" (matSortChange)="onSortChange($event)" matSort>
      @for (columnName of columnsToDisplayIn; track columnName) {
        <ng-container [matColumnDef]="columnName" [sticky]="columnName === 'Id' || columnName === 'Name'">
          @if (checkColumnIsSystemProperty(columnName) || columnName === 'Name') {
            <mat-header-cell mat-header-cell *matHeaderCellDef mat-sort-header>{{ columnName }}</mat-header-cell>
          } @else {
            <mat-header-cell mat-header-cell *matHeaderCellDef>{{ columnName }}</mat-header-cell>
          }
          <mat-cell mat-cell *matCellDef="let element">
            @switch (columnName) {
              @case ('Id') {
                <div aria-hidden="true" class="id-route" (click)="routeOut(element.tableId)">
                  {{ element.tableId }}
                </div>
              }
              @case ('Name') {
                <div>
                  {{ element.name }}
                </div>
              }
            }
            @for (column of element.columns; track column) {
              @if (column.columnName === columnName) {
                <div
                  aria-hidden="true"
                  (click)="column.component?.componentType?.name !== ('Password' || 'Organization') ?? editDashboardComponent(element.tableId, column.value, column.component, column.id)">
                  @switch (column.component?.componentType?.name) {
                    @case (
                      column.component?.componentType?.name === 'Text' || column.component?.componentType?.name === 'Text Area' || column.component?.componentType?.name === 'Icon & Text' || column.component?.componentType?.name === 'WYSIWYG'
                        ? column.component?.componentType?.name
                        : ''
                    ) {
                      <div class="inner-container" [ngStyle]="!column.value ? { color: 'gray', 'font-style': 'italic' } : {}">
                        {{ (column.value | parsePipe: element.tableId | async) ?? column.component.componentType.name }}
                      </div>
                    }
                    @case ('Image Upload Field') {
                      @if (column.value) {
                        <div class="inner-container image-container">
                          <img [src]="setImageUrl(column.value)" [alt]="''" />
                        </div>
                      } @else {
                        <div class="inner-container" style="color: 'gray'; font-style: 'italic'">{{ column.component.componentType.name }}</div>
                      }
                    }
                    <!-- <ng-container *ngIf="column.component?.componentType?.name === 'Dropdown' && column.isMultiple">
                <ng-container *ngIf="column.value">
                  <app-chip-list [type]="column.component?.componentType?.name" [listItems]="column.listItems"></app-chip-list>
                </ng-container>
                <ng-container *ngIf="!column.value">
                  <div class="inner-container" style="color: 'gray'; font-style: 'italic'">{{ column.component.componentType.name }}</div>
                </ng-container>
              </ng-container> -->
                    <!-- DROPDOWN -->
                    @case ('Dropdown') {
                      @if (
                        column.listItems?.length > 0 &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'User Tags' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Organization Status' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Instance Status'
                      ) {
                        <app-chip-list (selectedToRemoveOut)="removeTagById($event.selected, element, column.component, $event.chip)" [type]="column.component?.componentType?.name" [listItems]="column.listItems"></app-chip-list>
                      } @else if (column.listItems?.length > 0 && column.component?.templateField?.dropDownLinkType?.title === 'User Tags') {
                        <app-tag-popover [viewType]="2" [selectedUserId]="element.userId" [dropDownLinkType]="'User Tags'" [component]="column.component"> </app-tag-popover>
                      } @else if (
                        column.listItems?.length === 0 &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'User Tags' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Organization Status' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Instance Status'
                      ) {
                        <div class="inner-container" [ngStyle]="!column.value ? { color: 'gray', 'font-style': 'italic' } : {}">{{ column.value ?? column.component.componentType.name }}</div>
                      } @else if (column.listItems?.length === 0 && column.component?.templateField?.dropDownLinkType?.title === 'User Tags') {
                        <app-tag-popover [viewType]="2" [selectedUserId]="element.userId" [dropDownLinkType]="'User Tags'" [component]="column.component"> </app-tag-popover>
                      } @else if (column.component?.templateField?.dropDownLinkType?.title === 'Organization Status' && orgStatuses) {
                        <!-- ORG STATUS -->
                        <app-org-publish-button
                          [orgStatuses]="orgStatuses"
                          [columnValue]="column.value"
                          (statusChanged)="updateComponent(element.tableId, $event, column.component, column.id)"></app-org-publish-button>
                      } @else if (column.component?.templateField?.dropDownLinkType?.title === 'Instance Status') {
                        <!-- INSTANCE STATUS -->
                        <app-instance-publish-button
                          [feature]="feature"
                          [columnValue]="column.value"
                          (statusChanged)="updateComponent(element.tableId, $event, column.component, column.id)"></app-instance-publish-button>
                      }
                    }
                    <!-- ICON & DROPDOWN -->
                    @case ('Icon & Dropdown') {
                      @if (
                        column.listItems?.length > 0 &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'User Tags' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Organization Status' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Instance Status'
                      ) {
                        <app-chip-list (selectedToRemoveOut)="removeTagById($event.selected, element, column.component, $event.chip)" [type]="'Dropdown'" [listItems]="column.listItems"></app-chip-list>
                      } @else if (column.listItems?.length > 0 && column.component?.templateField?.dropDownLinkType?.title === 'User Tags') {
                        <app-tag-popover [viewType]="2" [selectedUserId]="element.userId" [dropDownLinkType]="'User Tags'" [component]="column.component"> </app-tag-popover>
                      } @else if (
                        column.listItems?.length === 0 &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'User Tags' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Organization Status' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Instance Status'
                      ) {
                        <div class="inner-container" [ngStyle]="!column.value ? { color: 'gray', 'font-style': 'italic' } : {}">{{ column.value ?? column.component.componentType.name }}</div>
                      } @else if (column.listItems?.length === 0 && column.component?.templateField?.dropDownLinkType?.title === 'User Tags') {
                        <app-tag-popover [viewType]="2" [selectedUserId]="element.userId" [dropDownLinkType]="'User Tags'" [component]="column.component"> </app-tag-popover>
                      } @else if (column.component?.templateField?.dropDownLinkType?.title === 'Organization Status' && orgStatuses) {
                        <!-- ORG STATUS -->
                        <app-org-publish-button
                          [orgStatuses]="orgStatuses"
                          [columnValue]="column.value"
                          (statusChanged)="updateComponent(element.tableId, $event, column.component, column.id)"></app-org-publish-button>
                      } @else if (column.component?.templateField?.dropDownLinkType?.title === 'Instance Status') {
                        <!-- INSTANCE STATUS -->
                        <app-instance-publish-button
                          [feature]="feature"
                          [columnValue]="column.value"
                          (statusChanged)="updateComponent(element.tableId, $event, column.component, column.id)"></app-instance-publish-button>
                      }
                    }
                    @case ('Icon') {
                      @if (
                        column.listItems?.length > 0 &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'User Tags' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Organization Status' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Instance Status'
                      ) {
                        <app-chip-list (selectedToRemoveOut)="removeTagById($event.selected, element, column.component, $event.chip)" [type]="column.component?.componentType?.name" [listItems]="column.listItems"></app-chip-list>
                      } @else if (column.listItems?.length > 0 && column.component?.templateField?.dropDownLinkType?.title === 'User Tags') {
                        <app-tag-popover [viewType]="2" [selectedUserId]="element.userId" [dropDownLinkType]="'User Tags'" [component]="column.component"> </app-tag-popover>
                      } @else if (
                        column.listItems?.length === 0 &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'User Tags' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Organization Status' &&
                        column.component?.templateField?.dropDownLinkType?.title !== 'Instance Status'
                      ) {
                        <div class="inner-container" [ngStyle]="!column.value ? { color: 'gray', 'font-style': 'italic' } : {}">{{ column.value ?? column.component.componentType.name }}</div>
                      } @else if (column.listItems?.length === 0 && column.component?.templateField?.dropDownLinkType?.title === 'User Tags') {
                        <app-tag-popover [viewType]="2" [selectedUserId]="element.userId" [dropDownLinkType]="'User Tags'" [component]="column.component"> </app-tag-popover>
                      } @else if (column.component?.templateField?.dropDownLinkType?.title === 'Organization Status' && orgStatuses) {
                        <!-- ORG STATUS -->
                        <app-org-publish-button
                          [orgStatuses]="orgStatuses"
                          [columnValue]="column.value"
                          (statusChanged)="updateComponent(element.tableId, $event, column.component, column.id)"></app-org-publish-button>
                      } @else if (column.component?.templateField?.dropDownLinkType?.title === 'Instance Status') {
                        <!-- INSTANCE STATUS -->
                        <app-instance-publish-button
                          [feature]="feature"
                          [columnValue]="column.value"
                          (statusChanged)="updateComponent(element.tableId, $event, column.component, column.id)"></app-instance-publish-button>
                      }
                    }
                    <!-- -------- -->
                    @case ('Persona Selector') {
                      <app-persona-chip-list [existingTags]="column.listItems" [component]="column.component" [selectedUserId]="column.id"></app-persona-chip-list>
                    }
                    @case ('Organization Networks') {
                      @if (column.listItems?.length > 0) {
                        <app-chip-list (selectedToRemoveOut)="removeTagById($event.selected, element, column.component, $event.chip)" [type]="column.component?.componentType?.name" [listItems]="column.listItems"></app-chip-list>
                      } @else {
                        <div class="inner-container" style="color: 'gray'; font-style: 'italic'">{{ column.component.componentType.name }}</div>
                      }
                    }
                    @case ('Password') {
                      <div class="inner-container">
                        <ion-button class="resetPassword" (click)="resetPassword(column.id); $event.stopPropagation()">Reset Password</ion-button>
                      </div>
                    }
                    @case ('Organization') {
                      <div class="inner-container">
                        <ion-button class="resetPassword" (click)="updateProductOrgUserRole(element); $event.stopPropagation()">User Organizations</ion-button>
                      </div>
                    }
                    @case ('Checkbox') {
                      <app-checkbox-value
                        [inheritedPropertyValue]="column.value | parsePipe: element.tableId | async"
                        [value]="column.value"
                        [label]="column?.component?.templateField?.label ?? ''"
                        (checkboxChanged)="updateComponent(element.tableId, $event, column.component, column.id)">
                      </app-checkbox-value>
                    }
                  }
                </div>
              }
            }
          </mat-cell>
        </ng-container>
      }
      <mat-header-row mat-header-row *matHeaderRowDef="columnsToDisplayIn; sticky: true"></mat-header-row>
      <mat-row mat-row *matRowDef="let row; columns: columnsToDisplayIn" style="cursor: pointer"></mat-row>
    </mat-table>
  </div>
</div>
@if (loading) {
  <div class="scroll-container">
    <ng-template #customLoadingTemplate>
      <div class="custom-class">
        <h3>Loading...</h3>
      </div>
    </ng-template>
    <ion-spinner name="lines"></ion-spinner>
  </div>
}
