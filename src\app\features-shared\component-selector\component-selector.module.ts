import { NgModule } from '@angular/core';
import { SharedModule } from '@app/shared/shared.module';
import { NetworkManagerModule } from '../network-manager/network-manager.module';
import { QuestionModule } from '../question/question.module';
import { featureComponents, standaloneComponents } from './component-selector.declarations';

@NgModule({
  declarations: [...featureComponents],
  imports: [...standaloneComponents, SharedModule, QuestionModule, NetworkManagerModule],
  exports: [...featureComponents, QuestionModule, NetworkManagerModule],
})
export class ComponentSelectorModule {}
