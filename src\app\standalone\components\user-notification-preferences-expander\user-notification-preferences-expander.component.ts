import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ICommunication, ITag, IUserCommunicationPreference, IUserCommunicationPreferenceSetting } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { map, Observable } from 'rxjs';
import { MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { IonicModule } from '@ionic/angular';
import { FormsModule } from '@angular/forms';
import { AsyncPipe } from '@angular/common';

@Component({
    selector: 'app-user-notification-preferences-expander',
    templateUrl: './user-notification-preferences-expander.component.html',
    styleUrls: ['./user-notification-preferences-expander.component.scss'],
    imports: [MatExpansionPanel, MatExpansionPanelHeader, IonicModule, FormsModule, AsyncPipe]
})
export class UserNotificationPreferencesExpanderComponent implements OnInit {
  @Input() category: ITag;
  @Input() userCommunicationPreference: IUserCommunicationPreference;
  @Output() userCommunicationPreferenceChanged = new EventEmitter<IUserCommunicationPreference>();
  communications$: Observable<ICommunication[]>;
  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.initData();
  }

  initData() {
    this.communications$ = this.dataService.getCommunications(this.category.id).pipe(
      map(x => {
        return x.map(y => ({ ...y, userCommunicationPreferenceSetting: this.getUserCommunicationPreferenceSetting(y.id) }) as ICommunication);
      })
    );
  }

  getUserCommunicationPreferenceSetting(communicationId: string) {
    let setting = this.userCommunicationPreference?.userCommunicationPreferenceSettings?.find(x => x.communicationId === communicationId);

    if (!setting) {
      setting = {
        id: communicationId,
        userCommunicationPreferenceId: this.userCommunicationPreference.id,
        communicationId: communicationId,
        shouldReceiveEmail: true,
        shouldReceiveSms: true,
        shouldReceivePush: true,
        shouldReceiveBanner: true,
      } as IUserCommunicationPreferenceSetting;

      this.userCommunicationPreference?.userCommunicationPreferenceSettings?.push(setting);
    }

    return setting;
  }

  settingChanged(setting: IUserCommunicationPreferenceSetting) {
    const index = this.userCommunicationPreference?.userCommunicationPreferenceSettings?.findIndex(x => x.id === setting.id) ?? -1;

    if (index !== -1 && this.userCommunicationPreference?.userCommunicationPreferenceSettings) {
      this.userCommunicationPreference.userCommunicationPreferenceSettings[index].shouldReceiveEmail = setting.shouldReceiveEmail;
      this.userCommunicationPreference.userCommunicationPreferenceSettings[index].shouldReceiveSms = setting.shouldReceiveSms;
      this.userCommunicationPreference.userCommunicationPreferenceSettings[index].shouldReceivePush = setting.shouldReceivePush;
      this.userCommunicationPreference.userCommunicationPreferenceSettings[index].shouldReceiveBanner = setting.shouldReceiveBanner;

      this.userCommunicationPreferenceChanged.emit(this.userCommunicationPreference);
    }
  }
}
