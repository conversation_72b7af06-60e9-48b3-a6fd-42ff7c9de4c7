import { NgClass } from '@angular/common';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { MatSortHeader } from '@angular/material/sort';
import { Mat<PERSON>ell, MatCellDef, MatColumnDef, MatHeaderCell, MatHeaderCellDef, MatRow, MatRowDef, MatTable, MatTableDataSource } from '@angular/material/table';
import { IInstance } from '@app/core/contracts/contract';
import { InstanceService } from '@app/core/services/instance-service';
import { LayoutService } from '@app/core/services/layout-service';
import { IonicModule } from '@ionic/angular';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-grading-table-users',
  standalone: true,
  imports: [IonicModule, MatTable, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, Mat<PERSON>ell, MatSortHeader, MatRowDef, <PERSON>R<PERSON>, NgClass],
  templateUrl: './grading-table-users.component.html',
  styleUrl: './grading-table-users.component.scss',
})
export class GradingTableUsersComponent implements OnInit, OnDestroy {
  @Input() instance: IInstance;
  @Input() assignment: any;
  @Input() people: any[] = [];

  dataSource = new MatTableDataSource<any>();
  displayedColumns: string[] = ['name', 'role', 'lastlogin', 'progress', 'grade'];

  componentDestroyed$: Subject<boolean> = new Subject();
  height = 300;

  constructor(
    private instanceService: InstanceService,
    public layoutService: LayoutService
  ) {}

  ngOnInit(): void {
    // Set the data source from the input people array
    this.dataSource = new MatTableDataSource(this.people);
  }

  // Navigate to grading view for a specific person
  navigateToGrading(person: any) {
    sessionStorage.setItem('showGradingView', 'true');
    if (this.instance?.feature?.featureType?.name === 'Modifiable Learning Container Pages') {
      this.instanceService.openInstance(this.assignment.id, null, null, 'player');
    } else {
      this.instanceService.openInstance(this.instance.id, null, null, 'player', null, true);
    }
  }

  timeAgo(date: Date | string): string {
    try {
      const dateObj = date instanceof Date ? date : new Date(date);

      if (isNaN(dateObj.getTime())) {
        return '-';
      }

      const now = new Date();
      const diffInMs = now.getTime() - dateObj.getTime();
      const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) return 'Today';
      if (diffInDays === 1) return 'Yesterday';
      return `${diffInDays} Days Ago`;
    } catch (error) {
      return '-';
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
