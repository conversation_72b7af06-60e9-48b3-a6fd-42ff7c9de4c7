@if (ready === false && builderPreviewView === true) {
  <div class="placeholder-container">
    <ion-card class="card-content-container">
      <app-authoring-header [componentName]="'Media Block'"></app-authoring-header>
    </ion-card>
  </div>
}
@if ((currentStream && ready) || (src && ready) || (!src && !currentStream && ready)) {
  <div
    class="parent-container"
    [ngStyle]="isCustomStyle ? { width: '40%', margin: '0 3.5vw 1vw' } : {}"
    [ngClass]="{
      'video-border-completed': instanceSectionComponent?.completed && instanceService.isScorm === false,
      'video-border-default': component?.templateField?.isBlockRequired && instanceService.isScorm === false,
    }">
    <ng-container>
      @if ((component?.templateField?.isBlockRequired || instanceSectionComponent?.completed) && instanceService.isScorm === false) {
        <div class="completed-header-container">
          <ion-row>
            @if (instanceSectionComponent?.completed) {
              <ion-col>
                <span class="inner-completed">COMPLETED</span>
              </ion-col>
            }
            @if (component?.templateField?.isBlockRequired) {
              <ion-col>
                <span class="inner-required">REQUIRED</span>
              </ion-col>
            }
          </ion-row>
        </div>
      }
    </ng-container>
    @if (currentStream && ready) {
      <vg-player (onPlayerReady)="onPlayerReady($event)">
        <vg-overlay-play></vg-overlay-play>
        <vg-buffering></vg-buffering>
        <vg-scrub-bar>
          <vg-scrub-bar-current-time [vgSlider]="true"></vg-scrub-bar-current-time>
          <vg-scrub-bar-buffering-time></vg-scrub-bar-buffering-time>
        </vg-scrub-bar>
        <vg-controls class="vg-controls" [vgAutohide]="true" [vgAutohideTime]="1.5">
          <vg-play-pause></vg-play-pause>
          @if (layoutService.currentScreenSize === 'lg' && !mini) {
            <vg-time-display vgProperty="current" vgFormat="hh:mm:ss"></vg-time-display>
          }
          <vg-scrub-bar vgFor="{{ assetId }}"></vg-scrub-bar>
          @if (currentStream && captionLang.length > 0) {
            <vg-track-selector vgFor="assetId"></vg-track-selector>
          }
          @if (currentStream && !mini) {
            <vg-quality-selector [bitrates]="dashBitrates" (onBitrateChange)="vgHls.setBitrate($event)"> </vg-quality-selector>
          }
          <vg-mute></vg-mute>
          @if (layoutService.currentScreenSize === 'lg' && !mini) {
            <vg-volume></vg-volume>
          }
          <vg-fullscreen></vg-fullscreen>
          @if (layoutService.currentScreenSize === 'lg' && !mini) {
            <vg-time-display vgProperty="total" vgFormat="hh:mm:ss"></vg-time-display>
          }
          @if (layoutService.currentScreenSize === 'lg' && !mini) {
            <vg-playback-button [playbackValues]="['0.5', '1.0', '2.0', '3.0']"></vg-playback-button>
          }
        </vg-controls>
        <video
          disablePictureInPicture
          style="width: 100%; border-radius: 11px"
          preload="none"
          (onGetBitrates)="setBitRat($event)"
          id="{{ assetId }}"
          #vid1
          [vgMedia]="$any(vid1)"
          #vgHls="vgHls"
          [vgHls]="currentStream"
          crossorigin>
          @for (l of captionLang; track l) {
            <track kind="subtitles" [label]="l.language" [src]="l.vttUrl" [srclang]="l.languageCode" />
          }
        </video>
      </vg-player>
    }
    @if (src && ready) {
      <vg-player (onPlayerReady)="onPlayerReady($event)">
        <vg-overlay-play></vg-overlay-play>
        <vg-buffering></vg-buffering>
        <vg-scrub-bar>
          <vg-scrub-bar-current-time [vgSlider]="true"></vg-scrub-bar-current-time>
          <vg-scrub-bar-buffering-time></vg-scrub-bar-buffering-time>
        </vg-scrub-bar>
        <vg-controls style="padding-right: 16px" [vgAutohide]="true" [vgAutohideTime]="1.5">
          <vg-play-pause></vg-play-pause>
          @if (layoutService.currentScreenSize === 'lg' && !mini) {
            <vg-time-display vgProperty="current" vgFormat="hh:mm:ss"></vg-time-display>
          }
          <vg-scrub-bar></vg-scrub-bar>
          @if (captionLang.length > 0) {
            <vg-track-selector vgFor="vid1"></vg-track-selector>
          }
          <vg-mute></vg-mute>
          <vg-volume></vg-volume>
          <vg-fullscreen></vg-fullscreen>
          @if (layoutService.currentScreenSize === 'lg' && !mini) {
            <vg-time-display vgProperty="total" vgFormat="hh:mm:ss"></vg-time-display>
          }
          @if (layoutService.currentScreenSize === 'lg' && !mini) {
            <vg-playback-button [playbackValues]="['0.5', '1.0', '2.0', '3.0']"></vg-playback-button>
          }
        </vg-controls>
        <video disablePictureInPicture style="width: 100%; border-radius: 15px 11px 11px 11px" preload="none" id="vid1" #vid1 [vgMedia]="$any(vid1)" crossorigin>
          <source [src]="src" />
          @for (l of captionLang; track l) {
            <track kind="subtitles" [label]="l.language" [src]="l.vttUrl" [srclang]="l.languageCode" />
          }
        </video>
      </vg-player>
    }
    @if (!src && !currentStream && ready) {
      <app-image-viewer-value></app-image-viewer-value>
    }
  </div>
}
