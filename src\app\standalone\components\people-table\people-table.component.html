<div class="parent-container">
  <ion-grid>
    <ion-row class="view-options-row">
      @if (type !== 'Accredited Package' && type !== 'Modifiable Package') {
      <div class="people-heading">
        <app-heading-value [headingModal]="heading" [fontSize]="22"></app-heading-value>
      </div>
      }
      @if (instance.feature.featureType.name === 'Modifiable Learning Container Pages') {
      <div class="top-header">
        <ion-button (click)="presentParentAddPopover($event)" color="warning">Add</ion-button>
        <ion-popover #parentAddPopover [isOpen]="isParentPopoverOpen" (didDismiss)="isParentPopoverOpen = false"
          side="bottom" alignment="start">
          <ng-template>
            <ion-content>
              <ion-item-group>
                <ion-item button="true" value="single" (click)="addUserModal()">Add a user</ion-item>
                <!-- @if (type !== 'Modifiable Learning Container Pages') {
                  <ion-item button="true" value="single" (click)="createUserModal()">Create a user</ion-item>
                } -->
                <!-- <ion-item button="true" value="bulk">Bulk add a user</ion-item> -->
              </ion-item-group>
            </ion-content>
          </ng-template>
        </ion-popover>
      </div>
      }
    </ion-row>
  </ion-grid>
  @if (isLoading) {
  <div class="loading-overlay">
    <div class="loading-spinner">
      <ion-spinner name="circular"></ion-spinner>
    </div>
  </div>
  }
  <mat-table #table [dataSource]="dataSource" matSort #sort="matSort" multiTemplateDataRows class="custom-table">
    <ng-container matColumnDef="select">
      <mat-header-cell style="border-right: none" *matHeaderCellDef>
        <mat-checkbox (change)="$event ? masterToggle() : null" [checked]="selection.hasValue() && isAllSelected()"
          [indeterminate]="selection.hasValue() && !isAllSelected()"> </mat-checkbox>
      </mat-header-cell>
      <mat-cell *matCellDef="let row">
        <mat-checkbox color="primary" (click)="$event.stopPropagation()"
          (change)="$event ? selection.toggle(row) : null" [checked]="selection.isSelected(row)"> </mat-checkbox>
      </mat-cell>
    </ng-container>
    <ng-container matColumnDef="name">
      <mat-header-cell *matHeaderCellDef mat-sort-header>NAME</mat-header-cell>
      <mat-cell *matCellDef="let element">
        <div class="name-column">
          <span class="yellow-name">{{ element.name }}</span>
          <span class="subheading">{{ element.email }}</span>
        </div>
      </mat-cell>
    </ng-container>

    <ng-container matColumnDef="role">
      <mat-header-cell *matHeaderCellDef mat-sort-header class="responsive-hidden-header"
        [ngClass]="{ 'hide-column': layoutService.peopleTableCompact }">ROLE</mat-header-cell>
      <mat-cell *matCellDef="let element" [ngClass]="{ 'hide-column': layoutService.peopleTableCompact }">{{
        element.roleName }}</mat-cell>
    </ng-container>

    <ng-container matColumnDef="lastlogin">
      <mat-header-cell *matHeaderCellDef mat-sort-header class="responsive-hidden-header"
        [ngClass]="{ 'hide-column': layoutService.peopleTableCompact }">LAST LOGIN</mat-header-cell>
      <mat-cell *matCellDef="let element" [ngClass]="{ 'hide-column': layoutService.peopleTableCompact }">
        {{ element.lastActivity ? timeAgo(element.lastActivity) : '-' }}
      </mat-cell>
    </ng-container>
    @if (hasGrade) {
    <ng-container matColumnDef="progress">
      <mat-header-cell *matHeaderCellDef mat-sort-header class="responsive-hidden-header">STUDENT
        PROGRESS</mat-header-cell>
      <mat-cell *matCellDef="let element">
        <div class="progress-indicator">
          <div class="progress-circle">
            <svg viewBox="0 0 24 24" class="circular-chart">
              <!-- Keep the background path for structure but it's now transparent -->
              <path class="circle-bg" d="M12 2
                  a 10 10 0 0 1 0 20
                  a 10 10 0 0 1 0 -20" />
              <!-- Only the green progress arc will be visible -->
              <path class="circle" [attr.stroke-dasharray]="element.progressPercentage + ', 100'" d="M12 2
                  a 10 10 0 0 1 0 20
                  a 10 10 0 0 1 0 -20" />
            </svg>
          </div>
          <div class="progress-text">
            <span class="items-count">
              <span class="completed-count" style="font-weight: bold; color: white">{{ element.completedCount || 0
                }}</span><span class="remaining-text">/{{ element.totalCount || 0 }} items</span>
            </span>
          </div>
        </div>
      </mat-cell>
    </ng-container>
    <ng-container matColumnDef="grade">
      <mat-header-cell *matHeaderCellDef mat-sort-header class="responsive-hidden-header">AVERAGE
        GRADE</mat-header-cell>
      <mat-cell *matCellDef="let element">
        <div class="row-space-between">
          <span class="grade-text">{{ element.grade }}%</span><ion-button class="grade-button" [ngClass]="{
                required: element.isGraded === false && element.completedCount === element.totalCount,
                notrequired: !(element.isGraded === false && element.completedCount === element.totalCount),
              }" (click)="setExpandedElement(element)">Grades<ion-icon class="grade-icon" slot="end"
              [name]="expandedElement === element ? 'chevron-up' : 'chevron-down'"></ion-icon></ion-button>
        </div>
      </mat-cell>
    </ng-container>
    <!-- <ng-container matColumnDef="expand">
        <mat-header-cell *matHeaderCellDef mat-sort-header></mat-header-cell>
        <mat-cell *matCellDef="let element">
          <ion-button (click)="openResults(element)" color="medium">Results</ion-button>
        </mat-cell>
      </ng-container> -->
    }

    <!-- Expanded Content Column - The detail row is made up of this one column that spans across all columns -->
    <ng-container matColumnDef="expandedDetail">
      <mat-cell *matCellDef="let element" [attr.colspan]="displayedColumns.length">
        @if (element === expandedElement) {
        <app-people-table-user-assignments [userId]="element.userId"
          [instance]="instance"></app-people-table-user-assignments>
        }
      </mat-cell>
    </ng-container>

    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
    <mat-row *matRowDef="let element; columns: displayedColumns" class="example-element-row"
      [class.example-expanded-row]="expandedElement === element"></mat-row>
    <mat-row *matRowDef="let row; columns: ['expandedDetail']" class="example-detail-row"
      [class.example-detail-expanded-row]="expandedElement === row"
      [@detailExpand]="expandedElement === row ? 'expanded' : 'collapsed'"></mat-row>
  </mat-table>
  @if (moreResults) {
  <div (click)="getPeopleTableById(id ?? '', true)" class="load-more">
    <ion-row>
      <ion-col size="12">
        <div>Load More</div>
        <div><ion-icon name="chevron-down-outline"></ion-icon></div>
      </ion-col>
    </ion-row>
  </div>
  }
</div>
@if (hasAdminAccess() && selection.selected.length > 0) {
<div class="selection-bar">
  <div class="selected-container"><ion-icon (click)="selection.clear()" name="close-outline"></ion-icon> {{
    selection.selected.length }} Selected</div>
  <div class="buttons-container">
    <ion-label (click)="updateTableUsers('Deleted')"> <ion-icon name="remove-circle"></ion-icon>Remove</ion-label>
  </div>
  <div class="buttons-container">
    <ion-label (click)="updateTableUsers('Active')"> <ion-icon name="people"></ion-icon>Approve</ion-label>
  </div>
  <div class="buttons-container">
    <ion-label (click)="updateTableUsers('Suspended')"> <ion-icon name="ban-outline"></ion-icon>Deny</ion-label>
  </div>
</div>
}