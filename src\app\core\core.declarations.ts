import { GlobalVersionComponent } from './components/version-modal/version-modal';
import { VersionModalComponent } from './components/version-modal/version.component';
import { AuthGuard } from './services/auth-guard';
import { AuthService } from './services/auth-service';
import { BreadcrumbService } from './services/breadcrumb-service';
import { DataService } from './services/data-service';
import { ErrorHandlerService } from './services/errorhandler-service';
import { Events } from './services/events-service';
import { InstanceService } from './services/instance-service';
import { InstanceStatusGuard } from './services/instance-status-guard';
import { LayoutService } from './services/layout-service';
import { MonitoringService } from './services/monitoring-service';
import { ParseService } from './services/parse-service';
import { RolesService } from './services/roles.service';
import { ScormService } from './services/scorm.service';
import { StorageService } from './services/storage-service';
import { QlikService } from './services/qlik-service';

export const featureServices = [
  ErrorHandlerService,
  DataService,
  AuthService,
  AuthGuard,
  InstanceStatusGuard,
  RolesService,
  GlobalVersionComponent,
  Events,
  MonitoringService,
  LayoutService,
  ParseService,
  StorageService,
  ScormService,
  InstanceService,
  BreadcrumbService,
  QlikService,
];

export const featureDialogs: any[] = [];

export const featureComponents: any[] = [GlobalVersionComponent, VersionModalComponent];

export const featureDirectives: any[] = [];

export const featurePipes: any[] = [];
