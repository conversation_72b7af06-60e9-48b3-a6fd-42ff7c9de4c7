:host {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  align-content: stretch;

  .parent-container {
    .edit-col {
      background-color: #333333;
      z-index: 101;
      height: calc(100vh - 263px);
      max-height: calc(100vh - 263px);
      overflow: scroll;

      .inner-content {
        padding: 10px;
        h5 {
          font-family: 'Exo 2';
          margin: 0;
          color: white;
        }

        p {
          font-family: 'Roboto';
          color: #aaa;
        }

        .fields-container {
          background-color: #1e1e1e;
          padding: 0px 10px;
        }
      }
    }
  }
}
