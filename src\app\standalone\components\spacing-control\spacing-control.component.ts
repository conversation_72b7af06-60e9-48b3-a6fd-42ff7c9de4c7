import { NgClass } from '@angular/common';
import { Component, Input, OnDestroy, OnInit, forwardRef } from '@angular/core';
import { FormsModule, NG_VALIDATORS, NG_VALUE_ACCESSOR, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';
import { IComponent } from '@app/core/contracts/contract';

@Component({
    selector: 'app-spacing-control',
    templateUrl: './spacing-control.component.html',
    styleUrls: ['./spacing-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => SpacingControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => SpacingControlComponent),
        },
    ],
    imports: [NgClass, IonicModule, FormsModule, ReactiveFormsModule, TextInputControlComponent]
})
export class SpacingControlComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() component: IComponent;
  @Input() sidePanelPadding = false;
  form: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private signalService: ComponentUpdateSignalService
  ) {
    super();
  }

  ngOnInit() {
    this.fieldValueChanged.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.createForm();
      this.formChanges();
    });
  }

  createForm() {
    this.form = new UntypedFormGroup({
      height: new UntypedFormControl(this.textValue, [Validators.required]),
    });
  }

  formChanges() {
    this.form.valueChanges.subscribe(() => {
      this.setValue(this.form.controls.height.value);
    });
  }

  override setValue(value: string) {
    this.writeValue(value);
    if(this.component.id)
      {
        this.signalService.triggerSignal({ componentId: this.component.id, updateValue: value })
      }    
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
