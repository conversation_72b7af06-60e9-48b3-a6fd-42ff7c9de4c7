import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { ICombinedUserRiasecFinalScore } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { PolarChartModule } from '@swimlane/ngx-charts';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { environment } from '@env/environment';

@Component({
    selector: 'app-user-riasec-score-chart',
    templateUrl: './user-riasec-score-chart.component.html',
    styleUrls: ['./user-riasec-score-chart.component.scss'],
    imports: [IonicModule, PolarChartModule, CommonModule]
})
export class UserRiasecScoreChartComponent implements OnI<PERSON><PERSON>, <PERSON><PERSON><PERSON>roy {
  // Use this for the stats list
  riasecResults = [] as { name: string; value: number; description: string; img: SafeResourceUrl }[];

  // this is for the cards
  riasecResultsSorted = [] as { name: string; value: number; description: string; img: SafeResourceUrl }[];
  score: boolean = false;
  statsIconUrl = `${environment.contentUrl}asset/7507848b-9f27-4ae3-a63e-1503591c4945/content`;

  // Icons for 1st, 2nd, and 3rd place
  numberedIcons = [
    `${environment.contentUrl}asset/a5d3f2e2-9653-42fa-a96d-be573fc4c5a4/content`,
    `${environment.contentUrl}asset/e0611e17-7160-469e-a5d8-0800d8259deb/content`,
    `${environment.contentUrl}asset/a29db6c1-4ea8-467d-bd38-f1dbf95a4903/content`,
  ].map(url => this.sanitizer.bypassSecurityTrustResourceUrl(url));

  componentDestroyed$ = new Subject<boolean>();

  constructor(
    private dataService: DataService,
    private globalToast: GlobalToastService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.getRiasecScore();
  }

  sanitizeUrl(url: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  getRiasecScore() {
    this.dataService
      .getUserRiasecScore()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((score: ICombinedUserRiasecFinalScore) => {
        if (score.efDynamicScore) {
          const { realistic, investigative, artistic, social, enterprising, conventional } = score.efDynamicScore;
          const allScores = [realistic, investigative, artistic, social, enterprising, conventional];
          const hasNonZeroScore = allScores.some(value => (value || 0) > 0);
          const totalScore = allScores.reduce((sum, score) => sum + (score || 0), 0);

          if (hasNonZeroScore) {
            this.score = true;
            this.riasecResults = [
              {
                name: 'Do-er',
                value: realistic ? Math.round((realistic / totalScore) * 100) : 0,
                description: "You're hands-on and active—you enjoy building, fixing, and working with tools, machines, or your own two hands.",
                img: this.sanitizeUrl(`${environment.contentUrl}asset/91863d2a-b886-4ae6-a02f-8cf7b1219d30/content`),
              },
              {
                name: 'Creator',
                value: artistic ? Math.round((artistic / totalScore) * 100) : 0,
                description: "You're creative and expressive—you love thinking outside the box, designing, and creating things that stand out.",
                img: this.sanitizeUrl(`${environment.contentUrl}asset/f0fc12cd-d4be-4301-8266-e2321ea01054/content`),
              },
              {
                name: 'Thinker',
                value: investigative ? Math.round((investigative / totalScore) * 100) : 0,
                description: 'You have a curious mind and enjoy figuring out how things work, doing research, and tackling challenging problems.',
                img: this.sanitizeUrl(`${environment.contentUrl}asset/5599ec62-f24c-4c46-9023-d78f32955825/content`),
              },
              {
                name: 'Helper',
                value: social ? Math.round((social / totalScore) * 100) : 0,
                description: "You're caring and people-focused—you enjoy helping others, listening, and making a difference in someone's day.",
                img: this.sanitizeUrl(`${environment.contentUrl}asset/cf2a1d35-06db-4439-8b2b-c590050d19e1/content`),
              },
              {
                name: 'Persuader',
                value: enterprising ? Math.round((enterprising / totalScore) * 100) : 0,
                description: "You're organized and detail-driven—you like working with systems, numbers, routines, and making sure everything runs smoothly.",
                img: this.sanitizeUrl(`${environment.contentUrl}asset/da148dea-c15d-4f2e-aa8b-7ffdd5fe9dfc/content`),
              },
              {
                name: 'Organizer',
                value: conventional ? Math.round((conventional / totalScore) * 100) : 0,
                description: "You're a go-getter and a leader—you enjoy taking charge, sharing ideas, and getting others excited to take action.",
                img: this.sanitizeUrl(`${environment.contentUrl}asset/7cf198cf-37fd-4e1a-965e-c17bb66eb139/content`),
              },
            ];
            this.riasecResultsSorted = [...this.riasecResults].sort((a, b) => b.value - a.value);
          } else {
            this.score = false;
          }
        } else {
          this.score = false;
        }
      });
  }

  reset() {
    this.dataService
      .resetUserRiasecScore()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.getRiasecScore();
        this.globalToast.presentNotificationToast('RIASEC Reset Successfully', false, false);
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
