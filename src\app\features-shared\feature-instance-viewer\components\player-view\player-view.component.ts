import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { IFeatureTab, IInstance, IInstanceTemplate, IRouteParams } from '@app/core/contracts/contract';
import { Events } from '@app/core/services/events-service';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
  selector: 'app-player-view',
  templateUrl: './player-view.component.html',
  styleUrls: ['./player-view.component.scss'],
  standalone: false,
})
export class PlayerViewComponent implements OnInit, OnDestroy {
  @Input() instanceTemplate: IInstanceTemplate;
  @Input() instance: IInstance;
  @Input() featureTab: IFeatureTab;
  @Input() routeParams: IRouteParams;
  @Input() isScorm = false;
  @Input() searchFilter: string;
  @Input() selectedUserId: string;
  @Output() buttonChanged = new EventEmitter<number>();
  @Output() optionSelected = new EventEmitter<number>();
  hidden = false;
  constructor(
    public layoutService: LayoutService,
    private eventService: Events
  ) {}

  ngOnInit() {
    this.checkHidden();
  }

  buttonClicked(option: any) {
    this.buttonChanged.emit(option);
  }

  setSelectedViewType(option: any) {
    this.optionSelected.emit(option);
  }

  rowFiltered(filter: any) {
    this.searchFilter = filter;
  }

  checkHidden() {
    //Hide If Contains PeopleTable.
    this.hidden = this.instanceTemplate.instanceSections?.some(x => x.section.components?.some(y => y.componentType.name === 'People Table'));
  }

  ngOnDestroy(): void {
    this.eventService.unsubscribe('checkCompletion');
    this.eventService.unsubscribe('instanceCompleted');
    this.eventService.unsubscribe('playerRowSelected');
    this.eventService.unsubscribe('instanceSectionComponentCompleted');
    this.eventService.unsubscribe('scrollToIndex');
    this.eventService.unsubscribe('instanceIsGraded');
    this.eventService.unsubscribe('isLastSection');
    sessionStorage.removeItem('showGradingView');
  }
}
