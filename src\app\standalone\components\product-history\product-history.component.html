<div class="parent-container">
  <mat-accordion>
    @for (productOrg of productOrganizationsHistory; track productOrg) {
      <mat-expansion-panel hideToggle [disabled]="true">
        <mat-expansion-panel-header class="expansion-panel-header">
          <div class="inner-panel">
            <div class="heading">{{ productOrg.productName }}</div>
            <div class="sub-heading">
              <span
                >{{ productOrg.period | uppercase }}
                @if (productOrg.expiryDate) {
                  <span>, {{ subscriptionStatus(productOrg.expiryDate) }} on</span>
                }
              </span>
              <span class="expiry-date">{{ productOrg.expiryDate | date: 'M/d/yyyy' }}</span>
            </div>
          </div>
          <div class="role-heading">
            @if (productOrg.orgUserRoleName) {
              <span>
                {{ productOrg.orgUserRoleName }}
              </span>
            }
          </div>
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }
    @if (moreResults) {
      <div (click)="getProductHistoryById(id ?? '', true, searchFilter)" class="load-more">
        <ion-row>
          <ion-col size="12">
            <div>Load More</div>
            <ion-icon name="chevron-down-outline"></ion-icon>
          </ion-col>
        </ion-row>
      </div>
    }
  </mat-accordion>
</div>
