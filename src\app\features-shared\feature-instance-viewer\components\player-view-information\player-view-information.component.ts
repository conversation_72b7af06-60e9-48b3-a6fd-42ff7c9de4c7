import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IInstance, IRouteParams } from '@app/core/contracts/contract';
import { FinishedInstanceDetails } from '@app/core/dtos/FinishedInstanceDetails';
import { ActionTypes } from '@app/core/enums/action-types.enum';

@Component({
    selector: 'app-player-view-information',
    templateUrl: './player-view-information.component.html',
    styleUrls: ['./player-view-information.component.scss'],
    standalone: false
})
export class PlayerViewInformationComponent {
  @Input() routeParams: IRouteParams;
  @Input() searchFilter: string;
  @Input() selectedUserId: string;
  @Input() isEducator = false;
  @Input() actionBw: ActionTypes | undefined;
  @Input() parentInstance: IInstance;
  @Input() instance: IInstance;
  @Output() finishedInstance = new EventEmitter<FinishedInstanceDetails>();

  constructor() {}

  setFinishedInstance(event: FinishedInstanceDetails) {
    this.finishedInstance.emit(event);
  }
}
