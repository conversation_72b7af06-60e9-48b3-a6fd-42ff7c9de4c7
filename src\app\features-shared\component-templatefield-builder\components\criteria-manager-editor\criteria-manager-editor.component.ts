import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-criteria-manager-editor',
    templateUrl: './criteria-manager-editor.component.html',
    styleUrls: ['./criteria-manager-editor.component.scss'],
    standalone: false
})
export class CriteriaManagerEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  fieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(private formBuilder: UntypedFormBuilder) {}

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.fieldForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      placeholder: [this.component?.templateField?.placeHolderText, Validators.required],
      helpTitle: [this.component?.templateField?.helpTitle],
      helpDescription: [this.component?.templateField?.helpDescription],
      rowNumber: [this.component?.builderRowNumber],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.fieldForm) {
      return;
    }

    this.fieldForm.controls.label.setValue(this.component.templateField.label);
    this.fieldForm.controls.placeholder.setValue(this.component.templateField.placeHolderText);
    this.fieldForm.controls.helpTitle.setValue(this.component.templateField.helpTitle);
    this.fieldForm.controls.helpDescription.setValue(this.component.templateField.helpDescription);
    this.fieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.fieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.fieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.fieldForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.fieldForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.fieldForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.fieldForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.fieldForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.fieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.fieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.fieldForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.fieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.fieldForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.fieldForm.valid) {
      this.component.templateField.label = this.fieldForm.controls.label.value;
      this.component.templateField.placeHolderText = this.fieldForm.controls.placeholder.value;
      this.component.templateField.helpTitle = this.fieldForm.controls.helpTitle.value;
      this.component.templateField.helpDescription = this.fieldForm.controls.helpDescription.value;
      this.component.builderRowNumber = this.fieldForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.fieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.fieldForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.fieldForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.fieldForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.fieldForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.fieldForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.fieldForm.controls.isViewField.value;
      this.component.templateField.colspan = this.fieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.fieldForm.controls.colNumber.value;
      this.component.templateField.useMaxWidth = this.fieldForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
