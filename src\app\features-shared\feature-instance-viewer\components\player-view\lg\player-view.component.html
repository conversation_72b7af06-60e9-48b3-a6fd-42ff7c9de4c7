<div class="player-row-flex">
  @if (isEducator$ | async; as showPlayer) {
    <div class="player-width">
      <div class="player-inner-container">
        <div class="top-border" slot="fixed">
          <app-view-options-row
            [template]="template"
            [canAdd]="instance?.title === 'Admin'"
            [selectedItem]="routeParams?.viewType ?? 0"
            [featureTab]="featureTab"
            [isEducator]="isEducator"
            [instance]="instance"
            [selectedUserId]="selectedUserId"
            (rowFiltered)="rowFilteredChange($event)"
            (buttonChanged)="buttonClicked($event)"
            (optionSelected)="setSelectedViewType($event)"
            (searchBarAvailableChanged)="setSearchBarAvailable($event)"
            (mobileMenuClicked)="openMobileMenu = !openMobileMenu"
            (userSelected)="setSelectedUserId($event)"></app-view-options-row>
        </div>
        <div
          class="content-wrapper player-view-info-container"
          [ngClass]="{
            'custom-info-height':
              (isEducator === true && parentClassExists === true && showGradingView == true) || (featureTab?.buttonText && featureTab.buttonText !== '' && featureTab.featureTabButtons.length > 0),
            'custorm-full-height':
              !(isEducator === true && parentClassExists === true && showGradingView == true) && !(featureTab?.buttonText && featureTab.buttonText !== '' && featureTab.featureTabButtons.length > 0),
            searchBar: searchBarAvailable,
          }">
          <div id="scrollContent" [ngClass]="{ 'player-outer-container': routeParams.viewType === viewTypes.Player }">
            <div
              class="full-parent-height player-border"
              [ngClass]="{
                'player-container': routeParams.viewType === viewTypes.Player && (!status || status?.length === 0 || !selectedUserId),
                'player-container-completed': routeParams.viewType === viewTypes.Player && status === 'Completed' && !(status === 'Completed' && selectedUserId && isGraded === false),
                'player-container-in-progress': routeParams.viewType === viewTypes.Player && (status === 'InProgress' || status === 'NotStarted') && selectedUserId,
                'player-container-incomplete': routeParams.viewType === viewTypes.Player && status === 'Completed' && selectedUserId && !isGraded && containsGrading,
              }">
              <app-player-view-information
                [routeParams]="routeParams"
                [searchFilter]="searchFilter"
                [selectedUserId]="selectedUserId"
                [isEducator]="isEducator"
                [actionBw]="actionBw"
                [parentInstance]="instance"
                (finishedInstance)="setFinishedInstance($event)">
              </app-player-view-information>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="player-side-panel-container">
      <app-player-view-side-panel
        [template]="template"
        [routeParams]="routeParams"
        [searchFilter]="searchFilter"
        [instance]="instance"
        [selectedUserId]="selectedUserId"
        (selectedChanged)="selectedChanged($event.event, $event.actionBw)"></app-player-view-side-panel>
    </div>
  }
</div>
