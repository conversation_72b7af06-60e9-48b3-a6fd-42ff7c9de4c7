<ion-row>
  <ion-col class="parent-container">
    <div [ngClass]="{ selected: showSelected, normal: !showSelected, 'no-padding': noPadding, 'no-border': noBorder, 'side-panel-input-padding': sidePanelPadding }">
      @if (showSelected !== true) {
        <ion-label position="stacked">
          {{ label }}
          <span class="reqAsterisk">
            @if (required) {
              <span>* </span>
            }
            <ion-icon name="information-circle-outline"></ion-icon>
          </span>
        </ion-label>
      }
      @if (defaultValue && disabled) {
        <div>
          <span [innerHTML]="defaultValue"></span>
        </div>
      } @else {
        <div class="quill-container">
          <app-content-quill-editor [hideQuillPersonalize]="hideQuillPersonalize" [placeHolder]="placeHolder" [disabled]="disabled" (dataChanged)="setValue($event)" [value]="textValue">
          </app-content-quill-editor>
          @if (errorMessage && touched) {
            <ion-text>
              {{ errorMessage }}
            </ion-text>
          }
        </div>
      }
    </div>
  </ion-col>
</ion-row>
