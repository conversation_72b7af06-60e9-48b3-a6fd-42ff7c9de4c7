import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { IComponent, IComponentType, IFeature, IFeatureTab, ISection, ITemplate } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-feature-repository-builder-fields',
    templateUrl: './builder-fields.component.html',
    styleUrls: ['./builder-fields.component.scss'],
    standalone: false
})
export class FeatureRepositoryBuilderFieldsComponent implements OnInit, OnDestroy {
  @Input() feature: IFeature;
  component: IComponent | null;
  section: ISection | null;
  componentTypes$: Observable<IComponentType[]>;
  componentDestroyed$: Subject<boolean> = new Subject();
  builderForm: UntypedFormGroup;
  template$: Observable<ITemplate>;
  showDefaultComponents = true;
  templateForm: UntypedFormGroup;

  constructor(
    private builderService: BuilderService,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.componentTypes$ = this.dataService.getComponentTypes();
    this.builderService.selectedComponent$.pipe(takeUntil(this.componentDestroyed$)).subscribe(comp => {
      this.component = comp;
      this.section = null;
    });

    this.builderService.selectedSection$.pipe(takeUntil(this.componentDestroyed$)).subscribe(section => {
      this.section = section;
      this.component = null;
    });

    this.builderService.templateFormCreated$.pipe(takeUntil(this.componentDestroyed$)).subscribe(form => {
      this.templateForm = form;
    });
  }

  componentChanged() {
    this.feature = { ...this.feature } as IFeature;
    this.feature.featureTabs = this.feature.featureTabs.map(x => ({ ...x }) as IFeatureTab);
    this.builderService.selectedComponent$.next(null);
  }

  setComponentList(event: any) {
    this.showDefaultComponents = event;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
