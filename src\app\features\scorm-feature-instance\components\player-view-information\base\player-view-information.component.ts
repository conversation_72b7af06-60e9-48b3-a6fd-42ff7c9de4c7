import { Directive, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { IInstance, IRouteParams } from '@app/core/contracts/contract';
import { InstanceService } from '@app/core/services/instance-service';
import { environment } from '@env/environment';
import { Subject, map, takeUntil } from 'rxjs';

@Directive()
export class PlayerViewInformationBaseComponent implements OnInit, OnChanges, OnDestroy {
  @Input() routeParams: IRouteParams;

  instance: IInstance;
  iconUrl: string;
  assetUrl: string;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(public instanceService: InstanceService) {}

  ngOnInit() {
    this.instanceService.reload$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.loadData();
    });

    this.loadData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedId']) {
      this.loadData();
    }
  }

  loadData() {
    if (!this.routeParams.instanceSlug) {
      return;
    }

    this.instanceService
      .getInstance(this.routeParams?.instanceSlug)
      .pipe(
        map(instance => {
          if (instance) {
            if (instance?.iconAssetId) {
              this.iconUrl = `${environment.contentUrl}asset/${instance.iconAssetId}/content`;
            } else {
              this.iconUrl = 'assets/images/no-image.png'; // `${environment.contentUrl}asset/default/${instance.id}?systemProperty=${'Instance.IconAssetId'}`;
            }
            if (instance?.coverMediaAssetId) {
              this.assetUrl = `${environment.contentUrl}asset/${instance.coverMediaAssetId}/content`;
            } else {
              this.assetUrl = 'assets/images/defaultbackgroundgradient.png'; //`${environment.contentUrl}asset/default/${instance.id}?systemProperty=${'Instance.CoverMediaAssetId'}`;
            }
          }
          return instance;
        })
      )
      .subscribe(instance => {
        if (instance) {
          this.instance = instance;
        }
      });
  }

  noImage() {
    this.iconUrl = 'assets/images/no-image.png';
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
