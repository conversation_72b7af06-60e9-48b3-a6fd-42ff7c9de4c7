  .parent-container {
    padding: 25px 0px;

    .main-container {
      height: 100%;

      &.Right {
        display: flex;
        justify-content: center;
      }

      @media (min-width: 641px) {

        .text-container {

          &.Bottom {
            width: 100%;
          }
        }
      }

      @media (min-width: 641px) {
        .text-main-container {

          &.Right {
            width: 50%;
          }
        }
      }

      @media (max-width: 640px) {
        .text-main-container {
          margin: 0 !important;
          width: 100%;
        }
      }

      .text-main-container {

        &.Bottom {
          width: 100%;
          display: flex;
          justify-content: center;
        }

        &.Right {
          margin-right: 30px;
        }

        @media (min-width: 641px) {
          .text-container {

            &.Bottom {
              text-align: center;
              width: 100%;
            }
          }
        }

        @media (max-width: 640px) {
          .text-container {

            &.Right {
              margin-top: 10px;
            }
          }
        }

        .darkText {
          color: black !important;
        }

        .text-container {

          .heading {
            color: #f7f6f6;
            font-family: 'Roboto';
            font-weight: 700;
            font-size: 22px;
            letter-spacing: 0.3px;
          }

          .paragraph {
            color: #aaaaaa;
            letter-spacing: 0.2px;

            &.Bottom {
              display: flex;
              justify-content: center;
            }
          }
        }

      }
    }

    .button-container {
      padding-bottom: 15px;
      align-items: center;
      display: flex;

      &.Bottom {
        width: 100%;
        display: flex;
        justify-content: center;
      }

      ion-button {
        margin: 0px;
        --padding-top: 15px;
        --padding-bottom: 15px;
        color: black;
        text-transform: none;
        border-radius: 3px;
        font-weight: 600;
        font-size: 1.125em;
        background-color: #f99e00;
        letter-spacing: 0.5px;
        line-height: 1.1;
        min-height: 25px;
        min-width: 120px;
        font-family: 'Roboto';
        --box-shadow: none !important;
      }

      @media screen and (max-width: 960px) {
        ion-button {
          font-size: 1em;
          --padding-top: 0px;
          --padding-bottom: 0px;
        }

        ion-button::part(native) {
          min-height: 36px;
        }
      }

      ion-button:hover {
        --background: #8d5e1f !important;
      }
    }

    ::ng-deep .ql-editor {
      padding-top: 2px;
      padding-bottom: 15px;
      padding-left: 0px;
      padding-right: 0px;
      font-size: 18px;
    }
  }