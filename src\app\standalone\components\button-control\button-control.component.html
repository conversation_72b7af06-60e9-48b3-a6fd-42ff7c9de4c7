<div class="parent-container" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }">
  <ion-card class="card-container">
    <form [formGroup]="textForm">
      <app-text-input-control
        [disabled]="disabled"
        [backgroundColor]="'#1E1E1E'"
        [placeHolder]="component?.templateField?.placeHolder3 ?? 'Start typing here'"
        [toolTip]="'Button name'"
        [label]="component?.templateField?.label3 ?? 'Button name'"
        formControlName="buttonName"
        [noPadding]="true"
        [itemBackgroundColor]="'#292929'"></app-text-input-control>

      <app-text-input-control
        [disabled]="disabled"
        [backgroundColor]="'#1E1E1E'"
        [placeHolder]="component?.templateField?.placeHolder4 ?? 'Start typing here'"
        [label]="component?.templateField?.label4 ?? 'Url'"
        [toolTip]="'Url'"
        formControlName="url"
        [noPadding]="true"
        [itemBackgroundColor]="'#292929'"></app-text-input-control>
      <app-tag-select-option-control
        [textValue]="button?.tagId"
        [options]="buttonTags"
        [label]="'Button Tag'"
        [placeHolder]="'--select--'"
        formControlName="tagId"
        [backgroundColor]="'#181818'"
        [allowClear]="true"
        [toolTip]="'Choose the button tag'"
        [dropDownLinkType]="''"></app-tag-select-option-control>
      <mat-slide-toggle style="width: 100%" color="primary" formControlName="sameUrlNavigation">Navigate on current page</mat-slide-toggle>
      <app-alignment-control formControlName="stylingDirection"></app-alignment-control>
    </form>
  </ion-card>
</div>
