@if (instance && showPublishedOptions) {
  <ion-grid class="parent-container">
    <ion-row class="view-options-row ion-justify-content-between">
      <ion-col class="preview-col" size="auto">
        <span class="preview-header">Preview</span>
      </ion-col>
      @if (hasEditAccess()) {
        <ion-col class="publish-col" size="auto">
          <ion-button class="publish-button" color="warning" fill="solid" (click)="openPublishOptions($event, instance.status)">
            @if (publishButtonText === 'publish') {
              <mat-icon slot="end" svgIcon="caret_down"></mat-icon>
            }
            {{ publishButtonText }}
          </ion-button>
        </ion-col>
      }
    </ion-row>
  </ion-grid>
}
