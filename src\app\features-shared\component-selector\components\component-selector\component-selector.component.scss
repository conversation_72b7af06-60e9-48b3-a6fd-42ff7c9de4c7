.page-margin {
  margin-left: var(--page-margin-left);
  margin-right: var(--page-margin-right);
}

.max-width {
  max-width: 1250px;
  margin: auto !important;
  padding-right: 2vw;
  padding-left: 2vw;
}

.max-width-media {
  width: 100%;
  padding-left: 2vw;
  padding-right: 2vw;
  max-width: 1250px;
  margin: auto;
  background: rgba(0, 0, 0, 0);
}

.media-text {
  &.grid-full-width {
    padding-bottom: 25px;
  }

  &.use-max-width {
    margin-left: auto;
    margin-right: auto;
    max-width: var(--section-max-width, 100%);
  }
}

.listing-details {
  &.grid-full-width,
  &.player-with-gradient {
    padding-bottom: 40px;
  }

  &.use-max-width {
    margin-left: auto;
    margin-right: auto;
    max-width: var(--section-max-width, 100%);
  }
}
