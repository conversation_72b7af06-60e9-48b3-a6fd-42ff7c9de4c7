@if (downloadBlockForm) {
  <form [formGroup]="downloadBlockForm">
    <ion-grid>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Add field label...'" formControlName="label"> </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Placeholder Text'" [placeHolder]="'Add field placeholder...'" formControlName="placeHolderText"> </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Row Number'" [placeHolder]="'Add field row number...'" formControlName="rowNumber" [type]="'number'">
          </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Column Number (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add column number (Out of 12)...'"
            formControlName="colNumber"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Colspan (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add field colspan (Out of 12)...'"
            formControlName="colspan"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Hover Sort Order'"
            [placeHolder]="'Add field hover sort order...'"
            formControlName="hoverSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Instance Sort Order'"
            [placeHolder]="'Add field instance sort order...'"
            formControlName="instanceSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Action Text'" [placeHolder]="'Add action text...'" formControlName="buttonText"> </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row class="file-size-parent-container">
        <ion-col size="12">
          <ion-item #control>
            <ion-label position="stacked" class="label-header">
              File Size
              <span class="reqAsterisk">
                <ion-icon name="information-circle-outline"></ion-icon>
              </span>
            </ion-label>
            <ion-row>
              <ion-col size="5">
                <app-text-input-control
                  [isCustom]="true"
                  [label]="'Min file size'"
                  [backgroundColor]="'#292929'"
                  [placeHolder]="'Kb'"
                  formControlName="minFileSize"
                  type="number"></app-text-input-control>
              </ion-col>
              <ion-col size="2"> <h6 style="text-align: center">to</h6> </ion-col>
              <ion-col size="5">
                <app-text-input-control [isCustom]="true" [label]="'Max file size'" [backgroundColor]="'#292929'" [placeHolder]="'Mb'" formControlName="maxFileSize" type="number">
                </app-text-input-control>
              </ion-col>
            </ion-row>
          </ion-item>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="select-padding">
          <app-select-option-control
            [multiple]="true"
            [label]="'File Types'"
            [options]="builderService.acceptedFileFormats"
            [backgroundColor]="'#333333'"
            [disabled]="false"
            formControlName="fileTypeBw">
          </app-select-option-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-card>
            <ion-card-content>
              <app-field-checkboxes-base [baseForm]="downloadBlockForm"></app-field-checkboxes-base>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
