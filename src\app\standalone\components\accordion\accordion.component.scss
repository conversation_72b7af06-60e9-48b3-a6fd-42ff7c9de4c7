.parent-container {
  margin: 8px;
  .reorder-container-left {
    float: left;
    margin: 10px;
    ion-icon {
      font-size: 20px;
      color: white;
    }

    .trash-icon {
      cursor: pointer;
    }

    .reorder-icon-container {
      margin-top: 40px;
    }
  }

  .card-container {
    padding: 10px;
    border-radius: 3px;
    background-color: #2d2e32;
    .count-heading {
      color: white;
      letter-spacing: 0.5px;
    }

    ion-item {
      margin-bottom: 5px;
      border-radius: 3px;
      border: 1px solid #4e4e4e;
    }
  }

  .add-item-col {
    padding: 0px;
    display: flex;

    align-items: center;

    ion-icon {
      color: rgb(249, 158, 0) !important;
      font-size: 30px;
      cursor: pointer;
    }

    .add-button-text {
      margin-left: 10px;
      font-size: 16px;
      color: white;
    }
  }
}
