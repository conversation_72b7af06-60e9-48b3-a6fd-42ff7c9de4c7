@if (!isScorm) {
  <div class="breadcrumb-container" [ngClass]="{ 'no-header-container': !hasPageTitle }">
    @if (!isFirstLevelView && (breadcrumbs.length >= 0 || routeParams.viewType === viewTypes.Player || onlyContent)) {
      <ion-fab-button size="small" [ngClass]="{ 'back-btn': !onlyContent, 'back-btn-large': onlyContent }" [color]="'dark'" aria-label="Go Back" (click)="goBack()">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-fab-button>
    }
    @if (breadcrumbs && breadcrumbs.length > 0) {
      <ion-breadcrumbs [ngClass]="{ 'no-header-breadcrumbs': !hasPageTitle }" color="light" class="breadcrum-margin" [maxItems]="3" [itemsBeforeCollapse]="0" [itemsAfterCollapse]="2">
        @for (breadcrumb of breadcrumbs; track breadcrumb; let i = $index) {
          <ion-breadcrumb [active]="i + 1 === breadcrumbs.length" [ngClass]="{ 'header-included': hasPageTitle }" (click)="goToInstance(breadcrumb, i, $event)">
            <span class="breadcrumb-text" [matTooltip]="breadcrumb.name">{{ breadcrumb.name }}</span>
          </ion-breadcrumb>
          @if (i >= breadcrumbs.length - 3 && i + 1 !== breadcrumbs.length) {
            <ion-icon class="forward-slash" name="chevron-forward-outline"></ion-icon>
          }
        }
      </ion-breadcrumbs>
    }
  </div>
}
