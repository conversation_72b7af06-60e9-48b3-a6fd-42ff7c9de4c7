@if (checkboxFieldForm) {
  <form [formGroup]="checkboxFieldForm">
    <ion-grid>
      <app-field-editor-base [fieldForm]="checkboxFieldForm"></app-field-editor-base>
      <ion-row>
        <ion-col>
          <ion-card>
            <ion-card-content>
              <app-field-checkboxes-base [baseForm]="checkboxFieldForm"></app-field-checkboxes-base>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVariable">Is Variable</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVisibleRepository">Is Visible in Repository</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isInherit">System Property</mat-slide-toggle>
              @if (isInheritControl.value) {
                <app-system-property-selector [templateField]="component.templateField" [formGroup]="checkboxFieldForm"></app-system-property-selector>
              }
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
