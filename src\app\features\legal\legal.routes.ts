import { Routes } from '@angular/router';
import { DataProcessingAgreementComponent } from './pages/data-processing-agreement/data-processing-agreement';
import { PrivacyPolicyComponent } from './pages/privacy-policy/privacy-policy';
import { PrivacyWhitepaperComponent } from './pages/privacy-whitepaper/privacy-whitepaper';
import { TermsOfUseComponent } from './pages/terms-of-use/terms-of-use';

export const ROUTES: Routes = [
  {
    path: 'terms-of-use',
    component: TermsOfUseComponent,
  },
  {
    path: 'privacy-policy',
    component: PrivacyPolicyComponent,
  },
  {
    path: 'privacy-whitepaper',
    component: PrivacyWhitepaperComponent,
  },
  {
    path: 'data-processing-agreement',
    component: DataProcessingAgreementComponent,
  },
];
