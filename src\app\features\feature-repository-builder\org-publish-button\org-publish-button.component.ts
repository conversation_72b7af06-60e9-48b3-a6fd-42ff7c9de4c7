import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { IOrganizationStatusType } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { OrganizationStatusType } from '@app/core/enums/OrganizationStatusTypes';
import { OverlayEventDetail } from '@app/features-shared/row-instance/components/content/grid-view/thumbnail-styles/styles/image-background/image-background.component';
import { ConfirmationDialogComponent } from '@app/standalone/modals/confirmation-dialog/confirmation-dialog.component';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-org-publish-button',
  standalone: false,
  templateUrl: './org-publish-button.component.html',
  styleUrl: './org-publish-button.component.scss',
})
export class OrgPublishButtonComponent implements OnChanges {
  @Input() orgStatuses: IOrganizationStatusType[];
  @Input() columnValue: string;
  @Output() statusChanged = new EventEmitter<string>();
  orgStatusTypeEnum = OrganizationStatusType;

  orgStatusTypes: KeyValue[];
  orgStatus?: IOrganizationStatusType;
  isPublishEnabled = false;
  isUnpublishEnabled = false;
  controlBackground = '#181818';

  constructor(private modalController: ModalController) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['orgStatuses'] || changes['columnValue']) {
      this.updateData();
    }
  }

  updateData() {
    this.setOrgStatusTypes(this.columnValue);
    this.setPublishEnabled(this.columnValue);
    this.setUnpublishEnabled(this.columnValue);
  }

  async delete() {
    const modal = await this.modalController.create({
      component: ConfirmationDialogComponent,
      cssClass: 'confirm-dialog',
      componentProps: {
        headerText: 'Delete your page',
        bodyText: `You're about to delete this organization. Once you delete your work it will no longer be visible to enrolled people and other users at your Organization.`,
        buttonText: 'Delete',
      },
    });

    modal.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
      if (overlayEventDetail.role === 'confirm') {
        this.changeStatus(64);
      }
      else {
        this.changeStatus(this.orgStatus!.typeBw);
      }
    });
    await modal.present();
  }

  setOrgStatusTypes(id: string) {
    if (!this.orgStatuses) {
      this.orgStatusTypes = [];
    }
    const orgStatusTypes = this.orgStatuses.map(x => ({ id: x.id, value: x.name }) as KeyValue);
    const record = this.orgStatuses.find(x => x.id === id);
    this.orgStatus = record;
    if (record?.typeBw === this.orgStatusTypeEnum.Approved || (record?.typeBw || 0) >= this.orgStatusTypeEnum.Published) {
      this.orgStatusTypes = orgStatusTypes;
      return;
    }

    this.orgStatusTypes = this.orgStatuses.filter(x => x.typeBw < this.orgStatusTypeEnum.Published).map(x => ({ id: x.id, value: x.name }) as KeyValue);
  }

  setPublishEnabled(id: string) {
    if (!this.orgStatuses) {
      this.isPublishEnabled = false;
      return;
    }

    const record = this.orgStatuses.find(x => x.id === id);
    this.isPublishEnabled = record?.typeBw === this.orgStatusTypeEnum.Approved || record?.typeBw === this.orgStatusTypeEnum.Unpublished;
  }

  setUnpublishEnabled(id: string) {
    if (!this.orgStatuses) {
      this.isUnpublishEnabled = false;
      return;
    }
    const record = this.orgStatuses.find(x => x.id === id);
    this.isUnpublishEnabled = record?.typeBw === this.orgStatusTypeEnum.Published;
  }

  updateStatus(id: string) {
    this.columnValue = id;
    if (id === this.orgStatuses.find(x => x.name === 'Deleted')?.id) {
      this.delete();
    } else {
      this.updateData();
      this.statusChanged.next(id ?? '');
    }
  }

  changeStatus(typeBw: number) {
    const id = this.orgStatuses.find(x => x.typeBw === typeBw)?.id;
    if (id) {
      this.columnValue = id;
      this.updateData();
      this.statusChanged.next(id ?? '');
    }
  }
}
