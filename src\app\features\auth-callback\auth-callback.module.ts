import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@app/shared/shared.module';
import { featureComponents } from './auth-callback.declarations';
import { ROUTES } from './auth-callback.routes';

@NgModule({
  imports: [SharedModule, RouterModule.forChild(ROUTES), FormsModule],
  declarations: [...featureComponents],
  exports: [...featureComponents],
})
export class AuthCallbackModule {}
