import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import {
  DashboardGrid,
  IAction,
  IAsset,
  IAssetCaptionLanguages,
  IAssetIn,
  IAssetRepo,
  ICampaignIn,
  ICombinedUserRiasecFinalScore,
  ICommunication,
  ICommunicationBlock,
  ICommunicationIn,
  IComponent,
  IComponentAchievement,
  IComponentAchievementIn,
  IComponentEditIn,
  IComponentIn,
  IComponentSortOrderIn,
  IComponentType,
  ICustomContentDetail,
  IDashboardGridFilters,
  IDropDownLinkType,
  IEarningCriteria,
  IEarningCriteriaContentIn,
  IEarningCriteriaContentSearch,
  IEarningCriteriaIn,
  IEarningCriteriaType,
  IEarningRowCriteria,
  IEngagementIn,
  IFeature,
  IFeatureCommunicationIn,
  IFeatureIn,
  IFeatureSearch,
  IFeatureSearchLite,
  IFeatureTab,
  IFeatureTabButtonLinkType,
  IFeatureTabIn,
  IFeatureTabType,
  IFeatureType,
  IFileOut,
  IInstance,
  IInstanceAchievement,
  IInstanceAchievementIn,
  IInstanceIn,
  IInstanceSection,
  IInstanceSectionComponent,
  IInstanceSectionComponentIn,
  IInstanceSectionComponentValue,
  IInstanceSectionIn,
  IInstanceSectionLite,
  IInstanceSectionsAndComponentsResult,
  IInstanceTagIn,
  IInstanceTemplate,
  IInstanceUserIn,
  IJoinResult,
  ILTIActivityIn,
  ILTIToken,
  ILTLink,
  ILTLinkIn,
  ILTSubmit,
  ILinkedUserEmails,
  INetwork,
  INetworkAnalytics,
  INetworkIn,
  INetworkOrganization,
  INetworkOrganizationSearch,
  INetworkProductHistory,
  INetworkProductLicenseCount,
  INotification,
  IOrgPrivacyTypeIn,
  IOrganization,
  IOrganizationIn,
  IOrganizationLite,
  IOrganizationSearch,
  IOrganizationSsoAuth,
  IOrganizationSsoAuthIn,
  IOrganizationSsoauthExternalUrl,
  IOrganizationStatusType,
  IOrganizationTableUser,
  IOrganizationUser,
  IPagedResult,
  IPasswordResetIn,
  IPeople,
  IProduct,
  IProductFeature,
  IProductFeatureIn,
  IProductFeatureInstance,
  IProductFeatureInstanceIn,
  IProductFeatureRole,
  IProductFeatureRoleIn,
  IProductHistory,
  IProductIn,
  IProductJoinCodeSetting,
  IProductOrgDomain,
  IProductOrganization,
  IProductRenew,
  IProductSearch,
  IProductTableUser,
  IQuestion,
  IQuestionIn,
  IQuestionType,
  IRepositoryBuilderDashboard,
  IResults,
  IRole,
  IRow,
  IRowAchievement,
  IRowAchievementIn,
  IRowContent,
  IRowContentIn,
  IRowFilter,
  IRowIn,
  IRowLite,
  IRowModifiable,
  IRowSettingsIn,
  IRowStylingIn,
  IRowTaggingIn,
  IRowType,
  IScormAuth,
  IScormAuthIn,
  IScormFile,
  IScormRosterIn,
  IScormUser,
  ISection,
  ISectionIn,
  ISystemProperty,
  ISystemPropertyValue,
  ITab,
  ITag,
  ITagIn,
  ITemplate,
  ITemplateField,
  ITemplateFieldIn,
  IThumbnailType,
  IUser,
  IUserAnswer,
  IUserAnswerIn,
  IUserAssessmentFeedbackIn,
  IUserClaimSearch,
  IUserCommunicationPreference,
  IUserContext,
  IUserDomain,
  IUserInstanceInterest,
  IUserInstanceTracking,
  IUserOrganizationProduct,
  IUserRole,
  IUserSetup,
  IUserTag,
  IWorkFlow,
  NetworkOrganizationTableData,
  OrganizationTableUserLite,
  IRowTagIn,
  IRowProductIn,
  SelectedOrganizationIn,
  IMyInstanceResult,
  IBadgeSearch,
  IQlikSheet,
  IQlikComponent,
  IInstanceResult,
  IRowContentById,
  IUserOrganizationInterest,
} from '../contracts/contract';

@Injectable()
export class DataService {
  reload$ = new Subject<any>();

  constructor(private http: HttpClient) {}

  /***************************************
   User Context
  ***************************************/

  public getUserContext(trackingId: string | null) {
    if (trackingId) {
      return this.http.get<IUserContext>(`user/context?trackingUserId=${trackingId}`);
    }
    return this.http.get<IUserContext>('user/context');
  }

  public resetPassword(data: IPasswordResetIn) {
    return this.http.post('user/password/reset', data);
  }

  public specificUserPasswordReset(userId: string) {
    return this.http.post('user/specified/password/reset', userId);
  }
  public getUserFullName(userId: string) {
    return this.http.get<IUserContext>(`user/${userId}/name`);
  }

  public userLookupByEmail(user: IUser) {
    return this.http.get<boolean>(`user/lookup/email?email=${user.email}`);
  }

  public userLookupByUsername(user: IUser) {
    return this.http.get<boolean>(`user/lookup/username?username=${user.username}`);
  }

  public addUser(user: IUser) {
    return this.http.post<string>('user/add/new', user);
  }

  public addInstanceBadgeUser(instanceId: string) {
    return this.http.post<boolean>(`user/add/instancebadgeuser/${instanceId}`, null);
  }

  public findInstanceBadgeUser(instanceId: string) {
    return this.http.get<boolean>(`user/find/instancebadgeuser/${instanceId}`);
  }

  public getUserOrgsByDomain() {
    return this.http.get<IUserDomain>(`user/domain/organizations`);
  }

  public saveDomainSettings(orgId: string, domain: string, domainRole: string) {
    return this.http.post(`user/domain/save/${orgId}?domain=${domain}&domainRole=${domainRole}`, null);
  }
  public saveUserDomainPopupPreference(shouldReceiveDomainPopup: boolean) {
    return this.http.post(`user/domainpreference/save?shouldReceiveDomainPopup=${shouldReceiveDomainPopup}`, null);
  }

  public getUserLinkedEmails() {
    return this.http.get<ILinkedUserEmails>(`user/linkedemails`);
  }

  public addLinkedEmail(email: string, location: string, userTrackingId: string) {
    return this.http.put(`user/linkedemails?emailAddress=${email}&location=${location}&userTrackingId=${userTrackingId}`, null);
  }

  public removeLinkedEmail(email: string, userTrackingId: string) {
    return this.http.delete(`user/linkedemails?emailAddress=${email}&userTrackingId=${userTrackingId}`);
  }

  public updateOrganizationUserQlik(organizationUserId: string, userId: string, qlikUser: boolean) {
    return this.http.put<boolean>(`user/organization/qlikuser/${organizationUserId}?userId=${userId}&qlikUser=${qlikUser}`, null);
  }

  public getUserIsEducator(instanceId: string) {
    return this.http.get<boolean>(`user/isEducator/${instanceId}`);
  }

  /***************************************
   Features
  ***************************************/
  public createFeature(featureTypeId: string) {
    return this.http.post<string>(`feature/${featureTypeId}`, null);
  }

  public getFeatures() {
    return this.http.get<IFeature[]>('feature');
  }

  public getFeatureTypeBySlug(slug: string) {
    return this.http.get<IFeatureType>(`feature/slug/${slug}`);
  }

  public createFeatureTab(featureTabIn: IFeatureTabIn) {
    return this.http.post<IFeatureTab>('feature/featureTab', featureTabIn);
  }

  public deleteFeatureTab(featureTabId: string) {
    return this.http.delete(`feature/featureTab/${featureTabId}`);
  }

  public updateTab(tab: ITab) {
    return this.http.put('feature/tab', tab);
  }

  public updateFeatureTab(featureTabIn: IFeatureTabIn) {
    return this.http.put('feature/featureTab', featureTabIn);
  }

  public getFeatureById(id: string) {
    return this.http.get<IFeature>('feature/' + id);
  }

  public getFeatureByTypeName(typeName: string) {
    return this.http.get<IFeature>(`feature/type/${typeName}`);
  }

  public getFeatureTab(id: string) {
    return this.http.get<IFeature>('feature/featuretab/' + id);
  }

  public getFeatureTabTypes() {
    return this.http.get<IFeatureTabType[]>('feature/tab/types');
  }

  public getFeatureTabButtonLinkTypes() {
    return this.http.get<IFeatureTabButtonLinkType[]>('feature/button/link/types');
  }

  public getFeatureTypes() {
    return this.http.get<IFeatureType[]>('feature/types');
  }

  public getMyFeatureTypes(instanceId: string) {
    return this.http.get<IFeatureType[]>('feature/my/types/' + instanceId);
  }

  public getFeatureDashboard(id: string, q: string, pageNo: number, pageSize: number) {
    return this.http.get<IPagedResult<IRepositoryBuilderDashboard[]>>('feature/' + id + '/dashboard?q=' + q + '&pageNo=' + pageNo + '&pageSize=' + pageSize);
  }

  public putFeature(id: string, featureIn: IFeatureIn) {
    return this.http.put<IFeature>('feature/' + id, featureIn);
  }

  public putFeatureCommunications(id: string, data: IFeatureCommunicationIn) {
    return this.http.put<boolean>('feature/' + id + '/communications', data);
  }

  public searchFeatures(query: string, currentAmount: number, getAmount: number) {
    return this.http.get<IFeatureSearchLite[]>(`feature/search?query=${query}&currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  /***************************************
   Rows
  ***************************************/
  public createRow(row: IRowIn) {
    return this.http.post<string>('row', row);
  }

  public getRowModifiable(id: string, readingMode: boolean) {
    return this.http.get<IRowModifiable>(`row/${id}/modifiable?readingMode=${readingMode}`);
  }

  public getRow(id: string, readingMode: boolean) {
    return this.http.get<IRow>(`row/${id}?readingMode=${readingMode}`);
  }

  public getRowContent(
    rowId: string,
    userLocation: IUserContext,
    limit: number,
    skip: number,
    isLoadAll: boolean,
    searchFilter: string | null = null,
    instanceId: string | null = null,
    entityId: string | null = null,
    featureTypeId: string | null = null,
    selectedUserId: string | null = null
  ) {
    const body = {
      userLocation: userLocation,
      limit: Math.round(limit),
      skip: Math.round(skip),
      isLoadAll: isLoadAll,
      searchFilter: searchFilter,
      instanceId: instanceId,
      entityId: entityId,
      featureTypeId: featureTypeId,
      selectedUserId: selectedUserId,
    };

    return this.http.post<IRowContent>(`row/${rowId}/content`, body);
  }

  public putRowSettings(id: string, data: IRowSettingsIn) {
    return this.http.put<boolean>('row/' + id + '/settings', data);
  }

  public putRowContentOrder(id: string, data: IRowContentIn) {
    return this.http.put<boolean>('row/' + id + '/content', data);
  }

  public putRowStyling(id: string, data: IRowStylingIn) {
    return this.http.put<boolean>('row/' + id + '/styling', data);
  }

  public putRowFilter(id: string, data: IRowFilter | undefined) {
    return this.http.put<boolean>('row/' + id + '/filter', data);
  }

  public putRowTagging(id: string, data: IRowTaggingIn) {
    return this.http.put<boolean>('row/' + id + '/tagging', data);
  }

  public getRowDisplayTypes() {
    return this.http.get<IRowType[]>('row/type/displaytypes');
  }

  public getRowThumbnailTypes(id: string) {
    return this.http.get<IThumbnailType[]>('row/type/' + id + '/thumbnailtypes');
  }

  public getRowTypes() {
    return this.http.get<IRowType[]>('row/type');
  }

  public putRowType(rowId: string, rowTypeBw: number) {
    return this.http.put<boolean>('row/' + rowId + '/type/' + rowTypeBw, null);
  }

  public addRowContent(rowId: string, instanceId: string) {
    return this.http.post<boolean>(`row/${rowId}/content/${instanceId}`, null);
  }

  public deleteRowContent(rowId: string, instanceId: string) {
    return this.http.delete<boolean>(`row/${rowId}/remove/${instanceId}`);
  }

  public deleteRowCacheItem(contentId: string) {
    return this.http.post<boolean>(`row/${contentId}/deleteCache`, null);
  }

  public editAddCustomRowContent(rowContentDetail: ICustomContentDetail) {
    return this.http.post<boolean>('row/editaddcustomcontent', rowContentDetail);
  }

  public createRowAchievement(rowAchievementIn: IRowAchievementIn) {
    return this.http.post<boolean>(`row/achievement`, rowAchievementIn);
  }

  public getRowInstanceAchievements(id: string, instanceId: string) {
    return this.http.get<IRowAchievement[]>(`row/${id}/instance/${instanceId}/achievments`);
  }

  public getRowProducts(rowId: string) {
    return this.http.get<IProduct[]>(`row/rowproducts?rowId=${rowId}`);
  }

  public getRowAllProducts(rowId: string) {
    return this.http.get<IProduct[]>(`row/rowallproducts?rowId=${rowId}`);
  }

  public addRowProduct(rowProductIn: IRowProductIn) {
    return this.http.post<boolean>(`row/addrowproduct`, rowProductIn);
  }

  public removeRowProduct(rowId: string, productId: string) {
    return this.http.delete<boolean>(`row/removerowproduct/${rowId}?productId=${productId}`);
  }

  /***************************************
   Instance
  ***************************************/
  public getInstance(id: string | undefined, isScorm = false, showInterest = false) {
    let route = `instance/${id}?isScorm=${isScorm}`;

    if (showInterest) {
      route = route + `&showInterest=${showInterest}`;
    }
    return this.http.get<IInstance>(route);
  }

  public getInstanceBySlug(slug: string, organizationId: string | null, isScorm = false, showInterest = false) {
    let route = `instance/slug/${slug}?`;

    if (organizationId) {
      route = route + `organizationId=${organizationId}&`;
    }

    if (showInterest) {
      route = route + `showInterest=${showInterest}&`;
    }
    return this.http.get<IInstance>(`${route}isScorm=${isScorm}`);
  }

  public updateInstance(instanceId: string, instance: IInstanceIn) {
    return this.http.put<string>(`instance/${instanceId}`, instance);
  }

  public updateInstanceStatus(instanceId: string, status: string) {
    return this.http.put<string>(`instance/${instanceId}/status?status=${status}`, null);
  }

  public getInstanceTemplate(instanceId: string, templateId: string, isViewField = false, isBuilderField = false, isScorm = false) {
    return this.http.get<IInstanceTemplate>(`instance/${instanceId}/template/${templateId}?isViewField=${isViewField}&isBuilderField=${isBuilderField}&isScorm=${isScorm}`);
  }

  public createInstanceByFeatureId(featureId: string, orgId: string) {
    return this.http.post<IRepositoryBuilderDashboard>(`instance/new/${featureId}/${orgId}`, null);
  }

  public createInstanceByFeatureTypeId(featureTypeId: string, orgId: string) {
    return this.http.post<IRepositoryBuilderDashboard>(`instance/new/featureType/${featureTypeId}/${orgId}`, null);
  }

  public createInstance(instanceIn: IInstanceIn) {
    return this.http.post<IRepositoryBuilderDashboard>('instance', instanceIn);
  }

  public createInstances(instanceIn: IInstanceIn[]) {
    return this.http.post<IRepositoryBuilderDashboard>('instance/multiple', instanceIn);
  }

  public deleteInstance(id: string) {
    return this.http.delete<boolean>(`instance/${id}`);
  }

  public deleteInstanceSection(id: string) {
    return this.http.delete<boolean>(`instance/section/${id}`);
  }

  public deleteInstanceSectionComponent(id: string) {
    return this.http.delete<boolean>(`instance/component/${id}`);
  }

  public searchInstances(orgId: string | null, search: string, userId: string | null = null) {
    const query = `instance/search?search=${search}`;

    if (orgId) {
      query.concat(`&orgId=${orgId ?? null}`);
    }

    if (userId) {
      query.concat(`&userId=${userId ?? null}`);
    }

    return this.http.get<IInstance[]>(query);
  }

  public getInstances(query: string | null, organizationId?: string) {
    return this.http.get<IInstance[]>(`instance?query=${query}&organizationId=${organizationId}`);
  }

  public getInstanceAchievements(instanceId: string) {
    return this.http.get<IInstanceAchievement[]>(`instance/${instanceId}/achievements`);
  }

  public updateInstanceAchievement(achievement: IInstanceAchievementIn) {
    return this.http.put<boolean>(`instance/achievement`, achievement);
  }

  public getMyInstances(query: string, instanceId: string, featureTypeId?: string) {
    let dataQuery = `instance/my?query=${query}&instanceId=${instanceId}`;

    if (featureTypeId) {
      dataQuery = dataQuery + `&featureTypeId=${featureTypeId}`;
    }

    return this.http.get<IMyInstanceResult>(dataQuery);
  }

  public getInstanceAssignments(instanceId: string, onlyAssignments = true) {
    return this.http.get<IMyInstanceResult>(`instance/${instanceId}/assignments?onlyAssignments=${onlyAssignments}`);
  }

  public getInstanceRowManagerRows(instanceId: string) {
    return this.http.get<IRowLite[]>(`instance/${instanceId}/rowmanager/rows`);
  }

  public getInstanceSections(instanceId: string, isScorm = false, userId: string | null) {
    let req = `instance/${instanceId}/sections?isScorm=${isScorm}`;

    if (userId) {
      req = req + `&userId=${userId}`;
    }

    return this.http.get<IInstanceSection[]>(req);
  }

  public getInstanceSectionsAndComponents(
    instanceId: string,
    userId: string | null = null,
    isViewField = false,
    isHoverField = false,
    isPreviewField = false,
    isRequiredField = false,
    isScorm = false
  ) {
    let url = `instance/${instanceId}/sections/components?isViewField=${isViewField}&isHoverField=${isHoverField}&isPreviewField=${isPreviewField}&isRequiredField=${isRequiredField}&isScorm=${isScorm}`;

    if (userId) {
      url += `&userId=${userId}`;
    }

    return this.http.get<IInstanceSectionsAndComponentsResult>(url);
  }

  public getInstanceSectionsAndComponentsBySlug(
    slug: string,
    userId: string | null = null,
    isViewField = false,
    isHoverField = false,
    isPreviewField = false,
    isRequiredField = false,
    isScorm = false
  ) {
    let url = `instance/slug/${slug}/sections/components?isViewField=${isViewField}&isHoverField=${isHoverField}&isPreviewField=${isPreviewField}&isRequiredField=${isRequiredField}&isScorm=${isScorm}`;

    if (userId) {
      url += `&userId=${userId}`;
    }

    return this.http.get<IInstanceSectionsAndComponentsResult>(url);
  }

  public assignToInstance(parentInstanceId: string, instanceId: string) {
    return this.http.put<boolean>(`instance/${parentInstanceId}/assign/${instanceId}`, null);
  }

  public updateInstanceSectionComponent(instanceSectionComponentId: string, value: string) {
    const instanceSectionComponentValue = {
      value: value,
    } as IInstanceSectionComponentValue;
    return this.http.put<boolean>(`instance/section/component/${instanceSectionComponentId}`, instanceSectionComponentValue);
  }

  public addInstanceSection(instanceSectionIn: IInstanceSectionIn) {
    return this.http.post<IInstanceSection>(`instance/addsection`, instanceSectionIn);
  }

  public updateInstanceSections(instanceSectionsIn: IInstanceSectionIn[], updateSortOrder = false) {
    return this.http.put<boolean>(`instance/updatesections?updateSortOrder=${updateSortOrder}`, instanceSectionsIn);
  }

  public getDynamicSectionComponentTypes(sectionId: string) {
    return this.http.get<IComponent[]>(`instance/dynamic/section/${sectionId}/componenttypes`);
  }

  public addInstanceSectionComponents(instanceSectionId: string, componentIds: string[]) {
    return this.http.post<IInstanceSectionComponent[]>(`instance/section/${instanceSectionId}/components`, componentIds);
  }

  public updateInstanceSectionComponents(instanceSectionComponentsIn: IInstanceSectionComponentIn[]) {
    return this.http.put<boolean>(`instance/updatesectioncomponents`, instanceSectionComponentsIn);
  }

  public addRowToClassroom(instanceId: string, rowId: string) {
    return this.http.post<boolean>(`instance/classroom/${instanceId}/row/${rowId}`, null);
  }

  public updateInstanceSection(instanceSectionLite: IInstanceSectionLite) {
    return this.http.put<boolean>(`instance/updatesection`, instanceSectionLite);
  }

  public checkInstanceSectionIsGraded(instanceId: string, instanceSectionId: string, userId: string | null) {
    return this.http.get<boolean>(`instance/${instanceId}/section/${instanceSectionId}/graded?userId=${userId}`);
  }

  public getInstanceStatusById(instanceId: string) {
    return this.http.get<boolean>(`instance/${instanceId}/status`);
  }

  public getInstanceStatusBySlug(slug: string) {
    return this.http.get<boolean>(`instance/slug/${slug}/status`);
  }

  public deleteInstanceAssignment(instanceId: string, parentId: string) {
    return this.http.delete<boolean>(`instance/assignment/${instanceId}/${parentId}`);
  }

  public getOrganizationsByInstanceId(instanceId: string) {
    return this.http.get<IOrganizationLite[]>(`instance/organizations/${instanceId}`);
  }

  public addOrganizationToInstance(instanceId: string, organizationId: string) {
    return this.http.post<boolean>(`instance/addOrganizationToInstance/${instanceId}/${organizationId}`, null);
  }

  public removeOrganizationFromInstance(instanceId: string, organizationId: string) {
    return this.http.put<boolean>(`instance/removeOrganizationFromInstance/${instanceId}/${organizationId}`, null);
  }

  /***************************************
   Templates
  ***************************************/
  public getTemplate(id: string) {
    return this.http.get<ITemplate>(`template/${id}`);
  }
  public getComponentTypes() {
    return this.http.get<IComponentType[]>(`template/component/types`);
  }

  public createSection(templateId: string, typeId: string) {
    return this.http.post<ISection>(`template/section/${templateId}/${typeId}`, null);
  }

  public updateSection(section: ISectionIn, persistSortOrder = false) {
    return this.http.patch<boolean>(`template/section?persistSortOrder=${persistSortOrder}`, section);
  }

  public updateSectionOrder(sectionId: string, sortOrder: number) {
    return this.http.patch<boolean>(`template/section/${sectionId}/order/${sortOrder}`, null);
  }

  public deleteSection(id: string) {
    return this.http.delete<ISection>(`template/section/${id}`);
  }

  public createTemplateField(componentId: string, label: string) {
    return this.http.post<boolean>(`template/component/${componentId}/createTemplateField/${label}`, null);
  }

  public createComponent(component: IComponentIn) {
    return this.http.post<IComponent>('template/component', component);
  }

  public editComponent(component: IComponentEditIn) {
    return this.http.patch('template/component/edit', component);
  }

  public deleteComponent(id: string) {
    return this.http.delete<boolean>(`template/component/${id}`);
  }

  public orderComponents(commponents: IComponentSortOrderIn[]) {
    return this.http.put<boolean>('template/component/order', commponents);
  }

  public updateTemplateField(templateField: ITemplateFieldIn) {
    return this.http.patch('template/field', templateField);
  }

  public updateTemplateFieldVisibleCheck(templateFieldId: string | undefined, isVisible: boolean) {
    return this.http.patch(`template/field/${templateFieldId}/visible`, isVisible);
  }

  public getFilters(id: string) {
    return this.http.get<ITemplateField[]>(`template/filters/${id}`);
  }

  public getComponentAchievements(componentId: string) {
    return this.http.get<IComponentAchievement[]>(`template/component/${componentId}/achievements`);
  }

  public getDropDownLinkTypes() {
    return this.http.get<IDropDownLinkType[]>(`template/dropdown/link/types`);
  }

  public updateComponentAchievement(achievement: IComponentAchievementIn) {
    return this.http.put<boolean>(`template/component/achievement`, achievement);
  }

  public updateComponentAchievements(componentId: string, achievementIds: string[]) {
    return this.http.post<boolean>(`template/component/${componentId}/achievements`, achievementIds);
  }

  public getSectionTypes() {
    return this.http.get<any>('template/section/types');
  }

  public toggleComponentLocked(componentId: string, isLocked: boolean) {
    return this.http.patch<boolean>(`template/component/${componentId}/lock/${isLocked}`, null);
  }
  /***************************************
   Assets
  ***************************************/
  public postAsset(asset: IFileOut, containerName: string, mediaId: string | null) {
    return this.http.post<IAsset>(`asset/upload/${containerName}?mediaId=${mediaId}`, asset.asset, {
      headers: new HttpHeaders({ noheader: 'true' }),
      reportProgress: true,
      observe: 'events',
    });
  }

  public deleteAsset(fileUrl: string | null, mediaId: string | null) {
    return this.http.delete<any>(`asset?fileUrl=${fileUrl}&&mediaId=${mediaId}`);
  }

  public getAssetDetailsById(assetId: string) {
    return this.http.get<IAsset>(`asset/${assetId}`);
  }

  public getAssetCaptionLanguages(assetId: string) {
    return this.http.get<IAssetCaptionLanguages[]>(`asset/${assetId}/captions`);
  }

  public getAssets(query: string) {
    return this.http.get<IAsset[]>(`asset?query=${query}`);
  }

  public downloadBlobFile(assetId: string) {
    return this.http.get(`asset/${assetId}/content/`, { responseType: 'blob', headers: new HttpHeaders({ cdn: 'true' }) });
  }

  public addAsset(uploadType: string) {
    return this.http.post<string>(`asset/${uploadType}`, null);
  }

  public updateAsset(asset: IAssetIn) {
    return this.http.put<string>('asset', asset);
  }

  public publishAsset(assetId: string) {
    return this.http.put<boolean>(`asset/${assetId}`, null);
  }

  /***************************************
   System Properties
  ***************************************/
  public getSystemProperties(searchString: string, featureId?: string) {
    return this.http.get<ISystemProperty[]>(`systemproperty?query=${searchString}&featureId=${featureId}`);
  }

  public getSystemPropertyContextValues(id: string, type: number) {
    return this.http.get<ISystemPropertyValue[]>(`systemproperty/${id}/type/${type}`);
  }

  public setSystemPropertyContextValues(id: string, type: number, properties: ISystemPropertyValue[]) {
    return this.http.post<boolean>(`systemproperty/${id}/type/${type}`, properties);
  }

  /***************************************
   Tags
  ***************************************/
  public getTags() {
    return this.http.get<ITag[]>(`tag`);
  }

  public getTag(id: string) {
    return this.http.get<ITag>(`tag/${id}`);
  }

  public getTagsByIds(selectedTagIdList: string[]) {
    return this.http.post<ITag[]>(`tag/tagsbyids`, selectedTagIdList);
  }

  public getTagChildren(tagId: string | null, treeLevel = 0, includeAncestors = false, linkType: string | null = null) {
    return this.http.get<ITag[]>(`tag/children?tagId=${tagId}&treeLevel=${treeLevel}&includeAncestors=${includeAncestors}&linkType=${linkType}`);
  }

  public getTagChildrenSearch(tagId: string | null, tagName: string | null = null) {
    return this.http.get<ITag[]>(`tag/children?tagId=${tagId}&tagName=${tagName}`);
  }

  public getInstanceComponentTagChildren(
    tagId: string | null,
    instanceId?: string,
    componentId?: string,
    linkType: string | null = null,
    selectedUserId: string | null = null,
    tagName: string | null = null
  ) {
    return this.http.get<ITag[]>(
      `tag/instance/component/children?tagId=${tagId}&instanceId=${instanceId}&componentId=${componentId}&linkType=${linkType}&selectedUserId=${selectedUserId}&tagName=${tagName}`
    );
  }

  public tagHasChildren(tagId: string) {
    return this.http.get<boolean>(`tag/has/children/${tagId}`);
  }

  public addToInstanceTag(instanceTag: IInstanceTagIn) {
    return this.http.post<boolean>(`tag/addinstancetag`, instanceTag);
  }

  public deleteInstanceTag(tagId: string, instanceId: string, componentId: string) {
    return this.http.delete<boolean>(`tag/removeinstancetag/${tagId}?instanceId=${instanceId}&componentId=${componentId}`);
  }

  public deleteTag(tagId: string) {
    return this.http.delete<boolean>(`tag/removetag/${tagId}`);
  }

  public getInstanceTags(instanceId: string, componentId: string) {
    return this.http.get<ITag[]>(`tag/instancetags?instanceId=${instanceId}&componentId=${componentId}`);
  }

  public getInstanceTagsByParentId(instanceId: string, parentId: string) {
    return this.http.get<ITag[]>(`tag/instancetagsbyparent/${instanceId}/${parentId}`);
  }

  public getTagChildrenByParentName(parentName: string) {
    return this.http.get<ITag[]>(`tag/children/parentname?parentname=${parentName}`);
  }

  public addTag(tag: ITagIn) {
    return this.http.post<ITag>(`tag`, tag);
  }

  public getRowTags(rowId: string) {
    return this.http.get<ITag[]>(`tag/rowtags?rowId=${rowId}`);
  }

  public getRowTagChildren(rowId: string, tagId: string | null, tagName: string | null = null) {
    return this.http.get<ITag[]>(`tag/rowtag/children?rowId=${rowId}&tagId=${tagId}&tagName=${tagName}`);
  }

  public addRowTag(rowTagIn: IRowTagIn) {
    return this.http.post<boolean>('tag/addrowtag', rowTagIn);
  }

  public removeRowTag(rowId: string, tagId: string) {
    return this.http.delete<boolean>(`tag/removerowtag/${rowId}?tagId=${tagId}`);
  }

  /***************************************
   Organizations
  ***************************************/

  public getAllOrganizations(currentAmount: number, getAmount: number, search?: string, selectedOrgIds?: SelectedOrganizationIn[]) {
    return this.http.post<IOrganizationLite[]>(`organization/all?search=${search}&currentAmount=${currentAmount}&getAmount=${getAmount}`, selectedOrgIds);
  }

  public getMyOrganizations(featureId?: string) {
    return this.http.get<IOrganizationLite[]>(`organization/my?featureId=${featureId}`);
  }

  public getMyQlikOrganizations() {
    return this.http.get<IOrganizationLite[]>(`organization/myQlikOrganizations`);
  }

  public getMyNetworks(featureId?: string) {
    return this.http.get<INetwork[]>(`organization/mynetworks`);
  }

  public getOrganizationStatusTypes() {
    return this.http.get<IOrganizationStatusType[]>(`organization/status/types`);
  }

  public getConnectedOrganizations(orgId: string) {
    return this.http.get<IOrganization[]>(`organization/${orgId}/connected`);
  }

  public assignOrganizationToInstance(instanceId: string, orgId: string) {
    return this.http.post<boolean>(`organization/${orgId}/instance/${instanceId}`, null);
  }

  public addOrganization(organization: IOrganizationIn) {
    return this.http.post<IOrganization>(`organization`, organization);
  }

  public searchOrganizationByProductId(productId: string, search: string, currentAmount: number, getAmount: number) {
    return this.http.get<IOrganizationSearch[]>(`organization/search/${productId}?search=${search}&currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public addOrganizationsToProduct(productId: string, selectedOrgs: IOrganizationSearch[]) {
    return this.http.post<IProductOrganization[]>(`organization/addorgstoproduct/${productId}`, selectedOrgs);
  }

  public addOrganizationToProduct(product: IProductRenew, selectedOrgs: IOrganizationSearch) {
    return this.http.post<IProductOrganization[]>(`organization/addorgtoproduct/${product.id}?period=${product.period}&expiryDate=${product.expiryDate}`, selectedOrgs);
  }

  public getOrganizationsById(id: string, type: string, currentAmount: number, getAmount: number, userId?: string) {
    let query = `organization/organizations/${id}?type=${type}&currentAmount=${currentAmount}&getAmount=${getAmount}`;
    if (userId) {
      query += `&userId=${userId}`;
    }
    return this.http.get<IOrganization[]>(query);
  }

  public getOrgSsoAuthById(orgId: string) {
    return this.http.get<IOrganizationSsoAuth>(`organization/orgssoauth/${orgId}`);
  }

  public updateOrgSsoAuth(orgSsoAuth: IOrganizationSsoAuthIn) {
    return this.http.put<boolean>(`organization/updateorgssoauth`, orgSsoAuth);
  }

  public updateOrgSsoAuthExternalUrl(orgSsoAuthExternalUrl: IOrganizationSsoauthExternalUrl) {
    return this.http.put<boolean>(`organization/updateorgssoauthexternalurl`, orgSsoAuthExternalUrl);
  }

  public updateOrgPrivacyType(privacyTypeIn: IOrgPrivacyTypeIn) {
    return this.http.put<boolean>(`organization/updateorgprivacytype`, privacyTypeIn);
  }

  public getPrivacyTypes() {
    return this.http.get<IUserRole[]>(`organization/privacytypes`);
  }

  public searchOrganizations(query: string, currentAmount: number, getAmount: number) {
    return this.http.get<IOrganizationSearch[]>(`organization/search?query=${query}&currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public getOrganizationById(id: string) {
    return this.http.get<IOrganizationLite>(`organization/lite/${id}`);
  }

  public getInstancesByOrganizationId(id: string) {
    return this.http.get<IInstance[]>(`organization/instances/${id}`);
  }

  public addInstanceToOrganization(organizationId: string, instanceId: string) {
    return this.http.post<boolean>(`organization/addInstanceToOrganization/${organizationId}/${instanceId}`, null);
  }

  public removeInstanceFromOrganization(organizationId: string, instanceId: string) {
    return this.http.put<boolean>(`organization/removeInstanceFromOrganization/${organizationId}/${instanceId}`, null);
  }

  public getOrganizationProducts(id: string) {
    return this.http.get<IProduct[]>(`organization/products/${id}`);
  }

  public getOrganizationTags(organizationId: string, componentId: string | null = null) {
    return this.http.get<ITag[]>(`organization/tags/${organizationId}?componentId=${componentId}`);
  }

  public getOrganizationTagChildren(organizationId: string, tagId: string) {
    return this.http.get<ITag[]>(`organization/tagchildren/${organizationId}?tagId=${tagId}`);
  }

  public updateRemoveOrganizationTag(organizationId: string, tagId: string) {
    return this.http.post<boolean>(`organization/updateremovetag/${organizationId}`, tagId);
  }

  /***************************************
   Networks
  ***************************************/

  public addNetwork(network: INetworkIn) {
    return this.http.post<INetwork>(`organization/network`, network);
  }

  public getNetworkOrganizationsById(id: string, type: string, currentAmount: number, getAmount: number) {
    return this.http.get<INetworkOrganization[]>(`organization/networkorgs/${id}?type=${type}&currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public removeNetworkOrganizationById(networkId: string, orgId: string) {
    return this.http.delete<boolean>(`organization/networkorgs/${networkId}/remove/${orgId}`);
  }
  public removeOrgsFromNetwork(networkId: string, orgs: INetworkOrganizationSearch[]) {
    return this.http.post<boolean>(`organization/networkorgs/${networkId}/removeOrgsFromNetwork`, orgs);
  }

  public addNetworkOrganizationById(networkId: string, org: INetworkOrganization) {
    return this.http.put<INetworkOrganization>(`organization/networkorgs/${networkId}`, org);
  }

  public addOrgsToNetwork(networkId: string, org: INetworkOrganizationSearch[]) {
    return this.http.put<NetworkOrganizationTableData[]>(`organization/networkorgs/${networkId}/addOrgsToNetwork`, org);
  }

  public addProductToNetwork(networkId: string, product: IProductRenew) {
    return this.http.post<IProductOrganization>(
      `organization/networkorgs/${networkId}/addProductToNetwork/${product.id}?period=${product.period}&expiryDate=${product.expiryDate}&availableLicenses=${product.availableLicenses}`,
      null
    );
  }

  public addNetworkOrganizationsToProduct(id: string, networkProductId: string, type: string, selectedNetworkOrganizations: INetworkOrganizationSearch[]) {
    return this.http.post<NetworkOrganizationTableData[]>(`organization/networkorgs/${id}/addToProduct/${networkProductId}?type=${type}`, selectedNetworkOrganizations);
  }

  public addNetworkOrganizationToProducts(id: string, networkOrgId: string, type: string, products: IProductSearch[]) {
    return this.http.post<IProductSearch[]>(`organization/networkorgs/${id}/addToProducts/${networkOrgId}?type=${type}`, products);
  }

  public deleteNetworkOrganizationsFromProduct(networkProductId: string, selectedNetworkOrganizations: INetworkOrganizationSearch[]) {
    return this.http.post<boolean>(`organization/networkorgs/deleteFromProduct/${networkProductId}`, selectedNetworkOrganizations);
  }

  public getNetworkOrgCountOnNetworkProduct(networkProductId: string) {
    return this.http.get<number>(`organization/networkorgs/getNetworkOrganizationCountByProductId/${networkProductId}`);
  }

  public getNetworkOrganizationsByNetworkProductId(
    id: string,
    networkProductId: string,
    type: string,
    currentAmount: number,
    getAmount: number,
    sortField: string | null = null,
    sortDirection: string | null = null,
    search?: string
  ) {
    return this.http.get<NetworkOrganizationTableData[]>(
      `organization/networkorgs/${id}/getByNetworkProductId/${networkProductId}?type=${type}&sortField=${sortField}&sortDirection=${sortDirection}&search=${search}&currentAmount=${currentAmount}&getAmount=${getAmount}`
    );
  }

  public getNetworkOrgsByNetworkId(networkId: string, currentAmount: number, getAmount: number, sortField: string | null = null, sortDirection: string | null = null, search?: string) {
    return this.http.get<NetworkOrganizationTableData[]>(
      `organization/networkorgs/${networkId}/getByNetworkId?sortField=${sortField}&sortDirection=${sortDirection}&search=${search}&currentAmount=${currentAmount}&getAmount=${getAmount}`
    );
  }

  public getNetworkOrgsWithoutProduct(id: string, networkProductId: string, type: string, currentAmount: number, getAmount: number, search?: string) {
    return this.http.get<INetworkOrganizationSearch[]>(
      `organization/networkorgs/${id}/getWithoutProduct/${networkProductId}?type=${type}&search=${search}&currentAmount=${currentAmount}&getAmount=${getAmount}`
    );
  }

  public getOrgsExclExistingNetworkOrgs(networkId: string, currentAmount: number, getAmount: number, search?: string) {
    return this.http.get<INetworkOrganizationSearch[]>(`organization/getOrgsExclExistingNetworkOrgs/${networkId}?search=${search}&currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  /***************************************
   Users
  ***************************************/

  public getUserTags(userId?: string, tagType?: string) {
    return this.http.get<IUserTag[]>(`user/tags?userId=${userId}&tagType=${tagType}`);
  }

  public getUserPersonaTags(userId?: string) {
    return this.http.get<ITag[]>(`user/personatags?userId=${userId}`);
  }

  public updateUserPersonaTag(tagId?: string, userId?: string) {
    return this.http.post<ITag[]>(`user/personatag?userId=${userId}`, tagId);
  }

  public deleteUserPersonaTag(tagId?: string, userId?: string) {
    let queryString = `${tagId}`;
    if (userId !== undefined || userId) {
      queryString += `?userId=${userId}`;
    }
    return this.http.delete<ITag[]>(`user/personatag/${queryString}`);
  }

  public deleteUserCampaignTag(tagId?: string, userId?: string) {
    let queryString = `${tagId}`;
    if (userId !== undefined || userId) {
      queryString += `?userId=${userId}`;
    }
    return this.http.delete<ITag[]>(`user/campaigntag/${queryString}`);
  }

  public updateRemoveUserTags(userTagIds?: string[], userId?: string) {
    return this.http.put<boolean>(`user/tags?userId=${userId}`, userTagIds);
  }

  public addUserTags(tagIds: string[]) {
    return this.http.post<boolean>(`user/tags`, tagIds);
  }

  public searchAllUsers(search: string) {
    return this.http.get<IUserClaimSearch[]>(`user/search/allusers?query=${search}`);
  }

  public searchProductOrgUsersExclExisting(search: string, productOrgId: string) {
    return this.http.get<IUserClaimSearch[]>(`user/search/${productOrgId}/productorgusers?query=${search}`);
  }

  public searchInstanceOrgUsersExclExisting(search: string, instanceId: string) {
    return this.http.get<IUserClaimSearch[]>(`user/search/${instanceId}/instanceorgusers?query=${search}`);
  }

  public searchRowInstanceUsersExclExisting(search: string, instanceId: string, rowId: string) {
    return this.http.get<IUserClaimSearch[]>(`user/search/${instanceId}/rowinstanceusers?rowid=${rowId}&query=${search}`);
  }

  public searchOrgUsersExclExisting(search: string, orgId: string) {
    return this.http.get<IUserClaimSearch[]>(`user/search/${orgId}/orgusers?query=${search}`);
  }

  public addAllPeopleToAssignment(instanceId: string, rowId: string) {
    return this.http.get<boolean>(`user/table/people/${instanceId}/${rowId}`);
  }

  public addProductOrganizationUsers(productOrgId: string, productOrgUsers: IUserClaimSearch[]) {
    return this.http.post<IProductTableUser[]>(`user/addproductorgusers/${productOrgId}`, productOrgUsers);
  }

  public addInstanceUsers(instanceId: string, instanceOrgUsers: IUserClaimSearch[]) {
    return this.http.post<IPeople[]>(`user/addinstanceusers/${instanceId}`, instanceOrgUsers);
  }

  public addOrganizationUsers(orgId: string, orgUsers: IUserClaimSearch[]) {
    return this.http.post<IOrganizationTableUser[]>(`user/addorgusers/${orgId}`, orgUsers);
  }

  public addRowInstanceUsers(rowId: string, rowOrgUsers: IUserClaimSearch[]) {
    return this.http.post<IPeople[]>(`user/addrowinstanceusers/${rowId}`, rowOrgUsers);
  }

  public getProductTableUsersByProdOrgId(
    productOrgId: string,
    currentAmount: number,
    getAmount: number,
    sortField: string | null = null,
    sortDirection: string | null = null,
    searchString: string | null = ''
  ) {
    return this.http.get<IProductTableUser[]>(
      `user/table/productusers/${productOrgId}?currentAmount=${currentAmount}&getAmount=${getAmount}&sortField=${sortField}&sortDirection=${sortDirection}&searchString=${searchString}`
    );
  }

  public getProductTableUsersByProdOrgIdCount(productOrgId: string) {
    return this.http.get<number>(`user/table/productUserCount/${productOrgId}`);
  }

  public getOrganizationTableUsersByOrgIdCount(orgId: string) {
    return this.http.get<number>(`user/table/orgUserCount/${orgId}`);
  }

  public getOrganizationTableUsersByOrgId(orgId: string, currentAmount = 0, getAmount = 0, sortField: string | null = null, sortDirection: string | null = null, searchString: string | null = '') {
    return this.http.get<IOrganizationTableUser[]>(
      `user/table/orgusers/${orgId}?currentAmount=${currentAmount}&getAmount=${getAmount}&sortField=${sortField}&sortDirection=${sortDirection}&searchString=${searchString}`
    );
  }

  public getOrganizationEducators(orgId: string, currentAmount = 0, getAmount = 0) {
    return this.http.get<IOrganizationTableUser[]>(`user/table/orgeducators/${orgId}?currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public updateOrganizationTableUsers(orgId: string, organizationTableUsers: OrganizationTableUserLite[]) {
    return this.http.put<boolean>(`user/table/orgusers/${orgId}`, organizationTableUsers);
  }

  public updateProductOrganizationTableUsers(organizationTableUsers: OrganizationTableUserLite[]) {
    return this.http.put<boolean>(`user/table/productorgusers`, organizationTableUsers);
  }

  public deleteOrganizationTableUsers(orgId: string, organizationTableUsers: IOrganizationTableUser[]) {
    return this.http.post<boolean>(`user/delete/table/orgusers/${orgId}`, organizationTableUsers);
  }

  public deleteProductOrganizationTableUsers(organizationTableUsers: IProductTableUser[]) {
    return this.http.post<boolean>(`user/delete/table/productorgusers`, organizationTableUsers);
  }

  public getResultsTableByInstanceId(instanceId: string, currentAmount: number, getAmount: number) {
    return this.http.get<IResults[]>(`user/table/results/${instanceId}?currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public getResultsTableByRowId(rowId: string, instanceId: string, orgId: string, currentAmount: number, getAmount: number) {
    return this.http.get<IResults[]>(`user/table/row/results/${rowId}/${instanceId}/${orgId}?currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public getPeopleTableByInstanceId(instanceId: string, currentAmount: number, getAmount: number, parentInstanceId: string | null = null) {
    return this.http.get<IPeople[]>(`user/table/people/${instanceId}?currentAmount=${currentAmount}&getAmount=${getAmount}${parentInstanceId !== null ? `&parentInstanceId=${parentInstanceId}` : ''}`);
  }

  public getPeopleTableAssignmentsByInstanceId(instanceId: string, userId: string) {
    return this.http.get<IInstanceResult[]>(`user/table/people/results/${instanceId}/${userId}`);
  }

  public getPeopleGradeTableAssignmentsByInstanceId(instanceId: string) {
    return this.http.get<IInstanceResult[]>(`user/table/people/grading/results/${instanceId}`);
  }

  public getPeopleTableByRowId(rowId: string, currentAmount: number, getAmount: number, orgId: string) {
    return this.http.get<IPeople[]>(`user/table/row/people/${rowId}?orgId=${orgId}&currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public updateInstanceUsers(instanceId: string, instanceUsers: IInstanceUserIn[]) {
    return this.http.put<boolean>(`user/table/instanceusers/${instanceId}`, instanceUsers);
  }

  public getUserRoles() {
    return this.http.get<IUserRole[]>(`user/roles`);
  }

  public getUserOrganizationRoles(organizationId: string) {
    return this.http.get<IUserRole[]>(`user/availableroles/${organizationId}`);
  }

  public getInstanceUserRoles(instanceId: string) {
    return this.http.get<IRole[]>(`user/instance/${instanceId}/roles`);
  }

  public getInstanceUserRolesBySlug(slug: string) {
    return this.http.get<IRole[]>(`user/instance/slug/${slug}/roles`);
  }

  public updateProductOrgUserRole(id: string, roleId: string) {
    return this.http.put<boolean>(`user/update/productorguser/role/${id}?roleId=${roleId}`, null);
  }

  public updateOrgUserRole(id: string, roleId: string) {
    return this.http.put<boolean>(`user/update/orguser/role/${id}?roleId=${roleId}`, null);
  }

  public updateProductOrganizationUserRole(productOrgId: string, orgId: string, roleId: string, userId?: string) {
    let query = `user/update/productorguser/role/${productOrgId}?organizationId=${orgId}&roleId=${roleId}`;
    if (userId) {
      query += `&userId=${userId}`;
    }
    return this.http.put<boolean>(query, null);
  }

  public organizationuser(orgId: string, roleId: string, userId: string) {
    const query = `user/update/orguser/role/${orgId}?userId=${userId}&roleId=${roleId}`;
    return this.http.put<boolean>(query, null);
  }

  public joinUser(joinCode: string) {
    return this.http.post<IJoinResult>(`user/join/${joinCode}`, null);
  }

  public getOrgUserById(orgId: string) {
    return this.http.get<IOrganizationUser>(`user/orguser/${orgId}`);
  }

  public getOrgUserRoleById(orgId: string, userId?: string) {
    return this.http.get<IRole>(`user/orguserrole/${orgId}?userId=${userId}`);
  }

  public getProductOrgUserRoleById(productOrgId: string) {
    return this.http.get<IRole>(`user/productorguserrole/${productOrgId}`);
  }

  public getNetworkProductOrgUserRoleById(productOrgId: string) {
    return this.http.get<IRole>(`user/network/productorguserrole/${productOrgId}`);
  }

  public setupUser(userId: string, userSetup: IUserSetup) {
    return this.http.post<boolean>(`user/${userId}/setupuser`, userSetup);
  }

  public join(joinCode: string) {
    return this.http.get<any>(`user/joincampaign/${joinCode}`);
  }

  public getUserOrganizationProducts(userId: string, orgId: string) {
    return this.http.get<IUserOrganizationProduct[]>(`user/organizationproducts/${userId}?organizationId=${orgId}`);
  }

  /***************************************
   Products
  ***************************************/
  public getMyProducts(takeAmount = 0) {
    return this.http.get<IProduct[]>(`product/my?takeAmount=${takeAmount}`);
  }

  public getAllProducts() {
    return this.http.get<IProduct[]>(`product/all`);
  }

  public getProductOrganizationsById(id: string, type: string, currentAmount: number, getAmount: number, searchFilter?: string) {
    return this.http.get<IProductOrganization[]>(`product/${id}/organizations/?type=${type}&currentAmount=${currentAmount}&getAmount=${getAmount}&searchFilter=${searchFilter ?? null}`);
  }

  public getNetworkProducts(id: string, type: string, currentAmount: number, getAmount: number, searchFilter?: string) {
    return this.http.get<IProductOrganization[]>(`product/network/${id}?type=${type}&currentAmount=${currentAmount}&getAmount=${getAmount}&searchFilter=${searchFilter}`);
  }

  public getProductHistoryById(orgId: string, type: string, currentAmount: number, getAmount: number, searchFilter?: string) {
    return this.http.get<IProductHistory[]>(`product/${orgId}/history/?type=${type}&currentAmount=${currentAmount}&getAmount=${getAmount}&searchFilter=${searchFilter ?? null}`);
  }

  public getProductOrganizationHistory(orgId: string, productId: string, type: string, currentAmount: number, getAmount: number) {
    return this.http.get<IProductHistory[]>(`product/${orgId}/history/${productId}?type=${type}&currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public getNetworkProductHistory(networkId: string, productId: string, currentAmount: number, getAmount: number) {
    return this.http.get<INetworkProductHistory[]>(`product/network/${networkId}/history/${productId}?currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public getNetworkProductLicenseCount(networkProductId: string) {
    return this.http.get<INetworkProductLicenseCount>(`product/network/licenseCount/${networkProductId}`);
  }

  public getProductFeaturesById(productId: string, currentAmount: number, getAmount: number) {
    return this.http.get<IProductFeature[]>(`product/features/${productId}?currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public getProductActionFeaturesById(id: string, type: string, currentAmount: number, getAmount: number) {
    return this.http.get<IProductFeature[]>(`product/features/actions/${id}?type=${type}&currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public getProductFeatureInstances(id: string, currentAmount: number, getAmount: number, searchFilter?: string) {
    return this.http.get<IProductFeatureInstance[]>(`product/feature/instances/${id}?currentAmount=${currentAmount}&getAmount=${getAmount}&searchFilter=${searchFilter ?? null}`);
  }

  public getProductOrganizationDomainsById(productOrgId: string) {
    return this.http.get<IProductOrgDomain[]>(`product/${productOrgId}/domains`);
  }

  public renewProductOrganizationSubscription(productOrganizationId: string, orgId: string, productId: string, period: string, expiryDate: string) {
    return this.http.post<boolean>(`product/${productOrganizationId}/renewSubscription/?orgId=${orgId}&productId=${productId}&period=${period}&expiryDate=${expiryDate}`, null);
  }

  public updateNetworkProductLicenseAmount(networkProductId: string, availableLicenses: number | null | undefined) {
    return this.http.put<boolean>(`product/network/productLicenseAmount?networkProductId=${networkProductId}&availableLicenses=${availableLicenses}`, null);
  }

  public renewNetworkProductSubscription(networkProductId: string, period: string, expiryDate: string, availableLicenses: number | null | undefined) {
    return this.http.post<INetworkProductHistory>(
      `product/network/renewProductSubscription?networkProductId=${networkProductId}&period=${period}&expiryDate=${expiryDate}&availableLicenses=${availableLicenses}`,
      null
    );
  }

  public updateProductOrganizationSubscription(productOrganizationId: string, period: string, expiryDate: string) {
    return this.http.post<boolean>(`product/${productOrganizationId}/updateSubscription/?period=${period}&expiryDate=${expiryDate}`, null);
  }

  public getProductFeatureRoles(featureId: string) {
    return this.http.get<IProductFeatureRole[]>(`product/feature/${featureId}/roles`);
  }

  public updateProductFeature(productFeature: IProductFeatureIn) {
    return this.http.put<IProductFeature>('product/feature/update', productFeature);
  }

  public updateProductFeatureRoles(productFeatureRoleIn: IProductFeatureRoleIn) {
    return this.http.put('product/prodfeature/roles/update', productFeatureRoleIn);
  }

  public updateProductFeatureInstance(productFeatureInstance: IProductFeatureInstanceIn) {
    return this.http.put<IProductFeatureInstance>('product/feature/instance/update', productFeatureInstance);
  }

  public updateProductOrgDomains(productOrgDomains: IProductOrgDomain[]) {
    return this.http.put<boolean>(`product/updateprodorgdomains`, productOrgDomains);
  }

  public getActions() {
    return this.http.get<IAction[]>(`product/actions`);
  }

  public getProductFeatureRolesById(productFeatId: string) {
    return this.http.get<IRole[]>(`product/${productFeatId}/roles`);
  }

  public searchFeaturesByProductId(productId: string, search: string) {
    return this.http.get<IFeatureSearch[]>(`product/searchfeature/${productId}?search=${search}`);
  }

  public getProductJoinCodeSettings(productOrganizationId: string) {
    return this.http.get<IProductJoinCodeSetting[]>(`product/organization/${productOrganizationId}/joincode/settings`);
  }

  public addProductFeatureByProductId(productId: string, featId: string) {
    return this.http.post<IProductFeature>(`product/addprodfeature/${productId}/feature/${featId}`, null);
  }

  public addProduct(productIn: IProductIn) {
    return this.http.post<IProduct>('product/dashboard/addproduct', productIn);
  }

  public setProductJoinCodeSettings(productOrganizationId: string, productJoinCodeSettings: IProductJoinCodeSetting[]) {
    return this.http.put<boolean>(`product/organization/${productOrganizationId}/joincode/settings`, productJoinCodeSettings);
  }

  public getProductClassrooms(productOrgId: string) {
    return this.http.get<IInstance[]>(`product/${productOrgId}/classrooms`);
  }

  public getProductById(id: string) {
    return this.http.get<IProduct>('product/' + id);
  }

  public searchProducts(query: string, currentAmount: number, getAmount: number) {
    return this.http.get<IProductSearch[]>(`product/search?query=${query}&currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public getProductsAvailableToOrg(organizationId: string) {
    return this.http.get<IProductSearch[]>(`product/available/org/${organizationId}`);
  }

  public getProductsAvailableToNetwork(networkId: string, currentAmount: number, getAmount: number, search?: string) {
    return this.http.get<IProductSearch[]>(`product/available/network/${networkId}?currentAmount=${currentAmount}&getAmount=${getAmount}&search=${search}`);
  }

  public getProductsAvailableToNetworkOrg(networkId: string, networkOrgId: string, currentAmount: number, getAmount: number, search?: string) {
    return this.http.get<IProductSearch[]>(`product/available/network/${networkId}/${networkOrgId}?currentAmount=${currentAmount}&getAmount=${getAmount}&search=${search}`);
  }

  public deleteProductFeature(productfeatureId: string) {
    return this.http.delete(`product/feature/${productfeatureId}`);
  }

  public deleteProductOrganization(productOrgId: string) {
    return this.http.delete(`product/organization/${productOrgId}`);
  }

  public deleteProductFromNetwork(id: string, productId: string) {
    return this.http.delete(`product/network/${id}/${productId}`);
  }

  public deleteNetworkOrgProduct(networkOrgId: string, networkProductId: string) {
    return this.http.delete(`product/network/org/${networkOrgId}?networkProductId=${networkProductId}`);
  }

  /***************************************
   FeatureRepositoryDashboard
  ***************************************/
  public getFeatureRepositoryDashboard(featureId: string, featureType: string, filters: IDashboardGridFilters) {
    return this.http.post<DashboardGrid>(`feature/${featureId}/${featureType}/repositorydashboard`, filters);
  }

  public getMediaDashboard(search: string, pageNo: number, pageSize: number) {
    return this.http.get<IPagedResult<IAssetRepo[]>>(`asset/dashboard?search=${search}&pageNo=${pageNo}&pageSize=${pageSize}`);
  }

  /***************************************
   Assessments
  ***************************************/
  public getQuestion(questionId: string) {
    return this.http.get<IQuestion>(`assessment/question/${questionId}`);
  }

  public getQuestions(questionIds: string[], isBuilder = false, instanceId = '') {
    return this.http.post<IQuestion[]>(`assessment/questions?isBuilder=${isBuilder}&instanceId=${instanceId !== '' ? instanceId : null}`, questionIds);
  }

  public searchQuestions(query: string) {
    return this.http.get<IQuestion[]>(`assessment/questions?query=${query}`);
  }

  public getQuestionTypes() {
    return this.http.get<IQuestionType[]>(`assessment/question/types`);
  }

  public submitAssessment(instanceId: string) {
    return this.http.post<boolean>(`assessment/${instanceId}/submit`, null);
  }

  public submitQuestion(questionId: string, instanceId: string, isDynamic: boolean) {
    return this.http.post<boolean>(`assessment/${instanceId}/question/${questionId}/submit?isDynamic=${isDynamic}`, null);
  }

  public unSubmitAssessment(instanceId: string) {
    return this.http.post<boolean>(`assessment/${instanceId}/unsubmit`, null);
  }

  public unSubmitQuestion(questionId: string, instanceId: string) {
    return this.http.post<boolean>(`assessment/${instanceId}/question/${questionId}/unsubmit`, null);
  }

  // public updateAssessmentQuestion(assessmentId: string, assessmentQuestion: IAssessmentQuestionIn) {
  //   return this.http.put<boolean>(`assessment/${assessmentId}/question`, assessmentQuestion);
  // }

  public updateQuestion(question: IQuestionIn, instanceId = '') {
    return this.http.put<boolean>(`assessment/question?instanceId=${instanceId !== '' ? instanceId : null}`, question);
  }

  public addUserAnswer(answer: IUserAnswerIn, instanceId: string, isDynamic: boolean) {
    return this.http.post<IUserAnswer>(`assessment/${instanceId}/user/answer?isDynamic=${isDynamic}`, answer);
  }

  public updateUserAnswer(answer: IUserAnswerIn, instanceId: string, isDynamic: boolean) {
    return this.http.put<IUserAnswer>(`assessment/${instanceId}/user/answer?isDynamic=${isDynamic}`, answer);
  }

  public createQuestion(question: IQuestionIn) {
    return this.http.post<string>(`assessment/question`, question);
  }

  public updateUserAssessmentGrade(assessmentGrade: IUserAssessmentFeedbackIn, instanceId: string) {
    return this.http.put<boolean>(`assessment/feedback/${instanceId}`, assessmentGrade);
  }

  public updateUserQuestionAnswerFeedback(userQuestionAnswerId: string, feedback: string) {
    return this.http.put<boolean>(`assessment/feedback/${userQuestionAnswerId}?feedback=${feedback}`, null);
  }

  public getUserRiasecScore() {
    return this.http.get<ICombinedUserRiasecFinalScore>(`assessment/riasec/score`);
  }

  public resetUserRiasecScore() {
    return this.http.put<boolean>(`assessment/reset/riasec`, null);
  }

  /***************************************
   Notifications

  ***************************************/
  public getNotifications(fetchFrom: number = 0) {
    return this.http.get<INotification[]>(`notification?from=${fetchFrom}`);
  }

  public postNotification() {
    return this.http.post(`notification`, null);
  }

  public completeNotification(notificationId: string) {
    return this.http.post(`notification/complete?id=${notificationId}`, null);
  }

  /***************************************
   Tracking

  ***************************************/
  public checkInstance(instanceId: string, userId: string | null = null, parentInstanceId: string | null = null) {
    return this.http.post<any>(`tracking/check/instance?instanceId=${instanceId}&userId=${userId}${parentInstanceId !== null ? `&parentInstanceId=${parentInstanceId}` : ''}`, null);
  }

  public checkRow(rowId: string, instanceId: string, userId: string | null, rowContentById?: IRowContentById, parentInstanceId: string | null = null) {
    return this.http.post<any>(
      `tracking/check/row?instanceId=${instanceId}&rowId=${rowId}&userId=${userId}${parentInstanceId !== null ? `&parentInstanceId=${parentInstanceId}` : ''}`,
      rowContentById
    );
  }

  public addInstanceSectionComponentEngagement(engagment?: IEngagementIn) {
    return this.http.post(`tracking/engaged/instanceSectionComponent`, engagment);
  }

  public addInstanceEngagement(engagment?: IEngagementIn) {
    return this.http.post(`tracking/engaged/instance`, engagment);
  }

  public getOrganizationInterests() {
    return this.http.get<IUserOrganizationInterest[]>(`tracking/organization/interest`);
  }

  public addOrganizationInterest(organizationId: string, nominalValue: number) {
    return this.http.post(`tracking/interest/organization?organizationId=${organizationId}&nominalValue=${nominalValue}`, null);
  }

  public addInstanceCompletion(instanceId?: string) {
    return this.http.post(`tracking/instance/${instanceId}/completion`, null);
  }

  public addInstanceSectionComponentCompletion(instanceSectionComponentId?: string, instanceId?: string, percentage?: number) {
    return this.http.post(`tracking/${instanceId}/instanceSectionComponent/${instanceSectionComponentId}/completion?percentage=${percentage}`, null);
  }

  public addInterest(tagId: string, level: number, nominalValue: number) {
    return this.http.post(`tracking/interest?tagId=${tagId}&level=${level}&nominalValue=${nominalValue}`, null);
  }

  public addInstanceInterest(instanceId: string, nominalValue: number) {
    return this.http.post(`tracking/interest?instanceId=${instanceId}&nominalValue=${nominalValue}`, null);
  }

  public getInstanceInterests() {
    return this.http.get<IUserInstanceInterest[]>('tracking/instance/interest');
  }

  public addKnowledge(tagId: string, level: number, nominalValue: number) {
    return this.http.post(`tracking/knowledge?tagId=${tagId}&level=${level}&nominalValue=${nominalValue}`, null);
  }

  public getUserInstanceTracking(instanceId: string) {
    return this.http.get<IUserInstanceTracking>(`tracking/instance/${instanceId}`);
  }

  public syncEarningCriteria() {
    return this.http.post(`tracking/sync/earningCriteria`, null);
  }

  /***************************************
   SCORM
  ***************************************/
  public rosterScormUser(data: IScormRosterIn) {
    return this.http.post<IScormUser>(`rostering/scorm`, data);
  }

  public validateScormAuth(data: IScormAuthIn) {
    return this.http.post<IScormAuth>(`rostering/scorm/auth`, data);
  }

  public generateScormFile(data: IScormFile) {
    return this.http.post(`scorm`, data, { responseType: 'blob' });
  }

  /***************************************
   COMMUNICATIONS
  ***************************************/
  public addCommunincations(comms: ICommunicationIn) {
    return this.http.post<ICommunication>(`communication`, comms);
  }

  public getCommunicationBlocks(communicationId: string | null, blockType: string | null = null) {
    return this.http.get<ICommunicationBlock[]>(`communication/block?communicationId=${communicationId}&blockType=${blockType}}`);
  }

  public updateAddCommunicationBlocks(communicationBlocksIn: ICommunicationBlock[]) {
    return this.http.put(`communication/block/updateadd`, communicationBlocksIn);
  }

  public getWorkFlows() {
    return this.http.get<[IWorkFlow]>('communication/workflows');
  }

  public getCommunications(categoryId: string) {
    return this.http.get<ICommunication[]>(`communication?categoryId=${categoryId}`);
  }

  public getUserCommunicationPreference() {
    return this.http.get<IUserCommunicationPreference>(`communication/preference`);
  }

  public updateUserCommunicationPreferences(preference: IUserCommunicationPreference) {
    return this.http.put<IUserCommunicationPreference>(`communication/preference`, preference);
  }
  /***************************************
   BADGES
  ***************************************/
  public getEarningCriteriaByInstanceId(instanceId: string) {
    return this.http.get<IEarningCriteria[]>(`badge/criteria/${instanceId}`);
  }

  public getEarningCriteriaTypes() {
    return this.http.get<IEarningCriteriaType[]>(`badge/criteria/types`);
  }

  public addCriteria(criteriaIn: IEarningCriteriaIn[]) {
    return this.http.post<boolean>(`badge/criteria`, criteriaIn);
  }

  public removeCriteria(criteriaIn: IEarningCriteriaIn[]) {
    return this.http.post<boolean>(`badge/criteria/remove`, criteriaIn);
  }

  public updateEarningCriteria(criteriaIn: IEarningCriteriaIn[]) {
    return this.http.put<boolean>(`badge/criteria`, criteriaIn);
  }

  public addRemoveEarningCriteriaContent(criteriaContentIn: IEarningCriteriaContentIn[]) {
    return this.http.put<boolean>(`badge/criteriacontent/addremove`, criteriaContentIn);
  }

  public searchEarningCriteriaContent(criteriaType: string, query: string, currentAmount: number, getAmount: number) {
    return this.http.get<IEarningCriteriaContentSearch[]>(`badge/search/completecontent?criteriaType=${criteriaType}&query=${query}&currentAmount=${currentAmount}&getAmount=${getAmount}`);
  }

  public getRowCriteria(rowId: string) {
    return this.http.get<IEarningRowCriteria>(`badge/getrowcriteria/${rowId}`);
  }

  public addRowCriteria(rowCriteria: IEarningRowCriteria) {
    return this.http.post<IEarningRowCriteria>(`badge/addrowcriteria`, rowCriteria);
  }

  public getCriteriaTags(instanceId?: string | undefined, tagId?: string | undefined, tagName?: string | null) {
    return this.http.get<ITag[]>(`badge/tags/${instanceId}?tagId=${tagId}&tagName=${tagName}`);
  }

  public updateRemoveCriteriaTags(instanceId?: string | undefined, tagIds?: string[]) {
    return this.http.put<boolean>(`badge/tags?instanceId=${instanceId}`, tagIds);
  }

  /***************************************
   Analytics
  ***************************************/
  public getAnalyticsForOrganizationOverview(objectId: string, objectType: string) {
    return this.http.get<any>(`analytics/objectorganizationdetails?objectId=${objectId}&objectType=${objectType}`);
  }

  public getAnalyticsForProductLicenses(objectId: string, objectType: string) {
    return this.http.get<any>(`analytics/objectproductlicensesdetails?objectId=${objectId}&objectType=${objectType}`);
  }

  public getAnalyticsForUserSetup(objectId: string, objectType: string) {
    return this.http.get<any>(`analytics/objectuserdetails?objectId=${objectId}&objectType=${objectType}`);
  }

  public getAnalyticsForIEPCbytype(objectId: string, objectType: string, iepcType: string, featureTypeIds: string | null = null, journeyStageId: string | null = null) {
    let query = `analytics/objectiepcbytype?objectId=${objectId}&objectType=${objectType}&iepcType=${iepcType}`;
    if (featureTypeIds) {
      query += `&featureTypeIds=${featureTypeIds}`;
    }
    if (journeyStageId) {
      query += `&journeyStageId=${journeyStageId}`;
    }
    return this.http.get<any>(query);
  }

  public getAnalyticsForMostPopular(objectId: string, objectType: string, roleType: string, type: string) {
    return this.http.get<any>(`analytics/objectmostpopular?objectId=${objectId}&objectType=${objectType}&roleType=${roleType}&type=${type}`);
  }

  public getAnalyticsUsernames(objectId: string, objectType: string, userIdList: string[]) {
    return this.http.get<any>(`analytics/objectusernames?objectId=${objectId}&objectType=${objectType}&userIdList=${userIdList}`);
  }

  public getAnalyticsObjectQlikPdf(objectId: string, objectType: string) {
    return this.http.get(`analytics/getobjectqlikpdf?objectId=${objectId}&objectType=${objectType}`, { reportProgress: true });
  }

  public getNetworkAnalytics(id: string) {
    return this.http.get<INetworkAnalytics[]>(`analytics/network/${id}/analytics`);
  }

  public getQlikSheets() {
    return this.http.get<IQlikSheet[]>(`analytics/qlik/sheets`);
  }

  public getQlikComponents(id: string) {
    return this.http.get<IQlikComponent[]>(`analytics/qlik/sheetobjects/${id}`);
  }
  /***************************************
   Campaigns
  ***************************************/

  public addCampaign(campaign: ICampaignIn) {
    return this.http.post<string>(`campaign`, campaign);
  }

  public updateCampaignTags(campaignTagIds?: string[], campaignId?: string) {
    return this.http.put<boolean>(`campaign/tags?campaignId=${campaignId}`, campaignTagIds);
  }

  public deleteCampaignUserTag(campaignTagId?: string, campaignId?: string) {
    return this.http.delete<boolean>(`campaign/tags/${campaignTagId}?campaignTagId=${campaignId}`);
  }

  public getCampaignTags(campaignId: string) {
    return this.http.get<ITag[]>(`campaign/tags/${campaignId}`);
  }

  public getTagsForCampaign(campaignId: string, tagId: string, tagName: string | null) {
    return this.http.get<ITag[]>(`campaign/tags/forcampaign/${campaignId}?tagId=${tagId}&tagName=${tagName}`);
  }

  public updateCampaignTag(campaignId: string, tagId: string) {
    return this.http.put<boolean>(`campaign/tag?campaignId=${campaignId}`, tagId);
  }

  public deleteCampaignTag(campaignId: string) {
    return this.http.delete<boolean>(`campaign/tag/${campaignId}`);
  }

  public getCampaignTag(campaignId: string) {
    return this.http.get<ITag[]>(`campaign/tag/${campaignId}`);
  }

  /***************************************
   LTI
  ***************************************/
  public getLTIAutoProvision(openidConfiguration: string, registrationToken: string) {
    return this.http.get<boolean>(`lti/register?openid_configuration=${openidConfiguration}&registration_token=${registrationToken}`);
  }

  public addLTIDeepLink(token: string, activities: ILTIActivityIn[]) {
    return this.http.post<ILTSubmit>(`lti/deeplinking?token=${token}`, activities);
  }

  public getLTILink(link: ILTLinkIn) {
    return this.http.post<ILTLink>(`lti/link`, link);
  }

  public addLTILink(link: ILTLinkIn) {
    return this.http.post<ILTLink>(`lti/link/add`, link);
  }
  public getLTIToken(id: string) {
    return this.http.get<ILTIToken>(`lti/token/${id}`);
  }

  /***************************************
   Credential Engine
  ***************************************/
  public upsertBadge(instanceId: string, status?: string) {
    return this.http.post<boolean>(`credentialEngine/badge/upsert?instanceId=${instanceId}&status=${status}`, null);
  }

  public removeBadge(instanceId: string) {
    return this.http.delete<boolean>(`credentialEngine/badge?instanceId=${instanceId}`);
  }

  public getUnsyncedOrNewBadges(search?: string) {
    return this.http.get<IBadgeSearch[]>(`credentialEngine/badge/unsyncedOrNewBadges?search=${search}`);
  }

  public getSyncedBadges(search?: string) {
    return this.http.get<IBadgeSearch[]>(`credentialEngine/badge/syncedBadges?search=${search}`);
  }

  public bulkUpsertBadges(selectedBadges: IBadgeSearch[]) {
    return this.http.post<string[]>(`credentialEngine/badge/bulkUpsert`, selectedBadges);
  }
}
