:host {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  align-content: stretch;
  height: calc(100vh - 260px);

  .overview-row {
    height: 100% !important;
  }

  .feature-overview {
    flex: 1;
    border-radius: 5px;
    margin: 0 16px 16px;
    background-color: #111111;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Exo 2';
    margin: 0;
    color: white;
  }

  p {
    font-family: 'Roboto';
    color: #aaa;
  }

  .save-button {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
  }

  .align-to-form {
    padding-left: 15px;
    padding-top: 15px;
  }

  .checkbox-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 10px;

    ion-checkbox {
      margin-left: 10px;
    }
  }
}