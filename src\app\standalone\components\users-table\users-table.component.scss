.parent-container {
  
  .view-options-row {
    margin-bottom: 10px;
    justify-content: flex-end;
    align-items: flex-end;

    .top-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .parent-container[_ngcontent-ble-c509]   .view-options-row[_ngcontent-ble-c509]   .top-header[_ngcontent-ble-c509] {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .end-container{
      display: flex;
      align-items: center;

      #user-count{
        margin: 0px 20px;
        font-size: 16px;
        color: #aaa;
      }

      ion-button {
        text-transform: none;
      }
    }

      ion-item {
        border-radius: 4px;
        display: flex;
        height: 36px;
      }
    }
  }

  .users-table {
    overflow-x: auto;
    height: fit-content;
    min-height: 150px;

    .table-container {
      .table-grid {
        width: 100%;
        background-color: #444444;

        .mat-mdc-header-row {
          height: 20px !important;
        }

        .mat-mdc-header-cell {
          color: white;
          background-color: rgba(34, 34, 34) !important;
          border-right: 1px solid #000;
          padding-left: 15px;
          padding-right: 15px;
          border-bottom: solid 1px #5b5b5b;
          font-size: 12px;
          font-weight: bold;
        }

        .mat-column-select {
          background-color: rgba(42, 42, 42);
          border-right: 1px solid #000;
        }

        .mat-column-id {
          background-color: rgba(42, 42, 42);
        }

        .mat-column-name {
          background-color: rgba(42, 42, 42);
          width: auto;
          max-width: 170px !important;
          white-space: inherit !important;
          border-right: #5b5b5b solid 2px !important;
          cursor: pointer;
          left: -1px !important;
        }

        .mat-column-details{
          min-width: 250px;
        }

        .mat-mdc-cell {
          color: white;
          max-width: 300px;
          font-size: 12px;
          padding-left: 15px;
          padding-right: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          border-right: #000 solid 1px;
          border-bottom: #000 solid 1px;
        }

        .details-container {
          cursor: pointer;
          .org-name {
            font-size: 16px;
            font-weight: bold;
          }

          .inner-container {
            display: flex;
            align-items: center;

            ion-icon {
              font-size: 5px;
              margin-left: 5px;
              margin-right: 5px;
            }
          }
        }
      }
    }

    .load-more {
      font-size: 16px;
      color: white;
      margin-top: 20px;
      text-align: center;
      display: flex;
      justify-content: center;
      cursor: pointer;
    }

    .first-group {
      border-right: 0px !important;
      border-bottom: none!important;
    }

    .parent-topheader-container {
      display: flex;

      .selected-container {
        font-size: 15px;
        margin-right: 10px;
      }

      .buttons-container {
        .disabled-btn {
          opacity: 0.4;
          cursor: default;
        }

        ion-label {
          cursor: pointer;
          display: inline-flex;
          margin-right: 10px;
          align-items: center;
          color: rgba(250, 167, 0) !important;
          font-size: 15px;

          ion-icon {
            margin-right: 5px;
            font-size: 15px;
          }
        }
      }
    }
  }
}
