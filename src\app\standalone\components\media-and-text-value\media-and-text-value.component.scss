.parent-container {
  padding: 25px 0px;

  .swiper-main-container {
    position: relative;

    @media (min-width: 641px) {
      .swiper-container {
        height: 100%;

        &.Left,
        &.Right {
          display: flex;
        }
      }

    }

    .swiper-container {
      @media (min-width: 641px) {

        .image-container {

          &.Top,
          &.Bottom {
            width: 100%;
          }

          &.Top {
            margin-bottom: 10px;
          }

          &.Left,
          &.Right {
            width: 50%;
            margin: auto;
          }
        }
      }

      .image-container {
        border-radius: 7px;

        img {
          aspect-ratio: 3 / 2;
          object-fit: cover;
          border-radius: 7px;
          max-height: 400px;
          display: flex;

          &.Top,
          &.Bottom {
            margin: 0 auto !important;
          }
        }
      }


      @media (min-width: 641px) {
        .text-main-container {

          &.Left,
          &.Right {
            width: 50%;
          }
        }
      }

      @media (max-width: 640px) {
        .text-main-container {
          margin: 0 !important;
          width: 100%;
        }
      }

      .text-main-container {

        &.Top,
        &.Bottom {
          width: 100%;
          display: flex;
          justify-content: center; 
          ::ng-deep .ql-editor {
            text-align: center;
          }
        }

        &.Left {
          margin-left: 30px;
        }

        &.Right {
          margin-right: 30px;
        }

        @media (min-width: 641px) {
          .text-container {

            &.Top,
            &.Bottom {
              text-align: center;
              width: 100%;
            }

            &.Left,
            &.Right {
              transform: translateY(-50%);
            }
          }
        }

        @media (max-width: 640px) {
          .text-container {

            &.Left,
            &.Right {
              margin-top: 10px;
            }
          }
        }

        .darkText {
          color: black !important;
        }

        .text-container {
          position: relative;
          top: 52%;

          .heading {
            color: #f7f6f6;
            font-family: 'Roboto';
            font-weight: 700;
            font-size: 22px;
            letter-spacing: 0.3px;
          }

          .paragraph {
            color: #aaaaaa;
            letter-spacing: 0.2px;
          }

          .button-container {
            padding-bottom: 15px;

            ion-button {
              margin: 0px;
              color: black;
              text-transform: none;
              border-radius: 3px;
              font-weight: 500;
              font-size: 18px;
              border: 2px solid #f99e00;
              background-color: #f99e00;
              letter-spacing: 0.5px;
              line-height: 1.1;
              min-height: 40px;
              font-family: "Roboto";
              font-size: 16px;
              --box-shadow: none !important;
              --padding-top: 0px;
              --padding-bottom: 0px;
            }

            @media screen and (max-width: 960px) {
              ion-button {
                --padding-top: 0px;
                --padding-bottom: 0px;
              }

              ion-button::part(native) {
                min-height: 36px;
              }
            }

            ion-button:hover {
              --background: #8d5e1f !important;
            }
          }
        }
      }
    }
  }

  ::ng-deep .ql-editor {
    padding-top: 2px;
    padding-bottom: 15px;
    padding-left: 0px;
    padding-right: 0px;
    font-size: 18px;
  }

  .nav-button {
    color: black;
    --swiper-navigation-size: 1em;
    padding: 1em;
    background-color: #abaeb5;
    border-radius: 1em;
  }

  .swiper-button-prev,
  .swiper-rtl .swiper-button-next {
    z-index: 1000;

    &.Top,
    &.Bottom {
      left: -10px
    }

    &.Left {
      left: -10px
    }

    &.Right {
      left: 50%;
    }

  }

  .swiper-button-next,
  .swiper-rtl .swiper-button-prev {
    z-index: 1000;

    &.Top,
    &.Bottom {
      right: -10px
    }

    &.Left {
      right: 50%;
    }

    &.Right {
      right: -10px
    }
  }

  @media (min-width:501px) and (max-width: 640px) {

    .swiper-button-prev,
    .swiper-rtl .swiper-button-next {
      top: 33%;
    }

    .swiper-button-next,
    .swiper-rtl .swiper-button-prev {
      top: 33%;
    }
  }

  @media (max-width: 500px) {

    .swiper-button-prev,
    .swiper-rtl .swiper-button-next {
      top: 30%;
    }

    .swiper-button-next,
    .swiper-rtl .swiper-button-prev {
      top: 30%;
    }
  }

  @media (max-width:640px) {
    .swiper-button-prev {
      left: -10px !important;
    }

    .swiper-button-next {
      right: -10px !important;
    }
  }
}
