<ion-grid>
  @if (isLoaded) {
    @for (type of communicationBlockTypes; track type) {
      <ion-row>
        <!--BANNER BLOCK-->
        @if (type === 'Banner') {
          <ion-col>
            <app-banner-block
              [communicationId]="communicationId ?? ''"
              [bannerBlock]="getCommunicationBlock(type)"
              (communicationBlockUpdated)="setCommunicationBlockUpdated($event)"></app-banner-block>
          </ion-col>
        }
        <!--BELL ALERT BLOCK-->
        @if (type === 'Bell') {
          <ion-col>
            <app-bell-alert-block [communicationId]="communicationId ?? ''" [bellBlock]="getCommunicationBlock(type)" (communicationBlockUpdated)="setCommunicationBlockUpdated($event)">
            </app-bell-alert-block>
          </ion-col>
        }
        <!--EMAIL BLOCK-->
        @if (type === 'Email') {
          <ion-col>
            <app-email-block [communicationId]="communicationId ?? ''" [emailBlock]="getCommunicationBlock(type)" (communicationBlockUpdated)="setCommunicationBlockUpdated($event)"> </app-email-block>
          </ion-col>
        }
        <!--PUSH BLOCK-->
        @if (type === 'Push') {
          <ion-col>
            <app-push-block [communicationId]="communicationId ?? ''" [pushBlock]="getCommunicationBlock(type)" (communicationBlockUpdated)="setCommunicationBlockUpdated($event)"></app-push-block>
          </ion-col>
        }
        <!--SMS BLOCK-->
        @if (type === 'SMS') {
          <ion-col>
            <app-sms-block [communicationId]="communicationId ?? ''" [smsBlock]="getCommunicationBlock(type)" (communicationBlockUpdated)="setCommunicationBlockUpdated($event)"></app-sms-block>
          </ion-col>
        }
      </ion-row>
    }
  }
</ion-grid>
