import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { IComponent, IComponentIn, IFeatureTab, IInstance, ISection, ITemplate } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { AlertService } from '@app/core/services/alert-service';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { PopoverController } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { OverlayEventDetail } from '@ionic/core';
import { OptionSelectorDialogComponent } from '@app/standalone/modals/option-selector-dialog/option-selector-dialog.component';

@Component({
    selector: 'app-feature-repository-builder-template',
    templateUrl: './template.component.html',
    styleUrls: ['./template.component.scss'],
    standalone: false
})
export class TemplateComponent implements OnInit, OnDestroy {
  @Input() templateId: string;
  @Input() instance: IInstance;
  @Input() featureTab: IFeatureTab;
  @Output() sectionChanged = new EventEmitter<any>();
  componentDestroyed$: Subject<boolean> = new Subject();
  template: ITemplate;
  comp: IComponent[];
  isActiveId: string;
  isDefaultInstanceTab: boolean;
  templateForm: UntypedFormGroup;
  sectionTypes: KeyValue[];

  constructor(
    private dataService: DataService,
    private alertService: AlertService,
    private builderService: BuilderService,
    private popOver: PopoverController
  ) {}

  ngOnInit(): void {
    this.dataService
      .getTemplate(this.templateId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.template = data;
        this.createTemplateFormGroup(this.template);
      });

    this.isDefaultInstanceTab = this.featureTab.isDefaultInstanceTab;
    this.getSectionTypes();

    this.builderService.sectionUpdated$.pipe(takeUntil(this.componentDestroyed$)).subscribe(section => {
      const index = this.template.sections.findIndex(x => x.id === section.id);
      if (index !== -1) {
        this.template.sections[index] = section;
        this.createTemplateFormGroup(this.template);
      }
    });
  }

  getSectionTypes() {
    this.dataService
      .getSectionTypes()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(res => {
        if (res) {
          const types = Object.entries(res).map(([id, value]) => {
            return { id, value } as KeyValue;
          });
          this.sectionTypes = types;
        }
      });
  }

  async openSelectSectionType() {
    if (this.sectionTypes) {
      const popover = await this.popOver.create({
        component: OptionSelectorDialogComponent,
        cssClass: 'question-type-popover',
        componentProps: {
          options: this.sectionTypes,
        },
        event: undefined,
        side: 'bottom',
      });

      popover.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
        if (overlayEventDetail.data) {
          this.addSection(overlayEventDetail.data.id);
        }
      });

      await popover.present();
    }
  }

  addSection(typeId: string) {
    this.dataService
      .createSection(this.templateId, typeId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(newSection => {
        if (newSection != null) {
          const formGroup = new UntypedFormGroup({
            title: new UntypedFormControl(),
            description: new UntypedFormControl(),
            hideBackground: new UntypedFormControl(),
            sortOrder: new UntypedFormControl(),
            typeId: new UntypedFormControl(),
          });
          formGroup.get('title')?.disable();
          formGroup.get('description')?.disable();
          formGroup.get('hideBackground')?.disable();
          formGroup.get('typeId')?.disable();
          this.templateForm.addControl(newSection.id, formGroup);
          this.template.sections.push(newSection);
          this.builderService.selectedSection$.next(newSection);
        }
      });
  }

  removeSection(sectionId: string) {
    const sectionIndex = this.template.sections.findIndex(x => x.id === sectionId);
    this.dataService
      .deleteSection(sectionId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(isDeleted => {
        if (isDeleted) {
          this.template.sections.splice(sectionIndex, 1);
          this.builderService.selectedSection$.next(null);
        }
      });
  }

  addComponent(event: any) {
    const index = this.template.sections.indexOf(event.container.data);
    const newComponent: IComponentIn = {
      sectionId: event.container.data.id,
      componentTypeId: event.item.data.id,
      componentTypeBw: event.item.data.typebw,
      templateField: null,
      builderRowNumber: null,
      isLocked: null,
      parentComponentId: null,
    };

    this.dataService
      .createComponent(newComponent)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(returnComponent => {
        if (returnComponent != null) {
          this.template.sections[index].components?.push(returnComponent);
        }
      });
  }

  deleteComponent(section: ISection, component: IComponent) {
    this.dataService
      .deleteComponent(component.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(isDeleted => {
        if (isDeleted) {
          const sectionIndex = this.template.sections.indexOf(section);
          const componentIndex = this.template.sections[sectionIndex].components?.indexOf(component) as number;
          this.template.sections[sectionIndex].components?.splice(componentIndex, 1);
        }
      });
  }

  deleteSectionConfirmation(sectionId: string) {
    this.alertService.presentAlert('Confirm Delete', 'Are you sure you want to delete this section').then(() => {
      this.removeSection(sectionId);
    });
  }

  createTemplateFormGroup(template: ITemplate) {
    // The form has nested forms which present the sections on the template
    const parentGroup: any = {};
    template.sections.forEach(section => {
      const group: any = {};
      group['title'] = new UntypedFormControl(section?.title);
      group['title'].disable();
      group['description'] = new UntypedFormControl(section?.description);
      group['description'].disable();
      group['hideBackground'] = new UntypedFormControl(section?.hideBackground);
      group['hideBackground'].disable();
      group['sortOrder'] = new UntypedFormControl(section?.sortOrder);
      group['typeId'] = new UntypedFormControl(section?.typeId);
      group['typeId'].disable();

      section.components
        ?.filter(x => x.componentType.name !== 'Assessment Block')
        .forEach(component => {
          group[component.id] = new UntypedFormControl({
            value: '',
            disabled: true,
          });
        });

      parentGroup[section.id] = new UntypedFormGroup(group);
    });

    this.templateForm = new UntypedFormGroup(parentGroup);
    this.builderService.templateFormCreated$.next(this.templateForm);
  }

  sectionUpdated() {
    this.sectionChanged.emit(null);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
