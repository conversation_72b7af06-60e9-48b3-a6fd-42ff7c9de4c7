<mat-table #table [dataSource]="dataSource" matSort #sort="matSort" multiTemplateDataRows class="custom-table">
  <ng-container matColumnDef="icon">
    <mat-cell *matCellDef="let element">
      <img class="icon" [src]="getCoverUrl(element?.instance?.coverMediaAssetId)" onerror="this.src='assets/images/no-image.png'" alt="icon" />
    </mat-cell>
  </ng-container>
  <ng-container matColumnDef="name">
    <mat-cell *matCellDef="let element">
      <div class="name-column">
        <span class="white-name">{{ element?.instance?.title }}</span>
        <span class="subheading">{{ element?.instance?.feature?.featureType?.name }}</span>
      </div>
    </mat-cell>
  </ng-container>
  <ng-container matColumnDef="grade-status">
    <mat-cell *matCellDef="let element">
      @if (element.isGraded === false && element.status === 'Completed' && element.containsGrading === true) {
        <div class="status-badge-container">
          <div class="status-badge grading-required">
            <span>GRADING REQUIRED</span>
          </div>
        </div>
      } @else if (element.isGraded === true && element.status === 'Completed' && element.containsGrading === true) {
        <div class="status-badge-container">
          <div class="status-badge completed">
            <span>GRADING COMPLETE</span>
          </div>
        </div>
      }
    </mat-cell>
  </ng-container>
  <ng-container matColumnDef="progress">
    <mat-cell *matCellDef="let element">
      <div class="status-badge-container">
        <div
          class="status-badge"
          [ngClass]="{
            'in-progress': element.status === 'InProgress',
            completed: element.status === 'Completed',
            'not-started': element.status !== 'Completed' && element.status !== 'InProgress' && element.isGraded !== false,
          }">
          <span>{{ getStatusText(element.status) }}</span>
        </div>
      </div>
    </mat-cell>
  </ng-container>
  <ng-container matColumnDef="grade">
    <mat-header-cell *matHeaderCellDef mat-sort-header>AVERAGE GRADE</mat-header-cell>
    <mat-cell *matCellDef="let element">
      <div class="row-space-between">
        <span>{{ (element.status !== 'Completed' && element.status !== 'InProgress') || !element.containsGrading ? ' - ' : element.grade + '%' }}</span>
        @if (element.containsGrading) {
          <ion-button [class.grade-button]="element.isGraded === false && element.status === 'Completed'" (click)="navigateToInstance(element?.instance?.id)">{{
            element.isGraded === false && element.status === 'Completed' ? 'Grade' : 'View'
          }}</ion-button>
        } @else {
          <ion-button color="light" [disabled]="true">{{ 'No Grading Items' }}</ion-button>
        }
      </div>
    </mat-cell>
  </ng-container>

  <mat-row
    *matRowDef="let element; columns: displayedColumns"
    class="example-element-row"
    [ngClass]="{
      'in-progress-row': element.status === 'InProgress',
      'completed-row': element.status === 'Completed',
      'grading-required-row': element.isGraded === false && element.status === 'Completed',
    }"></mat-row>
</mat-table>
