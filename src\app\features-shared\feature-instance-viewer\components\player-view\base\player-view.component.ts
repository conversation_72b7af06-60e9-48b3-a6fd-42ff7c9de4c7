import { AfterViewInit, Directive, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, Renderer2 } from '@angular/core';
import { IFeatureTab, IInstance, IInstanceTemplate, IRole, IRouteParams } from '@app/core/contracts/contract';
import { FinishedInstanceDetails } from '@app/core/dtos/FinishedInstanceDetails';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { ViewType } from '@app/core/enums/view-type';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RolesService } from '@app/core/services/roles.service';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { map, Observable, of, Subject, takeUntil } from 'rxjs';
import { LayoutService } from '@app/core/services/layout-service';
import { DataService } from '@app/core/services/data-service';
import { AuthService } from '@app/core/services/auth-service';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { BannerService } from '@app/core/services/banner.service';

@Directive()
export class PlayerViewBaseComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() template: IInstanceTemplate;
  @Input() instance: IInstance;
  @Input() featureTab: IFeatureTab;
  @Input() routeParams: IRouteParams;
  @Input() searchFilter: string;
  @Input() selectedUserId: string;
  @Output() rowFiltered = new EventEmitter<string>();
  @Output() buttonChanged = new EventEmitter<number>();
  @Output() optionSelected = new EventEmitter<number>();
  viewTypes = ViewType;
  openMobileMenu = false;
  instanceUserRoles: IRole[];
  componentDestroyed$: Subject<boolean> = new Subject();
  status: string;
  containsGrading: boolean;
  isGraded: boolean;
  isEducator: boolean = false;
  toastSubscription: any;
  actionBw: ActionTypes | undefined;
  searchBarAvailable = false;
  isEducator$: Observable<boolean>;
  parentClassExists = false;
  showGradingView = false;

  constructor(
    private instanceService: InstanceService,
    private eventService: Events,
    private globalToastService: GlobalToastService,
    private renderer: Renderer2,
    private el: ElementRef,
    public layoutService: LayoutService,
    private dataService: DataService,
    private authService: AuthService,
    private breadcrumbService: BreadcrumbService,
    private rolesService: RolesService,
    private bannerService: BannerService
  ) {}

  ngOnInit() {
    this.showGradingView = sessionStorage.getItem('showGradingView') === 'true';
    this.parentClassExists = this.breadcrumbService.featureTypeExists('Accredited Learning Container Pages');

    this.checkUserIsEducator();

    this.bannerService.bannerVisible$.subscribe((state: { visible: any }) => {
      let infoContainer: any | undefined;

      if (this.layoutService.currentScreenSize === 'xs' && !this.layoutService.isTablet) {
        infoContainer = this.el.nativeElement.querySelector('.player-outer-container');
      } else {
        infoContainer = this.el.nativeElement.querySelector('.player-view-info-container');
      }

      if (infoContainer) {
        if (state.visible) {
          this.renderer.addClass(infoContainer, 'toast-visible');
        } else {
          this.renderer.removeClass(infoContainer, 'toast-visible');
        }
      }
    });
  }

  ngAfterViewInit() {
    this.eventService.subscribe('section', id => {
      this.scrollToElement(id);
    });
  }

  selectedChanged(event: any, actionBw?: ActionTypes) {
    this.actionBw = actionBw;
  }

  scrollToElement(id: string) {
    const scrollTop = document.getElementById(id)?.offsetTop;
    const visualRow = document.getElementById('visualRow');

    const myDiv = document.getElementById('scrollContent');
    if (myDiv && scrollTop && visualRow) {
      myDiv.scrollTo(0, scrollTop + visualRow.offsetHeight + visualRow.offsetTop);
    }
  }

  buttonClicked(option: any) {
    this.buttonChanged.emit(option);
  }

  setSelectedViewType(option: any) {
    this.optionSelected.emit(option);
  }

  setSearchBarAvailable(event: any) {
    this.searchBarAvailable = event;
  }

  setFinishedInstance(event: FinishedInstanceDetails) {
    if (event.shouldNavigate === true) {
      this.optionSelected.emit(1);
    }

    this.status = event.status;
    this.containsGrading = event.containsGrading;
    this.isGraded = event.isGraded;
  }

  rowFilteredChange(filter: string) {
    this.searchFilter = filter;
    this.rowFiltered.emit(filter);
  }

  setSelectedUserId(userId: any) {
    this.selectedUserId = userId;
  }

  checkUserIsEducator() {
    // This gets checked on the parent instance, because the educator on the parent instance can edit the child instance grades.
    this.isEducator$ = this.dataService.getUserIsEducator(this.instance.id).pipe(
      takeUntil(this.componentDestroyed$),
      map(x => {
        this.isEducator = this.instance.isOwner === true || x === true;
        return true;
      })
    );
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
    this.eventService.unsubscribe('section');
  }
}
