<div class="grading-container">
  <ion-grid>
    <ion-row>
      <ion-col size="3" class="item">
        <ion-label position="stacked">Results for</ion-label>
        <ng-container>
          @if (users$ | async; as users) {
            <ion-select style="--background-color:{{ backgroundColor }};" interface="popover" [(ngModel)]="selectedUserId">
              @for (user of users; track user) {
                <ion-select-option [value]="user.userId">
                  {{ user.name }}
                </ion-select-option>
              }
            </ion-select>
          }
        </ng-container>
      </ion-col>
    </ion-row>
  </ion-grid>
</div>
