import { Component, Input, OnInit, Output, EventEmitter, SimpleChanges, OnChanges, OnDestroy } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-media-block-editor',
    templateUrl: './media-block-editor.component.html',
    styleUrls: ['./media-block-editor.component.scss'],
    standalone: false
})
export class MediaBlockEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  mediaBlockForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}
  get isInheritControl(): AbstractControl {
    return this.mediaBlockForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  setTypeBwValues(typeBw: number | undefined) {
    if (typeBw !== undefined) {
      return this.builderService.acceptedVideoFormats.filter(x => Number(x.id) & typeBw).map(x => x.id);
    }
    return null;
  }

  createForm() {
    this.mediaBlockForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      placeholder: [this.component?.templateField?.placeHolderText, Validators.required],
      helpTitle: [this.component?.templateField?.helpTitle],
      helpDescription: [this.component?.templateField?.helpDescription],
      rowNumber: [this.component?.builderRowNumber],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      isInherit: [this.component?.templateField?.isInherit ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      isSrcDevice: [this.component?.templateField?.isSrcDevice ?? false],
      isSrcRepository: [this.component?.templateField?.isSrcRepository ?? false],
      isSrcEmbedCode: [this.component?.templateField?.isSrcEmbedCode ?? false],
      fileTypeBw: [this.setTypeBwValues(this.component?.templateField?.fileTypeBw), Validators.required],
      minFileSize: [this.component?.templateField?.minFileSize],
      maxFileSize: [this.component?.templateField?.maxFileSize],
      upgradeMessage: [this.component?.templateField?.upgradeMessage],
      systemProperty: [this.component?.templateField?.systemProperty?.id],
      isBlockRequired: [this.component?.templateField?.isBlockRequired],
      percentageToComplete: [this.component?.templateField?.percentageToComplete],
      isAuthorRequired: [this.component?.templateField?.isAuthorRequired],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.mediaBlockForm) {
      return;
    }

    this.mediaBlockForm.controls.label.setValue(this.component.templateField.label);
    this.mediaBlockForm.controls.placeholder.setValue(this.component.templateField.placeHolderText);
    this.mediaBlockForm.controls.helpDescription.setValue(this.component.templateField.helpDescription);
    this.mediaBlockForm.controls.helpTitle.setValue(this.component.templateField.helpTitle);
    this.mediaBlockForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.mediaBlockForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.mediaBlockForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.mediaBlockForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.mediaBlockForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.mediaBlockForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.mediaBlockForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.mediaBlockForm.controls.isInherit.setValue(this.component.templateField.isInherit);
    this.mediaBlockForm.controls.systemProperty.setValue(this.component.templateField.systemProperty);
    this.mediaBlockForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.mediaBlockForm.controls.isSrcDevice.setValue(this.component.templateField.isSrcDevice);
    this.mediaBlockForm.controls.isSrcRepository.setValue(this.component.templateField.isSrcRepository);
    this.mediaBlockForm.controls.isSrcEmbedCode.setValue(this.component.templateField.isSrcEmbedCode);
    this.mediaBlockForm.controls.fileTypeBw.setValue(this.component.templateField.fileTypeBw);
    this.mediaBlockForm.controls.minFileSize.setValue(this.component.templateField.minFileSize);
    this.mediaBlockForm.controls.maxFileSize.setValue(this.component.templateField.maxFileSize);
    this.mediaBlockForm.controls.upgradeMessage.setValue(this.component.templateField.upgradeMessage);
    this.mediaBlockForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.mediaBlockForm.controls.isBlockRequired.setValue(this.component.templateField.isBlockRequired);
    this.mediaBlockForm.controls.isAuthorRequired.setValue(this.component.templateField.isAuthorRequired);
    this.mediaBlockForm.controls.percentageToComplete.setValue(this.component.templateField.percentageToComplete);
    this.mediaBlockForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.mediaBlockForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.mediaBlockForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.mediaBlockForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.mediaBlockForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.mediaBlockForm.valid) {
      this.component.templateField.label = this.mediaBlockForm.controls.label.value;
      this.component.templateField.placeHolderText = this.mediaBlockForm.controls.placeholder.value;
      this.component.templateField.helpTitle = this.mediaBlockForm.controls.helpTitle.value;
      this.component.templateField.helpDescription = this.mediaBlockForm.controls.helpDescription.value;
      this.component.builderRowNumber = this.mediaBlockForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.mediaBlockForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.mediaBlockForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.mediaBlockForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.mediaBlockForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.mediaBlockForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.mediaBlockForm.controls.isHoverField.value;
      this.component.templateField.isInherit = this.mediaBlockForm.controls.isInherit.value;
      this.component.templateField.isVisibleRepository = this.mediaBlockForm.controls.isVisibleRepository.value;
      this.component.templateField.isViewField = this.mediaBlockForm.controls.isViewField.value;
      this.component.templateField.isSrcDevice = this.mediaBlockForm.controls.isSrcDevice.value;
      this.component.templateField.isSrcRepository = this.mediaBlockForm.controls.isSrcRepository.value;
      this.component.templateField.isSrcEmbedCode = this.mediaBlockForm.controls.isSrcEmbedCode.value;
      this.component.templateField.fileTypeBw = this.mediaBlockForm.controls.fileTypeBw.value.reduce((a: number, b: number) => a + b, 0);
      this.component.templateField.minFileSize = this.mediaBlockForm.controls.minFileSize.value;
      this.component.templateField.maxFileSize = this.mediaBlockForm.controls.maxFileSize.value;
      this.component.templateField.upgradeMessage = this.mediaBlockForm.controls.upgradeMessage.value;
      this.component.templateField.isBlockRequired = this.mediaBlockForm.controls.isBlockRequired.value;
      this.component.templateField.isAuthorRequired = this.mediaBlockForm.controls.isAuthorRequired.value;
      this.component.templateField.percentageToComplete = this.mediaBlockForm.controls.percentageToComplete.value;
      this.component.templateField.useMaxWidth = this.mediaBlockForm.controls.useMaxWidth.value;
      if (this.mediaBlockForm.controls.isBlockRequired.value === true) {
        this.component.templateField.percentageToComplete = this.mediaBlockForm.controls.percentageToComplete.value;
      }
      this.component.templateField.colspan = this.mediaBlockForm.controls.colspan.value;
      this.component.templateField.colNumber = this.mediaBlockForm.controls.colNumber.value;
    }
  }

  checkPercentageInputLength(event: any) {
    const numberVal = Number(event.detail.value);
    if (numberVal >= 100) {
      this.mediaBlockForm.controls.percentageToComplete.setValue(100);
    } else if (numberVal < 0) {
      this.mediaBlockForm.controls.percentageToComplete.setValue(0);
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
