import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IProductRenew } from '@app/core/contracts/contract';
import { format } from 'date-fns';
import { IonicModule } from '@ionic/angular';
import { DatePipe } from '@angular/common';

@Component({
    selector: 'app-renew-product',
    templateUrl: './renew-product.component.html',
    styleUrls: ['./renew-product.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, DatePipe]
})
export class RenewProductComponent implements OnInit {
  @Input() currentSubscriptionExpires: any;
  @Input() title = 'Renew Product';
  @Input() identifier = 'date';
  @Input() featureType: string;
  @Input() newProduct: boolean;
  @Input() networkOrgCount: number;
  @Output() productRenew = new EventEmitter();
  productRenewForm: UntypedFormGroup;
  period: UntypedFormControl;
  startDate: UntypedFormControl;
  customValues: UntypedFormControl;
  availableLicenses: UntypedFormControl;
  unlimitedLicenses: UntypedFormControl;
  minDate: string;

  subscriptionTerms: any = ['Year', 'Month'];

  constructor() {}

  ngOnInit(): void {
    this.setMinDate();
    this.createFormControls();
    this.createForm();
    this.productRenewForm.valueChanges.subscribe(() => {
      if (this.productRenewForm.valid) {
        this.renewProduct();
      } else {
        this.productRenew.emit(null);
      }
    });
  }

  setMinDate(): void {
    const minDate = new Date(this.currentSubscriptionExpires);
    minDate.setDate(minDate.getDate() + 1); // Add one day
    this.minDate = minDate.toISOString();
  }

  public renewProduct() {
    const period = this.productRenewForm.controls.period.value;
    const startDate = this.startDate.value ?? this.currentSubscriptionExpires;
    const expiryDate = this.calculateExpiryDate(period, startDate);
    if (expiryDate === null) return;
    const productRenew: IProductRenew = {
      period: period,
      expiryDate: expiryDate,
    };

    if (this.featureType === 'Network Manager') {
      productRenew.unlimitedLicenses = this.productRenewForm.controls.unlimitedLicenses.value;
      if (productRenew.unlimitedLicenses) {
        productRenew.availableLicenses = this.newProduct || this.networkOrgCount == 0 ? 1 : this.networkOrgCount;
      } else {
        productRenew.availableLicenses = this.productRenewForm.controls.availableLicenses.value;
      }
    }
    this.productRenew.emit(productRenew);
  }

  public customTermClicked(event: any) {
    const isChecked = event.detail.checked;
    if (isChecked) {
      this.period.setValue('');
    } else {
      this.period.setValue('Year');
    }
  }

  public customUnlimitedLicenses() {
    this.availableLicenses.setValue(this.newProduct || this.networkOrgCount == 0 ? 1 : this.networkOrgCount);
  }

  private createFormControls() {
    this.period = new UntypedFormControl('Year', [Validators.required, Validators.pattern('^\\d+\\s(months|years|Months|Years)$|^(Month|Year)$')]);
    this.startDate = new UntypedFormControl(this.minDate, Validators.required);
    this.customValues = new UntypedFormControl(false);

    if (this.featureType === 'Network Manager') {
      this.availableLicenses = new UntypedFormControl(this.newProduct || this.networkOrgCount == 0 ? 1 : this.networkOrgCount, [
        Validators.required,
        Validators.min(this.newProduct || this.networkOrgCount == 0 ? 1 : this.networkOrgCount),
      ]);
      this.unlimitedLicenses = new UntypedFormControl(false);
    }
  }

  private createForm() {
    this.productRenewForm = new UntypedFormGroup({
      ['period']: this.period,
    });
    this.productRenewForm.addControl('startDate', this.startDate, { emitEvent: false });
    this.productRenewForm.addControl('customValues', this.customValues, { emitEvent: false });

    if (this.featureType === 'Network Manager') {
      this.productRenewForm.addControl('availableLicenses', this.availableLicenses, { emitEvent: false });
      this.productRenewForm.addControl('unlimitedLicenses', this.unlimitedLicenses, { emitEvent: false });
    }
    this.renewProduct();
  }

  private calculateExpiryDate(subscriptionTerm: string, startDate: Date): string | null {
    const subscriptionTermValues = subscriptionTerm.split(' ', 2);
    const date = new Date(startDate);

    if (subscriptionTermValues.length === 1) {
      if (subscriptionTerm === 'Month') {
        return format(new Date(date.setMonth(new Date(date).getMonth() + 1)), 'MM/dd/yyyy');
      } else if (subscriptionTerm === 'Year') {
        return format(new Date(date.setMonth(date.getMonth() + 12)), 'MM/dd/yyyy');
      }
    } else {
      const amountOfPeriod: number = +subscriptionTermValues[0];
      const period = subscriptionTermValues[1].toLocaleLowerCase();

      if (period === 'Months' || period === 'months') {
        return format(new Date(date.setMonth(new Date(date).getMonth() + amountOfPeriod)), 'MM/dd/yyyy');
      } else if (period === 'Years' || period === 'years') {
        return format(new Date(date.setMonth(date.getMonth() + 12 * amountOfPeriod)), 'MM/dd/yyyy');
      }
    }
    return null;
  }
}
