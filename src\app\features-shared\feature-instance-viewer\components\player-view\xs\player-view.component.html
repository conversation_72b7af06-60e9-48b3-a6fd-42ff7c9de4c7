@if (isEducator$ | async; as showPlayer) {
  @if (this.layoutService.isTablet) {
    <div class="player-row-flex" [ngClass]="{ searchBar: searchBarAvailable }">
      <div class="player-width">
        <div class="player-inner-container">
          <div class="top-border" slot="fixed">
            <app-view-options-row
              [template]="template"
              [canAdd]="instance?.title === 'Admin'"
              [selectedItem]="routeParams?.viewType ?? 0"
              [featureTab]="featureTab"
              [isEducator]="isEducator"
              [instance]="instance"
              [selectedUserId]="selectedUserId"
              (rowFiltered)="rowFilteredChange($event)"
              (buttonChanged)="buttonClicked($event)"
              (optionSelected)="setSelectedViewType($event)"
              (searchBarAvailableChanged)="setSearchBarAvailable($event)"
              (userSelected)="setSelectedUserId($event)"></app-view-options-row>
          </div>
          <div
            class="content-wrapper player-view-info-container"
            [ngClass]="{ 'custom-info-height': isEducator || (featureTab?.buttonText && featureTab.buttonText !== '' && featureTab.featureTabButtons.length > 0), searchBar: searchBarAvailable }">
            <div id="scrollContent" [ngClass]="{ 'player-outer-container': routeParams.viewType === viewTypes.Player }">
              <div
                class="full-parent-height player-border"
                [ngClass]="{
                  'player-container': routeParams.viewType === viewTypes.Player && (!status || status?.length === 0 || !selectedUserId),
                  'player-container-completed': routeParams.viewType === viewTypes.Player && status === 'Completed' && selectedUserId && isGraded && containsGrading,
                  'player-container-in-progress': routeParams.viewType === viewTypes.Player && (status === 'InProgress' || status === 'NotStarted') && selectedUserId,
                  'player-container-incomplete': routeParams.viewType === viewTypes.Player && status === 'Completed' && selectedUserId && containsGrading && !isGraded,
                }">
                @if (selectedUserId) {
                  <div class="completed-header-container">
                    <ion-row>
                      <ion-col>
                        @if (status === 'Completed' && isGraded && containsGrading) {
                          <span class="inner-completed">GRADED</span>
                        }
                        @if (status === 'InProgress') {
                          <span class="inner-in-progress">STUDENT IN PROGRESS</span>
                        }
                        @if (status === 'NotStarted') {
                          <span class="inner-in-progress">STUDENT NOT STARTED</span>
                        }
                        @if (status === 'Completed' && !isGraded && containsGrading) {
                          <span class="inner-incomplete">UNGRADED</span>
                        }
                      </ion-col>
                    </ion-row>
                  </div>
                }
                <app-player-view-information
                  [routeParams]="routeParams"
                  [searchFilter]="searchFilter"
                  [selectedUserId]="selectedUserId"
                  [isEducator]="isEducator"
                  [actionBw]="actionBw"
                  [parentInstance]="instance"
                  (finishedInstance)="setFinishedInstance($event)">
                </app-player-view-information>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="player-side-panel-container">
        <app-player-view-side-panel
          [template]="template"
          [routeParams]="routeParams"
          [searchFilter]="searchFilter"
          [instance]="instance"
          [selectedUserId]="selectedUserId"
          (selectedChanged)="selectedChanged($event.event, $event.actionBw)"></app-player-view-side-panel>
      </div>
    </div>
  } @else {
    <div class="parent-container">
      <div class="top-border" slot="fixed">
        <app-view-options-row
          [template]="template"
          [canAdd]="instance?.title === 'Admin'"
          [selectedItem]="routeParams?.viewType ?? 0"
          [featureTab]="featureTab"
          [instance]="instance"
          (rowFiltered)="rowFilteredChange($event)"
          (buttonChanged)="buttonClicked($event)"
          (optionSelected)="setSelectedViewType($event)"
          (searchBarAvailableChanged)="setSearchBarAvailable($event)"
          (mobileMenuClicked)="openMobileMenu = !openMobileMenu"></app-view-options-row>
      </div>
      <mat-sidenav-container class="player-row-flex" [ngClass]="{ searchBar: searchBarAvailable }">
        <mat-sidenav #sidenav position="end" mode="side" [(opened)]="openMobileMenu">
          <div class="player-side-panel-container">
            <app-player-view-side-panel
              [template]="template"
              [routeParams]="routeParams"
              [searchFilter]="searchFilter"
              [instance]="instance"
              (selectedChanged)="selectedChanged($event.event, $event.actionBw)"></app-player-view-side-panel>
          </div>
        </mat-sidenav>
        <mat-sidenav-content class="player-width" (click)="openMobileMenu ? sidenav.toggle() : null">
          <div class="player-inner-container">
            @if (!openMobileMenu) {
              <div class="content-wrapper player-view-info-container">
                <div [class.player-outer-container]="routeParams.viewType === viewTypes.Player" [ngClass]="{ searchBar: searchBarAvailable }">
                  <div [class.player-container]="routeParams.viewType === viewTypes.Player">
                    <app-player-view-information
                      [isEducator]="isEducator"
                      [routeParams]="routeParams"
                      [searchFilter]="searchFilter"
                      [actionBw]="actionBw"
                      [parentInstance]="instance"
                      (finishedInstance)="setFinishedInstance($event)">
                    </app-player-view-information>
                  </div>
                </div>
              </div>
            }
          </div>
        </mat-sidenav-content>
      </mat-sidenav-container>
    </div>
  }
}
