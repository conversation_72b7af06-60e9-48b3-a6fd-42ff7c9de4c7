.parent-container {
  margin: 8px;
  .card-content-container {
    background-color: #2d2e32;
    .inner-container {
      display: flex;
      align-items: center;
      margin: 10px;

      ion-input {
        border: 1px solid #4e4e4e;
        border-radius: 5px;
        margin-top: 0.2em;
        background: rgb(30, 30, 30);
        color: white;
        font-size: 16px;
        --padding-start: 8px !important;
        caret-color: #7f550c;
      }

      .remove-button {
        margin-left: 10px;
      }

      .reorder-icon-container {
        margin-right: 10px;
        ion-reorder {
          display: flex;

          align-items: center;

          ion-icon {
            font-size: 20px;
            color: white;
          }
        }
      }
    }

    .icon-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 10px;

      ion-icon {
        font-size: 35px;
        color: rgb(186, 122, 19) !important;
        cursor: pointer;
      }
    }
  }
}

.side-panel-input-padding {
  margin: 0px !important;
  ion-card {
    margin: 0px !important;
  }
}
