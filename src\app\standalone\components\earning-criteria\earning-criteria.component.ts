import { AfterViewInit, Component, ElementRef, Input, OnDestroy, OnInit } from '@angular/core';
import { IEarningCriteria } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { ParseContentPipe } from '@app/shared/pipes/parse-content';

@Component({
    selector: 'app-earning-criteria',
    templateUrl: './earning-criteria.component.html',
    styleUrls: ['./earning-criteria.component.scss'],
    imports: [IonicModule, AsyncPipe, ParseContentPipe]
})
export class EarningCriteriaComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() instanceId: string | undefined;
  @Input() isCriteriaManagerView = false;

  anchors: any;
  componentDestroyed$: Subject<boolean> = new Subject();
  earningCriteria: IEarningCriteria[];
  html = '';
  constructor(
    private dataService: DataService,
    private instanceService: InstanceService,
    private elementRef: ElementRef
  ) {}

  ngOnInit() {
    this.getEarningCriteria();
  }

  ngAfterViewInit(): void {
    this.getAnchors();
  }

  handleAnchorClick = (event: any) => {
    const content = this.earningCriteria.map(x => x.earningCriteriaContent.find(y => y.name === event.target?.innerHTML));
    this.navigateToBadge(content[0]);
  };

  getEarningCriteria() {
    if (this.instanceId) {
      this.dataService
        .getEarningCriteriaByInstanceId(this.instanceId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(async (earningCriteria: IEarningCriteria[]) => {
          if (earningCriteria && earningCriteria.length > 0) {
            this.earningCriteria = earningCriteria;
            await this.parseEarningCriteria();
            this.getAnchors();
          }
        });
    }
  }

  parseEarningCriteria() {
    this.earningCriteria.forEach(criteria => {
      if (criteria.earningCriteriaContent && criteria.earningCriteriaContent.length > 0) {
        let selectText = criteria.earningCriteriaType.selectText;
        if (selectText) {
          const handlebars = this.extractHandleBar(selectText);
          handlebars?.forEach(handlebar => {
            if (handlebar) {
              switch (handlebar[1]) {
                case 'RefId':
                  let contentToComplete = '';
                  let index = 0;
                  criteria.earningCriteriaContent.forEach(content => {
                    if (content.name) {
                      if (index < 1) {
                        contentToComplete += '&nbsp;' + `<a class="badge-link" id="${content?.refId}">` + content.name + '</a>' + '&nbsp;';
                      } else if (criteria.earningCriteriaContent.length - 1 !== index) {
                        contentToComplete += ',' + '&nbsp;' + `<a class="badge-link" id="${content?.refId}">` + content.name + '</a>' + '&nbsp;';
                      } else if (criteria.earningCriteriaContent.length - 1 === index) {
                        contentToComplete += 'and' + '&nbsp;' + `<a class="badge-link" id="${content?.refId}">` + content.name + '</a>' + '&nbsp;';
                      }
                    }
                    index++;
                  });
                  if (contentToComplete) {
                    selectText = selectText?.replace(handlebar[0], contentToComplete);
                  }
                  break;
                default:
                  break;
              }
            }
          });
          criteria.earningCriteriaType.selectText = selectText;
        }
        let earningText = criteria.earningCriteriaType.earningText;
        if (earningText) {
          const handlebars = this.extractHandleBar(earningText);
          handlebars?.forEach(handlebar => {
            if (handlebar) {
              switch (handlebar[1]) {
                case 'RefId':
                  let contentToComplete = '';
                  let index = 0;
                  criteria.earningCriteriaContent.forEach(content => {
                    if (content.name) {
                      if (index < 1) {
                        contentToComplete += '&nbsp;' + `<a class="badge-link">` + content.name + '</a>' + '&nbsp;';
                      } else if (criteria.earningCriteriaContent.length - 1 !== index) {
                        contentToComplete += ',' + '&nbsp;' + `<a class="badge-link">` + content.name + '</a>' + '&nbsp;';
                      } else if (criteria.earningCriteriaContent.length - 1 === index) {
                        contentToComplete += 'and' + '&nbsp;' + `<a (click)="nav($event)" class="badge-link">` + content.name + '</a>' + '&nbsp;';
                      }
                    }
                    index++;
                  });
                  if (contentToComplete) {
                    earningText = earningText?.replace(handlebar[0], contentToComplete);
                  }
                  break;
                case 'MinValue':
                  if (criteria.minValue) {
                    earningText = earningText?.replace(handlebar[0], this.readyValue(criteria.minValue, criteria.earningCriteriaType.name));
                  }
                  break;
                case 'MaxValue':
                  if (criteria.maxValue) {
                    earningText = earningText?.replace(handlebar[0], this.readyValue(criteria.maxValue, criteria.earningCriteriaType.name));
                  }
                  break;
                case 'RowId':
                  criteria.rowCriteria.forEach(content => {
                    const rowHandleBars = this.extractHandleBar(content.earningText ?? '');
                    if (rowHandleBars && rowHandleBars.length > 0) {
                      rowHandleBars?.forEach(rowHandlebar => {
                        if (rowHandlebar) {
                          switch (rowHandlebar[1]) {
                            case 'RefId':
                              if (content.earningText) {
                                content.earningText = content.earningText?.replace(rowHandlebar[0], content.title ?? '');
                              }
                              break;
                            case 'MinValue':
                              if (content.minValue) {
                                content.earningText = content.earningText?.replace(rowHandlebar[0], content.minValue.toString());
                              }
                              break;
                            default:
                              break;
                          }
                        }
                      });
                    }
                    content.earningText += '\n';
                  });
                  earningText = '';
                  break;
                default:
                  break;
              }
            }
          });
          criteria.earningCriteriaType.earningText = selectText + earningText;
        }
      }
    });
  }

  getAnchors() {
    setTimeout(() => {
      this.anchors = this.elementRef.nativeElement.querySelectorAll('a');
      this.anchors.forEach((anchor: HTMLAnchorElement) => {
        anchor.addEventListener('click', this.handleAnchorClick);
      });
    }, 3500);
  }

  readyValue(value: number, type: string): string {
    let val: any;
    switch (type) {
      case 'AssessmentProficiency':
        val = value.toString();
        val += '%';
        break;
      case 'CompleteDuringMonth':
        if (value) {
          val = new Date(value).toDateString();
        }
        break;
      default:
        val = value.toString();
        break;
    }
    val = '&nbsp;' + `<a class="badge-link">` + val + '</a>' + '&nbsp;';
    return val;
  }

  navigateToBadge(earningCriteriaContent: any) {
    if (earningCriteriaContent.type !== 'Organization') {
      this.instanceService.openInstance(this.instanceId, earningCriteriaContent?.refId, null, 'player', null, true);
    }
  }

  extractHandleBar(text: string): RegExpMatchArray[] | null {
    let handlebarsExist = true;
    const handlebars: RegExpMatchArray[] = [];
    while (handlebarsExist) {
      const handlebar = text.match('{{([^)].*?)}}');
      if (handlebar) {
        text = text.replace(handlebar[0], '');
        handlebars.push(handlebar);
      } else {
        handlebarsExist = false;
      }
    }
    return handlebars as RegExpMatchArray[];
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
