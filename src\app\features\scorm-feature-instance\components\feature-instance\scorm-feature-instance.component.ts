import { Component, HostListener, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IGuestContext, IInstance, IProductFeatureRole, IRouteParams } from '@app/core/contracts/contract';
import { SystemPropertyType } from '@app/core/enums/system-property-type.enum';
import { ViewType } from '@app/core/enums/view-type';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RolesService } from '@app/core/services/roles.service';
import { ScormService } from '@app/core/services/scorm.service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { TabFilterPipe } from '@app/shared/pipes/tab-filter';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { ViewWillLeave } from '@ionic/angular';
import { BehaviorSubject, Subject, forkJoin, takeUntil } from 'rxjs';
@Component({
    selector: 'app-scorm-feature-instance',
    templateUrl: './scorm-feature-instance.component.html',
    styleUrls: ['./scorm-feature-instance.component.scss'],
    standalone: false
})
export class ScormFeatureInstanceComponent implements OnInit, OnDestroy, ViewWillLeave {
  instanceId: string;
  componentDestroyed$: Subject<boolean> = new Subject();
  instance: IInstance | null;
  selectedIndex = 0;
  productFeatureRoles: IProductFeatureRole[];
  scrollPosition = 0;
  isScorm = false;
  scormLoading = true;
  progress = new BehaviorSubject(0);
  routeParams: IRouteParams;

  constructor(
    private activatedRoute: ActivatedRoute,
    private systemPropertiesService: SystemPropertiesService,
    private geolocation: Geolocation,
    private rolesServie: RolesService,
    private eventsService: Events,
    private scormService: ScormService,
    private authService: AuthService,
    private instanceService: InstanceService,
    private tabFilterPipe: TabFilterPipe,
    private dataService: DataService
  ) {}

  //TODO call completed once progress has been impletmented
  // window.parent.postMessage({ command: 'ScormProcessSetValue', payload: { element: 'cmi.core.lesson_status', value: 'completed' } }, '*');
  //window.parent.postMessage({ command: 'ScormProcessSetValue', payload: { element: 'cmi.core.lesson_status', value: 'incomplete' } }, '*');
  //window.parent.postMessage({ command: 'ScormProcessSetValue', payload: { element: 'cmi.core.score.raw', value: '80' } }, '*');

  @HostListener('window:message', ['$event'])
  onMessage(e: any) {
    if (!this.scormService.userId && e.data && e.data.payload) {
      const authString = this.activatedRoute.snapshot.queryParams['auth'];
      this.scormService.validAuthToken(authString, e.data.payload.parentLocation.origin).subscribe(val => {
        if (val.valid) {
          this.scormService.rosterScormUser(e.data.payload.parentLocation.origin, e.data.payload.userId, authString).subscribe(() => {
            this.authService.setGuestContext({
              id: this.scormService.userId,
              browsingAs: '',
              instanceId: '',
            } as IGuestContext);
            this.scormLoading = false;
            this.setData();
          });
        }
      });
    }
  }

  ngOnInit() {
    // const hubspot = document.getElementById('hubspot-messages-iframe-container');
    // if (hubspot !== null) {
    //   hubspot.style.display = 'none';
    // }
    // this.activatedRoute.queryParams.subscribe(params => {
    //   const t = params['token'];
    //   this.dataService.getLTIToken(t).subscribe(data => {
    //     const token: string = data.token;
    //     const decoded = jwtDecode(token);
    //     const q = { issuer: decoded.iss, subject: decoded.sub } as ILTLinkIn;

    //     this.dataService.getLTILink(q).subscribe(user => {
    //       if (this.authService.userContext && !user) {
    //         q.userId = this.authService.userContext.userId;
    //         this.dataService.addLTILink(q).subscribe(u => {
    //           user = u;
    //         });
    //       }

    //       if (user) {
    //         this.authService.setGuestContext({
    //           id: user.userId,
    //           browsingAs: '',
    //           instanceId: '',
    //         } as IGuestContext);

    //         this.scormLoading = false;
    //         this.setData();
    //       } else {
    //         this.authService.startAuthentication();
    //       }
    //     });
    //   });
    // });

    if (this.authService?.userContext?.userId) {
      this.authService.setGuestContext({
        id: this.authService.userContext.userId,
        browsingAs: '',
        instanceId: '',
      } as IGuestContext);
      this.scormLoading = false;
      this.setData();
    }

    this.eventsService.subscribe('videoDone', id => {
      if (id === this.instance?.id) {
        this.videoComplete();
      }
    });
  }

  getRouteParamValue(paramName: string): string {
    return this.activatedRoute.snapshot.data[paramName] ?? this.activatedRoute.snapshot.params[paramName];
  }

  setData() {
    this.instance = null;
    this.setRouteParams();

    this.instanceService.setPrevFeatureSlug(this.routeParams.featureSlug ?? '');

    if (this.routeParams?.featureSlug && this.routeParams.featureSlug?.toLocaleLowerCase() === 'directory') {
      this.geolocation.getCurrentPosition().then(() => {});
    }

    this.setInstance();
  }

  setRouteParams() {
    const featureSlug = this.getRouteParamValue('featureSlug');
    const instanceSlug = (this.getRouteParamValue('instanceslug') && this.getRouteParamValue('instanceslug')) !== 'default' ? this.getRouteParamValue('instanceslug') : null;
    const tabName = (this.getRouteParamValue('tabName') && this.getRouteParamValue('tabName')) !== 'default' ? this.getRouteParamValue('tabName') : null;

    this.routeParams = { featureSlug: featureSlug, instanceSlug: instanceSlug, tabName: tabName, viewType: ViewType.Player };
  }

  ionViewWillLeave(): void {
    this.eventsService.publish('viewLeft', null);
  }

  setInstance() {
    let slug = this.routeParams.featureSlug;
    if (this.routeParams.featureSlug?.toLocaleLowerCase() === 'instance') {
      slug = this.routeParams.instanceSlug;
    }

    let orgId = '';

    if (this.instanceService.isValidGUID(this.routeParams.instanceSlug ?? '')) {
      orgId = this.routeParams.instanceSlug ?? '';
    }

    if (!slug || slug === 'undefined') {
      return;
    }

    this.instanceService
      .getInstance(slug, orgId, true)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((instance: IInstance) => {
        this.setInstanceData(instance);

        if (!(instance?.isDefault || instance?.feature?.featureType?.name === 'Accredited Learning Container Pages') && !this.routeParams.instanceSlug) {
          this.routeParams.instanceSlug = this.routeParams.featureSlug;
        }
      });
  }

  setInstanceData(instance: IInstance) {
    this.rolesServie.getProductFeatureRoles(instance?.feature?.id).pipe(takeUntil(this.componentDestroyed$)).subscribe();
    this.getSystemProperties(instance);
  }

  getSystemProperties(instance: IInstance) {
    // Clear old data
    this.systemPropertiesService.clearAllSystemProperties();

    if (instance) {
      const requestList = [];
      requestList.push(this.systemPropertiesService.getSystemPropertyValues(SystemPropertyType.Instance, instance.id));

      if (instance?.feature?.id) {
        requestList.push(this.systemPropertiesService.getSystemPropertyValues(SystemPropertyType.Feature, instance.feature.id));
      }

      if (instance.organizationId && instance.feature?.featureType?.systemPropertyType?.typeBw !== SystemPropertyType.Organization) {
        requestList.push(this.systemPropertiesService.getSystemPropertyValues(SystemPropertyType.Organization, instance.organizationId));
      }

      if (instance.feature?.featureType?.systemPropertyType?.typeBw) {
        if (this.routeParams?.instanceSlug && this.instanceService.isValidGUID(this.routeParams?.instanceSlug)) {
          const systemPropertyType = instance.feature.featureType.systemPropertyType.typeBw;
          if (systemPropertyType) {
            requestList.push(this.systemPropertiesService.getSystemPropertyValues(systemPropertyType, this.routeParams?.instanceSlug));
          }
        } else if (instance.feature?.featureType?.systemPropertyType?.typeBw === SystemPropertyType.User) {
          //MyJourneyDefaultSlug
          requestList.push(this.systemPropertiesService.getSystemPropertyValues(SystemPropertyType.User, instance.id));
        }
      }

      forkJoin(requestList)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.instance = instance;
        });
    }
  }

  videoComplete() {
    window.parent.postMessage({ command: 'ScormProcessSetValue', payload: { element: 'cmi.core.lesson_status', value: 'completed' } }, '*');
    window.parent.postMessage({ command: 'KillFrame', payload: {} }, '*');
  }

  getTab() {
    const tabs = this.tabFilterPipe.transform(this.instance?.feature.featureTabs, this.instance?.isDefault ?? false);

    if (tabs) {
      return tabs[0];
    }

    return null;
  }

  ngOnDestroy() {
    this.eventsService.unsubscribe('videoDone');
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
