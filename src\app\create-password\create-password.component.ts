import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { IGuestContext, IPasswordResetIn } from '@app/core/contracts/contract';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { Subject, map, takeUntil } from 'rxjs';

@Component({
    selector: 'app-create-password',
    templateUrl: './create-password.component.html',
    styleUrls: ['./create-password.component.scss'],
    standalone: false
})
export class CreatePasswordComponent implements OnInit, OnDestroy {
  componentDestroyed$: Subject<boolean> = new Subject();
  passwordForm: FormGroup;
  password: FormControl;
  confirmPassword: FormControl;
  secret: string | null;
  buttonDisabled = false;
  constructor(
    private route: ActivatedRoute,
    private dataService: DataService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    this.secret = this.route.snapshot.paramMap.get('id');
    this.setContext();
    this.createFormControls();
    this.createForm();
  }

  setContext() {
    this.authService.guestUserContext = {
      id: this.authService.persona?.id,
      browsingAs: this.authService.persona?.display,
      instanceId: '',
      impersonate: false,
    } as IGuestContext;
  }

  createFormControls() {
    this.password = new FormControl('', Validators.required);
    this.confirmPassword = new FormControl('', [Validators.required, this.comparePasswords]);
  }

  comparePasswords: ValidatorFn = (control: AbstractControl) => {
    const password = this.passwordForm?.get('password')?.value;
    const confirmPassword = control.value;
    return password === confirmPassword ? null : { passwordsDoNotMatch: true };
  };

  createForm() {
    this.passwordForm = new FormGroup({
      password: this.password,
      confirmPassword: this.confirmPassword,
    });
  }

  onSubmit() {
    this.passwordForm.markAllAsTouched();
    if (this.passwordForm.valid && this.secret) {
      this.buttonDisabled = true;
      const data = {
        newPassword: this.passwordForm?.get('password')?.value,
        confirmPassword: this.passwordForm?.get('confirmPassword')?.value,
        secret: this.secret,
      } as IPasswordResetIn;
      this.dataService
        .resetPassword(data)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe({
          next: () => this.openAuth(),
          error: () => {
            this.buttonDisabled = false;
          },
        });
    }
  }

  openAuth() {
    this.authService
      .setUserContext(false)
      .pipe(takeUntil(this.componentDestroyed$))
      .pipe(
        map(() => {
          this.authService.startAuthentication();
        })
      )
      .subscribe();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
