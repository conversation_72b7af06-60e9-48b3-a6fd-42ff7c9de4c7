<ion-content>
  @if (settingsForm) {
    <div [formGroup]="settingsForm" class="container">
      <h1 class="italic">Tab Settings</h1>
      <ion-label class="gray-text">Indicate the function of the tab and who is able to see it.</ion-label>
      <ion-button class="close" fill="clear" color="light" (click)="close()">
        <ion-icon name="close"></ion-icon>
      </ion-button>
      <ion-grid>
        <ion-row class="text-row">
          <div class="text-and-checkbox">
            <ion-label class="white-text">Show only for Edge Factor Admins</ion-label>
            <ion-checkbox [formControlName]="'showForEfAdmin'"></ion-checkbox>
          </div>
        </ion-row>
        <ion-row class="text-row">
          <div class="text-and-checkbox">
            <ion-label class="white-text">Allow Guest access</ion-label>
            <ion-checkbox [formControlName]="'showForGuest'"></ion-checkbox>
          </div>
        </ion-row>
        <ion-row class="input-row">
          <ion-col size="6">
            @if (featureTabTypes$ | async; as featureTabTypes) {
              <app-select-option-control
                [toolTip]="'Select Function'"
                [placeHolder]="'Select Function'"
                [label]="'Function'"
                [formControlName]="'function'"
                [backgroundColor]="controlBackground"
              [options]="featureTabTypes"></app-select-option-control>
            }
          </ion-col>
          <ion-col size="6" [ngClass]="{ disabled: featureTab?.showForEfAdmin }">
            @if (actions$ | async; as actions) {
              <app-select-option-control
                [toolTip]="'Select Tab Access'"
                [placeHolder]="'Select Tab Access'"
                [label]="'Tab Access'"
                [formControlName]="'access'"
                [multiple]="true"
                [backgroundColor]="controlBackground"
              [options]="actions"></app-select-option-control>
            }
          </ion-col>
          <ion-col size="6">
            @if (personaSelectOptions$ | async; as defaultOptions) {
              <app-select-option-control
                [toolTip]="'Select Persona Tab Access'"
                [placeHolder]="'Select Persona Tab Access'"
                [label]="'Persona Access'"
                [formControlName]="'personaAccess'"
                [backgroundColor]="controlBackground"
                [multiple]="true"
              [options]="defaultOptions"></app-select-option-control>
            }
          </ion-col>
          <ion-col size="6" [ngClass]="{ disabled: featureTab?.showForEfAdmin }">
            @if (actions$ | async; as actions) {
              <app-select-option-control
                [toolTip]="'Select Tab Access'"
                [placeHolder]="'Select Tab Access'"
                [label]="'Tab Edit Access'"
                [formControlName]="'editAccess'"
                [multiple]="true"
                [backgroundColor]="controlBackground"
              [options]="actions"></app-select-option-control>
            }
          </ion-col>
        </ion-row>
        <ion-row class="text-row">
          <ion-col size="12">
            <ion-label class="gray-text">Set up the action button on the tab.</ion-label>
          </ion-col>
        </ion-row>
        <ion-row class="input-row">
          <ion-col size="4">
            <app-text-input-control
              [toolTip]="'Enter Button Text'"
              [placeHolder]="'Enter Button Text'"
              [label]="'Button Text'"
              [formControlName]="'buttonText'"
            [itemBackgroundColor]="controlBackground"></app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row class="text-row input-row">
          <ion-col size="12">
            <ion-label class="gray-text">These are the actions triggered by the page buttons.</ion-label>
            <app-tab-button-actions [featureTab]="featureTab"></app-tab-button-actions>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="12" class="text-row">
            <ion-label class="gray-text">Choose <span class="white-text">how</span> content can be added to a row. Check all that apply.</ion-label>
            @if (rowTypes$ | async; as rowTypes) {
              <div class="radio-group-container">
                @for (type of rowTypes; track type) {
                  <div class="properties-container">
                    <div class="property">
                      <div class="property-left">
                        <ion-checkbox (ionChange)="checkboxChanged($event, type)" [checked]="isSelected(type.id)"></ion-checkbox>
                        <div style="display: flex; flex-direction: column">
                          <div class="heading">{{ getRowTypeLabel(type?.name ?? '') }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                }
              </div>
            }
            <ion-label class="gray-text italic">
              <span>NOTE: Users with 'Manage' access are not limited by these controls. They can use any of the methods listed above.</span>
            </ion-label>
          </ion-col>
        </ion-row>
      </ion-grid>
    </div>
  }
</ion-content>
