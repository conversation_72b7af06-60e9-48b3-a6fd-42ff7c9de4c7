import { Input, Directive, inject, effect } from '@angular/core';
import { IInstanceSectionComponent } from '@app/core/contracts/contract';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';

@Directive()
export abstract class BaseValueComponent {
  @Input() instanceComponent: IInstanceSectionComponent | undefined;

  protected signalService = inject(ComponentUpdateSignalService);

  constructor() {
    effect(() => {
      const changeDetection = this.signalService.signal();
      if (changeDetection?.componentId == this.instanceComponent?.component.id) {
        this.setData();
      }
      return;
    });
  }

  abstract setData(): void;

}
