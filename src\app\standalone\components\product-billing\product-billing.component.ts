import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort, MatSortHeader } from '@angular/material/sort';
import { MatTableDataSource, MatTable, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell, MatHeaderRowDef, MatHeaderRow, MatRowDef, MatRow } from '@angular/material/table';
import { CurrencyPipe, DatePipe } from '@angular/common';

@Component({
    selector: 'app-product-billing',
    templateUrl: './product-billing.component.html',
    styleUrls: ['./product-billing.component.scss'],
    imports: [
        MatTable,
        MatSort,
        MatColumnDef,
        MatHeaderCellDef,
        MatHeaderCell,
        MatSortHeader,
        MatCellDef,
        MatCell,
        MatHeaderRowDef,
        MatHeaderRow,
        MatRowDef,
        MatRow,
        MatPaginator,
        CurrencyPipe,
        DatePipe,
    ]
})
export class ProductBillingComponent implements OnInit {
  @Input() id: string;
  @ViewChild('paginator', { static: true }) paginator: MatPaginator;
  @ViewChild('sort', { static: true }) sort: MatSort;
  dataSource = new MatTableDataSource<any>();
  resultsLength = 0;
  displayedColumns: string[] = ['invoiceId', 'dueDate', 'amount', 'status', 'download'];

  constructor() {}

  ngOnInit() {
    this.getDummyData();
  }

  getProductBillingHistory() {
    //Use DummyData For Now.
  }

  getDummyData() {
    const currentDate = new Date();
    const dummyData: any[] = [
      { invoiceId: 1334626, dueDate: currentDate.valueOf(), amount: 2345.52, status: 'Paid' },
      { invoiceId: 1743726, dueDate: currentDate.valueOf(), amount: 7542.75, status: 'Paid' },
      { invoiceId: 1985842, dueDate: currentDate.valueOf(), amount: 523.12, status: 'Paid' },
    ];

    this.dataSource = new MatTableDataSource(dummyData);
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }
}
