import { Component, EventEmitter, Input, OnChanges, OnDestroy, Output, SimpleChanges } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent, IRow, IRowType } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-row-options-editor',
    templateUrl: './row-options-editor.component.html',
    styleUrls: ['./row-options-editor.component.scss'],
    standalone: false
})
export class RowOptionsEditorComponent implements OnDestroy, OnChanges {
  @Input() component: IComponent;
  @Output() formChange: EventEmitter<UntypedFormGroup> = new EventEmitter<UntypedFormGroup>();
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  rowTypes: IRowType[] = [];
  row: IRow;
  rowEditorForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;
  formLoaded = false;

  constructor(
    private dataService: DataService,
    private formBuilder: UntypedFormBuilder
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.createForm(1);
    }
  }

  getRowTypes() {
    this.dataService
      .getRowTypes()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(rowTypes => {
        this.rowTypes = rowTypes;
      });
  }

  getRowTypeBw() {
    this.dataService
      .getRow(this.component.rowId, false)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(row => {
        this.row = row;
        this.setFormValues(row.rowType.typeBw);
      });
  }

  createForm(rowTypeBw: number) {
    this.formLoaded = false;
    this.rowEditorForm = this.formBuilder.group({
      rowType: [rowTypeBw],
      rowNumber: [this.component?.builderRowNumber, Validators.required],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      label: [this.component?.templateField?.label, Validators.required],
      description: [this.component?.templateField?.helpDescription],
      caption: [this.component?.templateField?.helpTitle],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.getRowTypes();
    this.getRowTypeBw();
  }

  setFormValues(rowTypeBw: number) {
    if (!this.rowEditorForm) {
      return;
    }

    this.rowEditorForm.controls.rowType.setValue(rowTypeBw);
    this.rowEditorForm.controls.rowNumber.setValue(this.component?.builderRowNumber);
    this.rowEditorForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.rowEditorForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.rowEditorForm.controls.label.setValue(this.component?.templateField?.label);
    this.rowEditorForm.controls.description.setValue(this.component?.templateField?.helpDescription);
    this.rowEditorForm.controls.caption.setValue(this.component?.templateField?.helpTitle);
    this.rowEditorForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
    this.formLoaded = true;
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.rowEditorForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setBuilderRowNumber();
      this.formChange.emit(this.rowEditorForm);
      this.formValidityChanged.emit(this.rowEditorForm.valid);
    });
  }

  setBuilderRowNumber() {
    if (this.rowEditorForm.valid) {
      this.component.builderRowNumber = this.rowEditorForm.controls.rowNumber.value;
      if (this.component.templateField) {
        this.component.templateField.label = this.rowEditorForm.controls.label.value;
        this.component.templateField.helpDescription = this.rowEditorForm.controls.description.value;
        this.component.templateField.helpTitle = this.rowEditorForm.controls.caption.value;
        this.component.templateField.colspan = this.rowEditorForm.controls.colspan.value;
        this.component.templateField.colNumber = this.rowEditorForm.controls.colNumber.value;
        this.component.templateField.useMaxWidth = this.rowEditorForm.controls.useMaxWidth.value;
      }
    }
  }

  setRowType(event: any) {
    this.rowTypes.find(x => x.typeBw === event.target.value);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
