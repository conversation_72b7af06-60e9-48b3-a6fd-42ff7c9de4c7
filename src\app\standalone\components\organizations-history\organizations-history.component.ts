import { DatePipe, UpperCasePipe } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IProductHistory, IProductRenew, IRole } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { UserRole } from '@app/core/enums/userRoles.enum';
import { DataService } from '@app/core/services/data-service';
import { ProductHistoryService } from '@app/core/services/product-history.service';
import { RolesService } from '@app/core/services/roles.service';
import { RenewProductModalComponent } from '@app/standalone/modals/renew-product-modal/renew-product-modal.component';
import { IonicModule, ModalController } from '@ionic/angular';

@Component({
    selector: 'app-organizations-history',
    templateUrl: './organizations-history.component.html',
    styleUrls: ['./organizations-history.component.scss'],
    imports: [IonicModule, MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, UpperCasePipe, DatePipe]
})
export class OrganizationsHistoryComponent implements OnInit {
  @Input() productOrganizationId: string;
  @Input() productId: string;
  @Input() orgId: string;
  @Input() expiryDate: number;
  @Input() featureType: string;
  @Input() isNetworkProduct: boolean;
  @ViewChild('sort', { static: true }) sort: MatSort;
  @ViewChild('parentAddPopover') parentAddPopover: any;

  currentDate = new Date();
  currentSubscriptionExpires = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
  dataSource = new MatTableDataSource<IProductHistory>();
  moreResults = false;
  isParentPanelClosed = false;
  hasChanges = false;

  userRoleName: string;
  userRoles: UserRole;
  productOrganizationsHistory: IProductHistory[] = [];
  currentAmount = 0;
  getAmount = 5;

  constructor(
    public dataService: DataService,
    private productHistoryService: ProductHistoryService,
    public modalController: ModalController,
    private rolesService: RolesService
  ) {}

  ngOnInit() {
    this.getProductOrganizationHistory(this.orgId, this.productId, false);
  }

  subscriptionStatus(date: number): string {
    return this.productHistoryService.subscriptionStatus(date);
  }

  async openModal() {
    const modal = await this.modalController.create({
      component: RenewProductModalComponent,
      cssClass: 'product-history-modal',
      componentProps: {
        currentSubscriptionExpires: this.currentSubscriptionExpires,
        featureType: this.featureType,
      },
      backdropDismiss: false,
    });

    modal.onDidDismiss().then((detail: any) => {
      if (detail?.data) {
        const renewProduct: IProductRenew = detail?.data;

        this.dataService.renewProductOrganizationSubscription(this.productOrganizationId, this.orgId, this.productId, renewProduct.period, renewProduct.expiryDate).subscribe((value: any) => {
          if (value) {
            this.productOrganizationsHistory = [];
            this.currentAmount = 0;
            this.getProductOrganizationHistory(this.orgId, this.productId, false);
          }
        });
      }
    });

    return await modal.present();
  }

  checkIfDateIsValid(inputExpiryDate: string) {
    const currentDate = new Date(); //Don't add if license is still valid because it sets current licence's join code to null
    const date = new Date(inputExpiryDate).setHours(0, 0, 0, 0);
    const currentExpiryDate = new Date(this.expiryDate).setHours(0, 0, 0, 0);
    return currentExpiryDate.valueOf() < currentDate.valueOf() && currentExpiryDate.valueOf() < date.valueOf();
  }

  getProductOrgUserRoleById(organization: IProductHistory) {
    if (organization != null) {
      this.dataService.getProductOrgUserRoleById(organization.id).subscribe((orgUserRole: IRole) => {
        if (orgUserRole != null) {
          organization.orgUserRoleName = orgUserRole.name;
        }
      });
    }
  }

  getProductOrganizationHistory(orgId: string, productId: string, loadMore: boolean) {
    if (orgId !== undefined) {
      this.dataService.getProductOrganizationHistory(orgId, productId, this.featureType, this.currentAmount, this.getAmount).subscribe((productOrganizationsHistory: IProductHistory[]) => {
        if (productOrganizationsHistory.length > 0) {
          //OnLoadMoreData
          if (!loadMore) {
            this.currentSubscriptionExpires = new Date(productOrganizationsHistory[0].expiryDate);
            this.productOrganizationsHistory = productOrganizationsHistory;
            this.currentAmount += productOrganizationsHistory.length;
          } else {
            productOrganizationsHistory.forEach(org => {
              this.productOrganizationsHistory = [...this.productOrganizationsHistory, org];
            });
            this.currentAmount += productOrganizationsHistory.length;
          }

          if (productOrganizationsHistory.length < this.getAmount) {
            this.moreResults = false;
          } else {
            this.moreResults = true;
          }

          this.productOrganizationsHistory.forEach(organization => {
            this.getProductOrgUserRoleById(organization);
          });
        }
      });
    }
  }

  hasAdminAccess() {
    return this.rolesService.hasFeatureRoleAccess([ActionTypes.Manage, ActionTypes.Publish]);
  }
}
