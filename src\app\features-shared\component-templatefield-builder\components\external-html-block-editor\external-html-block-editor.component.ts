import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-external-html-block-editor',
    templateUrl: './external-html-block-editor.component.html',
    styleUrls: ['./external-html-block-editor.component.scss'],
    standalone: false
})
export class ExternalHtmlBlockEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  externalHtmlForm: FormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: FormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.externalHtmlForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.externalHtmlForm = this.formBuilder.group({
      label: ['External Html'],
      placeHolderText: ['External Html'],
      defaultImageUrl: [this.component?.templateField?.defaultImageUrl, Validators.required],
      rowNumber: [this.component?.builderRowNumber ?? 0],
      hoverSortOrder: [this.component?.hoverSortOrder ?? 0],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0],
      isRequired: [false],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      openExternal: [this.component?.templateField?.openExternal ?? true],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.externalHtmlForm) {
      return;
    }

    this.externalHtmlForm.controls.label.setValue(this.component.templateField.label);
    this.externalHtmlForm.controls.placeHolderText.setValue(this.component.templateField.placeHolderText);
    this.externalHtmlForm.controls.defaultImageUrl.setValue(this.component.templateField.defaultImageUrl);
    this.externalHtmlForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.externalHtmlForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.externalHtmlForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.externalHtmlForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.externalHtmlForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.externalHtmlForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.externalHtmlForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.externalHtmlForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.externalHtmlForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.externalHtmlForm.controls.openExternal.setValue(this.component.templateField.openExternal);
    this.externalHtmlForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.externalHtmlForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.externalHtmlForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.externalHtmlForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.externalHtmlForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.externalHtmlForm.valid) {
      this.component.templateField.label = this.externalHtmlForm.controls.label.value;
      this.component.templateField.placeHolderText = this.externalHtmlForm.controls.placeHolderText.value;
      this.component.templateField.defaultImageUrl = this.externalHtmlForm.controls.defaultImageUrl.value;
      this.component.builderRowNumber = this.externalHtmlForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.externalHtmlForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.externalHtmlForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.externalHtmlForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.externalHtmlForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.externalHtmlForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.externalHtmlForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.externalHtmlForm.controls.isViewField.value;
      this.component.templateField.isVisibleRepository = this.externalHtmlForm.controls.isVisibleRepository.value;
      this.component.templateField.openExternal = this.externalHtmlForm.controls.openExternal.value;
      this.component.templateField.colspan = this.externalHtmlForm.controls.colspan.value;
      this.component.templateField.colNumber = this.externalHtmlForm.controls.colNumber.value;
      this.component.templateField.useMaxWidth = this.externalHtmlForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
