import { Component, Input } from '@angular/core';
import { NgClass } from '@angular/common';

@Component({
    selector: 'app-builder-side-panel-heading',
    templateUrl: './builder-side-panel-heading.component.html',
    styleUrls: ['./builder-side-panel-heading.component.scss'],
    imports: [NgClass]
})
export class BuilderSidePanelHeadingComponent {
  @Input() text: string;
  @Input() isSectionHeading = false;
  constructor() {}
}
