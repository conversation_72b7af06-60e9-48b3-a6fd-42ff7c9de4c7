@if (analytics) {
  <div class="parent-container">
    <div [ngClass]="{ 'image-container': assetUrl }" [style]="'--background-image:url(' + assetUrl + ');'">
      <ion-row>
        @if (iconUrl) {
          <ion-col class="icon-col">
            <img class="icon" ngSrc="{{ iconUrl }}" fill="true" />
          </ion-col>
        }
        <ion-col style="display: flex">
          <div class="header-container">
            <div class="heading">
              {{ templateField?.helpTitle ?? '' }}
            </div>
            <div class="sub-heading">{{ templateField?.helpDescription ?? '' }}</div>
          </div>
        </ion-col>
      </ion-row>
    </div>
    <div class="row">
      @for (analytic of analytics; track analytic) {
        <div class="card" [style]="'width: calc(' + cardWidth + ' - 15px)'">
          <div class="card-content">
            <div class="inner-panel">
              @if (analytic?.name) {
                <div class="heading">{{ analytic.name }}</div>
              }
              @if (analytic?.headers) {
                <div class="amount-container">
                  @for (header of analytic.headers; track header; let i = $index) {
                    <div class="amounts">
                      <div class="amount">
                        {{ header?.amount?.toLocaleString() }}
                      </div>
                      <div class="label">
                        @if (header?.name) {
                          <div class="icons-and-text-container">
                            @if (header.name === 'Impressions') {
                              <mat-icon class="icon" svgIcon="impressions"></mat-icon>
                            }
                            @if (header.name === 'Opens') {
                              <mat-icon class="icon" svgIcon="opens"></mat-icon>
                            }
                            @if (header.name === 'Engagements') {
                              <mat-icon class="icon" svgIcon="engagements"></mat-icon>
                            }
                            @if (header.name === 'Completions') {
                              <mat-icon class="icon" svgIcon="completions"></mat-icon>
                            }
                            <span class="name">
                              {{ header.name }}
                            </span>
                          </div>
                        }
                        @if (i === 0) {
                          <div class="underline"></div>
                        }
                      </div>
                    </div>
                  }
                </div>
              }
              <div class="description">
                {{ analytic.description }}
              </div>
            </div>
          </div>
          <div class="black-underline"></div>
          @for (analyticTableContent of analytic?.userBreakdown; track analyticTableContent; let i = $index) {
            <app-analytics-table [id]="id" [componentId]="componentId" [organizationUserBreakDown]="analyticTableContent" [type]="analytic?.type" [index]="i"> </app-analytics-table>
          }
        </div>
      }
    </div>
  </div>
}
