<div class="parent-container" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }">
  <ion-card class="card-container">
    <form class="form-container" [formGroup]="textForm">
      <app-text-input-control
        [disabled]="disabled"
        [noPadding]="true"
        [backgroundColor]="'#1E1E1E'"
        [placeHolder]="component?.templateField?.placeHolderText ?? 'Start typing here'"
        [label]="component?.templateField?.label ?? 'Icon text'"
        formControlName="iconText"
        [toolTip]="'Icon and text'"
        [itemBackgroundColor]="'#292929'"></app-text-input-control>
    </form>
  </ion-card>
</div>
