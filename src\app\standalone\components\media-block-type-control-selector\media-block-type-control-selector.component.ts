import { Component, EventEmitter, forwardRef, Input, <PERSON><PERSON><PERSON><PERSON>, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { IAssetIn, IComponent, ISystemProperty } from '@app/core/contracts/contract';
import { MediaUploadType } from '@app/core/enums/media-upload-type';
import { DataService } from '@app/core/services/data-service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { Subject, takeUntil, debounceTime } from 'rxjs';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { FileUploadControlComponent } from '@app/standalone/components/file-upload-control/file-upload-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { MediaBlockComponent } from '@app/standalone/components/media-block/media-block.component';

@Component({
    selector: 'app-media-block-type-control-selector',
    templateUrl: './media-block-type-control-selector.component.html',
    styleUrls: ['./media-block-type-control-selector.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => MediaBlockTypeControlSelectorComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => MediaBlockTypeControlSelectorComponent),
        },
    ],
    imports: [FormsModule, ReactiveFormsModule, FileUploadControlComponent, TextInputControlComponent, MediaBlockComponent]
})
export class MediaBlockTypeControlSelectorComponent extends BaseControlComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Input() controlBackground: string;
  @Input() assetId?: string;
  @Input() urlUpload?: string;
  @Input() embedCode?: string;
  @Input() mediaUploadType = 'Upload';
  @Input() isDisabled = false;
  @Output() mediaRemoved = new EventEmitter<any>();
  formGroup: UntypedFormGroup | null;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private formBuilder: UntypedFormBuilder,
    private dataService: DataService,
    private activatedRoute: ActivatedRoute,
    private systemPropertyService: SystemPropertiesService
  ) {
    super();
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(): void {
    this.updateFormValues();
  }

  createForm() {
    this.formGroup = this.formBuilder.group({
      fileUpload: [this.assetId],
      embed: [this.mediaUploadType === 'Embed' ? this.embedCode : null],
      url: [this.mediaUploadType === 'Url' ? this.urlUpload : null],
    });

    this.subscribeToChanges();
  }

  updateFormValues() {
    this.formGroup?.controls.fileUpload.setValue(this.assetId);
    if (this.mediaUploadType === 'Embed') {
      this.formGroup?.controls.embed.setValue(this.embedCode);
    }
    if (this.mediaUploadType === 'Url') {
      this.formGroup?.controls.url.setValue(this.urlUpload);
    }
  }

  assetUpdated(event: any) {
    if (event === null || event === '') {
      this.mediaRemoved.emit(null);
    }
  }

  subscribeToChanges() {
    this.formGroup?.valueChanges.pipe(debounceTime(700), takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.assetId && !this.formGroup?.controls.fileUpload.value) {
        return this.setValue(null);
      }
      if (this.assetId !== this.formGroup?.controls.fileUpload.value || this.embedCode !== this.formGroup?.controls.embed.value || this.urlUpload !== this.formGroup?.controls.url.value) {
        this.assetId = this.formGroup?.controls.fileUpload.value ?? this.assetId;
        this.embedCode = this.formGroup?.controls.embed.value;
        this.urlUpload = this.formGroup?.controls.url.value;

        const editedAsset: IAssetIn = {
          id: this.assetId,
          embedCode: this.embedCode,
          urlUpload: this.urlUpload,
          mediaUploadType: this.activatedRoute.snapshot.queryParams['uploadType'] ? MediaUploadType[+this.activatedRoute.snapshot.queryParams['uploadType']] : this.mediaUploadType,
        };

        this.mediaUploadType = editedAsset.mediaUploadType;

        this.dataService
          .updateAsset(editedAsset)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(assetId => {
            this.setValue(assetId);

            this.systemPropertyService.setAssetSystemPropertyValueByKey('Asset.EmbedCode', editedAsset.embedCode ?? '');
            this.systemPropertyService.setAssetSystemPropertyValueByKey('Asset.UrlUpload', editedAsset.urlUpload ?? '');
          });
      }
    });
  }

  getSystemPropertyValue(systemProperty: ISystemProperty | undefined) {
    if (systemProperty) {
      const systemPropertyValue = this.systemPropertyService.getSystemPropertyValue(systemProperty);
      if (systemPropertyValue != null) {
        return systemPropertyValue;
      }
    }

    return null;
  }

  override setValue(value: string | null): void {
    this.writeValue(value);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
