<div class="parent-container">
  @if (imageTextList.length > 0) {
    <div class="swiper-main-container">
      <swiper-container #swiper class="swiper">
        @for (item of imageTextList; track item) {
          <swiper-slide [ngClass]="['swiper-container', stylingDirection]">
            <ng-container
              [ngTemplateOutlet]="stylingDirection === 'Left' ? left : stylingDirection === 'Bottom' ? bottom : stylingDirection === 'Top' ? top : right"
              [ngTemplateOutletContext]="{ item: item }">
            </ng-container>
          </swiper-slide>
        }
      </swiper-container>
      @if (imageTextList.length > 1) {
        <div>
          <div (click)="slidePrev()" [ngClass]="['swiper-button-prev', stylingDirection]" class="swiper-button-prev nav-button"></div>
          <div (click)="slideNext()" [ngClass]="['swiper-button-next', stylingDirection]" class="swiper-button-next nav-button"></div>
        </div>
      }
      <!-- Text Bottom and Image Top -->
      <ng-template #top let-item="item">
        <ng-container [ngTemplateOutlet]="image" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
        <ng-container [ngTemplateOutlet]="text" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
      </ng-template>
      <!-- Text Top and Image Bottom -->
      <ng-template #bottom let-item="item">
        <ng-container [ngTemplateOutlet]="text" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
        <ng-container [ngTemplateOutlet]="image" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
      </ng-template>
      <!-- Image Left and Text Right -->
      <ng-template #left let-item="item">
        <ng-container [ngTemplateOutlet]="image" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
        <ng-container [ngTemplateOutlet]="text" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
      </ng-template>
      <!-- Text Left and Image Right -->
      <ng-template #right let-item="item">
        <ng-container [ngTemplateOutlet]="text" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
        <ng-container [ngTemplateOutlet]="image" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
      </ng-template>
      <ng-template #image let-item="item">
        <div [ngClass]="['image-container', stylingDirection]">
          <div (mouseenter)="toggleDrag(item.assetType)" (mouseleave)="toggleDrag('on')" class="swiper-slide">
            @if (item.assetType && item.assetType.includes('video')) {
              <app-video-player [assetId]="item.asset" [mini]="true"></app-video-player>
            } @else {
              <img [ngClass]="[stylingDirection]" src="{{ item.assetUrl }}" />
            }
          </div>
          <div class="swiper-pagination"></div>
        </div>
      </ng-template>
      <ng-template #text let-item="item">
        <div [ngClass]="['text-main-container', stylingDirection]">
          <div [ngClass]="['text-container', stylingDirection]">
            <span class="heading" [ngClass]="{ darkText: darkText }">{{ item.heading }}</span>
            <div class="paragraph" [ngClass]="{ darkText: darkText }">
              <quill-view [content]="item.paragraph" format="html" theme="snow"></quill-view>
            </div>
            @if (item.buttonText && item.buttonUrl) {
              <div class="button-container">
                <ion-button (click)="openLink(item)">{{ item.buttonText }}</ion-button>
                <ng-template let-item="item">
                  <div [ngClass]="['text-main-container', stylingDirection]">
                    <div [ngClass]="['text-container', stylingDirection]">
                      <div class="mobile-center">
                        <span class="heading">{{ item.heading }}</span>
                      </div>
                      <div class="mobile-center">
                        <div class="paragraph">
                          <quill-view [content]="item.paragraph" format="html" theme="snow"></quill-view>
                        </div>
                      </div>
                      @if (item.buttonText && item.buttonUrl) {
                        <div class="mobile-center">
                          <div class="button-container">
                            <ion-button (click)="openLink(item.buttonUrl)">{{ item.buttonText }}</ion-button>
                          </div>
                        </div>
                      }
                    </div>
                  </div>
                </ng-template>
              </div>
            }
          </div>
        </div>
      </ng-template>
    </div>
  }
</div>
