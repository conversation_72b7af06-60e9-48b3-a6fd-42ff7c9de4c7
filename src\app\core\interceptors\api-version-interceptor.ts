import { <PERSON>ttp<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '@env/environment';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { GlobalVersionComponent } from '../components/version-modal/version-modal';

@Injectable()
export class ApiVersionInterceptor implements HttpInterceptor {
  counter = 0;
  constructor(private modal: GlobalVersionComponent) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          const apiVersion = +event.headers.get('minmobileversion').replace('.', '').replace('.', '');
          const appVersion = +environment.version.replace('.', '').replace('.', '');
          if (apiVersion > appVersion) {
            if (this.counter === 0) {
              this.counter++;
              this.modal.presentModal();
              setTimeout(() => {
                this.counter = 0;
              }, 1000);
            }

            const msg = 'Application out of date: req version >' + apiVersion.toString();
            throw msg;
          }
        }
      })
    );
  }
}
