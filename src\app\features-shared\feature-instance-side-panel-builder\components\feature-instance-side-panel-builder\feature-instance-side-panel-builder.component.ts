import { CdkDragMove } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { IComponent, IFeatureTab, IInstance, IInstanceSection, IInstanceSectionComponent, IInstanceTemplate, IRouteParams, ISidePanelBuilder } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { SidePanelBuilderType } from '@app/core/enums/builder-selection-types';
import { SectionTypes } from '@app/core/enums/section-types.enum';
import { ViewType } from '@app/core/enums/view-type';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RolesService } from '@app/core/services/roles.service';
import { environment } from '@env/environment';
import { forkJoin, Observable, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-feature-instance-side-panel-builder',
    templateUrl: './feature-instance-side-panel-builder.component.html',
    styleUrls: ['./feature-instance-side-panel-builder.component.scss'],
    standalone: false
})
export class FeatureInstanceSidePanelBuilderComponent implements OnInit, OnChanges, OnDestroy {
  @Input() template: IInstanceTemplate;
  @Input() instance: IInstance;
  @Input() featureTab: IFeatureTab;
  @Input() routeParams: IRouteParams;
  @Input() onlyContent = false;
  @Output() setupSteps = new EventEmitter<any>();
  openMenu = false;
  builderType = SidePanelBuilderType;
  editPanelBuilder: ISidePanelBuilder = {} as ISidePanelBuilder;
  editInstanceSections: IInstanceSection[] = [];
  drawerWidth = 40;
  leftMargin = 0;
  contentWidth = 100;
  dragPosition = { x: 0, y: 0 };
  componentDestroyed$: Subject<boolean> = new Subject();
  selectedId: string;
  isManager = false;
  assetUrl: string;
  constructor(
    private rolesService: RolesService,
    private dataService: DataService,
    private instanceService: InstanceService,
    private breadCrumbservice: BreadcrumbService
  ) {}

  ngOnInit() {
    this.setupSidePanelBuilder(false);
    this.setGradientImageOverlay();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['template']) {
      this.setupSidePanelBuilder(false);
    }
  }

  setGradientImageOverlay() {
    if (this.instance.coverMediaAssetId) {
      this.assetUrl = `${environment.contentUrl}asset/${this.instance.coverMediaAssetId}/content`;
    } else if (this.instance.feature.coverMediaAssetId) {
      this.assetUrl = `${environment.contentUrl}asset/${this.instance.feature.coverMediaAssetId}/content`;
    } else {
      this.assetUrl = 'assets/images/defaultbackgroundgradient.png';
    }
  }

  setupSidePanelBuilder(isEditOnClick: boolean) {
    this.isManager = this.instance.feature.featureType.name.toLocaleLowerCase().indexOf('manager') !== -1;
    if (
      (this.instance.feature.featureType.name.toLocaleLowerCase().indexOf('user') !== -1 || this.instance.feature.featureType.name.toLocaleLowerCase().indexOf('organization') !== -1) &&
      window.innerWidth > 768 &&
      !isEditOnClick
    ) {
      return;
    }
    const sectionIndex = this.template.instanceSections.findIndex(x => x.instanceSectionComponents?.length > 0);
    const instanceSection = this.template.instanceSections[sectionIndex];

    if (instanceSection && instanceSection.instanceSectionComponents?.some(x => x) && (this.publishEnabled() || this.isManager === true)) {
      const comp = instanceSection.instanceSectionComponents[0].component;
      if (comp) {
        this.editPanelBuilder = {
          id: comp.id,
          builderType: this.builderType.EditComponent,
          heading: 'Edit Component',
        };

        this.openSideMenu();
      }
    } else if (instanceSection && instanceSection.section.typeBw === SectionTypes.Dynamic && !instanceSection.section.templateId && this.publishEnabled()) {
      this.editPanelBuilder = {
        id: instanceSection.id,
        builderType: this.builderType.EditSection,
        heading: 'Edit Section',
      };

      this.openSideMenu();
    } else {
      this.closeMenu();
    }
  }

  publishEnabled(): boolean {
    return this.isManager === true || this.rolesService.hasFeatureRoleAccess([ActionTypes.Publish]);
  }

  sideBuilderUpdated() {
    this.editPanelBuilder = { ...this.editPanelBuilder } as ISidePanelBuilder;
    if (this.editPanelBuilder.builderType === this.builderType.SortSection || this.editPanelBuilder.builderType === this.builderType.SortComponent) {
      this.closeMenu();
    } else {
      this.openSideMenu();
    }
  }

  openSideMenu() {
    if (
      this.publishEnabled() &&
      (this.isManager !== true ||
        this.instance.feature.featureType.name.toLocaleLowerCase().indexOf('user') !== -1 ||
        this.instance.feature.featureType.name.toLocaleLowerCase().indexOf('organization') !== -1) &&
      window.innerWidth > 768
    ) {
      this.contentWidth = 100 - this.drawerWidth;
      this.leftMargin = this.drawerWidth;
    } else if (this.isManager === true || window.innerWidth <= 768) {
      this.contentWidth = 0;
      this.leftMargin = 0;
      this.drawerWidth = 100;
    }

    if (this.publishEnabled() || this.isManager === true || window.innerWidth <= 768) {
      this.openMenu = true;
    }
  }

  closeMenu() {
    this.openMenu = false;
    this.contentWidth = 100;
    this.leftMargin = 0;
  }

  onDragMove(event: CdkDragMove) {
    this.drawerWidth = this.getNewWidth(event);
    this.contentWidth = 100 - this.drawerWidth;
    this.leftMargin = this.drawerWidth;
  }

  updateInstanceStatus(status: string) {
    const requestList: Observable<any>[] = [this.dataService.updateInstanceStatus(this.instance.id, status)];
    if (this.instance?.feature?.featureType?.name === 'Media Manager' && this.routeParams.instanceSlug) {
      requestList.push(this.dataService.publishAsset(this.routeParams.instanceSlug));
    }

    forkJoin(requestList)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.instance.status = status;
        this.setupSidePanelBuilder(false);
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }

  getId() {
    return this.instanceService.isValidGUID(this.routeParams.instanceSlug ?? '') ? this.routeParams.instanceSlug : this.instance.id;
  }

  goBack(viewType: ViewType) {
    this.breadCrumbservice.goToPrev(viewType);
  }

  forceChangeDetection() {
    this.template.instanceSections = [
      ...this.template.instanceSections.map(
        x => ({ ...x, instanceSectionComponents: x.instanceSectionComponents.map(y => ({ ...y, component: { ...y.component } as IComponent }) as IInstanceSectionComponent) }) as IInstanceSection
      ),
    ];
    this.template = { ...this.template } as IInstanceTemplate;
  }

  editPanelBuilderChanged() {
    this.editPanelBuilder = { ...this.editPanelBuilder } as ISidePanelBuilder;
  }

  private getNewWidth(event: any): number {
    const parentWidth = document.getElementById('#side-nav-container')?.offsetWidth ?? 0;
    const sideNavWidth = document.getElementById('#side-nav')?.offsetWidth ?? 0;
    const totalWidth = event.source.getFreeDragPosition().x + sideNavWidth;
    this.dragPosition = { x: 0, y: 0 };
    const widthPercentage = (totalWidth / parentWidth) * 100;
    return widthPercentage < 50 ? (widthPercentage > 20 ? widthPercentage : 20) : 50;
  }
}
