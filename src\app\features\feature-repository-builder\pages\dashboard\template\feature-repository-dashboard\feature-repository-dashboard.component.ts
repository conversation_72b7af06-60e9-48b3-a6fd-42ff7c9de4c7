import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { Sort } from '@angular/material/sort';
import {
  DashboardGrid,
  DashboardGridRow,
  IAsset,
  ICampaignIn,
  ICommunicationIn,
  IDashboardGridFilters,
  IFeature,
  IGuestContext,
  IInstanceIn,
  IKeyValue,
  INetworkIn,
  IOrganizationIn,
  IProductIn,
  IQuestionIn,
} from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { MediaUploadType } from '@app/core/enums/media-upload-type';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { AddSearchModalComponent } from '@app/standalone/modals/add-search-modal/add-search-modal.component';
import { OptionSelectorDialogComponent } from '@app/standalone/modals/option-selector-dialog/option-selector-dialog.component';
import { AlertController, ModalController, PopoverController } from '@ionic/angular';
import { OverlayEventDetail } from '@ionic/core';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { AddMediaComponent } from '../../add-media/add-media.component';
import { RepositoryDashboardTableComponent } from '../../repository-dashboard-table/repository-dashboard-table.component';
import { SyncBadgeToCredentialEngineModalComponent } from '@app/standalone/modals/sync-badge-to-credential-engine-modal/sync-badge-to-credential-engine-modal.component';

@Component({
    selector: 'app-feature-repository-dashboard',
    templateUrl: './feature-repository-dashboard.component.html',
    styleUrls: ['./feature-repository-dashboard.component.scss'],
    standalone: false
})
export class FeatureRepositoryDashboardComponent implements OnInit, OnDestroy {
  @Input() feature: IFeature;
  @ViewChild(RepositoryDashboardTableComponent, { static: false })
  tableComponent: RepositoryDashboardTableComponent;
  componentDestroyed$: Subject<boolean> = new Subject();
  dataSource: DashboardGridRow[];
  displayedColumns: string[];
  searchForm: UntypedFormGroup;
  featureRepoSearchValue: UntypedFormControl;
  staticColumns: string[] = ['Name', 'Id'];
  currentAmount = 0;
  getAmount = 25;
  showAdd = true;
  loading = false;
  searchQuery = '';
  controlBackground = '#222428';
  statusTypes: KeyValue[];
  sortBy: string;
  sortDir: string;
  statusTypeId: string;
  name: string;

  instanceStatusTypes: KeyValue[] = [
    { id: 'AwaitingReview', value: 'Awaiting Review' },
    { id: 'Approved', value: 'Approved' },
    { id: 'EdgeFactorRejected', value: 'Edge Factor Rejected' },
    { id: 'CustomerRejected', value: 'Customer Rejected' },
    { id: 'Public', value: 'Published' },
    { id: 'Private', value: 'Unpublished' },
    { id: 'Deleted', value: 'Deleted' },
  ];

  constructor(
    private dataService: DataService,
    private popOver: PopoverController,
    public alertController: AlertController,
    private authService: AuthService,
    private instanceService: InstanceService,
    public modalController: ModalController
  ) {}

  ngOnInit() {
    if (this.feature.featureType) {
      this.name = this.feature.featureType.name;
    } else {
      this.showAdd = false;
    }
    if (this.name && (this.name === 'Internal' || this.name.includes('User Manager') || this.name.includes('Search'))) {
      this.showAdd = false;
    }
    this.createFormControls();
    this.createForm();
    this.getRepositoryDashboard(false);
    this.initData();
  }

  initData() {
    if(this.feature.featureType.name === 'Organization Manager')
    {
    this.dataService
      .getOrganizationStatusTypes()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.statusTypes = data.map(t => ({ id: t.id, value: t.name }) as KeyValue);
      });
    }
    else {
      this.statusTypes = this.instanceStatusTypes;
    }
  }

  createFormControls() {
    this.featureRepoSearchValue = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      featureRepoSearchValue: this.featureRepoSearchValue,
    });
  }

  searchRepoValue($event: any) {
    this.currentAmount = 0;
    this.featureRepoSearchValue.setValue($event.target.value);
    this.getRepositoryDashboard(false);
  }

  filterValues(status: string) {
    this.statusTypeId = status;
    this.currentAmount = 0;
    this.refreshData();
  }

  getRepositoryDashboard(event: any) {    
    if (!this.name || this.loading) {
      return;
    }
    const searchValue = encodeURIComponent(this.featureRepoSearchValue.value);
    this.loading = true;
    this.dataService
      .getFeatureRepositoryDashboard(this.feature.id, this.name, {
        repoSearchValue: searchValue,
        currentAmount: this.currentAmount,
        getAmount: this.getAmount,
        sortBy: this.sortBy,
        sortDir: this.sortDir,
        filterField: 'Status',
        filterValue: this.statusTypeId,
      } as IDashboardGridFilters)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((dashboardData: DashboardGrid) => {
        if (dashboardData.rows != null && dashboardData.rows.length > 0) {
          //OnScrollDataLoad
          if (!event) {
            this.dataSource = dashboardData.rows;
            if (dashboardData.columnNames?.some(x => x === 'Status')) {
              const index: number = dashboardData.columnNames.indexOf('Status');

              if (index !== -1) {
                const removed: string[] = dashboardData.columnNames.splice(index, 1);

                dashboardData.columnNames.push(removed[0]);
              }
            }
            this.displayedColumns = dashboardData.columnNames;
            this.setStaticColumns();
          } else {
            dashboardData.rows.forEach(row => {
              this.dataSource = [...this.dataSource, row];
            });
          }
          this.currentAmount += dashboardData.rows.length;
        }
        else
        {
          if (this.statusTypeId) {
            this.displayedColumns = [];
            this.dataSource = [];
          }
        }
        this.loading = false;
      });
  }

  refreshData() {
    //SetDefault
    this.getRepositoryDashboard(false);
  }

  setStaticColumns() {
    this.staticColumns.forEach(item => {
      //UserManager Only Id Static Default.
      if (!this.displayedColumns.includes(item)) {
        this.displayedColumns.unshift(item);
      }
    });
  }

  async openSyncBadgeModal() {
    const modal = await this.modalController.create({
      component: SyncBadgeToCredentialEngineModalComponent,
      componentProps: {},
    });
    await modal.present();
  }

  add(event: any) {
    switch (this.name) {
      case 'Product Manager':
        this.addProduct();
        break;
      case 'Question Manager':
        this.openSelectQuestionType(event);
        break;
      case 'Organization Manager':
        this.addOrganization();
        break;
      case 'Communication Manager':
        this.addCommunication();
        break;
      case 'Media Manager':
        this.addMedia(event);
        break;
      case 'Network Manager':
        this.addNetwork();
        break;
      case 'Campaign Manager':
        this.addCampaign();
        break;
      case 'Feature Manager':
        this.addFeature(event);
        break;
      case 'User Manager':
        break;
      default:
        this.addInstance(event);
        break;
    }
  }

  async openSelectQuestionType(event: any) {
    this.dataService.getQuestionTypes().subscribe(async data => {
      if (data) {
        const popover = await this.popOver.create({
          component: OptionSelectorDialogComponent,
          cssClass: 'question-type-popover',
          componentProps: {
            options: data.map(x => ({ key: x.id, value: x.name }) as IKeyValue<string, string>),
          },
          event: event,
          side: 'bottom',
        });

        popover.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
          if (overlayEventDetail.data) {
            this.createQuestion(overlayEventDetail.data.key);
          }
        });

        await popover.present();
      }
    });
  }

  createQuestion(typeId: string) {
    this.dataService.createQuestion({ id: null, questionTypeId: typeId } as IQuestionIn).subscribe(returnId => {
      if (returnId) {
        this.openInstanceBuilder(returnId);
      }
    });
  }

  createInstances(orgIds: string[]) {
    const instanceList: IInstanceIn[] = [];
    for (const orgId of orgIds) {
      instanceList.push(this.populateNewInstanceIn(orgId));
    }

    this.dataService
      .createInstances(instanceList)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {});
  }

  openInstanceBuilder(id: string) {
    if (this.feature.featureType != null) {
      if (this.feature.featureType.name === 'Feature Manager') {
        // this.router.navigate([`repository/builder/${id}`]);
        this.instanceService.openInstance('repository/builder', id);
      } else if (this.name === 'Favorites' || this.name === 'Portfolio') {
      } else if (
        this.name === 'Product Manager' ||
        this.name === 'Organization Manager' ||
        this.name === 'Question Manager' ||
        this.name === 'Network Manager' ||
        this.name === 'Communication Manager' ||
        this.name === 'Campaign Manager'
      ) {
        // this.router.navigate([`${this.feature.featureSlug}/${id}`]);
        this.instanceService.openInstance(this.feature.featureSlug, id, 'default', 'builder');
      } else if (this.name === 'User Manager') {
        this.dataService
          .getUserFullName(id)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(context => {
            this.authService.setGuestContext({
              id: id,
              browsingAs: context.fullName,
              instanceId: '',
              impersonate: true,
              qlikUserId: context.qlikUserId
            } as IGuestContext);
            // this.router.navigate([`${this.feature.featureSlug}`]);
            this.instanceService.openInstance(this.feature.featureSlug);
          });
      } else if (this.name === 'Media Manager') {
        if (!id || id.length === 0) {
          return;
        }

        this.dataService
          .getAssetDetailsById(id)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe((data: IAsset) => {
            let uploadType = MediaUploadType.Upload;

            switch (data.mediaUploadType) {
              case 'Url':
                uploadType = MediaUploadType.Url;
                break;
              case 'Embed':
                uploadType = MediaUploadType.Embed;
                break;
              default:
                uploadType = MediaUploadType.Upload;
                break;
            }

            this.instanceService.openInstance(this.feature.featureSlug, id, 'default', 'builder', { uploadType: +uploadType });
          });
      } else {
        // this.router.navigate([`instance/builder/${id}`]);
        this.instanceService.openInstance('instance', id, 'default', 'builder');
      }
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }

  private addCampaign() {
    const newCampaign: ICampaignIn = {
      title: this.feature.title + Math.floor(1000 + Math.random() * 9000),
      featureId: this.feature.id,
    };

    this.dataService
      .addCampaign(newCampaign)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(campaignId => {
        if (campaignId) {
          this.openInstanceBuilder(campaignId);
        }
      });
  }

  private addProduct() {
    const newProduct: IProductIn = {
      name: this.feature.title + Math.floor(1000 + Math.random() * 9000),
      description: this.feature.description as string,
    };
    this.dataService
      .addProduct(newProduct)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(product => {
        this.openInstanceBuilder(product.id);
      });
  }

  private addOrganization() {
    const newOrganization: IOrganizationIn = {
      name: this.feature.title + Math.floor(1000 + Math.random() * 9000),
    };

    this.dataService
      .addOrganization(newOrganization)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(org => {
        if (org) {
          this.openInstanceBuilder(org.id);
        }
      });
  }

  private addInstance(event: any) {
    this.dataService
      .getMyOrganizations(this.feature.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(async data => {
        if (data.length < 1) {
          await this.presentAlert();
          return;
        }
        if (data.length > 1 || this.authService.userContext?.canManage === true) {
          this.addInstanceToOrganization(event);
        } else {
          this.saveInstance(data[0].id);
        }
      });
  }

  private async addMedia(event: any) {
    const popover = await this.popOver.create({
      component: AddMediaComponent,
      cssClass: 'add-media-popover',
      componentProps: {
        onClose: () => {
          //  this.getRow();
        },
      },
      event: event,
      side: 'bottom',
    });

    await popover.present();
  }

  private async addNetwork() {
    const newNetwork: INetworkIn = {
      name: this.feature.title + Math.floor(1000 + Math.random() * 9000),
    };
    this.dataService
      .addNetwork(newNetwork)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(
        network => {
          if (network) {
            this.openInstanceBuilder(network.id);
          }
        },
        err => {
          console.error(err);
        }
      );
  }

  private async addCommunication() {
    const newComms: ICommunicationIn = {
      name: this.feature.title + Math.floor(1000 + Math.random() * 9000),
    };

    this.dataService
      .addCommunincations(newComms)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(coms => {
        if (coms) {
          this.openInstanceBuilder(coms.id);
        }
      });
  }

  private async addInstanceToOrganization(event: any) {
    const popover = await this.popOver.create({
      component: AddSearchModalComponent,
      cssClass: 'add-search-modal',
      componentProps: { linkTypeName: 'Organizations', criteriaType: null, options: null },
      event: event,
      side: 'bottom',
    });

    popover.onDidDismiss().then((result: any) => {
      if (result.data) {
        this.saveInstance(result.data.id);
      }
    });

    await popover.present();
  }

  private saveInstance(orgId: string) {
    const newInstance = this.populateNewInstanceIn(orgId);
    this.dataService
      .createInstance(newInstance)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(instance => {
        this.openInstanceBuilder(instance.id);
      });
  }

  private async presentAlert() {
    const alert = await this.alertController.create({
      cssClass: '',
      header: 'Please Note:',
      message: 'User not linked to an organization.',
      buttons: ['OK'],
    });

    await alert.present();

    await alert.onDidDismiss();
  }

  private populateNewInstanceIn(orgId: string) {
    const newInstance: IInstanceIn = {
      title: this.feature.title,
      description: this.feature.description,
      featureId: this.feature.id,
      organizationId: orgId,
      isDefault: false,
      status: this.name === 'Modifiable Learning Container Pages' ? 'public' : 'private',
    };
    return newInstance;
  }

  private async addFeature(event: Event) {
    this.dataService
      .getFeatureTypes()
      .pipe(
        takeUntil(this.componentDestroyed$),
        map(types => {
          return types.map(t => ({ key: t.id, value: t.name }) as IKeyValue<string, string>);
        })
      )
      .subscribe(async (featureTypes: IKeyValue<string, string>[]) => {
        const popover = await this.popOver.create({
          component: OptionSelectorDialogComponent,
          cssClass: 'question-type-popover',
          componentProps: {
            header: 'Please select a feature type:',
            options: featureTypes,
          },
          event: event,
          side: 'bottom',
        });

        popover.onDidDismiss().then(async (result: any) => {
          if (result.data) {
            this.dataService
              .createFeature(result.data.key)
              .pipe(takeUntil(this.componentDestroyed$))
              .subscribe(featureId => {
                this.instanceService.openInstance(`repository/builder`, featureId);
              });
          }
        });
        await popover.present();
      });
  }

  sortChanged(value: Sort) {
    this.sortBy = value?.active;
    this.sortDir = value?.direction;
    this.refreshData();
  }
}
