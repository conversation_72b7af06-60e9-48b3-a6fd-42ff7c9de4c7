<div class="parent-container" [ngClass]="{ 'no-container': noContainer, 'side-panel-input-padding': sidePanelPadding }">
  <ion-card style="margin: 0" class="inner-container">
    <ion-item class="inner-item" [ngClass]="{ 'no-padding': noPadding }">
      @if (!noPadding) {
        <ion-label position="stacked" [ngStyle]="{ 'margin-bottom': label !== '' ? null : '10px' }">
          {{ label }}
          <span class="reqAsterisk">
            @if (required) {
              <span> * </span>
            }
            <ion-icon name="information-circle-outline"></ion-icon>
          </span>
          @if (identifierText) {
            <span class="identifier">{{ identifierText }}</span>
          }
        </ion-label>
      }
      <div
        [ngClass]="{ disabledNoOfCasesDiv: disabled, 'border-color-red': required && noPadding }"
        [class.download-container]="isFile"
        [class.video-container]="isVideo"
        class="image-container"
        (fileDropped)="onFileDrop($event)"
        appFileUploadControl>
        @if (imageSizeWarning) {
          <div class="size-warning" [ngClass]="getWarningColor()">
            {{ imageSizeWarning }}
          </div>
        }

        @if ((mediaId || asset) && !disabled) {
          <div class="update-button-container">
            <ion-button (click)="toggleUpdateMode()">
              {{ isUpdateMode ? 'Cancel Update' : 'Update Media' }}
            </ion-button>
          </div>
        }

        @if (includesImage && assetUrl) {
          <img [src]="assetUrl" />
        }

        <div [ngStyle]="{ 'background-color': assetUrl ? null : 'rgb(30, 30, 30)' }" class="main-container" [ngClass]="{ image: includesImage && assetUrl }">
          @if (!assetUrl || isUpdateMode) {
            <div>
              <input [disabled]="disabled || (!isUpdateMode && assetUrl)" type="file" [accept]="acceptedUploadFormat" #fileDropRef id="fileDropRef" (change)="fileBrowseHandler($event)" />
              <div class="text-area">
                <ion-icon style="font-size: 2em" name="cloud-upload"></ion-icon>
                <p>{{ placeHolderText }}</p>
                <ion-button [disabled]="disabled || (!isUpdateMode && assetUrl)" color="light" for="fileDropRef" (click)="fileBrowseHandler($event)">{{ buttonText }}</ion-button>
              </div>
            </div>
          }

          @if (assetUrl && !isUpdateMode) {
            <div class="delete-block">
              @if (!disabled) {
                <div class="delete" (click)="deleteFile()">
                  <ion-icon name="close-circle"></ion-icon>
                </div>
              }

              @if (isPending) {
                <ion-icon name="hourglass-outline"></ion-icon>
                <p>{{ asset?.name }}</p>
              }

              @if (isVideo) {
                @defer (on immediate) {
                  <app-video-player [assetId]="asset?.id" [component]="component"></app-video-player>
                }
              }

              @if (isEmbed) {
                @defer (on immediate) {
                  <app-video-player [assetId]="asset?.id" [component]="component"></app-video-player>
                }
              }

              @if (isFile) {
                <app-file-download-control [assetId]="asset?.id"></app-file-download-control>
              }
            </div>
          }
        </div>
        @if (!noPadding) {
          <ion-progress-bar color="primary" [value]="progress | async"></ion-progress-bar>
        }
      </div>
    </ion-item>
    @if (noPadding) {
      <div>
        <ion-progress-bar color="primary" [value]="progress | async"></ion-progress-bar>
      </div>
    }
  </ion-card>
</div>
