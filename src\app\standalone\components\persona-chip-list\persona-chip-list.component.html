<div class="persona-list-container">
  <mat-form-field class="persona-list">
    <mat-chip-grid #chipGrid aria-label="Persona Tag selection">
      @for (tag of existingTags; track tag) {
        <mat-chip-option (removed)="removeDirect(tag.id)">
          <div class="name">{{ tag.name }}</div>
          <button matChipRemove [attr.aria-label]="'remove ' + tag.name">
            <mat-icon>cancel</mat-icon>
          </button>
        </mat-chip-option>
      }
      <input [matChipInputFor]="chipGrid" [matChipInputSeparatorKeyCodes]="separatorKeysCodes" [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="add($event)" />
      <mat-label (click)="openTagModal()">{{ component?.templateField?.placeHolderText }}</mat-label>
    </mat-chip-grid>
  </mat-form-field>
</div>
