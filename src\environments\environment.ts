const connectionString =
  'InstrumentationKey=941d87e4-51d6-4aa3-b179-edcdc0847d1c;IngestionEndpoint=https://centralus-2.in.applicationinsights.azure.com/;LiveEndpoint=https://centralus.livediagnostics.monitor.azure.com/';
export const environment = {
  production: true,
  version: require('../../package.json').version,
  platform: 'production',
  apiUrl: 'https://api.edgefactor.com/v1/',
  appUrl: 'https://app.edgefactor.com/',
  contentUrl: 'https://ef-gdhmd9grg3ecb7dg.z03.azurefd.net/v1/',
  signalRUrl: 'https://sr.edgefactor.com/',
  appInsights: {
    connectionString: connectionString,
  },
  client_id: 'edgefactor',
  authority_ca: 'https://idca.edgefactor.com',
  authority_usa: 'https://idusa.edgefactor.com',
  redirect_uri: 'https://app.edgefactor.com/auth-callback',
  post_logout_redirect_uri: 'https://app.edgefactor.com/',
  silent_redirect_uri: 'https://app.edgefactor.com/silent-callback.html',
  convey_this_api_key: 'pub_de84665e79ea7a4dd10c6da9101c77b0',
  googleMapApiKey: 'AIzaSyAYKflWJabFnpz5QL-4kdUjDL8O9StPnGs',
  html5Path: 'https://app.edgefactor.com/html5',
  clarityId: 'rgai8bqdja',
  showShareBtn: false,
};
