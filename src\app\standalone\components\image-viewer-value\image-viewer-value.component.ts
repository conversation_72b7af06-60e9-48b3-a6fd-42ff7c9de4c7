import { Component, OnInit } from '@angular/core';
import { DataService } from '@app/core/services/data-service';
import { ParseService } from '@app/core/services/parse-service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { HeaderImageBaseComponent } from '../header-image-base/header-image-base.component';

@Component({
  selector: 'app-image-viewer-value',
  templateUrl: './image-viewer-value.component.html',
  styleUrls: ['./image-viewer-value.component.scss'],
  standalone: true,
})
export class ImageViewerValueComponent extends HeaderImageBaseComponent implements OnInit {
  constructor(builderService: BuilderService, parseService: ParseService, dataService: DataService, systemPropertiesService: SystemPropertiesService) {
    super(parseService, builderService, dataService, systemPropertiesService);
    this.derivedComponentType = 'image-viewer-value';
  }

  ngOnInit() {
    this.setIconAndCoverUrl();
  }
}
