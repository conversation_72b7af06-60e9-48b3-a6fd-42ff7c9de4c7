.tab-container {
  height: 100%;
  min-height: 100%;
  overflow-y: hidden;

  .mat-mdc-tab-header-pagination-chevron {
    border-color: white;
  }

  > .mat-mdc-tab-group {
    > .mat-mdc-tab-header {
      padding-left: var(--page-margin-left);
      padding-right: var(--page-margin-right);
      background-color: #1e1e1e;
    }

    .mat-mdc-tab {
      margin-right: 35px !important;
      padding: 0px !important;
      justify-content: flex-start !important;
      min-width: 0px !important;
      width: auto !important;
      color: #ffffff;
      border-bottom: #f99e00;
      font-size: 1em;
      font-weight: 700;
      letter-spacing: 0.02em;
      opacity: 100%;
    }

    .mdc-tab--active {
      color: #f99e00;
    }

    .mat-ink-bar {
      background: #f99e00;
    }
    .mdc-tab__ripple::before {
      color: white;
      background-color: transparent;
    }
    .mat-mdc-tab-ripple {
      color: white;
      background-color: transparent;
    }
    .mdc-tab-indicator {
      display: none !important;
    }

    .mdc-tab:hover {
      .mdc-tab__text-label {
        color: #aaa !important;
      }
    }

    .mdc-tab__text-label {
      color: #aaa;
    }
  }

  .mat-mdc-tab-body-wrapper {
    height: Calc(100% - 50px) !important;
    min-height: Calc(100% - 50px) !important;
  }

  div#mat-mdc-tab-0-0 {
    padding: 0px 0px 0px 0px !important;
    width: auto !important;
    margin-left: 0px !important;
  }

  .header-less-tabs {
    > .mat-mdc-tab-header {
      display: none;
    }

    .mat-mdc-tab-body-wrapper {
      height: 100% !important;
      min-height: 100% !important;
    }
  }
}
