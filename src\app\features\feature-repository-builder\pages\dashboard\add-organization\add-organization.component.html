<ion-content>
  <ion-grid class="base">
    <ion-row>
      <ion-col class="info">
        <h1>Who are you creating this for?</h1>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-searchbar color="dark" (ionChange)="search($event)" type="search" placeholder="Search for Organization" showCancelButton="focus" debounce="600"></ion-searchbar>
      </ion-col>
    </ion-row>
    @if (filteredOrganizations?.length > 0) {
      <ion-row class="org-list">
        <ion-col>
          <ion-radio-group>
            @for (org of filteredOrganizations; track org) {
              <ion-row class="clickable org-single">
                <ion-col>
                  <ion-row>
                    <ion-col size="1">
                      <ion-radio (click)="updateAssignedList(org.id)"></ion-radio>
                    </ion-col>
                    <ion-col>
                      {{ org.name }}
                    </ion-col>
                  </ion-row>
                  <ion-row class="org-sub">
                    <ion-col>{{ org.type }} - in {{ org.city }}, {{ org.country }}</ion-col>
                  </ion-row>
                </ion-col>
              </ion-row>
            }
          </ion-radio-group>
          <br />
        </ion-col>
      </ion-row>
    } @else {
      <ion-row class="org-list">
        <ion-col>
          <ion-row class="org-single"> No Results Found </ion-row>
        </ion-col>
      </ion-row>
    }
    <ion-row>
      <ion-col>
        <ion-button fill="clear" color="light" (click)="onClose()">Cancel</ion-button>
      </ion-col>
      <ion-col class="add-to">
        <ion-button fill="solid" color="primary" (click)="onAddTo()">Add To</ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
