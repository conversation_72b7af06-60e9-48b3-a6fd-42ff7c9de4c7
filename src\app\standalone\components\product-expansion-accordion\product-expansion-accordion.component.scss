.parent-container {
  .view-options-row {
    color: white;
    justify-content: space-between;
    margin-bottom: 10px;

    ion-col {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      height: 50px;
    }

    .end-col-buttons {
      justify-content: flex-end;
    }
  }

  mat-expansion-panel {
    background-color: rgba(41, 41, 41);
    box-shadow: none;
    margin-bottom: 10px !important;

    .expansion-panel-header {
      height: 100%;
      border-radius: 8px;
      padding: 15px;

      .dlt-button-col {
        display: flex;
        justify-content: flex-end;
        padding-right: 20px;
        align-items: center;
      }

      .inner-panel {
        .heading {
          font-weight: bold;
          font-size: 20px;
          color: white;
          margin-top: 10px;
        }

        .sub-heading-margin {
          margin-top: 10px;
          margin-bottom: 5px;
        }
      }

      .role-heading {
        color: rgba(170, 170, 170);
        right: 0;
      }

      .row {
        display: flex;
        flex: flex-grow;
      }
    }

    .expansion-panel-header:hover {
      background-color: rgba(41, 41, 41) !important;
    }

    ::ng-deep .mat-expansion-indicator::after,
    .mat-expansion-panel-header-description {
      border-color: white;
    }

    ::ng-deep .mat-mdc-tab {
      min-width: 25px !important;
      height: 25px;
      padding: 0px;
      background-color: transparent;
      margin-right: 15px;
      font-size: 16px;
      color: white;
    }

    ::ng-deep .mat-mdc-tab-header {
      margin-bottom: 15px !important;
      color: white;
    }

    ::ng-deep .mat-ink-bar {
      background-color: rgba(250, 167, 0) !important;
    }
  }

  .sub-heading {
    font-style: italic;
    color: rgba(170, 170, 170);

    .expiry-date {
      margin-left: 5px;
      color: white;
    }
  }

  .load-more {
    font-size: 16px;
    color: white;
    margin-top: 10px;
    text-align: center;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }

  mat-chip-option {
    background-color: #444444;
    color: white;
    height: auto;
    width: auto;
    border: 1px solid #444444;
    padding: 0px 11px;
  }

  .bold-white {
    color: white;
    font-weight: bold;
  }
}
