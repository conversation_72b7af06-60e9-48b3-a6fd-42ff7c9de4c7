import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { UntypedFormGroup, FormsModule } from '@angular/forms';
import { IOrganizationSsoAuth, IOrganizationSsoauthExternalUrl } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { LayoutService } from '@app/core/services/layout-service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { IonicModule } from '@ionic/angular';

@Component({
    selector: 'app-orgainization-scorm-settings',
    templateUrl: './organization-scorm-settings.component.html',
    styleUrls: ['./organization-scorm-settings.component.scss'],
    imports: [IonicModule, FormsModule]
})
export class OrganizationScormSettingsComponent implements OnInit, OnDestroy {
  @Input() organizationId: string;
  orgSsoAuth: IOrganizationSsoAuth;
  orgSsoAuthGroupForm: UntypedFormGroup;
  dataLoaded = false;

  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    public layoutService: LayoutService
  ) {}

  ngOnInit() {
    if (this.organizationId != null) {
      this.getOrgSsoAuthById(this.organizationId);
    }
  }

  getOrgSsoAuthById(organizationId: string) {
    this.dataService
      .getOrgSsoAuthById(organizationId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(orgAuth => {
        this.dataLoaded = true;
        this.orgSsoAuth = orgAuth;
      });
  }

  updateExternalUrl(checked: any, externalUrl: IOrganizationSsoauthExternalUrl) {
    externalUrl.isEnabled = checked;
    this.dataService.updateOrgSsoAuthExternalUrl(externalUrl).pipe(takeUntil(this.componentDestroyed$)).subscribe();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
