import { Injectable, signal } from '@angular/core';
import { IUserContext } from '@app/core/contracts/contract';
import { IQlikSelection } from '../contracts/contract';

@Injectable({ providedIn: 'root' })
export class QlikService {
  // Signal to hold the current QlikSelection state
  qlikSelection = signal<IQlikSelection | null>(null);

  constructor() {
    // @ts-ignore
    window['getToken'] = () => this.getToken();
  }

  async getToken() {
    const hostConfig = {
      host: 'https://edgefactor.us.qlikcloud.com',
    };

    const userContext = JSON.parse(localStorage.getItem('user_context') as string) as IUserContext;
    const guestUserContext = JSON.parse(localStorage.getItem('guest_context') as string) as IUserContext;
    const qlikUserId = guestUserContext?.qlikUserId ?? userContext.qlikUserId ?? '';

    const payload = {
      client_id: '04f318adea1c742fa79cb06926b329e2',
      client_secret: '5e953858bac77395e44ff87c440945488d4a181d6cd18e1539dfc7dc9fb2404f',
      grant_type: 'urn:qlik:oauth:user-impersonation',
      user_lookup: {
        field: 'userId',
        value: qlikUserId,
      },
      scope: 'user_default',
    };

    if (qlikUserId.length > 1) {
      const getToken = await fetch(`${hostConfig.host}/oauth/token`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const response = await getToken.json().then();
      sessionStorage.setItem('token', JSON.stringify(response));
      return response.access_token;
    } else {
      return '';
    }
  }

  // Method to update the QlikSelection signal
  updateQlikSelection(filterType: string | undefined, filterValue: string, filterOptions: string[] = [], bypassWait: boolean = false) {
    this.qlikSelection.set({
      filterType,
      filterValue,
      filterOptions,
      bypassWait,
    });
  }
}
