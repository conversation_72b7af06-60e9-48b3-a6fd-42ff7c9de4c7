:host {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  align-content: stretch;

  .view-options-row {
    padding-top: 10px;
    padding-bottom: 10px;
    color: white;
    justify-content: space-between;

    ion-col {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      height: 50px;
    }

    .end-col-buttons {
      justify-content: flex-end;
      // ion-button {
      //   margin-right: 10px;
      // }
    }
  }

  ion-searchbar {
    --ion-background-color: #333333;
    --icon-color: white;
    --color: white;
    --box-shadow: 0;
  }

  .table-container {
    flex: 1;

    .table-grid {
      flex: 1;
      width: 100%;
      background-color: #444444;
    }

    .mat-mdc-header-cell {
      color: white;
      background-color: rgba(34, 34, 34) !important;
      border-right: 1px solid rgba(155, 152, 152, 0.5);
      padding-left: 15px;
    }
    .mat-column-checkBox {
      width: 20px;
      background-color: rgba(42, 42, 42);
    }
    .mat-column-name {
      background-color: rgba(42, 42, 42);
      width: 400px;
    }
    .mat-column-sku {
      background-color: rgba(42, 42, 42);
      width: 300px;
    }
    .mat-column-availableTo {
      background-color: rgba(42, 42, 42);
      width: 200px;
    }
    .mat-column-price {
      background-color: rgba(42, 42, 42);
      width: 150px;
    }
    .mat-mdc-cell {
      color: white;
      max-width: 400px;
      padding-left: 15px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    ::ng-deep .mdc-checkbox__checkmark {
      border-color: grey;
    }
  }

  .mat-mdc-paginator {
    background-color: #aaa;
  }
}
