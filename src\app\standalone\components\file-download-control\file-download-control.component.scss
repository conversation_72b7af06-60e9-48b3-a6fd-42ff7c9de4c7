.parent-container {
  background: #232323;
  border-color: #454545;
  border-width: 2px;
  border-style: solid;
  border-radius: 15px 11px 11px 11px;
  padding-left: 15px;
  ion-label {
    color: white;
  }

  .row-container {
    margin: 0px;
    padding: 0px;
    justify-content: space-between;
    min-height: 80px !important;

    .text-area ion-button {
      --background: lightgray;
      color: black;
      border-radius: 1em;
      text-transform: none;
    }

    .description-container {
      display: flex;
      align-items: center;

      font-weight: 900;
      font-size: 18px;

      ion-label {
        display: flex;
      }
    }

    .asset-name-label {
      margin-left: 15px;

      @media screen and (max-device-width: 960px) {
        font-size: 0.875em;
      }
    }

    .file-info {
      ion-label {
        color: #aaaaaa;
        font-family: 'Roboto';
        font-weight: 400;
        font-size: 14px;
        font-style: italic;
        line-height: 1;
        letter-spacing: 0.3px;
        text-align: left;
      }
    }

    .download-block-container {
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: flex-end;

      ion-label,
      ion-button {
        margin-right: 10px;
      }

      mat-icon {
        color: #aaa;
        height: 35px;
        width: 35px;
      }
    }

    ion-label {
      cursor: help !important;
    }
  }
}
