@if (authoringForm) {
  <form class="parent-form-container" [formGroup]="authoringForm">
    <ion-grid>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="header">Edit {{ component.componentType.name }}</div>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Instance Sort Order'"
            [placeHolder]="'Add field instance sort order...'"
            formControlName="instanceSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="row-number-col">
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Row Number'"
            [placeHolder]="'Add field row number...'"
            formControlName="rowNumber"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Column Number (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add column number (Out of 12)...'"
            formControlName="colNumber"
            [type]="'number'">
          </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Colspan (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add field colspan (Out of 12)...'"
            formControlName="colspan"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
