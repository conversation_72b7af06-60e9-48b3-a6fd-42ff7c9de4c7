<ion-grid>
  <ion-row>
    @if(!orgStatus || orgStatus.name != 'Deleted')
    {
    <ion-col size="5">
      <app-select-option-control
        [toolTip]="'Select Status'"
        [placeHolder]="'Select Status'"
        [label]="''"
        [backgroundColor]="controlBackground"
        [textValue]="columnValue ?? ''"
        [options]="orgStatusTypes"
        (valueChanged)="updateStatus($event)"></app-select-option-control>
    </ion-col>
    <ion-col class="button-col" size="5">
      @if (!isUnpublishEnabled) {
        <ion-button [disabled]="!isPublishEnabled" fill="solid" color="primary" (click)="changeStatus(orgStatusTypeEnum.Published)">Publish</ion-button>
      }
      @if (isUnpublishEnabled) {
        <ion-button fill="solid" color="primary" (click)="changeStatus(orgStatusTypeEnum.Unpublished)">Unpublish</ion-button>
      }   
    </ion-col>
      @if(!orgStatus || orgStatus.name != 'Deleted'){
        <ion-col size="2" class="trash-col">
          <mat-icon svgIcon="trash" (click)="delete()"></mat-icon>     
    </ion-col>
    }   
  }
    @else {
      <ion-button fill="solid" color="primary" (click)="changeStatus(orgStatusTypeEnum.Unpublished)">Restore</ion-button>
     }
 
  </ion-row>
</ion-grid>
