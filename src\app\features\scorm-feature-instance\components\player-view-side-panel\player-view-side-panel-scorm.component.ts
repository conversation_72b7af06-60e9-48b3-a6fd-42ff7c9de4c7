import { Component, Input } from '@angular/core';
import { IInstance, IInstanceTemplate, IRouteParams } from '@app/core/contracts/contract';

@Component({
    selector: 'app-player-view-side-panel-scorm',
    templateUrl: './player-view-side-panel-scorm.component.html',
    styleUrls: ['./player-view-side-panel-scorm.component.scss'],
    standalone: false
})
export class PlayerViewSidePanelScormComponent {
  @Input() template: IInstanceTemplate;
  @Input() routeParams: IRouteParams;
  @Input() instance: IInstance;
  @Input() searchFilter: string;

  constructor() {}
}
