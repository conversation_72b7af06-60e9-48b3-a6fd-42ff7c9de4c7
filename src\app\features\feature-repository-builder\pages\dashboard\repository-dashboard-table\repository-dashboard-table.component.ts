import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { Sort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { DashboardGridRow, IComponent, IFeature, IOrganizationStatusType, ISystemPropertyValue } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { OverlayEventDetail } from '@app/features-shared/row-instance/components/content/grid-view/thumbnail-styles/styles/image-background/image-background.component';
import { RepositoryDashboardEditorDialogComponent } from '@app/features/feature-repository-builder/pages/dashboard/repository-dashboard-editor-dialog/repository-dashboard-editor-dialog.component';
import { ConfirmationDialogComponent } from '@app/standalone/modals/confirmation-dialog/confirmation-dialog.component';
import { UserOrganizationsModalComponent } from '@app/standalone/modals/user-organizations-modal/user-organizations-modal.component';
import { environment } from '@env/environment';
import { ModalController } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-repository-dashboard-table',
    templateUrl: './repository-dashboard-table.component.html',
    styleUrls: ['./repository-dashboard-table.component.scss'],
    standalone: false
})
export class RepositoryDashboardTableComponent implements OnInit, OnDestroy {
  @Input() dataSourceIn: DashboardGridRow[];
  @Input() columnsToDisplayIn: string[];
  @Input() feature: IFeature;
  @Input() loading: boolean;
  @Output() routeOnClick = new EventEmitter<string>();
  @Output() loadMoreData = new EventEmitter<any>();
  @Output() refreshData = new EventEmitter<any>();
  @Output() sortChange = new EventEmitter<Sort>();
  dataSource = new MatTableDataSource<DashboardGridRow>();
  componentDestroyed$: Subject<boolean> = new Subject();
  formGroup: UntypedFormGroup;
  assetUrl: string | null;
  scrollTop: number;

  orgStatuses: IOrganizationStatusType[];
  constructor(
    private modalController: ModalController,
    private dataService: DataService,
    private globalToast: GlobalToastService
  ) {}

  ngOnInit(): void {
    this.initData();
  }

  checkColumnIsSystemProperty(name: string) {
    const element = this.dataSourceIn[0].columns.find(x => x.columnName === name);

    if (element && element?.component?.templateField?.systemProperty) {
      return true;
    }

    return false;
  }

  onSortChange(value: Sort) {
    const element = this.dataSourceIn[0].columns.find(x => x.columnName === value.active);

    if (element && element?.component?.templateField?.systemProperty) {
      const systemPropertyLink = element.component.templateField.systemProperty.property;
      value.active = systemPropertyLink.substring(systemPropertyLink.indexOf('.') + 1);
    }

    this.sortChange.next(value);
  }

  routeOut(rowId: string) {
    this.routeOnClick.emit(rowId);
  }

  loadMoreEmit(event: any) {
    this.loadMoreData.emit(event);
  }

  initData() {
    this.dataService
      .getOrganizationStatusTypes()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.orgStatuses = data;
      });
  }

  previousScrollTop = 0;

  scrolled(event: any) {
    //In chrome and some browser scroll is given to body tag
    const pos = event.target.scrollTop + event.target.offsetHeight;
    const max = event.target.scrollHeight;    
    // pos/max will give you the distance between scroll bottom and and bottom of screen in percentage.    
    if (pos >= max && this.previousScrollTop != event.target.scrollTop) {
      //Do your action here
      this.loadMoreEmit(event);
    }
    this.previousScrollTop = event.target.scrollTop;
  }

  async editDashboardComponent(tableId: string, textValue: string, component: IComponent, id?: string) {
    // Org Status gets changed on the table itself......
    if (component?.templateField?.dropDownLinkType?.title === 'Organization Status') {
      return;
    }

    if (component?.templateField.isBuilderEnabled) {
      const modal = await this.modalController.create({
        component: RepositoryDashboardEditorDialogComponent,
        componentProps: { component: component, textValue: textValue, id: tableId },
        cssClass: 'repo-dashboard-dialog',
      });

      modal.onDidDismiss().then(value => {
        if (value.data) {
          if (value.data === 'imageRemoved' || value.data === 'userTagsUpdated') {
            //Image Asset Id Has Been Removed From Instance Comp From Upload Control.
            this.refreshData.emit();
          } else {
            this.updateComponent(tableId, value.data, component, id);
          }
        }
      });

      await modal.present();
    }
  }

  updateComponent(tableId: string, value: any, component: IComponent, id?: string) {
    if (value?.value != null) {
      value = value.value;
    }

    //SystemProperty
    if (component?.templateField?.systemProperty) {
      const systemProperty = component.templateField.systemProperty;
      const propertyValue: ISystemPropertyValue = {
        id: systemProperty.id,
        key: systemProperty.property,
        value: value,
      };

      this.dataService
        .setSystemPropertyContextValues(tableId, systemProperty.type.typeBw, [propertyValue])
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(result => {
          if (result) {
            this.refreshData.emit();
          }
        });
    }

    if (component?.templateField?.dropDownLinkType?.title === 'Campaign User Tags') {
      this.dataService
        .updateCampaignTags(value, id)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(result => {
          if (result) {
            this.refreshData.emit();
          }
        });
    }

    if (component?.templateField?.isTag) {
      this.refreshData.emit();
    }
  }

  async updateProductOrgUserRole(element: any) {
    const firstName = element.columns.filter((x: { columnName: string }) => x.columnName === 'First Name (*)')[0].value;
    const lastName = element.columns.filter((x: { columnName: string }) => x.columnName === 'Last Name (*)')[0].value;
    const userId = element.userId;
    const modal = await this.modalController.create({
      component: UserOrganizationsModalComponent,
      cssClass: 'user-organizations-modal',
      componentProps: { userId: userId, organizationId: '00000000-0000-0000-0000-000000000000', featureType: 'User Repository', name: `${firstName} ${lastName}` },
      backdropDismiss: false,
    });
    await modal.present();
  }

  setImageUrl(id: string) {
    return `${environment.contentUrl}asset/${id}/content`;
  }

  resetPassword(userId: string) {
    const id = JSON.stringify(userId);
    this.dataService
      .specificUserPasswordReset(id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.globalToast.presentNotificationToast('Password Reset Successfully', false, false);
      });
  }

  async removeTagById(event: any, element: any, component: IComponent, chip?: any) {
    if (component?.templateField?.dropDownLinkType?.title === 'Campaign User Tags' || component?.templateField?.dropDownLinkType?.title === 'User Tags') {
      const modal = await this.modalController.create({
        component: ConfirmationDialogComponent,
        cssClass: 'confirm-dialog',
        componentProps: {
          headerText: 'Remove User Tag',
          bodyText: `Are you sure you would like to remove ${event.name}?`,
          buttonText: 'Remove',
        },
      });

      modal.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
        if (overlayEventDetail.role === 'confirm') {
          this.dataService
            .deleteUserCampaignTag(event.id, element.userId)
            .pipe(takeUntil(this.componentDestroyed$))
            .subscribe(result => {
              if (result) {
                chip?.remove();
                this.refreshData.emit();
              }
            });
        }
      });
      await modal.present();    
  }    
    else if(component?.templateField?.dropDownLinkType?.title === 'Tags')
    {
    const modal = await this.modalController.create({
      component: ConfirmationDialogComponent,
      cssClass: 'confirm-dialog',
      componentProps: {
        headerText: 'Remove Instance Tag',
        bodyText: `Are you sure you would like to remove ${event.name}?`,
        buttonText: 'Remove',
      },
    });

    modal.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
      if (overlayEventDetail.role === 'confirm') {
        this.dataService
          .deleteInstanceTag(event.id, element.tableId, component.id)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(result => {
            if (result) {
              this.refreshData.emit();
            }
          });
      }
    });
    await modal.present();  
  }  
}


  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
