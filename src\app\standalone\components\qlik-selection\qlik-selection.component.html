<div class="parent-container" [style]="(qlikElement?.lastElementChild?.lastElementChild?.children?.length ?? 0 > 0) ? 'display: flex' : 'display: none'">
  <qlik-initial-selections app-id="6c11698a-1745-41d9-ae8b-134c1b2d11b2" clear-all="true">
    <field [attr.name]="filterType" [attr.match]="id"></field>
    <field name="Event Date" [attr.match]="date"></field>
  </qlik-initial-selections>
  <div class="parent-container" #qlikElement>
    @if (componentType === 'sheets' && showComponent === true) {
      <qlik-embed ui="analytics/sheet" app-id="6c11698a-1745-41d9-ae8b-134c1b2d11b2" [attr.object-id]="componentId" context:json="{interactions:{select: false, edit: false}}"></qlik-embed>
    }
    @if (componentType === 'components' && showComponent === true) {
      <qlik-embed ui="analytics/chart" app-id="6c11698a-1745-41d9-ae8b-134c1b2d11b2" [attr.object-id]="componentId" context:json="{interactions:{select: false, edit: false}}"></qlik-embed>
    }
  </div>
</div>

@if (!(qlikElement?.lastElementChild?.lastElementChild?.children?.length ?? 0 > 0)) {
  <div class="spinner">
    <div class="spinner-inner">
      <mat-spinner mode="indeterminate"></mat-spinner>
    </div>
  </div>
}
