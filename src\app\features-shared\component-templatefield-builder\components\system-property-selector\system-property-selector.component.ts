import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AbstractControl, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ISystemProperty, ITemplateField } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { debounceTime, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-system-property-selector',
    templateUrl: './system-property-selector.component.html',
    styleUrls: ['./system-property-selector.component.scss'],
    standalone: false
})
export class SystemPropertySelectorComponent implements OnInit, OnDestroy {
  @Input() templateField: ITemplateField;
  @Input() formGroup: UntypedFormGroup;
  @Input() isParentSystemPropertyLinkField = false;
  @Output() propertyChanged = new EventEmitter();
  featureId?: string;
  systemProperties: ISystemProperty[];
  searchString: string;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private dataService: DataService, private activatedRoute: ActivatedRoute) {}

  get systemPropertyControl(): AbstractControl {
    return this.isParentSystemPropertyLinkField ? this.formGroup.controls.parentIdSystemPropertyLink : this.formGroup.controls.systemProperty;
  }

  ngOnInit() {
    this.activatedRoute.params.pipe(takeUntil(this.componentDestroyed$)).subscribe(params => {
      this.featureId = params['id'];
    });
    this.getSystemProperties();
  }

  getSystemProperties() {
    this.dataService
      .getSystemProperties(this.searchString, this.featureId ?? "")
      .pipe(debounceTime(1000), takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        if (data) {
          this.systemProperties = data;
        }
      });
  }

  setPropertySelected(propertyId: string) {
    if (this.isParentSystemPropertyLinkField) {
      this.setParentSystemPropertySelected(propertyId);
    } else {
      this.setSystemPropertySelected(propertyId);
    }
  }

  setSystemPropertySelected(propertyId: string) {
    if (this.templateField?.systemProperty?.id === propertyId) {
      this.systemPropertyControl.setValue(undefined);
      this.templateField.systemProperty = undefined;
    } else {
      this.systemPropertyControl.setValue(this.systemProperties.find(x => x.id === propertyId)?.id);
      this.templateField.systemProperty = this.systemProperties.find(x => x.id === propertyId);
    }
  }

  setParentSystemPropertySelected(propertyId: string) {
    if (this.templateField?.parentIdSystemPropertyLink?.id === propertyId) {
      this.systemPropertyControl.setValue(undefined);
      this.templateField.parentIdSystemPropertyLink = undefined;
    } else {
      this.systemPropertyControl.setValue(this.systemProperties.find(x => x.id === propertyId)?.id);
      this.templateField.parentIdSystemPropertyLink = this.systemProperties.find(x => x.id === propertyId);
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
