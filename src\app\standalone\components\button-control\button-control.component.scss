.parent-container {
  margin: 8px;

  .card-container {
    background-color: #2d2e32;
    padding: 10px;

    .styling-container {
      width: 100%;
      border-top: 2px solid #1d1d1d;
      color: #ffffff;
      font-family: '<PERSON>o';
      font-weight: bold;

      .styling-heading {
        font-size: 16px !important;
      }

      .styles-container {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        gap: 5px;

        .text-container {
          flex: 1;
          border-radius: 5px;
          justify-content: center;
          display: flex;
          padding: 20px 10px;
          cursor: pointer;
        }

        .selected {
          outline: 2px solid #f99e00;
          border-radius: 8px;
        }
      }
    }
  }
}

.side-panel-input-padding {
  margin: 0px !important;

  ion-card {
    margin: 0px !important;
  }
}