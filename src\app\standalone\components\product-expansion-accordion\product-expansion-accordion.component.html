<div class="parent-container">
  @if (featureType === 'Product Manager' || hasChanges || (featureType === 'Organization Manager' && isEfAdmin)) {
    <ion-row class="view-options-row">
      <ion-col size="12" class="end-col-buttons">
        @if (featureType === 'Product Manager') {
          <ion-button (click)="addOrgToProduct()" color="warning">Add</ion-button>
        }
        @if (hasChanges) {
          <ion-button (click)="saveProductSettings()" color="warning">Save</ion-button>
        }
        @if (featureType === 'Organization Manager' && isEfAdmin) {
          <ion-button (click)="addProductToOrg()" color="warning">Add Products</ion-button>
        }
      </ion-col>
    </ion-row>
  }

  <mat-accordion multi>
    @for (productOrg of productOrganizations; track productOrg) {
      <mat-expansion-panel (opened)="openGroup(true)" (closed)="openGroup(false)">
        <mat-expansion-panel-header class="expansion-panel-header">
          <ion-col size="9">
            <div class="inner-panel">
              @if (productOrg.isJoinCodeProduct === true) {
                <mat-chip-listbox [selectable]="false">
                  @if (productOrg.isJoinCodeProduct && productOrg.orgUserRoleName !== 'Learner') {
                    <mat-chip-option>
                      <app-join-code [joinCode]="productOrg.instructorJoinCode" [roleName]="'INSTRUCTOR'"></app-join-code>
                    </mat-chip-option>
                  }
                  @if (productOrg.isJoinCodeProduct) {
                    <mat-chip-option>
                      <app-join-code [joinCode]="productOrg.joinCode" [roleName]="'LEARNER'"></app-join-code>
                    </mat-chip-option>
                  }
                </mat-chip-listbox>
              }
              <div class="heading">
                {{ featureType === 'Organization Manager' || featureType === 'User Manager' ? productOrg.productName : productOrg.orgName }}
              </div>
              @if (featureType === 'Organization Manager' || featureType === 'User Manager') {
                <div class="sub-heading sub-heading-margin">
                  <span
                    >Managed by:
                    <span class="bold-white">{{ productOrg.networkName ?? productOrg.orgName }}</span>
                  </span>
                </div>
              }
            </div>
            <div class="role-heading">
              @if (productOrg.orgUserRoleName) {
                <span>
                  {{ productOrg.orgUserRoleName }}
                </span>
              }
            </div>
          </ion-col>
          @if (featureType === 'Organization Manager' && productOrg.isNetworkProduct !== true) {
            <ion-col size="3" class="dlt-button-col">
              <ion-button color="primary" (click)="removeProduct(productOrg); $event.stopPropagation()">Expire</ion-button>
            </ion-col>
          }
        </mat-expansion-panel-header>
        <div style="cursor: pointer; margin-bottom: 10px" class="sub-heading">
          @if (productOrg.period) {
            <span style="font-weight: bold">{{ productOrg.period | uppercase }}, </span>
          }
          {{ subscriptionStatus(productOrg.expiryDate) }} on
          <span class="expiry-date">{{ productOrg.expiryDate | date: 'MMM d, y' }}</span>
        </div>
        <ng-template matExpansionPanelContent>
          <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start">
            <mat-tab label="ABOUT">
              <ng-template matTabContent>
                <app-product-details
                  [productTabType]="productTabType.About"
                  [productId]="productOrg.productId"
                  [organizationId]="id"
                  [productJoinCodeSettings]="productOrg.productJoinCodeSettings"
                  [isJoinCodeProduct]="false"
                  [joinCode]="productOrg.joinCode"></app-product-details>
              </ng-template>
            </mat-tab>
            @if (hasAccess) {
              <mat-tab label="SETTINGS">
                <ng-template matTabContent>
                  <app-product-details
                    (productOrgDomainsChange)="saveProductOrgDomains($event)"
                    (orgSsoAuthChange)="saveOrgSsoAuth($event)"
                    (productJoinCodesChange)="saveProductJoinCodes()"
                    [productTabType]="productTabType.Settings"
                    [productOrgId]="productOrg.id"
                    [productId]="productOrg.productId"
                    [privacyTypeId]="productOrg.privacyTypeId"
                    [organizationId]="productOrg.orgId"
                    [productJoinCodeSettings]="productOrg.productJoinCodeSettings"
                    [isJoinCodeProduct]="productOrg.isJoinCodeProduct"
                    [joinCode]="productOrg.joinCode"></app-product-details>
                </ng-template>
              </mat-tab>
            }
            @if (hasAccess) {
              <mat-tab label="USERS">
                <ng-template matTabContent>
                  <app-users-table [id]="productOrg.id" [productOrg]="productOrg" [name]="productOrg.orgName" [type]="'Product Manager'"></app-users-table>
                </ng-template>
              </mat-tab>
            }
            @if (hasAccess) {
              <mat-tab label="HISTORY">
                <ng-template matTabContent>
                  <app-organizations-history
                    [expiryDate]="productOrg.expiryDate"
                    [productOrganizationId]="productOrg.id"
                    [productId]="productOrg.productId"
                    [orgId]="productOrg.orgId"
                    [featureType]="'Product Manager'"
                    [isNetworkProduct]="productOrg.isNetworkProduct">
                  </app-organizations-history>
                </ng-template>
              </mat-tab>
            }
          </mat-tab-group>
        </ng-template>
      </mat-expansion-panel>
    }
    @if (moreResults && !isParentPanelClosed) {
      <div (click)="getProductOrganizationsById(id, true, searchFilter)" class="load-more">
        <ion-row>
          <ion-col size="12">
            <div>Load More</div>
            <div><ion-icon name="chevron-down-outline"></ion-icon></div>
          </ion-col>
        </ion-row>
      </div>
    }
  </mat-accordion>
</div>
