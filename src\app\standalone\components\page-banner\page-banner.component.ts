import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Changes, OnInit } from '@angular/core';
import { IRouteParams, ITemplateField } from '@app/core/contracts/contract';
import { ViewType } from '@app/core/enums/view-type';
import { DataService } from '@app/core/services/data-service';
import { ParseService } from '@app/core/services/parse-service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { NgClass, NgStyle } from '@angular/common';
import { HeaderImageBaseComponent } from '../header-image-base/header-image-base.component';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
  selector: 'app-page-banner',
  templateUrl: './page-banner.component.html',
  styleUrls: ['./page-banner.component.scss'],
  imports: [<PERSON><PERSON><PERSON>, <PERSON><PERSON>ty<PERSON>],
})
export class PageBanner<PERSON>omponent extends HeaderImageBaseComponent implements OnInit, OnDestroy, OnChanges {
  @Input() routeParams: IRouteParams;
  gradientStyle!: string;
  templateField: ITemplateField | undefined;
  heightPx: number | undefined;
  viewTypes = ViewType;
  isMobile: boolean = false;
  override height: number = 800;

  constructor(
    builderService: BuilderService,
    parseService: ParseService,
    dataService: DataService,
    systemPropertiesService: SystemPropertiesService,
    public layoutService: LayoutService
  ) {
    super(parseService, builderService, dataService, systemPropertiesService);
    this.derivedComponentType = 'page-banner';
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['inheritedPropertyValue']) {
      this.setImageUrl();
    }
  }

  ngOnInit() {
    this.isMobile = this.layoutService.currentScreenSize === 'xs';
    this.templateField = this.instanceSectionComponent?.component?.templateField;
    this.setImageUrl();
  }

  async setImageUrl() {
    this.setIconAndCoverUrl();
  }
}
