import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { IInstance } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { debounceTime, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-component-achievements-field',
    templateUrl: './component-achievements-field.component.html',
    styleUrls: ['./component-achievements-field.component.scss'],
    standalone: false
})
export class ComponentAchievementsFieldComponent implements OnInit, OnDestroy {
  @Input() selectedAchievementIds: string[] = [];
  @Output() selectionChanged = new EventEmitter();
  query: string;
  instances: IInstance[];
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.search();
  }

  checkboxChanged($event: any, instanceId: string) {
    if ($event?.srcElement?.checked) {
      this.selectedAchievementIds.push(instanceId);
    } else {
      const index = this.selectedAchievementIds.indexOf(instanceId);
      this.selectedAchievementIds.splice(index, 1);
    }
    this.selectionChanged.emit();
  }

  isSelected(instanceId: string) {
    return this.selectedAchievementIds ? this.selectedAchievementIds.findIndex(x => x === instanceId) !== -1 : false;
  }

  search() {
    this.dataService
      .getInstances(this.query)
      .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
      .subscribe(data => {
        this.instances = data;
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
