.parent-container {
  ion-row {
    ion-col {
      display: flex;
      align-items: center;
    }

    .heading-col {
      padding: 10px;
    }
    .col-padding {
      padding-right: 10px;
    }

    span {
      padding-left: 4px;
    }

    .value {
      ion-input {
        border: 1px solid #4e4e4e;
        border-radius: 5px;
        color: white;
        font-size: 16px;
        --background: #242323;
        text-overflow: ellipsis !important;
      }
    }
  }
}
