export class UserOrgProdRoles {
  userOrganizationProductRoles: UserOrganizationProductRole[];
  constructor() {
    this.userOrganizationProductRoles = [
      { id: '278bbbe6-036b-4137-b4e0-4ae65c6b0e6a', name: 'Administrator', isSelected: false },
      { id: '0adacbd2-22cb-484b-9184-e27fd4f93bc1', name: 'ContentDeveloper', isSelected: false },
      { id: '695e0199-1ba9-421a-8f54-7160f9f8b1b4', name: 'Instructor', isSelected: false },
      { id: '52dc3b0e-4d9e-40fc-adf5-40e813f7a055', name: 'Learner', isSelected: false },
    ];
  }
}

export class UserOrganizationProductRole {
  id: string;
  name: string;
  isSelected: boolean;
}
