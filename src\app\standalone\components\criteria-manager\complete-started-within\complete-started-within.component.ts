import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { FormBuilder, UntypedFormGroup } from '@angular/forms';
import { IEarningCriteria, IEarningCriteriaIn } from '@app/core/contracts/contract';
import { IonicModule } from '@ionic/angular';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-complete-started-within',
    templateUrl: './complete-started-within.component.html',
    styleUrls: ['./complete-started-within.component.scss'],
    imports: [IonicModule]
})
export class CompleteStartedWithinComponent implements OnDestroy {
  @Input() type: string;
  @Input() earningCriteria: IEarningCriteria;
  @Output() criteriaUpdated: EventEmitter<IEarningCriteriaIn> = new EventEmitter();

  earningCriteriaIn: IEarningCriteriaIn;

  formValueChanges$: Subscription;
  completeStartedWithinForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private formBuilder: FormBuilder) {}

  createForm() {
    this.earningCriteriaIn = { ...this.earningCriteria } as IEarningCriteriaIn;
    this.completeStartedWithinForm = this.formBuilder.group({
      minValue: [this.earningCriteria?.minValue],
      refId: [this.earningCriteria.earningCriteriaContent?.[0]?.refId],
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.completeStartedWithinForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setObjectValues();
    });
  }

  setObjectValues() {
    if (this.completeStartedWithinForm.valid) {
      if (this.completeStartedWithinForm.controls['minValue'].value) {
        this.earningCriteriaIn.minValue = this.completeStartedWithinForm.controls.minValue.value;
        this.criteriaUpdated.emit(this.earningCriteriaIn);
      }
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
