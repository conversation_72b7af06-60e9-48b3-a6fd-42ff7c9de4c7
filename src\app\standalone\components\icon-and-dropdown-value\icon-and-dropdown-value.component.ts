import { Component, Input } from '@angular/core';
import { IInstance, ITemplateField, IInstanceSectionComponent } from '@app/core/contracts/contract';
import { environment } from '@env/environment';
import { IonicModule } from '@ionic/angular';
import { SelectOptionValueComponent } from '../select-option-value/select-option-value.component';
import { TagValuesComponent } from '../tag-values/tag-values.component';

@Component({
    selector: 'app-icon-and-dropdown-value',
    templateUrl: './icon-and-dropdown-value.component.html',
    styleUrls: ['./icon-and-dropdown-value.component.scss'],
    imports: [IonicModule, SelectOptionValueComponent, TagValuesComponent]
})
export class IconAndDropdownValueComponent {
  @Input() instance: IInstance;
  @Input() templateField: ITemplateField | undefined;
  @Input() instanceSectionComponent: IInstanceSectionComponent | undefined;
  @Input() inheritedPropertyValue: string | null;
  constructor() {}

  get componentId() {
    return this.instanceSectionComponent?.component.id ?? '';
  }

  setIcon() {
    if (this.templateField?.defaultImageUrl) {
      return `${environment.contentUrl}asset/${this.templateField?.defaultImageUrl}/content`;
    }
    return 'assets/images/no-image.png';
  }
}
