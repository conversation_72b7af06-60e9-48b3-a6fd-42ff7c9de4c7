<!-- NB! THIS RETURNS A FORM CONTROL COMPONENT USED FOR EDITING -->
@if (showPlaceHolder === true) {
  <div class="static-container">
    <app-authoring-header [componentName]="component.componentType.name"></app-authoring-header>
  </div>
} @else {
  @if (component.rowId || component.templateField) {
    <div [formGroup]="formGroup">
      @if (component?.componentType?.parentTypeName === 'Question') {
        <!-- True or False -->
        <!-- Yes or No -->
        <!-- Multiple Choice -->
        <!-- Short Answer -->
        <!-- Matching List -->
        <!-- Picture Matching -->
        <!-- Text to Picture -->
        <ng-container [formGroupName]="formGroupName">
          @if (viewType === 3) {
            <app-authoring-header [componentName]="component.componentType.name"></app-authoring-header>
          } @else {
            <app-question-builder
              [component]="component"
              [formControlName]="component.id"
              [question]="component.question"
              (questionUpdated)="updateQuestionValue($event)"
              [onlyQuestion]="true"></app-question-builder>
          }
        </ng-container>
      } @else {
        @switch (component?.componentType?.name) {
          <!-- User Risec Score Chart -->
          @case ('User RIASEC Score Chart') {
            <app-authoring-header [componentName]="component?.componentType?.name ?? 'User RIASEC Score Chart'"></app-authoring-header>
          }
          <!-- ROW COMPONENT -->
          @case ('Default Row') {
            <app-row-instance
              [builderView]="rowBuilderView === true || viewType === 3"
              [readingMode]="rowBuilderView !== true && viewType !== 3"
              [routeParams]="routeParams"
              [rowId]="component.rowId"
              [instance]="instance"></app-row-instance>
          }
          @case ('Smart Row') {
            @if (component.rowId) {
              <app-row-instance
                [builderView]="rowBuilderView === true || viewType === 3"
                [readingMode]="viewType === 1"
                [routeParams]="routeParams"
                [rowId]="component.rowId"
                [instance]="instance"></app-row-instance>
            }
          }
          <!-- TEXT INPUT -->
          @case (component?.componentType?.name === 'Text' || component?.componentType?.name === 'Website Link' ? component?.componentType?.name : '') {
            <ng-container [formGroupName]="formGroupName">
              <app-text-input-control
                [component]="component"
                [sidePanelPadding]="isSidePanelBuilder"
                [noBorder]="isSidePanelBuilder"
                [backgroundColor]="controlBackground"
                [toolTip]="component?.templateField?.toolTip ?? ''"
                [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                [label]="component?.templateField?.label ?? ''"
                [formControlName]="component.id"
                [showSelected]="showSelected"
                [defaultValue]="component?.templateField?.defaultText"
                [itemBackgroundColor]="controlBackground"></app-text-input-control>
            </ng-container>
          }
          <!-- WYSIWYG -->
          @case ('WYSIWYG') {
            <ng-container [formGroupName]="formGroupName">
              <app-dynamic-text-input-control
                [component]="component"
                [sidePanelPadding]="isSidePanelBuilder"
                [noBorder]="isSidePanelBuilder"
                [noPadding]="isSidePanelBuilder"
                [toolTip]="component?.templateField?.toolTip ?? ''"
                [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                [label]="component?.templateField?.label ?? ''"
                [formControlName]="component.id"
                [defaultValue]="component?.templateField?.defaultText"
                [showSelected]="showSelected"
                [itemBackgroundColor]="controlBackground">
              </app-dynamic-text-input-control>
            </ng-container>
          }
          <!-- LABEL FIELD -->
          @case ('Label') {
            <ng-container [formGroupName]="formGroupName">
              <app-text-input-control
                [component]="component"
                [sidePanelPadding]="isSidePanelBuilder"
                [noBorder]="isSidePanelBuilder"
                [backgroundColor]="'#1E1E1E'"
                [toolTip]="component?.templateField?.toolTip ?? ''"
                [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                [label]="component?.templateField?.label ?? ''"
                [formControlName]="component.id"
                [showSelected]="showSelected"
                [defaultValue]="component?.templateField?.defaultText"
                [itemBackgroundColor]="controlBackground">
              </app-text-input-control>
            </ng-container>
          }
          <!-- TEXT AREA -->
          @case (component?.componentType?.name === 'Text Area' || component?.componentType?.name === 'Feedback Block' ? component?.componentType?.name : '') {
            <ng-container [formGroupName]="formGroupName">
              <app-text-area-input-control
                [sidePanelPadding]="isSidePanelBuilder"
                [noBorder]="isSidePanelBuilder"
                [showSelected]="showSelected"
                [backgroundColor]="controlBackground"
                [toolTip]="component?.templateField?.toolTip ?? ''"
                [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                [label]="component?.templateField?.label ?? ''"
                [formControlName]="component.id">
              </app-text-area-input-control>
            </ng-container>
          }
          <!--DROP DOWN SELECTORS-->
          @case (component?.componentType?.name?.indexOf('Dropdown') !== -1 ? component.componentType.name : '') {
            @if (component?.templateField?.isTag !== true) {
              <!--SELECTSINGLEOPTIONS-->
              @if (
                component?.templateField?.dropDownLinkType?.title !== 'Tags' &&
                component?.templateField?.dropDownLinkType?.title !== 'User Tags' &&
                component?.templateField?.dropDownLinkType?.title !== 'Campaign User Tags'
              ) {
                <div [formGroupName]="formGroupName">
                  @if (selectOptions) {
                    <app-select-option-control
                      [sidePanelPadding]="isSidePanelBuilder"
                      [toolTip]="component?.templateField?.toolTip ?? ''"
                      [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                      [label]="component?.templateField?.label ?? ''"
                      [textValue]="getFormControlValue()"
                      [formControlName]="component.id"
                      [backgroundColor]="controlBackground"
                      [options]="selectOptions"
                      [linkTypeName]="component?.templateField?.dropDownLinkType?.title"
                      [showSelected]="showSelected">
                    </app-select-option-control>
                  }
                  @if (!selectOptions) {
                    <app-select-option-control
                      [sidePanelPadding]="isSidePanelBuilder"
                      [toolTip]="component?.templateField?.toolTip ?? ''"
                      [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                      [label]="component?.templateField?.label ?? ''"
                      [textValue]="getFormControlValue()"
                      [formControlName]="component.id"
                      [backgroundColor]="controlBackground"
                      [options]="selectOptions"
                      [linkTypeName]="component?.templateField?.dropDownLinkType?.title"
                      [showSelected]="showSelected">
                    </app-select-option-control>
                  }
                </div>
              } @else {
                <div [formGroupName]="formGroupName">
                  <app-tag-select-option-control
                    [dropDownLinkType]="component?.templateField?.dropDownLinkType?.title ?? ''"
                    [sidePanelPadding]="isSidePanelBuilder"
                    [toolTip]="component?.templateField?.toolTip ?? ''"
                    [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                    [label]="component?.templateField?.label ?? ''"
                    [textValue]="getFormControlValue()"
                    [backgroundColor]="controlBackground"
                    [formControlName]="component.id"
                    [viewType]="viewType"
                    [limitTo]="component?.templateField?.limitTo ?? 1"
                    [options]="selectOptions"
                    [selectedOptions]="selectedUserOptions"
                    [showSelected]="showSelected"
                    (userTagsUpdated)="setFormControlValue($event)">
                  </app-tag-select-option-control>
                </div>
              }
              <!--SELECTTAGOPTIONS-->
            } @else {
              <!-- ISTAG DROPDOWN -->
              <ng-container [formGroupName]="formGroupName">
                <app-tag-popover
                  [component]="component"
                  [selectedUserId]="userId"
                  [dropDownLinkType]="component.templateField.dropDownLinkType?.title ?? ''"
                  [instanceId]="instanceId"
                  [viewType]="viewType"
                  [campaignId]="campaignId"
                  [featureTypeName]="instance?.feature?.featureType?.name">
                </app-tag-popover>
              </ng-container>
            }
          }
          <!------->
          <!-- USER PERSONA CHIPLIST -->
          @case ('Persona Selector') {
            <ng-container [formGroupName]="formGroupName">
              <app-persona-tag-popover [selectedUserId]="userId" [component]="component"> </app-persona-tag-popover>
            </ng-container>
          }
          <!------->
          <!-- PASSWORD -->
          @case ('Password') {
            <app-password-control></app-password-control>
          }
          <!-- Row Manager -->
          @case ('Row Manager') {
            <ng-container [formGroupName]="formGroupName">
              <div [ngClass]="viewType === 3 ? 'static-container' : ''">
                @if (viewType === 3) {
                  <h3>ROW MANAGER</h3>
                }
                @if (viewType !== 3) {
                  <!-- <app-achievement-bank [component]="component"></app-achievement-bank> -->
                }
              </div>
            </ng-container>
          }
          <!-- Network QlikView PDF Download -->
          @case ('Network QlikView PDF Download') {
            <ng-container [formGroupName]="formGroupName">
              <div [ngClass]="viewType === 3 ? 'static-container' : ''">
                @if (viewType === 3) {
                  <h3>Network QlikView PDF Download</h3>
                }
              </div>
            </ng-container>
          }
          <!-- FILE UPLOAD & DOWNLOAD BLOCK & MEDIA BLOCK -->
          @case (
            component?.componentType?.name === 'Image Upload Field' ||
            component?.componentType?.name === 'Download Block' ||
            component?.componentType?.name === 'Page Banner' ||
            component?.componentType?.name === 'HTML5 Game'
              ? component?.componentType?.name
              : ''
          ) {
            <ng-container [formGroupName]="formGroupName">
              <app-file-upload-control
                [component]="component"
                [label]="component?.templateField?.label ?? ''"
                [itemBackgroundColor]="controlBackground"
                [fileFormat]="'/*/'"
                [containerName]="component?.componentType?.name === 'HTML5 Game' ? 'html5' : 'static'"
                [fileTypeBw]="component?.templateField?.fileTypeBw"
                [minFileSize]="component?.templateField?.minFileSize"
                [maxFileSize]="component?.templateField?.maxFileSize"
                [componentType]="component?.componentType?.name"
                [formControlName]="component.id"
                [defaultImageUrl]="component?.templateField?.defaultImageUrl ?? ''"
                [buttonText]="component?.templateField?.buttonText ?? ''"
                [placeHolderText]="component?.templateField?.placeHolderText ?? ''"></app-file-upload-control>
            </ng-container>
          }
          <!-- MEDIA BLOCK -->
          @case ('Media Block') {
            <ng-container [formGroupName]="formGroupName">
              <app-media-block-control
                [component]="component"
                [formControlName]="component.id"
                [sidePanelPadding]="isSidePanelBuilder"
                [label]="component?.templateField?.label ?? ''"
                (controlValueChanged)="setFormControlValue($event)"
                [mediaFormGroup]="formGroup"
                [mediaId]="mediaId ?? getFormControlValue()"
                [controlBackground]="controlBackground"></app-media-block-control>
            </ng-container>
          }
          <!-- EMAIL CHIPS -->
          @case ('Email Chips') {
            <ng-container [formGroupName]="formGroupName">
              <app-email-chip [sidePanelPadding]="isSidePanelBuilder" [formControlName]="component.id" [id]="id"> </app-email-chip>
            </ng-container>
          }
          <!-- PRODUCTS -->
          <!-- PRODUCT SETTINGS -->
          @case ('Product Settings') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>PRODUCT SETTINGS</h3>
              }
              @if (viewType !== 3) {
                <app-product-expansion-accordion [featureType]="instance.feature.featureType?.name ?? ''" [id]="id"> </app-product-expansion-accordion>
              }
            </div>
          }
          <!-- PRODUCT HISTORY -->
          @case ('Product History') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>PRODUCT HISTORY</h3>
              }
              @if (viewType !== 3) {
                <app-product-history [featureType]="instance.feature.featureType?.name ?? ''" [id]="id"> </app-product-history>
              }
            </div>
          }
          <!-- PRODUCT BILLING -->
          @case ('Product Billing') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>PRODUCT BILLING</h3>
              }
              @if (viewType !== 3) {
                <app-product-billing [id]="id"> </app-product-billing>
              }
            </div>
          }
          <!--PRODUCT ACCESS/ACTIONS-->
          @case ('Product Actions') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>PRODUCT ACTIONS</h3>
              } @else {
                <app-product-action-roles [type]="instance.feature.featureType?.name ?? ''" [id]="id"> </app-product-action-roles>
              }
            </div>
          }
          <!--USER TABLE-->
          @case ('Users Table') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>USERS TABLE</h3>
              }
              @if (viewType !== 3 && instance.feature.featureType?.name !== 'User Manager') {
                <app-users-table [type]="instance.feature.featureType?.name ?? ''" [id]="id"> </app-users-table>
              }
            </div>
          }
          <!--PEOPLE TABLE-->
          @case ('People Table') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>PEOPLE TABLE</h3>
              } @else {
                <app-people-table [name]="instance.title" [id]="instance.id" [type]="instance.feature.featureType?.name"> </app-people-table>
              }
            </div>
          }
          <!--GRADING TABLE-->
          @case ('Grading Table') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>GRADING TABLE</h3>
              } @else {
                <app-grading-table [name]="instance.title" [id]="instance.id" [type]="instance.feature.featureType?.name"></app-grading-table>
              }
            </div>
          }
          <!-- ORGANIZATION NETWORKS -->
          @case ('Organization Networks') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>ORGANIZATION NETWORKS</h3>
              } @else {
                <app-network-organization-expansion-accordion [type]="instance.feature.featureType?.name ?? ''" [id]="id"> </app-network-organization-expansion-accordion>
              }
            </div>
          }
          <!-- NETWORK ORGANIZATIONS -->
          @case ('Network Organizations') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>NETWORK ORGANIZATIONS</h3>
              } @else {
                <app-network-organizations [type]="instance.feature.featureType?.name ?? ''" [id]="id"> </app-network-organizations>
              }
            </div>
          }
          <!-- NETWORK LICENSE MANAGER -->
          @case ('Network License Manager') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>Network License Manager</h3>
              } @else {
                <app-network-license-manager [featureType]="instance.feature.featureType?.name ?? ''" [id]="id"> </app-network-license-manager>
              }
            </div>
          }
          <!-- ORGANIZATIONS -->
          @case ('Organization') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>ORGANIZATIONS</h3>
              }
              @if (viewType !== 3) {
                <app-organization-expansion-control [id]="id" [featureType]="instance.feature.featureType?.name ?? ''"> </app-organization-expansion-control>
              }
            </div>
          }
          <!-- COMMUNICATION MANAGER -->
          @case ('Communication Manager') {
            <ng-container [formGroupName]="formGroupName">
              <div [ngClass]="viewType === 3 ? 'static-container' : ''">
                @if (viewType === 3) {
                  <h3>COMMUNICATION MANAGER</h3>
                } @else {
                  <app-communication-manager (communicationBlockUpdated)="setFormControlValue($event)" [communicationId]="id"> </app-communication-manager>
                }
              </div>
            </ng-container>
          }
          <!--Google Address Search-->
          @case ('Address Search') {
            <ng-container [formGroupName]="formGroupName">
              <app-google-maps-autocomplete
                [sidePanelPadding]="isSidePanelBuilder"
                [toolTip]="component?.templateField?.toolTip ?? ''"
                [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                [label]="component?.templateField?.label ?? ''"
                [formControlName]="component.id"
                [backgroundColor]="controlBackground"
                [sectionId]="formGroupName"
                [featureType]="instance?.feature?.featureType?.name"
                [showSelected]="showSelected">
              </app-google-maps-autocomplete>
            </ng-container>
          }
          <!-- Question Builder -->
          @case ('Question Builder') {
            <ng-container [formGroupName]="formGroupName">
              <span>Question Builder</span>
              <app-question-builder [question]="component.question" [formControlName]="component.id" [component]="component"></app-question-builder>
            </ng-container>
          }
          <!-- Instance Header -->
          @case ('Instance Header') {
            <ng-container [formGroupName]="formGroupName">
              <app-instance-header-control
                [instanceId]="instance.id"
                [instanceTitle]="instance?.title"
                [featureTitle]="instance?.feature?.title"
                [formControlName]="component.id"
                [itemBackgroundColor]="controlBackground"></app-instance-header-control>
            </ng-container>
          }
          <!-- Achievement -->
          @case ('Achievement') {
            <ng-container [formGroupName]="formGroupName">
              <app-achievement-control [achievementInstance]="instance" [formControlName]="component.id" [itemBackgroundColor]="controlBackground"></app-achievement-control>
            </ng-container>
          }
          <!-- EXTERNAL HTML BLOCK -->
          @case ('External Html Block') {
            <ng-container [formGroupName]="formGroupName">
              <div [ngClass]="viewType === 3 ? 'static-container' : ''">
                @if (viewType === 3) {
                  <h3>EXTERNAL HTML BLOCK</h3>
                }
                @if (viewType !== 3) {
                  <app-text-input-control
                    [component]="component"
                    [noBorder]="true"
                    [noPadding]="true"
                    [toolTip]="component?.templateField?.toolTip ?? ''"
                    [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                    [label]="component?.templateField?.label ?? ''"
                    [formControlName]="component.id"
                    [backgroundColor]="'#1E1E1E'"
                    [showSelected]="showSelected"></app-text-input-control>
                }
              </div>
            </ng-container>
          }
          <!-- Organization Settings -->
          @case ('Organization Settings') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>ORGANIZATION SETTINGS</h3>
              }
            </div>
          }
          <!-- Qlik Analytics -->
          @case ('Qlik Analytics') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>QLIK ANALYTICS</h3>
              }
            </div>
          }
          @case ('Qlik Analytics Type Selector') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>QLIK ANALYTICS</h3>
              }
            </div>
          }
          <!-- CRITERIA -->
          @case ('Criteria Manager') {
            <ng-container [formGroupName]="formGroupName">
              <div [ngClass]="viewType === 3 ? 'static-container' : ''">
                @if (viewType === 3) {
                  <h3>CRITERIA MANAGER</h3>
                } @else {
                  <app-criteria-manager-component
                    [featureType]="instance.feature.featureType.name"
                    [status]="instance.status"
                    (emitCriteriaUpdated)="setFormControlValue($event)"
                    [instanceId]="instance.id"></app-criteria-manager-component>
                }
              </div>
            </ng-container>
          }
          <!-- NOTIFICATION PREFERENCES -->
          @case ('Notification Preferences') {
            <ng-container class="page-margin">
              <div [ngClass]="viewType === 3 ? 'static-container' : ''">
                @if (viewType === 3) {
                  <h3>NOTIFICATION PREFERENCES</h3>
                } @else {
                  <app-user-notification-preferences></app-user-notification-preferences>
                }
              </div>
            </ng-container>
          }
          <!-- BANNER -->
          @case ('Banner') {
            <div [ngClass]="viewType === 3 ? 'static-container' : ''">
              @if (viewType === 3) {
                <h3>BANNER</h3>
              } @else {
                <app-banner [component]="component"></app-banner>
              }
            </div>
          }
          <!--AUTHORING TOOLS-->
          <!------->
          <!--HEADING-->
          @case ('Heading') {
            <ng-container [formGroupName]="formGroupName">
              <app-heading-control [component]="component" [formControlName]="component.id" [label]="component.templateField.label" [description]="component.templateField.helpDescription">
              </app-heading-control>
            </ng-container>
          }
          <!--PARAGRAPH-->
          @case ('Paragraph') {
            <ng-container [formGroupName]="formGroupName">
              <app-dynamic-text-input-control
                [component]="component"
                [hideQuillPersonalize]="true"
                [label]="'Paragraph'"
                [backgroundColor]="controlBackground"
                [formControlName]="component.id"
                [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                [defaultValue]="getFormControlValue()"
                [showSelected]="showSelected">
              </app-dynamic-text-input-control>
            </ng-container>
          }
          <!--VIDEO-->
          @case ('Video') {
            <ng-container [formGroupName]="formGroupName">
              <app-video [formControlName]="component.id" [label]="component.templateField.label" [description]="component.templateField.helpDescription"></app-video>
            </ng-container>
          }
          <!--EMBEDDEDURL-->
          @case ('Embedded URL') {
            <ng-container [formGroupName]="formGroupName">
              <app-embedded-url [formControlName]="component.id" [label]="component.templateField.label" [description]="component.templateField.helpDescription"></app-embedded-url>
            </ng-container>
          }
          <!--BULLETLIST-->
          @case ('Bullet List') {
            <ng-container [formGroupName]="formGroupName">
              <app-bullet-list-control
                [listType]="'Bullet List'"
                [sidePanelPadding]="isSidePanelBuilder"
                [formControlName]="component.id"
                [label]="component.templateField.label"
                [description]="component.templateField.helpDescription"
                [showSelected]="showSelected"></app-bullet-list-control>
            </ng-container>
          }
          <!--NUMBEREDBULLETLIST-->
          @case ('Numbered Bullet List') {
            <ng-container [formGroupName]="formGroupName">
              <app-bullet-list-control
                [listType]="'Numbered List'"
                [sidePanelPadding]="isSidePanelBuilder"
                [formControlName]="component.id"
                [label]="component.templateField.label"
                [description]="component.templateField.helpDescription"
                [showSelected]="showSelected"></app-bullet-list-control>
            </ng-container>
          }
          <!--TEXT&BUTTON-->
          @case ('Text & Button') {
            <ng-container [formGroupName]="formGroupName">
              @if (routeParams) {
                <app-text-and-button-control
                  [sidePanelPadding]="isSidePanelBuilder"
                  [formControlName]="component.id"
                  [label]="component.templateField.label"
                  [description]="component.templateField.helpDescription"
                  [component]="component"></app-text-and-button-control>
              }
              @if (!routeParams) {
                <app-authoring-header [componentName]="component.componentType.name"></app-authoring-header>
              }
            </ng-container>
          }
          <!--BUTTON-->
          @case ('Button') {
            <ng-container [formGroupName]="formGroupName">
              @if (routeParams) {
                <app-button-control
                  [component]="component"
                  [sidePanelPadding]="isSidePanelBuilder"
                  [formControlName]="component.id"
                  [label]="component.templateField.label"
                  [description]="component.templateField.helpDescription">
                </app-button-control>
              }
              @if (!routeParams) {
                <app-authoring-header [componentName]="component.componentType.name"></app-authoring-header>
              }
            </ng-container>
          }
          <!--INSTANCE INTEREST-->
          @case ('Instance Interest') {
            <ng-container [formGroupName]="formGroupName">
              @if (routeParams) {
                <app-instance-interest-control
                  [sidePanelPadding]="isSidePanelBuilder"
                  [formControlName]="component.id"
                  [label]="component.templateField.label"
                  [description]="component.templateField.helpDescription"
                  [component]="component"></app-instance-interest-control>
              } @else {
                <app-authoring-header [componentName]="component.componentType.name"></app-authoring-header>
              }
            </ng-container>
          }
          <!--ACCORDION-->
          @case ('Accordion') {
            <ng-container [formGroupName]="formGroupName">
              <app-accordion
                [component]="component"
                [type]="component.componentType.name"
                [formControlName]="component.id"
                [label]="component.templateField.label"
                [description]="component.templateField.helpDescription"></app-accordion>
            </ng-container>
          }
          <!--FLASH CARD-->
          @case (component?.componentType?.name === 'Flash Card' || component?.componentType?.name === 'Picture Flash Card' ? component?.componentType?.name : '') {
            <ng-container [formGroupName]="formGroupName">
              <app-accordion
                [component]="component"
                [type]="component.componentType.name"
                [formControlName]="component.id"
                [label]="component.templateField.label"
                [description]="component.templateField.helpDescription"></app-accordion>
            </ng-container>
          }
          <!--IMAGE CENTERED-->
          @case ('Image Centered') {
            <ng-container [formGroupName]="formGroupName">
              <app-image-centered
                [formControlName]="component.id"
                [label]="component.templateField.label"
                [description]="component.templateField.helpDescription"
                [component]="component"></app-image-centered>
            </ng-container>
          }
          <!--MEDIA AND TEXT-->
          @case ('Media & Text') {
            <ng-container [formGroupName]="formGroupName">
              <app-media-and-text [component]="component" [formControlName]="component.id" [label]="component.templateField.label" [description]="component.templateField.helpDescription">
              </app-media-and-text>
            </ng-container>
          }
          <!--ICON AND TEXT-->
          @case ('Icon & Text') {
            <ng-container [formGroupName]="formGroupName">
              <app-icon-and-text [component]="component" [sidePanelPadding]="isSidePanelBuilder" [formControlName]="component.id" [label]="component.templateField.label"> </app-icon-and-text>
            </ng-container>
          }
          <!--ATTACHMENT-->
          @case ('Attachment') {
            <ng-container [formGroupName]="formGroupName">
              <app-file-upload-control
                [sidePanelPadding]="isSidePanelBuilder"
                [label]="component?.templateField?.label ?? ''"
                [itemBackgroundColor]="controlBackground"
                [fileFormat]="'/*/'"
                [fileTypeBw]="component?.templateField?.fileTypeBw"
                [minFileSize]="component?.templateField?.minFileSize"
                [maxFileSize]="component?.templateField?.maxFileSize"
                [componentType]="component?.componentType?.name"
                [formControlName]="component.id"
                [defaultImageUrl]="component?.templateField?.defaultImageUrl ?? ''"
                [buttonText]="component?.templateField?.buttonText ?? ''"
                [component]="component"
                [placeHolderText]="component?.templateField?.placeHolderText ?? ''"></app-file-upload-control>
            </ng-container>
          }
          <!--INSTANCE DETAILS-->
          @case ('Listing Details') {
            <ng-container [formGroupName]="formGroupName">
              <app-instance-details-control [instance]="instance" [component]="component" [formControlName]="component.id" [textValue]="getFormControlValue()"></app-instance-details-control>
            </ng-container>
          }
          <!--FULLNAME-->
          @case ('Full Name') {
            <ng-container [formGroupName]="formGroupName">
              <app-full-name
                [sidePanelPadding]="isSidePanelBuilder"
                [formControlName]="component.id"
                [label]="component.templateField.label"
                [description]="component.templateField.helpDescription"></app-full-name>
            </ng-container>
          }
          <!-- PHONE NUMBER -->
          @case ('Phone Number') {
            <ng-container [formGroupName]="formGroupName">
              <app-phone-number [placeHolder]="'(___) ___-____'" [label]="component?.templateField?.label ?? ''" [formControlName]="component.id"> </app-phone-number>
            </ng-container>
          }
          <!-- CONTACT INFO -->
          @case ('Contact Info') {
            <ng-container [formGroupName]="formGroupName">
              <app-contact-info [sidePanelPadding]="isSidePanelBuilder" [id]="id" [formControlName]="component.id" [instance]="instance"> </app-contact-info>
            </ng-container>
          }
          <!-- LEARNING OUTCOMES -->
          @case ('Learning Outcomes') {
            <ng-container [formGroupName]="formGroupName">
              <app-dynamic-text-input-control
                [component]="component"
                [hideQuillPersonalize]="true"
                [sidePanelPadding]="isSidePanelBuilder"
                [noBorder]="isSidePanelBuilder"
                [noPadding]="isSidePanelBuilder"
                [toolTip]="component?.templateField?.toolTip ?? ''"
                [placeHolder]="component?.templateField?.placeHolderText ?? ''"
                [label]="component?.templateField?.label ?? ''"
                [formControlName]="component.id"
                [defaultValue]="component?.templateField?.defaultImageUrl"
                [showSelected]="showSelected"
                [itemBackgroundColor]="controlBackground">
              </app-dynamic-text-input-control>
            </ng-container>
          }
          <!-- Checkbox -->
          @case ('Checkbox') {
            <ng-container [formGroupName]="formGroupName">
              <app-checkbox-control [label]="component?.templateField?.label ?? ''" [formControlName]="component.id"> </app-checkbox-control>
            </ng-container>
          }
          <!--SPACING-->
          @case ('Spacing') {
            <ng-container [formGroupName]="formGroupName">
              @if (routeParams) {
                <app-spacing-control [component]="component" [sidePanelPadding]="isSidePanelBuilder" [formControlName]="component.id"></app-spacing-control>
              }
              @if (!routeParams) {
                <app-authoring-header [componentName]="component.componentType.name"></app-authoring-header>
              }
            </ng-container>
          }
        }
      }
    </div>
  }
}
