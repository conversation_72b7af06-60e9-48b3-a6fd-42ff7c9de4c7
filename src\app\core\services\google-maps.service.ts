import { Injectable } from '@angular/core';
import { environment } from '@env/environment';

@Injectable({
  providedIn: 'root',
})
export class GoogleMapsService {
  constructor() {}

  async registerGoogleAsync(): Promise<any> {
    return new Promise((resolve, reject) => {
      const id = 'googleMaps';
      if (document.getElementById(id) === null) {
        const scriptTag: HTMLScriptElement = document.createElement('script');
        scriptTag.id = id;
        scriptTag.src = `https://maps.googleapis.com/maps/api/js?libraries=places&key=${environment.googleMapApiKey}`;
        scriptTag.async = true;
        document.body.appendChild(scriptTag);
        scriptTag.onload = () => resolve(null);
        scriptTag.onerror = error => reject(error);
      } else {
        resolve(null);
      }
    });
  }
}
